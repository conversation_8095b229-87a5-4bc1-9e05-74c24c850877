import 'package:json_annotation/json_annotation.dart';

part 'config_area_response.g.dart';

@JsonSerializable(
  explicitToJson: true,
  genericArgumentFactories: true,
)
class ConfigAreaResponse {
  final DateTime? applyTime;
  @Json<PERSON>ey(name: 'active', defaultValue: false)
  final bool? isActive;

  ConfigAreaResponse({
    this.applyTime,
    this.isActive,
  });

  factory ConfigAreaResponse.fromJson(Map<String, dynamic> json) =>
      _$ConfigAreaResponseFromJson(json);

  Map<String, dynamic> toJson() => _$ConfigAreaResponseToJson(this);
}
