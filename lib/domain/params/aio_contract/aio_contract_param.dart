import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_supply_entity.dart';
import 'package:vcc/domain/params/aio_contract/aio_area.dart';
import 'package:vcc/domain/params/aio_contract/aio_customer.dart';
import 'package:vcc/domain/params/aio_contract/app_param.dart';
import 'package:vcc/domain/params/aio_contract/cat_province.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';

part 'aio_contract_param.g.dart';

@JsonSerializable(
  includeIfNull: false,
  explicitToJson: true,
)
class AioContractParam {
  final SysUserRequest? sysUserRequest;
  final AioContractEntity? aioContractDTO;
  final CatProvince? catProvinceDTO;
  final AioArea? aioAreaDTO;
  final List<AioPackageEntity>? lstAIOPackageDetail;
  final List<AioServiceEntity?>? hsServiceList;
  final AppParam? appParam;
  final String? listEmployee;
  final int? sysUserId;
  final int? surveyId;
  final int? sysGroupId;
  final bool? canOrderRequest;
  final AioCustomer? aioCustomerDTO;
  final List<int?>? listGoodsId;
  final int? contractId;
  final List<AioPackageEntity>? lstAIOContractMobileDTO;
  final List<AioSupplyEntity>? listProductSelected;
  String? performTogether;
  String? keySearch;
  int? stockId;

  AioContractParam({
    this.sysUserRequest,
    this.stockId,
    this.keySearch,
    this.lstAIOContractMobileDTO,
    this.hsServiceList,
    this.aioContractDTO,
    this.catProvinceDTO,
    this.aioAreaDTO,
    this.lstAIOPackageDetail,
    this.appParam,
    this.listEmployee,
    this.sysUserId,
    this.surveyId,
    this.sysGroupId,
    this.canOrderRequest,
    this.aioCustomerDTO,
    this.listGoodsId,
    this.contractId,
    this.listProductSelected,
    this.performTogether,
  });

  factory AioContractParam.fromJson(Map<String, dynamic> json) =>
      _$AioContractParamFromJson(json);

  Map<String, dynamic> toJson() => _$AioContractParamToJson(this);
}
