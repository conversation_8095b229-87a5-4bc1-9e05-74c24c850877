import 'package:vcc/domain/entities/campaign_entity.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/enums/collection_status.dart';
import 'package:vcc/domain/enums/user_type.dart';

class FilterCollectionEntity {
  CampaignEntity? campaign;
  UserType? customerType;
  List<CollectionStatus>? collectionStatus;
  List<CodeEntity>? districtCodes;
  String? keySearch;

  FilterCollectionEntity({
    this.campaign,
    this.customerType,
    this.collectionStatus,
    this.keySearch,
    this.districtCodes,
  });

  bool get isFilter {
    return campaign != null ||
        customerType != null ||
        collectionStatus != null ||
        districtCodes != null;
  }
}
