import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';

part 'aio_invoice_entity.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class AioInvoiceEntity {
  int? contractId;
  int? aioOrderId;
  int? performerId;
  String? contractCode;
  String? customerPhone;
  int? totalAmount;
  int? customerType;
  int? invoiceType;
  int? createdBy;
  String? address;
  String? bankAccount;
  String? bankName;
  String? companyName;
  String? customerName;
  String? paymentMethod;
  String? email;
  String? description;
  String? taxCode;
  List<AioInvoiceItemEntity>? aioInvoiceItemDTOS;

  AioInvoiceEntity({
    this.contractId,
    this.aioOrderId,
    this.createdBy,
    this.performerId,
    this.contractCode,
    this.customerPhone,
    this.totalAmount,
    this.customerType,
    this.invoiceType,
    this.address,
    this.bankAccount,
    this.bankName,
    this.companyName,
    this.customerName,
    this.paymentMethod,
    this.email,
    this.description,
    this.taxCode,
    this.aioInvoiceItemDTOS,
  });

  factory AioInvoiceEntity.fromJson(Map<String, dynamic> json) =>
      _$AioInvoiceEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AioInvoiceEntityToJson(this);
}
