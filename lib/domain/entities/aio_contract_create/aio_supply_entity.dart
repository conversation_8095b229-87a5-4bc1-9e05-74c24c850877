import 'package:json_annotation/json_annotation.dart';

part 'aio_supply_entity.g.dart';

@JsonSerializable(
  includeIfNull: false,
  explicitToJson: true,
)
class AioSupplyEntity {
  int? goodsId;
  String? goodsName;
  int? sysUserId;
  double? quantityInStock;
  String? phone;
  String? employeeCode;
  String? fullName;
  String? goodsCode;
  String? goodsUnitName;
  double? quantity;
  double? applyPrice;
  int? isSerial;
  String? serialNumber;
  bool? isSelected;
  double? quantitySelected;

  AioSupplyEntity({
    this.goodsId,
    this.goodsName,
    this.sysUserId,
    this.quantityInStock,
    this.phone,
    this.employeeCode,
    this.fullName,
    this.goodsCode,
    this.goodsUnitName,
    this.quantity,
    this.applyPrice,
    this.isSerial,
    this.serialNumber,
    this.isSelected,
    this.quantitySelected,
  });

  AioSupplyEntity copyWith({
    int? goodsId,
    String? goodsName,
    int? sysUserId,
    double? quantityInStock,
    String? phone,
    String? employeeCode,
    String? fullName,
    String? goodsCode,
    String? goodsUnitName,
    double? quantity,
    double? applyPrice,
    int? isSerial,
    String? serialNumber,
    bool? isSelected,
    double? quantitySelected,
  }) {
    return AioSupplyEntity(
      goodsId: goodsId ?? this.goodsId,
      goodsName: goodsName ?? this.goodsName,
      sysUserId: sysUserId ?? this.sysUserId,
      quantityInStock: quantityInStock ?? this.quantityInStock,
      phone: phone ?? this.phone,
      employeeCode: employeeCode ?? this.employeeCode,
      fullName: fullName ?? this.fullName,
      goodsCode: goodsCode ?? this.goodsCode,
      goodsUnitName: goodsUnitName ?? this.goodsUnitName,
      quantity: quantity ?? this.quantity,
      applyPrice: applyPrice ?? this.applyPrice,
      isSerial: isSerial ?? this.isSerial,
      serialNumber: serialNumber ?? this.serialNumber,
      isSelected: isSelected ?? this.isSelected,
      quantitySelected: quantitySelected ?? this.quantitySelected,
    );
  }

  factory AioSupplyEntity.fromJson(Map<String, dynamic> json) =>
      _$AioSupplyEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AioSupplyEntityToJson(this);
}
