import 'package:json_annotation/json_annotation.dart';

part 'schedule_entity.g.dart';

@JsonSerializable()
class ScheduleEntity {
  @JsonKey(name: "workingDay")
  DateTime? workingDay;
  @Json<PERSON>ey(name: "hasWork")
  bool? hasWork;

  ScheduleEntity({
    this.workingDay,
    this.hasWork,
  });

  factory ScheduleEntity.fromJson(Map<String, dynamic> json) =>
      _$ScheduleEntityFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduleEntityToJson(this);
}
