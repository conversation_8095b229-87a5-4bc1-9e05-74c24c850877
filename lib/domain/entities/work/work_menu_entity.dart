import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/enums/order_type.dart';

part 'work_menu_entity.g.dart';

@JsonSerializable()
class WorkMenuEntity {
  final String? title;
  final String? code;
  final String? icon;
  final List<WorkMenuEntity>? items;
  final OrderType? orderType;
  final bool showMenu;

  WorkMenuEntity({
    this.title,
    this.code,
    this.icon,
    this.items,
    this.orderType,
    this.showMenu = true,
  });

  factory WorkMenuEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkMenuEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkMenuEntityToJson(this);
}
