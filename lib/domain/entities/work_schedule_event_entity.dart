import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/schedule_entity.dart';

part 'work_schedule_event_entity.g.dart';

@JsonSerializable()
class WorkScheduleEventEntity {
  @JsonKey(name: "schedules")
  List<ScheduleEntity>? schedules;

  WorkScheduleEventEntity({
    this.schedules,
  });

  factory WorkScheduleEventEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkScheduleEventEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkScheduleEventEntityToJson(this);
}
