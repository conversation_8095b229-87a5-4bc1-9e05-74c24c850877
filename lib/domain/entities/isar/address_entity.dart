import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/address_info.dart';
import 'package:vcc/domain/entities/code_entity.dart';

part 'address_entity.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class AddressEntity {
  String? id;
  String? username;
  CodeEntity? province;
  CodeEntity? district;
  CodeEntity? ward;
  String? addressDetail;
  bool? isGetFromLocal;

  AddressEntity({
    this.province,
    this.district,
    this.ward,
    this.addressDetail,
    this.isGetFromLocal,
    this.username,
    this.id,
  });

  String getDistrictAndProvince() {
    return "${district?.name}, ${province?.name}";
  }

  String get getFullAddress {
    final addressDetailTemp =
        (addressDetail ?? '').isEmpty ? '' : '$addressDetail, ';

    final wardTemp = (ward?.name ?? '').isEmpty ? '' : '${ward?.name}';

    final districtTemp =
        (district?.name ?? '').isEmpty ? '' : ', ${district?.name}';

    final provinceTemp =
        (province?.name ?? '').isEmpty ? '' : ', ${province?.name}';

    return addressDetailTemp + wardTemp + districtTemp + provinceTemp;
  }

  AddressInfo get convertToAddressInfo {
    return AddressInfo(
      address: getFullAddress,
      addressDetail: addressDetail,
      ward: ward,
      district: district,
      province: province,
      isGetFromLocal: isGetFromLocal,
    );
  }

  factory AddressEntity.fromJson(Map<String, dynamic> json) =>
      _$AddressEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AddressEntityToJson(this);
}
