import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/user/organization_entity.dart';
import 'package:vcc/domain/entities/user/sale_address_entity.dart';
import 'package:vcc/domain/enums/timekeeping/timekeeping_role_user_enum.dart';
import 'package:vcc/domain/enums/user_company_type.dart';

part 'internal_staff_entity.g.dart';

@JsonSerializable()
class InternalStaffEntity {
  final String? username;
  final int? userId;
  final int? sysUserId;
  final int? sysGroupId;
  final String? fullName;
  final String? employeeCode;
  final String? loginName;
  final String? dob;
  final String? phoneNumber;
  final String? introducedCode;
  final String? email;
  final String? gender;
  final String? position;
  final String? positionName;
  final bool? isActive;
  final String? provinceCode;
  final String? districtCode;
  final String? wardCode;
  final String? keySearch;
  final String? areaCode;
  final SaleAddressEntity? saleAddress;
  final OrganizationEntity? groups;
  final bool? isMobileCall;
  final String? saleGroupType;
  bool? isSelect;
  bool? isSalePoint;
  int? percentBonus;
  final List<String>? roles;

  //for case convert to seller in order
  @JsonKey(
    includeFromJson: false,
    includeToJson: false,
  )
  final bool? isCreatedOrder;

  @JsonKey(fromJson: _userTypeFromString)
  final UserCompanyType? userType;

  InternalStaffEntity({
    this.username,
    this.areaCode,
    this.keySearch,
    this.employeeCode,
    this.loginName,
    this.userId,
    this.sysUserId,
    this.sysGroupId,
    this.fullName,
    this.dob,
    this.phoneNumber,
    this.introducedCode,
    this.email,
    this.gender,
    this.userType,
    this.positionName,
    this.isActive,
    this.provinceCode,
    this.districtCode,
    this.wardCode,
    this.isSelect,
    this.saleAddress,
    this.percentBonus,
    this.groups,
    this.isMobileCall,
    this.isCreatedOrder,
    this.position,
    this.saleGroupType,
    this.isSalePoint,
    this.roles,
  });

  String get getProvinceDefault {
    return saleAddress?.provinceCode ?? '';
  }

  bool get canViewGroup {
    return position == 'SALE_MEMBER';
  }

  bool get canCreateSalePointOrder {
    return position == 'SALE_MEMBER' && isSalePoint == true;
  }

  bool get isTimekeepingManager {
    return roles?.contains(TimekeepingRoleUserEnum.cnctManager.serverKey) ?? false;
  }

  bool get isTimekeepingFT3 {
    return roles?.contains(TimekeepingRoleUserEnum.ft3.serverKey) ?? false;
  }

  bool get isTimekeepingIconVisible {
    return isTimekeepingManager || isTimekeepingFT3;
  }

  static UserCompanyType? _userTypeFromString(String? userType) =>
      UserCompanyTypeExtension.fromString(userType);

  factory InternalStaffEntity.fromJson(Map<String, dynamic> json) =>
      _$InternalStaffEntityFromJson(json);

  Map<String, dynamic> toJson() => _$InternalStaffEntityToJson(this);
}
