import 'package:json_annotation/json_annotation.dart';

part 'file_upload_entity.g.dart';

@JsonSerializable(
  includeIfNull: false,
  explicitToJson: true,
)
class FileUploadEntity {
  final String? fileName;
  final String? base64String;
  final int? utilAttachDocumentId;
  final String? objectId;
  String? type;
  final String? description;
  final String? path;
  final String? createdDate;
  final String? link;

  FileUploadEntity({
    this.fileName,
    this.base64String,
    this.utilAttachDocumentId,
    this.objectId,
    this.type,
    this.description,
    this.path,
    this.createdDate,
    this.link,
  });

  factory FileUploadEntity.fromJson(Map<String, dynamic> json) =>
      _$FileUploadEntityFromJson(json);

  Map<String, dynamic> toJson() => _$FileUploadEntityToJson(this);
}
