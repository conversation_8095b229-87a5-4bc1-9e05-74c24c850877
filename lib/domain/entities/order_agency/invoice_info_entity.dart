import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/complain/result_info.dart';

part 'invoice_info_entity.g.dart';

@JsonSerializable()
class InvoiceInfoEntity {
  @Json<PERSON>ey(name: "resultInfo")
  final ResultInfo? resultInfo;
  @Json<PERSON>ey(name: "data")
  final InvoiceData? data;

  InvoiceInfoEntity({
    this.resultInfo,
    this.data,
  });

  InvoiceInfoEntity copyWith({
    ResultInfo? resultInfo,
    InvoiceData? data,
  }) =>
      InvoiceInfoEntity(
        resultInfo: resultInfo ?? this.resultInfo,
        data: data ?? this.data,
      );

  factory InvoiceInfoEntity.fromJson(Map<String, dynamic> json) =>
      _$InvoiceInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => _$InvoiceInfoEntityToJson(this);
}

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class InvoiceData {
  final String? defaultSortField;
  final int? messageColumn;
  final bool? isSize;
  final int? contractId;
  final String? customerName;
  final String? address;
  final String? customerPhone;
  final String? email;
  final String? description;
  final dynamic invoiceDate;
  final dynamic invoiceDateFrom;
  final dynamic invoiceDateTo;
  final dynamic createdDate;
  final dynamic createdDateFrom;
  final dynamic createdDateTo;
  final dynamic updatedDate;
  final dynamic updatedDateFrom;
  final dynamic updatedDateTo;
  final int? totalAmount;
  final int? customerType;
  final int? invoiceType;
  final String? contractCode;
  final dynamic startDate;
  final dynamic endDate;
  final dynamic rejectedDate;
  final dynamic approveDate;
  final int? performerId;
  final List<AioInvoiceItemDto>? aioInvoiceItemDTOS;
  final List<String>? notes;

  InvoiceData({
    this.defaultSortField,
    this.messageColumn,
    this.isSize,
    this.contractId,
    this.customerName,
    this.address,
    this.customerPhone,
    this.email,
    this.description,
    this.invoiceDate,
    this.invoiceDateFrom,
    this.invoiceDateTo,
    this.createdDate,
    this.createdDateFrom,
    this.createdDateTo,
    this.updatedDate,
    this.updatedDateFrom,
    this.updatedDateTo,
    this.totalAmount,
    this.customerType,
    this.invoiceType,
    this.contractCode,
    this.startDate,
    this.endDate,
    this.rejectedDate,
    this.approveDate,
    this.performerId,
    this.aioInvoiceItemDTOS,
    this.notes,
  });

  InvoiceData copyWith({
    String? defaultSortField,
    int? messageColumn,
    bool? isSize,
    int? contractId,
    String? customerName,
    String? address,
    String? customerPhone,
    String? email,
    String? description,
    dynamic invoiceDate,
    dynamic invoiceDateFrom,
    dynamic invoiceDateTo,
    dynamic createdDate,
    dynamic createdDateFrom,
    dynamic createdDateTo,
    dynamic updatedDate,
    dynamic updatedDateFrom,
    dynamic updatedDateTo,
    int? totalAmount,
    int? customerType,
    int? invoiceType,
    String? contractCode,
    dynamic startDate,
    dynamic endDate,
    dynamic rejectedDate,
    dynamic approveDate,
    int? performerId,
    List<AioInvoiceItemDto>? aioInvoiceItemDTOS,
    List<String>? notes,
  }) =>
      InvoiceData(
        defaultSortField: defaultSortField ?? this.defaultSortField,
        messageColumn: messageColumn ?? this.messageColumn,
        isSize: isSize ?? this.isSize,
        contractId: contractId ?? this.contractId,
        customerName: customerName ?? this.customerName,
        address: address ?? this.address,
        customerPhone: customerPhone ?? this.customerPhone,
        email: email ?? this.email,
        description: description ?? this.description,
        invoiceDate: invoiceDate ?? this.invoiceDate,
        invoiceDateFrom: invoiceDateFrom ?? this.invoiceDateFrom,
        invoiceDateTo: invoiceDateTo ?? this.invoiceDateTo,
        createdDate: createdDate ?? this.createdDate,
        createdDateFrom: createdDateFrom ?? this.createdDateFrom,
        createdDateTo: createdDateTo ?? this.createdDateTo,
        updatedDate: updatedDate ?? this.updatedDate,
        updatedDateFrom: updatedDateFrom ?? this.updatedDateFrom,
        updatedDateTo: updatedDateTo ?? this.updatedDateTo,
        totalAmount: totalAmount ?? this.totalAmount,
        customerType: customerType ?? this.customerType,
        invoiceType: invoiceType ?? this.invoiceType,
        contractCode: contractCode ?? this.contractCode,
        startDate: startDate ?? this.startDate,
        endDate: endDate ?? this.endDate,
        rejectedDate: rejectedDate ?? this.rejectedDate,
        approveDate: approveDate ?? this.approveDate,
        performerId: performerId ?? this.performerId,
        aioInvoiceItemDTOS: aioInvoiceItemDTOS ?? this.aioInvoiceItemDTOS,
        notes: notes ?? this.notes,
      );

  factory InvoiceData.fromJson(Map<String, dynamic> json) =>
      _$InvoiceDataFromJson(json);

  Map<String, dynamic> toJson() => _$InvoiceDataToJson(this);
}

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class AioInvoiceItemDto {
  String? defaultSortField;
  int? messageColumn;
  bool? isSize;
  int? taxPercent;
  int? goodsId;
  String? goodsCode;
  String? goodsName;
  int? quantity;
  double? price;
  double? amountBeforeTax;
  double? preTaxAmount;
  dynamic createdDate;
  dynamic updatedDate;
  int? goodsUnitId;
  String? goodsUnitName;
  int? amount;
  int? packageId;
  final dynamic selection;
  final dynamic isDisable;

  AioInvoiceItemDto({
    this.defaultSortField,
    this.messageColumn,
    this.isSize,
    this.taxPercent,
    this.goodsId,
    this.goodsCode,
    this.goodsName,
    this.quantity,
    this.price,
    this.amountBeforeTax,
    this.preTaxAmount,
    this.createdDate,
    this.updatedDate,
    this.goodsUnitId,
    this.goodsUnitName,
    this.amount,
    this.packageId,
    this.selection,
    this.isDisable,
  });

  AioInvoiceItemDto copyWith({
    String? defaultSortField,
    int? messageColumn,
    bool? isSize,
    int? taxPercent,
    int? goodsId,
    String? goodsCode,
    String? goodsName,
    int? quantity,
    double? price,
    double? amountBeforeTax,
    double? preTaxAmount,
    dynamic createdDate,
    dynamic updatedDate,
    int? goodsUnitId,
    String? goodsUnitName,
    int? amount,
    int? packageId,
    dynamic selection,
    dynamic isDisable,
  }) =>
      AioInvoiceItemDto(
        defaultSortField: defaultSortField ?? this.defaultSortField,
        messageColumn: messageColumn ?? this.messageColumn,
        isSize: isSize ?? this.isSize,
        taxPercent: taxPercent ?? this.taxPercent,
        goodsId: goodsId ?? this.goodsId,
        goodsCode: goodsCode ?? this.goodsCode,
        goodsName: goodsName ?? this.goodsName,
        quantity: quantity ?? this.quantity,
        price: price ?? this.price,
        amountBeforeTax: amountBeforeTax ?? this.amountBeforeTax,
        preTaxAmount: preTaxAmount ?? this.preTaxAmount,
        createdDate: createdDate ?? this.createdDate,
        updatedDate: updatedDate ?? this.updatedDate,
        goodsUnitId: goodsUnitId ?? this.goodsUnitId,
        goodsUnitName: goodsUnitName ?? this.goodsUnitName,
        amount: amount ?? this.amount,
        packageId: packageId ?? this.packageId,
        selection: selection ?? this.selection,
        isDisable: isDisable ?? this.isDisable,
      );

  factory AioInvoiceItemDto.fromJson(Map<String, dynamic> json) =>
      _$AioInvoiceItemDtoFromJson(json);

  Map<String, dynamic> toJson() => _$AioInvoiceItemDtoToJson(this);
}
