import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_confirm_type.dart';

part 'aio_contract_detail_entity.g.dart';

@JsonSerializable()
class AioContractDetailEntity {
  final String? name;
  final String? code;
  final String? workName;
  final String? startDate;
  final String? endDate;
  final String? customerPhone;
  final int? packageDetailId;
  final String? packageName;
  final int? type;
  final int? contractId;
  final int? isMoney;
  final int? contractDetailId;
  String? isInternal;
  final int? isProvinceBought;
  final int? subAction;
  final int? detailFinished;
  final String? signDate;
  final String? approvedDate;
  final int? customerId;
  final int? contractAmount;
  final int? amountPay;
  final int? approvedPay;
  final int? contractType;
  final int? isBill;
  final int? amountBeforeVat;
  final int? configServiceStatus;
  final int? amountDetail;
  final int? acceptanceRecordsId;
  final int? utilAttachDocumentId;
  final String? dateStartContract;
  final String? startDateDelete;
  final String? contractCode;
  final String? saleChannel;
  final String? serviceCode;
  final String? industryCode;
  final String? customerName;
  final String? description;
  final String? customerAddress;
  final int? performerId;
  final int? aioCancelOrderId;
  final bool? canProposeCancel;
  final bool? canDeleteProposeCancel;
  final String? havePackageClose;
  int? haveLastPackage;
  AioConfirmType isBillType;
  String? reasonOutOfDate;
  DateTime? cancelDate;
  int? amount;
  num? quantity;
  int? payType;
  int? packageId;
  int? status;
  int? statusContract;
  int accessoriesCost;
  int moneyPromotion;
  int discountStaff;
  int performCosts;
  int marketingCosts;
  int guestCosts;
  int shippingUnloadingCosts;
  int outsourcingCosts;
  int branchCosts;
  int saleCosts;
  int accessoriesCosts;
  int performerCosts;
  int? price;
  int? checkBill;

  AioContractDetailEntity({
    this.name,
    this.price,
    this.checkBill,
    this.quantity,
    this.code,
    this.workName,
    this.startDate,
    this.endDate,
    this.customerPhone,
    this.packageDetailId,
    this.packageName,
    this.status,
    this.type,
    this.contractId,
    this.contractDetailId,
    this.statusContract,
    this.amountPay,
    this.isMoney,
    this.isProvinceBought,
    this.isInternal,
    this.subAction,
    this.detailFinished,
    this.signDate,
    this.approvedDate,
    this.customerId,
    this.contractAmount,
    this.payType,
    this.approvedPay,
    this.contractType,
    this.isBill,
    this.amountBeforeVat,
    this.configServiceStatus,
    this.packageId,
    this.amountDetail,
    this.acceptanceRecordsId,
    this.utilAttachDocumentId,
    this.dateStartContract,
    this.startDateDelete,
    this.contractCode,
    this.saleChannel,
    this.serviceCode,
    this.industryCode,
    this.customerName,
    this.description,
    this.customerAddress,
    this.reasonOutOfDate,
    this.cancelDate,
    this.performerId,
    this.aioCancelOrderId,
    this.canProposeCancel,
    this.canDeleteProposeCancel,
    this.amount,
    this.haveLastPackage,
    this.havePackageClose,
    this.isBillType = AioConfirmType.no,
    this.guestCosts = 0,
    this.accessoriesCost = 0,
    this.moneyPromotion = 0,
    this.shippingUnloadingCosts = 0,
    this.saleCosts = 0,
    this.branchCosts = 0,
    this.discountStaff = 0,
    this.performCosts = 0,
    this.marketingCosts = 0,
    this.outsourcingCosts = 0,
    this.accessoriesCosts = 0,
    this.performerCosts = 0,
  });

  AioContractEntity toAioContractEntity() {
    return AioContractEntity(
      contractId: contractId,
      contractCode: contractCode,
      customerName: customerName,
      customerPhone: customerPhone,
      customerAddress: customerAddress,
      performerId: performerId,
      status: status,
      type: type,
      isMoney: isMoney,
      contractAmount: contractAmount,
      payType: payType,
      approvedPay: approvedPay,
      contractDetailId: contractDetailId,
      contractType: contractType,
      detailFinished: detailFinished,
      subAction: subAction,
      canProposeCancel: canProposeCancel,
      canDeleteProposeCancel: canDeleteProposeCancel,
      dateStartContract: dateStartContract,
      configServiceStatus: configServiceStatus,
      packageDetailId: packageDetailId,
      amountDetail: amountDetail,
      discountStaff: discountStaff,
      acceptanceRecordsId: acceptanceRecordsId,
      utilAttachDocumentId: utilAttachDocumentId,
    );
  }

  factory AioContractDetailEntity.fromJson(Map<String, dynamic> json) =>
      _$AioContractDetailEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AioContractDetailEntityToJson(this);
}
