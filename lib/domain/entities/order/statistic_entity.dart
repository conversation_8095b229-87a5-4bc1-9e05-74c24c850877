import 'package:flutter/material.dart';
import 'package:json_annotation/json_annotation.dart';

part 'statistic_entity.g.dart';

@JsonSerializable()
class StatisticEntity {
  final String? title;
  final String? label;
  final String? code;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final Widget? icon;
  final int? orderCount;
  final int? orderCreatedSum;
  final bool? isOrder;

  StatisticEntity({
    this.title,
    this.label,
    this.code,
    this.icon,
    this.orderCount,
    this.orderCreatedSum,
    this.isOrder = true,
  });

  factory StatisticEntity.fromJson(Map<String, dynamic> json) =>
      _$StatisticEntityFromJson(json);

  Map<String, dynamic> toJson() => _$StatisticEntityToJson(this);
}
