import 'package:json_annotation/json_annotation.dart';

part 'work_count_entity.g.dart';

@JsonSerializable()
class WorkCountEntity {
  @<PERSON><PERSON><PERSON><PERSON>(name: "deploymentOrderCount")
  final int? deploymentOrderCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "bundleOrderCount")
  final int? bundleOrderCount;
  @<PERSON>son<PERSON>ey(name: "customerClaimCount")
  final int? customerClaimCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "customerInformationGatherCount")
  final int? customerInformationGatherCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "warrantyRequestCount")
  final int? warrantyRequestCount;

  WorkCountEntity({
    this.deploymentOrderCount,
    this.bundleOrderCount,
    this.customerClaimCount,
    this.customerInformationGatherCount,
    this.warrantyRequestCount,
  });

  WorkCountEntity copyWith({
    int? deploymentOrderCount,
    dynamic bundleOrderCount,
    dynamic customerClaimCount,
    dynamic customerInformationGatherCount,
    int? warrantyRequestCount,
  }) =>
      WorkCountEntity(
        deploymentOrderCount: deploymentOrderCount ?? this.deploymentOrderCount,
        bundleOrderCount: bundleOrderCount ?? this.bundleOrderCount,
        customerClaimCount: customerClaimCount ?? this.customerClaimCount,
        customerInformationGatherCount: customerInformationGatherCount ??
            this.customerInformationGatherCount,
        warrantyRequestCount: warrantyRequestCount ?? this.warrantyRequestCount,
      );

  factory WorkCountEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkCountEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkCountEntityToJson(this);
}
