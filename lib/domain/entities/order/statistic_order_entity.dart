import 'package:json_annotation/json_annotation.dart';

part 'statistic_order_entity.g.dart';

@JsonSerializable()
class StatisticOrderEntity {
  @<PERSON><PERSON><PERSON><PERSON>(name: "ordersCreatedCount")
  int? ordersCreatedCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ordersCreatedSum")
  int? ordersCreatedSum;
  @Json<PERSON><PERSON>(name: "ordersDeployedCount")
  int? ordersDeployedCount;
  @<PERSON>son<PERSON><PERSON>(name: "ordersDeployedSum")
  int? ordersDeployedSum;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ordersSaleCount")
  int? ordersSaleCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ordersSaleSum")
  int? ordersSaleSum;
  @<PERSON><PERSON><PERSON><PERSON>(name: "ordersWorkerCount")
  int? ordersWorkerCount;
  @Json<PERSON>ey(name: "ordersWorkerSum")
  int? ordersWorkerSum;
  @<PERSON><PERSON><PERSON><PERSON>(name: "totalCommission")
  int? totalCommission;
  @<PERSON><PERSON><PERSON><PERSON>(name: "totalIncome")
  int? totalIncome;

  StatisticOrderEntity({
    this.ordersCreatedCount,
    this.ordersCreatedSum,
    this.ordersDeployedCount,
    this.ordersDeployedSum,
    this.ordersSaleCount,
    this.ordersSaleSum,
    this.ordersWorkerCount,
    this.ordersWorkerSum,
    this.totalCommission,
    this.totalIncome,
  });

  factory StatisticOrderEntity.fromJson(Map<String, dynamic> json) =>
      _$StatisticOrderEntityFromJson(json);

  Map<String, dynamic> toJson() => _$StatisticOrderEntityToJson(this);
}
