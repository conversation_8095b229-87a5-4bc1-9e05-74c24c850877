import 'package:flutter/material.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/order/work_data_entity.dart';

part 'daily_work_entity.g.dart';

@JsonSerializable()
class DailyWorkEntity {
  final String? title;
  final String? code;
  @JsonKey(includeFromJson: false, includeToJson: false)
  final Widget? icon;
  final OrderType? orderType;
  final bool? showMenu;
  bool isExpand;
  final int? orderCount;
  List<WorkDataEntity>? workData;
  final String? workTypeKey;

  DailyWorkEntity({
    this.title,
    this.code,
    this.icon,
    this.orderType,
    this.orderCount,
    this.workData,
    this.showMenu = true,
    this.isExpand = false,
    this.workTypeKey,
  });

  DailyWorkEntity copyWith({
    String? title,
    String? code,
    Widget? icon,
    OrderType? orderType,
    bool? showMenu,
    bool? isExpand,
    int? orderCount,
    List<WorkDataEntity>? workData,
    String? workTypeKey,
  }) {
    return DailyWorkEntity(
      title: title ?? this.title,
      code: code ?? this.code,
      icon: icon ?? this.icon,
      orderType: orderType ?? this.orderType,
      orderCount: orderCount ?? this.orderCount,
      showMenu: showMenu ?? this.showMenu,
      isExpand: isExpand ?? this.isExpand,
      workData: workData ?? this.workData,
      workTypeKey: workTypeKey ?? this.workTypeKey,
    );
  }

  factory DailyWorkEntity.fromJson(Map<String, dynamic> json) =>
      _$DailyWorkEntityFromJson(json);

  Map<String, dynamic> toJson() => _$DailyWorkEntityToJson(this);
}
