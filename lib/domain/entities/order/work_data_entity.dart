import 'package:json_annotation/json_annotation.dart';

part 'work_data_entity.g.dart';

@JsonSerializable()
class WorkDataEntity {
  @<PERSON><PERSON><PERSON><PERSON>(name: "code")
  final String? code;
  @<PERSON><PERSON><PERSON><PERSON>(name: "status")
  final String? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: "name")
  final String? name;
  @<PERSON><PERSON><PERSON><PERSON>(name: "address")
  final String? address;
  @<PERSON><PERSON><PERSON><PERSON>(name: "startTime")
  final DateTime? startTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: "endTime")
  final DateTime? endTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: "isHotOrder")
  final bool? isHotOrder;
  @<PERSON><PERSON><PERSON><PERSON>(name: "id")
  final int? id;
  @<PERSON><PERSON><PERSON><PERSON>(name: "processKPIStatus")
  final String? processKPIStatus;

  WorkDataEntity({
    this.code,
    this.status,
    this.name,
    this.address,
    this.startTime,
    this.endTime,
    this.isHotOrder,
    this.id,
    this.processKPIStatus,
  });

  WorkDataEntity copyWith({
    String? code,
    String? status,
    String? name,
    String? address,
    DateTime? startTime,
    DateTime? endTime,
    bool? isHotOrder,
    int? id,
    String? processKPIStatus,
  }) =>
      WorkDataEntity(
        code: code ?? this.code,
        status: status ?? this.status,
        name: name ?? this.name,
        address: address ?? this.address,
        startTime: startTime ?? this.startTime,
        endTime: endTime ?? this.endTime,
        isHotOrder: isHotOrder ?? this.isHotOrder,
        id: id ?? this.id,
        processKPIStatus: processKPIStatus ?? this.processKPIStatus,
      );

  factory WorkDataEntity.fromJson(Map<String, dynamic> json) =>
      _$WorkDataEntityFromJson(json);

  Map<String, dynamic> toJson() => _$WorkDataEntityToJson(this);
}
