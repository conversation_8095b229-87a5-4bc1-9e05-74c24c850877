import 'package:intl/intl.dart';
import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/enums/process_kpi_status.dart';
import 'package:vcc/domain/enums/transport_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/enums/verify_schedule_status.dart';
import 'package:vcc/utils/app_utils.dart';

part 'detail_order_shipping_entity.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class DetailOrderShippingEntity {
  final String? receiverId;
  final String? receiverName;
  @JsonKey(fromJson: _customerTypeFromString)
  final UserType? customerType;
  final String? receiverPhone;
  final String? receiverAddress;
  final String? note;
  final String? startTime;
  final String? endTime;
  final String? scheduleTime;
  final String? customerId;
  final int? shippingServiceFee;
  final int? inputtedDistance;
  @JsonKey(fromJson: _scheduleTimeFromString)
  final VerifyScheduleStatus? scheduleStatus;

  String? meetingTime;

  final String? customerPhone;
  final String? districtCode;
  final String? provinceCode;
  final String? wardCode;

  @JsonKey(fromJson: _scheduleKpiStatusFromString)
  final ProcessKpiStatus? scheduleKPIStatus;
  @JsonKey(fromJson: _expiredKpiStatusFromString)
  final ProcessKpiStatus? processKPIStatus;
  final String? expireTime;
  @JsonKey(fromJson: _transportFromString)
  final TransportType? transportType;

  final String? courierFullName; //ten buu ta
  final String? courierPhoneNumber; //sdt buu ta
  final String? postShippingCode; //mã vận đơn

  DetailOrderShippingEntity({
    this.receiverId,
    this.receiverName,
    this.customerType,
    this.receiverPhone,
    this.receiverAddress,
    this.note,
    this.startTime,
    this.endTime,
    this.customerId,
    this.meetingTime,
    this.scheduleTime,
    this.shippingServiceFee,
    this.inputtedDistance,
    this.scheduleStatus,
    this.customerPhone,
    this.districtCode,
    this.provinceCode,
    this.scheduleKPIStatus,
    this.processKPIStatus,
    this.expireTime,
    this.transportType,
    this.courierFullName,
    this.courierPhoneNumber,
    this.postShippingCode,
    this.wardCode,
  });

  String get getTimeRange {
    if ((startTime ?? '').isNotEmpty && (endTime ?? '').isNotEmpty) {
      return AppUtils.formatDateTimeRange(startTime!, endTime!);
    }
    return '';
  }

  String get startTimeFormatted {
    if ((startTime ?? '').isNotEmpty) {
      return DateFormat(DateTimeFormater.hotOrderTimeFormat).format(
        DateTime.parse(startTime!),
      );
    }
    return '';
  }

  String get endTimeFormatted {
    if ((endTime ?? '').isNotEmpty) {
      return DateFormat(DateTimeFormater.hotOrderTimeFormat).format(
        DateTime.parse(endTime!),
      );
    }
    return '';
  }

  String get scheduleTimeFormatted {
    if ((scheduleTime ?? '').isNotEmpty) {
      return DateFormat(DateTimeFormater.extendDateTimeFormat).format(
        DateTime.parse(scheduleTime!),
      );
    }
    return '';
  }

  bool get pendingSchedule {
    return scheduleStatus == VerifyScheduleStatus.wait && scheduleTime != null;
  }

  static UserType? _customerTypeFromString(String? status) =>
      UserTypeExtension.fromString(status);

  static VerifyScheduleStatus? _scheduleTimeFromString(
    String? confirmScheduleTimeStatus,
  ) {
    return VerifyScheduleStatusExtension.fromString(confirmScheduleTimeStatus);
  }

  static ProcessKpiStatus? _scheduleKpiStatusFromString(
          String? scheduleKPIStatus) =>
      ProcessKpiStatusExtension.fromString(scheduleKPIStatus);

  static ProcessKpiStatus? _expiredKpiStatusFromString(
          String? processKPIStatus) =>
      ProcessKpiStatusExtension.fromString(processKPIStatus);

  static TransportType? _transportFromString(String? type) =>
      TransportTypeExtension.fromString(type);

  factory DetailOrderShippingEntity.fromJson(Map<String, dynamic> json) =>
      _$DetailOrderShippingEntityFromJson(json);

  Map<String, dynamic> toJson() => _$DetailOrderShippingEntityToJson(this);
}
