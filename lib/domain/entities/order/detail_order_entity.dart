import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/entities/order/detail_order_intervene_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_item_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_payment_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_shipping_entity.dart';
import 'package:vcc/domain/entities/order/file_entity.dart';
import 'package:vcc/domain/enums/commission_confirm_status.dart';
import 'package:vcc/domain/enums/file_type.dart';
import 'package:vcc/domain/enums/order_generate_type_enum.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/order_sub_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/role_order_shipping_enum.dart';
import 'package:vcc/domain/enums/source_order.dart';
import 'detail_order_order_amount_entity.dart';

part 'detail_order_entity.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class DetailOrderEntity {
  @JsonKey(fromJson: _statusFromString)
  OrderStatus? status;
  @JsonKey(fromJson: _subStatusFromString)
  OrderSubStatus? orderStatus;
  final DetailOrderShippingEntity? shippingInfo;
  final List<DetailOrderItemEntity>? orderItems;
  final DetailOrderOrderAmountEntity? orderAmount;
  final DetailOrderPaymentEntity? paymentInfo;
  DetailOrderInterveneEntity? processUserInfo;
  @JsonKey(fromJson: _orderTypeFromString)
  final OrderType? orderType;
  @JsonKey(fromJson: _orderGenerateTypeFromString)
  final OrderGenerateTypeEnum? generateType;
  final String? orderCode;
  final String? orderName;
  final String? packageCode;
  final List<String>? roles;
  final String? chanel;
  @JsonKey(fromJson: _sourceOrderFromString)
  final SourceOrder? source;
  final String? requestCancelOrderId;
  final bool? isCalled;
  final int? shippingServiceFee;
  final int? distance;
  final bool? isHotOrder;
  final String? contactRequestCode;
  final List<String>? images;
  final List<FileEntity>? certificates;
  @JsonKey(name: 'requestExtendKPIId')
  final String? requestExtendKpiId;
  final String? completeOrderTime;
  final String? tenantId;
  final bool? isConnectAio;
  final bool? isEcommerceAio;
  bool? isFromCommission;
  final String? ticketCode;

  DetailOrderEntity({
    this.status,
    this.orderCode,
    this.orderName,
    this.orderStatus,
    this.shippingInfo,
    this.orderItems,
    this.paymentInfo,
    this.processUserInfo,
    this.orderAmount,
    this.orderType,
    this.roles,
    this.chanel,
    this.source,
    this.isCalled,
    this.requestCancelOrderId,
    this.shippingServiceFee,
    this.distance,
    this.isHotOrder,
    this.contactRequestCode,
    this.images,
    this.packageCode,
    this.requestExtendKpiId,
    this.certificates,
    this.generateType,
    this.completeOrderTime,
    this.tenantId,
    this.isConnectAio,
    this.isEcommerceAio,
    this.isFromCommission,
    this.ticketCode,
  });

  factory DetailOrderEntity.fromJson(Map<String, dynamic> json) =>
      _$DetailOrderEntityFromJson(json);

  Map<String, dynamic> toJson() => _$DetailOrderEntityToJson(this);

  static OrderStatus? _statusFromString(String? status) =>
      OrderStatusExtension.fromString(status);

  static OrderSubStatus? _subStatusFromString(String? subStatus) =>
      OrderSubStatusExtension.fromString(subStatus);

  static OrderType? _orderTypeFromString(String? orderType) =>
      OrderTypeExtension.fromString(orderType);

  static OrderGenerateTypeEnum? _orderGenerateTypeFromString(
          String? orderGenerateType) =>
      OrderGenerateTypeEnumExtension.fromString(orderGenerateType);

  static SourceOrder? _sourceOrderFromString(String? source) =>
      SourceOrderExtension.fromString(source);

  bool get isRequestCancel {
    return orderStatus == OrderSubStatus.paying ||
        orderStatus == OrderSubStatus.commission ||
        orderStatus == OrderSubStatus.confirmWaiting ||
        orderStatus == OrderSubStatus.cnctWaiting ||
        orderStatus == OrderSubStatus.ttqhWaiting ||
        orderStatus == OrderSubStatus.processUserWaiting ||
        orderStatus == OrderSubStatus.packageWaiting ||
        orderStatus == OrderSubStatus.processWaiting ||
        orderStatus == OrderSubStatus.shipping;
  }

  bool get isShowCallOrderShipping {
    return status == OrderStatus.confirmWaiting ||
        status == OrderStatus.processWaiting ||
        status == OrderStatus.processing ||
        status == OrderStatus.processed ||
        status == OrderStatus.complete ||
        status == OrderStatus.cancel;
  }

  bool get isShowCallOrder {
    bool validStatus = status != OrderStatus.registered &&
        status != OrderStatus.receptionWaiting &&
        status != OrderStatus.cancel;

    if ((completeOrderTime ?? '').isNotEmpty) {
      DateTime completeTime = DateTime.parse(completeOrderTime!);
      DateTime now = DateTime.now();
      if (now.difference(completeTime).inHours > 72) {
        validStatus = false;
      }
    }
    return validStatus;
  }

  bool get showCheckList {
    return status == OrderStatus.processing ||
        status == OrderStatus.processed ||
        status == OrderStatus.complete;
  }

  bool get isWorker {
    return (roles ?? []).contains(RoleOrderShippingEnum.worker.serverKey);
  }

  bool get isSeller {
    return (roles ?? []).contains(RoleOrderShippingEnum.seller.serverKey);
  }

  bool get isTeleSale {
    return (roles ?? []).contains(RoleOrderShippingEnum.telesale.serverKey);
  }

  bool get isTeleSaleManager {
    return (roles ?? [])
        .contains(RoleOrderShippingEnum.teteSaleManager.serverKey);
  }

  bool get isRegionalManager {
    return (roles ?? [])
        .contains(RoleOrderShippingEnum.regionalManager.serverKey);
  }

  bool get isBoft3 {
    return (roles ?? []).contains(RoleOrderShippingEnum.boft3.serverKey);
  }

  bool get isFt3m {
    return (roles ?? []).contains(RoleOrderShippingEnum.ft3m.serverKey);
  }

  bool get isFt3 {
    return (roles ?? []).contains(RoleOrderShippingEnum.ft3.serverKey);
  }

  bool get showDialogBuySupplyForCustomer {
    return orderType == OrderType.combo ||
        orderType == OrderType.service ||
        orderType == OrderType.supply ||
        orderType == OrderType.package ||
        orderType == OrderType.maintenance;
  }

  bool get isOrderPartner {
    return orderType == OrderType.partnerWarrantyService ||
        orderType == OrderType.partnerIsInstallingOrder ||
        orderType == OrderType.partnerMaintenance ||
        orderType == OrderType.partnerOperate ||
        orderType == OrderType.partnerResolveProblem ||
        orderType == OrderType.partnerNotWarrantyService;
  }

  bool get showMoneyDifferent {
    return orderType == OrderType.partnerIsInstallingOrder ||
        orderType == OrderType.partnerMaintenance ||
        orderType == OrderType.partnerOperate ||
        orderType == OrderType.partnerResolveProblem ||
        orderType == OrderType.partnerNotWarrantyService;
  }

  bool get showGetBillOrder {
    if (orderType == OrderType.partnerResolveProblem ||
        orderType == OrderType.partnerMaintenance ||
        orderType == OrderType.partnerNotWarrantyService ||
        orderType == OrderType.partnerWarrantyService) {
      return false;
    } else if (orderType == OrderType.partnerIsInstallingOrder ||
        orderType == OrderType.partnerOperate) {
      if (isConnectAio == true || isEcommerceAio == true) {
        return true;
      }
      return false;
    } else {
      return true;
    }
  }

  bool get isOrderWarranty {
    return orderType == OrderType.partnerWarrantyService ||
        orderType == OrderType.partnerNotWarrantyService;
  }

  bool get isCreateOrder {
    bool isCheck = false;

    if ((processUserInfo?.togetherUserInfo ?? []).isNotEmpty) {
      final listUser = processUserInfo!.togetherUserInfo!;
      final user = GlobalData.instance.userInfo;

      for (int i = 0; i < listUser.length; i++) {
        if (listUser[i].isCreatedOrder == true &&
            user?.username == listUser[i].userCode) {
          isCheck = true;
          break;
        }
      }
    }

    return isCheck;
  }

  bool get canEditOrder {
    if (isFromCommission ?? false) {
      return false;
    }

    switch (status) {
      case OrderStatus.registered:
        if ((isCreateOrder)) {
          return true;
        }
        return false;
      case OrderStatus.receptionWaiting:
        if (((isBoft3) || (isFt3m) || (isFt3))) {
          return true;
        }
        return false;
      case OrderStatus.processWaiting:
        if (shippingInfo?.pendingSchedule ?? false) {
          return false;
        } else {
          if (isWorker) {
            return true;
          }
          return false;
        }
      case OrderStatus.processing:
        if (requestExtendKpiId != null) {
          return false;
        } else {
          if (isWorker) {
            return true;
          }
          return false;
        }
      default:
        return false;
    }
  }

  bool get showBillButton {
    if (isFromCommission ?? false) {
      return false;
    }

    if (status == OrderStatus.processed || status == OrderStatus.complete) {
      return true;
    }
    return canEditOrder;
  }

  List<FileEntity> get getListImage {
    List<FileEntity> listImage = [];

    if (certificates != null) {
      for (int i = 0; i < certificates!.length; i++) {
        if (certificates![i].type == FileType.image) {
          listImage.add(certificates![i]);
        }
      }
    }

    return listImage;
  }

  List<FileEntity> get getListFile {
    List<FileEntity> listFile = [];

    if (certificates != null) {
      for (int i = 0; i < certificates!.length; i++) {
        if (certificates![i].type == FileType.file) {
          listFile.add(certificates![i]);
        }
      }
    }

    return listFile;
  }

  CommissionConfirmStatus? get getCommissionStatus {
    if (processUserInfo?.togetherUserInfo == null) {
      return null;
    }

    final listUser = processUserInfo!.togetherUserInfo!;
    final user = GlobalData.instance.userInfo;

    if (listUser.length == 1) {
      return listUser[0].confirmStatus;
    }

    if (isCreateOrder) {
      CommissionConfirmStatus? status;

      for (int i = 0; i < listUser.length; i++) {
        if (listUser[i].confirmStatus == CommissionConfirmStatus.rejected) {
          return CommissionConfirmStatus.rejected;
        } else if (listUser[i].confirmStatus ==
            CommissionConfirmStatus.waitConfirm) {
          status = CommissionConfirmStatus.waitConfirm;
        }
      }

      if (status == null) {
        return CommissionConfirmStatus.confirmed;
      } else {
        return status;
      }
    } else {
      for (int i = 0; i < listUser.length; i++) {
        if (user?.username == listUser[i].userCode) {
          return listUser[i].confirmStatus;
        }
      }
    }

    return null;
  }
}
