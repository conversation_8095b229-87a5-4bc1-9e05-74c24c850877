import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_item_entity.dart';
import 'package:vcc/domain/enums/bill_type.dart';

part 'bill_detail_entity.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class BillDetailEntity {
  String? orderCode;
  String? billId;
  String? customerName;
  String? companyName;
  String? taxCode;
  String? address;
  String? bankAccount;
  String? bankName;
  String? contactName;
  String? contactNumber;
  String? paymentMethodStr;
  String? description;
  String? reason;
  String? base64Report;
  String? phoneNumber;
  String? email;
  String? customerType;
  String? note;
  String? paymentType;
  int? totalMoney;
  bool? isInvoiceRequested;
  List<String>? notes;
  @JsonKey(fromJson: _billStatusFromString)
  BillType? status;
  List<BillDetailItemEntity>? items;
  List<BillDetailItemEntity>? coupons;
  int? invoiceType;

  BillDetailEntity({
    this.orderCode,
    this.billId,
    this.customerName,
    this.companyName,
    this.taxCode,
    this.address,
    this.bankAccount,
    this.bankName,
    this.contactName,
    this.contactNumber,
    this.paymentMethodStr,
    this.description,
    this.items,
    this.status,
    this.reason,
    this.base64Report,
    this.email,
    this.phoneNumber,
    this.isInvoiceRequested,
    this.customerType,
    this.notes,
    this.coupons,
    this.paymentType,
    this.note,
    this.totalMoney,
    this.invoiceType,
  });

  static BillType? _billStatusFromString(String? status) =>
      BillTypeExtension.fromString(status);

  factory BillDetailEntity.fromJson(Map<String, dynamic> json) =>
      _$BillDetailEntityFromJson(json);

  Map<String, dynamic> toJson() => _$BillDetailEntityToJson(this);

  AioInvoiceEntity toAioInvoiceEntity() {
    return AioInvoiceEntity(
      customerPhone: phoneNumber,
      customerType: int.parse(customerType ?? "1"),
      address: address,
      bankAccount: bankAccount,
      bankName: bankName,
      companyName: companyName,
      customerName: customerName,
      email: email,
      description: description,
      taxCode: taxCode,
      aioInvoiceItemDTOS:
          (items ?? []).map((it) => it.toAioInvoiceItemEntity()).toList(),
      invoiceType: invoiceType,
    );
  }
}
