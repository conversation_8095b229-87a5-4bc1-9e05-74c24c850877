import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';

part 'bill_detail_item_entity.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class BillDetailItemEntity {
  double? taxPercent;
  String? goodsName;
  int? goodsId;
  String? goodsCode;
  String? name;
  String? uuid;
  double? quantity;
  double? price;
  double? unitPrice;
  String? goodsUnitName;
  double? amountBeforeTax;
  double? preTaxAmount;
  int? amount;
  bool? discount;
  int? packageId;

  BillDetailItemEntity({
    this.uuid,
    this.taxPercent,
    this.name,
    this.goodsId,
    this.goodsCode,
    this.goodsName,
    this.quantity,
    this.price,
    this.unitPrice,
    this.goodsUnitName,
    this.amountBeforeTax,
    this.preTaxAmount,
    this.amount,
    this.discount,
    this.packageId,
  });

  factory BillDetailItemEntity.fromJson(Map<String, dynamic> json) =>
      _$BillDetailItemEntityFromJson(json);

  Map<String, dynamic> toJson() => _$BillDetailItemEntityToJson(this);

  AioInvoiceItemEntity toAioInvoiceItemEntity() {
    return AioInvoiceItemEntity(
      taxPercent: taxPercent,
      goodsId: goodsId,
      goodsCode: goodsCode,
      goodsName: goodsName,
      price: price,
      quantity: quantity,
      amountBeforeTax: amountBeforeTax,
      preTaxAmount: preTaxAmount,
      goodsUnitName: goodsUnitName,
      amount: amount,
      packageId: packageId,
    );
  }
}
