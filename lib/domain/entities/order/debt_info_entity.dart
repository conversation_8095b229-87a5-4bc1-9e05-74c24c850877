import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/enums/process_kpi_status.dart';

part 'debt_info_entity.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class DebtInfoEntity {
  final String? orderCode;
  final String? paymentMethod;
  final int? amount;
  final String? debtAssignedDate;
  final String? expirationDate;
  final String? paymentStatus;
  final String? transferCode;
  @JsonKey(fromJson: _statusFromString)
  final ProcessKpiStatus? status;
  final String? source;

  DebtInfoEntity({
    this.orderCode,
    this.paymentMethod,
    this.amount,
    this.debtAssignedDate,
    this.expirationDate,
    this.paymentStatus,
    this.transferCode,
    this.status,
    this.source,
  });

  static ProcessKpiStatus? _statusFromString(String? value) =>
      ProcessKpiStatusExtension.fromString(value);

  factory DebtInfoEntity.fromJson(Map<String, dynamic> json) =>
      _$DebtInfoEntityFromJson(json);

  Map<String, dynamic> toJson() => _$DebtInfoEntityToJson(this);
}
