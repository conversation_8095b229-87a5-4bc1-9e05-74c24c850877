import 'package:freezed_annotation/freezed_annotation.dart';
import 'package:vcc/domain/entities/order/deployment_order_entity.dart';
import 'package:vcc/domain/entities/warranty/warranty_claim_entity.dart';

part 'deployment_order_count_entity.g.dart';

//TODO Not used yet
@JsonSerializable()
class DeploymentOrderCountEntity {
  @JsonKey(name: "deploymentOrderCount")
  int? deploymentOrderCount;
  @<PERSON>sonKey(name: "deploymentOrders")
  List<DeploymentOrderEntity>? deploymentOrders;
  @<PERSON><PERSON><PERSON><PERSON>(name: "bundleOrderCount")
  dynamic bundleOrderCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "bundleOrders")
  dynamic bundleOrders;
  @Json<PERSON>ey(name: "customerInformationGatherCount")
  dynamic customerInformationGatherCount;
  @JsonKey(name: "customerInformationGathers")
  dynamic customerInformationGathers;
  @JsonKey(name: "warrantyRequestCount")
  int? warrantyRequestCount;
  @<PERSON><PERSON><PERSON><PERSON>(name: "warrantyRequests")
  List<WarrantyClaimEntity>? warrantyRequests;
  @JsonKey(name: "customerClaimCount")
  dynamic customerClaimCount;
  @JsonKey(name: "customerClaims")
  dynamic customerClaims;

  DeploymentOrderCountEntity({
    this.deploymentOrderCount,
    this.deploymentOrders,
    this.bundleOrderCount,
    this.bundleOrders,
    this.customerInformationGatherCount,
    this.customerInformationGathers,
    this.warrantyRequestCount,
    this.warrantyRequests,
    this.customerClaimCount,
    this.customerClaims,
  });

  factory DeploymentOrderCountEntity.fromJson(Map<String, dynamic> json) =>
      _$DeploymentOrderCountEntityFromJson(json);

  Map<String, dynamic> toJson() => _$DeploymentOrderCountEntityToJson(this);
}
