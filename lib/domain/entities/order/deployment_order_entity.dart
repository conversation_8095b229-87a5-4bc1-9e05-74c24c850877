import 'package:json_annotation/json_annotation.dart';

part 'deployment_order_entity.g.dart';

@JsonSerializable()
class DeploymentOrderEntity {
  @<PERSON><PERSON><PERSON><PERSON>(name: "orderCode")
  String? orderCode;
  @<PERSON><PERSON><PERSON><PERSON>(name: "status")
  String? status;
  @<PERSON><PERSON><PERSON><PERSON>(name: "orderName")
  String? orderName;
  @<PERSON><PERSON><PERSON><PERSON>(name: "address")
  String? address;
  @<PERSON><PERSON><PERSON><PERSON>(name: "startTime")
  DateTime? startTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: "endTime")
  DateTime? endTime;
  @<PERSON><PERSON><PERSON><PERSON>(name: "hotOrder")
  bool? hotOrder;

  DeploymentOrderEntity({
    this.orderCode,
    this.status,
    this.orderName,
    this.address,
    this.startTime,
    this.endTime,
    this.hotOrder,
  });

  factory DeploymentOrderEntity.fromJson(Map<String, dynamic> json) =>
      _$DeploymentOrderEntityFromJson(json);

  Map<String, dynamic> toJson() => _$DeploymentOrderEntityToJson(this);
}
