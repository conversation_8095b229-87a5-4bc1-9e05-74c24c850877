import 'dart:core';
import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';

part 'aio_contract_info_item_entity.g.dart';

@JsonSerializable()
class AioContractInfoItemEntity {
  final String? customField;
  final bool? isSize;
  final int? contractDetailId;
  final int? contractId;
  final String? workName;
  final int? packageId;
  final String? packageName;
  final int? packageDetailId;
  final double? quantity;
  final double? amount;
  final String? startDate;
  final String? endDate;
  final int? status;
  final int? isBill;
  final int? isProvinceBought;
  final String? saleChannel;
  final double? time;
  final int? acceptanceRecordsId;
  final int? packageType;
  final double? discountComboContract;
  final String? saleChannelPackage;
  final double? amountBeforeVat;
  final double? vatPackage;
  final int? statusService;
  final String? industryCode;
  final String? serviceCode;
  final int? price;

  AioContractInfoItemEntity({
    this.customField,
    this.isSize,
    this.contractDetailId,
    this.contractId,
    this.workName,
    this.packageId,
    this.packageName,
    this.packageDetailId,
    this.quantity,
    this.amount,
    this.startDate,
    this.endDate,
    this.status,
    this.isBill,
    this.isProvinceBought,
    this.saleChannel,
    this.time,
    this.acceptanceRecordsId,
    this.packageType,
    this.discountComboContract,
    this.saleChannelPackage,
    this.amountBeforeVat,
    this.vatPackage,
    this.statusService,
    this.industryCode,
    this.serviceCode,
    this.price,
  });

  AioPackageEntity toAioPackageEntity() {
    return AioPackageEntity(
      price: (amount ?? 0).toInt() ~/ (quantity ?? 1).toInt(),
      quantity: (quantity ?? 1).toInt(),
      aioPackageDetailId: packageDetailId,
      contractDetailId: contractDetailId,
      aioPackageId: packageId,
      packageType: packageType,
      statusService: statusService,
      configServiceStatus: statusService,
      aioPackageName: packageName,
      industryCode: industryCode,
      serviceCode: serviceCode,
      contractId: contractId,
      startDate: startDate,
      endDate: endDate,
      status: status,
      isBill: isBill,
      isProvinceBought: isProvinceBought,
      saleChannel: saleChannel,
      time: time,
      acceptanceRecordsId: acceptanceRecordsId,
      discountComboContract: discountComboContract,
      saleChannelPackage: saleChannelPackage,
      customField: customField,
    );
  }

  factory AioContractInfoItemEntity.fromJson(Map<String, dynamic> json) =>
      _$AioContractInfoItemEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AioContractInfoItemEntityToJson(this);
}
