import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/params/aio_contract/aio_image.dart';

part 'aio_contract_entity.g.dart';

@JsonSerializable()
class AioContractEntity {
  final String? name;
  final int? contractId;
  final String? contractCode;
  final String? contractContent;
  final int? performerId;
  final int? type;
  final String? description;
  final int? contractAmount;
  final String? serviceCode;
  final String? industryCode;
  final int? isInternal;
  final String? staffCode;
  final int? payType;
  final int? contractType;
  final int? detailFinished;
  final int? subAction;
  final String? goodsType;
  final List<AioImage>? listImage;
  final List<String>? lstSerial;
  final String? field;
  final int? done;
  final int? sizeTimePay;
  final int? isCompany;
  final int? notDone;
  final int? transportType;
  final int? configServiceStatus;
  final int? status;
  final int? customerTypeId;
  final int? utilAttachDocumentId;
  final int? statusImage;
  final int? obstructedId;
  final String? customerTypeName;
  final String? referralCode;
  final String? dateStartContract;
  final String? scheduleTime;
  final String? contactPhone;
  final bool? canOrderRequest;
  final bool? canProposeCancel;
  final bool? canDeleteProposeCancel;
  final bool? staffPhoneNumber;
  final String? listEmployee;
  final int? sysUserId;
  final int? surveyId;
  final int? sysGroupId;
  final int? ordersId;
  final int? surveyCustomerId;
  final int? aioOrdersId;
  final int? contractOrdersId;
  final int? contractSurveyCustomerId;
  final List<AioServiceEntity>? hsServiceList;
  final int? internalStaff;
  final String? contractCodeHard;
  final String? startContractHard;
  final String? endContractHard;
  final String? customerAddress;
  final int? xcare;
  final int? capitalSource;
  final int? formSignContract;
  final int? deploymentMethod;
  final String? sellerName;
  final String? customerName;
  final String? customerPhone;
  final String? createdDate;
  final String? keySearch;
  final String? reasonRejectContract;
  final int? isPaying;
  String? reasonOutOfDate;
  String? appointmentDate;
  String? contractPauseDto;
  int? invoiceType;
  final List<AioImage>? listFile;
  int? contractDetailId;
  int? packageId;
  int? packageDetailId;
  int? quantity;
  int? amount;
  int? amountDetail;
  int? approvedPay;
  int? isMoney;
  int? amountBeforeVat;
  int? vatPackage;
  int? discountStaff;
  int? acceptanceRecordsId;
  int? price;
  int? checkBill;
  int? data;
  int? deploymentMethodContract;
  int? collectedMoney;
  int? receivablesMoney;
  final LoadStatus? uploadImageStatus;
  final String? amountStr;
  final String? customerReviews;
  final String? customerReviewsContents;
  final String? otp;
  int? isProvinceBought;

  AioContractEntity({
    this.name,
    this.isProvinceBought,
    this.collectedMoney,
    this.receivablesMoney,
    this.deploymentMethodContract,
    this.invoiceType,
    this.contractId,
    this.contractDetailId,
    this.customerAddress,
    this.ordersId,
    this.status,
    this.contractCode,
    this.contractContent,
    this.performerId,
    this.type,
    this.description,
    this.contractAmount,
    this.serviceCode,
    this.industryCode,
    this.isInternal,
    this.staffCode,
    this.payType,
    this.contractType,
    this.detailFinished,
    this.subAction,
    this.goodsType,
    this.listImage,
    this.field,
    this.done,
    this.sizeTimePay,
    this.isCompany,
    this.notDone,
    this.transportType,
    this.configServiceStatus = 0,
    this.customerTypeId,
    this.utilAttachDocumentId,
    this.statusImage,
    this.obstructedId,
    this.customerTypeName,
    this.referralCode,
    this.dateStartContract,
    this.scheduleTime,
    this.contactPhone,
    this.canOrderRequest,
    this.canProposeCancel,
    this.canDeleteProposeCancel,
    this.staffPhoneNumber,
    this.listEmployee,
    this.sysUserId,
    this.surveyId,
    this.sysGroupId,
    this.hsServiceList,
    this.surveyCustomerId,
    this.aioOrdersId,
    this.contractOrdersId,
    this.contractSurveyCustomerId,
    this.internalStaff,
    this.contractCodeHard,
    this.startContractHard,
    this.endContractHard,
    this.xcare,
    this.capitalSource,
    this.formSignContract,
    this.deploymentMethod,
    this.sellerName,
    this.createdDate,
    this.customerName,
    this.customerPhone,
    this.keySearch,
    this.packageDetailId,
    this.reasonOutOfDate,
    this.appointmentDate,
    this.isPaying,
    this.lstSerial,
    this.listFile,
    this.amount,
    this.amountDetail,
    this.approvedPay,
    this.isMoney,
    this.amountBeforeVat,
    this.vatPackage,
    this.discountStaff,
    this.acceptanceRecordsId,
    this.price,
    this.checkBill,
    this.reasonRejectContract,
    this.data,
    this.uploadImageStatus,
    this.amountStr,
    this.otp,
    this.customerReviews,
    this.customerReviewsContents,
  });

  factory AioContractEntity.fromJson(Map<String, dynamic> json) =>
      _$AioContractEntityFromJson(json);

  Map<String, dynamic> toJson() => _$AioContractEntityToJson(this);
}
