import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';

part 'aio_contract_detail_request.g.dart';

@JsonSerializable()
class AioContractDetailRequest {
  final int? data;
  final SysUserRequest? sysUserRequest;

  AioContractDetailRequest({
    this.sysUserRequest,
    this.data,
  });

  factory AioContractDetailRequest.fromJson(Map<String, dynamic> json) =>
      _$AioContractDetailRequestFromJson(json);

  Map<String, dynamic> toJson() => _$AioContractDetailRequestToJson(this);
}
