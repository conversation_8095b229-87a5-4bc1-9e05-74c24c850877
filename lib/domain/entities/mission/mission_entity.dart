import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/enums/mission/mission_kpi.dart';
import 'package:vcc/domain/enums/mission/mission_type.dart';
import 'package:vcc/domain/enums/mission/mission_status.dart';
import 'package:vcc/domain/enums/mission/mission_extension_status.dart';

part 'mission_entity.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class MissionEntity {
  final String? code;
  final String? name;
  @Json<PERSON>ey(fromJson: _missionStatusFromString)
  final MissionStatus? status;
  @JsonKey(fromJson: _missionKpiFromString)
  final MissionKpiEnum? kpiType;
  @JsonKey(fromJson: _missionTypeFromString)
  final MissionTypeEnum? type;
  final DateTime? startAt;
  final DateTime? endAt;
  final DateTime? createdAt;
  final DateTime? renewalDate;
  final String? renewalReason;
  final DateTime? pendingRenewalDate;
  final String? pendingReason;
  final String? detail;
  final bool? hasPlan;
  final int? planQuantity;
  final String? result;
  final List<String>? urls;
  final List<String>? taskPlans;
  @JsonKey(fromJson: _missionExtensionStatusFromString)
  final MissionExtensionStatusEnum? renewalStatus;
  final String? renewalRejectReason;
  @JsonKey(fromJson: _missionExtensionStatusFromString)
  final MissionExtensionStatusEnum? closeStatus;
  final String? closeRejectReason;

  MissionEntity({
    this.code,
    this.name,
    this.status,
    this.kpiType,
    this.type,
    this.startAt,
    this.endAt,
    this.createdAt,
    this.renewalDate,
    this.renewalReason,
    this.pendingRenewalDate,
    this.pendingReason,
    this.detail,
    this.hasPlan,
    this.planQuantity,
    this.result,
    this.urls,
    this.taskPlans,
    this.renewalStatus,
    this.renewalRejectReason,
    this.closeStatus,
    this.closeRejectReason,
  });

  static MissionStatus? _missionStatusFromString(String? status) =>
      MissionStatusExtension.fromString(status);

  static MissionKpiEnum? _missionKpiFromString(String? kpi) =>
      MissionKpiExtension.fromString(kpi);

  static MissionTypeEnum? _missionTypeFromString(String? type) =>
      MissionTypeExtension.fromString(type);

  static MissionExtensionStatusEnum? _missionExtensionStatusFromString(
          String? status) =>
      MissionExtensionStatusExtension.fromString(status);

  factory MissionEntity.fromJson(Map<String, dynamic> json) =>
      _$MissionEntityFromJson(json);

  Map<String, dynamic> toJson() => _$MissionEntityToJson(this);
}
