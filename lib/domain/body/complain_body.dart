import 'package:json_annotation/json_annotation.dart';

part 'complain_body.g.dart';

@JsonSerializable(
  includeIfNull: false,
  explicitToJson: true,
)
class ComplainBody {
  final String? keySearch;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool? isManager;
  final List<int>? listStatus;
  final List<int>? listCompleteStatus;
  final int? pageIndex;
  final int? pageSize;
  final int? sysUserId;
  final int? productCategoryId;
  final int? complaintGroupId;
  final int? ticketId;
  final String? keyWord;

  ComplainBody({
    this.startDate,
    this.endDate,
    this.keySearch,
    this.isManager,
    this.listStatus,
    this.listCompleteStatus,
    this.sysUserId,
    this.pageIndex,
    this.pageSize,
    this.productCategoryId,
    this.complaintGroupId,
    this.ticketId,
    this.keyWord,
  });

  factory ComplainBody.fromJson(Map<String, dynamic> json) =>
      _$ComplainBodyFromJson(json);

  Map<String, dynamic> toJson() => _$ComplainBodyToJson(this);
}
