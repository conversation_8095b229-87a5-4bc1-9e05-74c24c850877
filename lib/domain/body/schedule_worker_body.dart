import 'package:json_annotation/json_annotation.dart';

part 'schedule_worker_body.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class ScheduleWorkerBody {
  final String? provinceCode;
  final String? districtCode;
  final String? wardCode;
  final String? startTime;

  ScheduleWorkerBody({
    this.provinceCode,
    this.districtCode,
    this.wardCode,
    this.startTime,
  });

  factory ScheduleWorkerBody.fromJson(Map<String, dynamic> json) =>
      _$ScheduleWorkerBodyFromJson(json);

  Map<String, dynamic> toJson() => _$ScheduleWorkerBodyToJson(this);
}
