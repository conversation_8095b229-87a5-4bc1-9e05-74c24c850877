import 'package:json_annotation/json_annotation.dart';

part 'save_plan_mission_body.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class SavePlanMissionBody {
  final List<String>? taskPlans;

  SavePlanMissionBody({
    this.taskPlans,
  });

  factory SavePlanMissionBody.fromJson(Map<String, dynamic> json) =>
      _$SavePlanMissionBodyFromJson(json);

  Map<String, dynamic> toJson() => _$SavePlanMissionBodyToJson(this);
}
