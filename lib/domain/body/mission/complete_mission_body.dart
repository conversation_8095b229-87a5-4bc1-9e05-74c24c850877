import 'package:json_annotation/json_annotation.dart';

part 'complete_mission_body.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class CompleteMissionBody {
  final String? result;
  final List<String>? imageUrls;

  CompleteMissionBody({
    this.result,
    this.imageUrls,
  });

  factory CompleteMissionBody.fromJson(Map<String, dynamic> json) =>
      _$CompleteMissionBodyFromJson(json);

  Map<String, dynamic> toJson() => _$CompleteMissionBodyToJson(this);
}
