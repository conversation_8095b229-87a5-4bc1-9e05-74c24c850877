import 'package:json_annotation/json_annotation.dart';

part 'extension_implementation_time_body.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class ExtensionImplementationTimeBody {
  final DateTime? renewalDate;
  final String? renewalReason;

  ExtensionImplementationTimeBody({
    this.renewalDate,
    this.renewalReason,
  });

  factory ExtensionImplementationTimeBody.fromJson(Map<String, dynamic> json) =>
      _$ExtensionImplementationTimeBodyFromJson(json);

  Map<String, dynamic> toJson() =>
      _$ExtensionImplementationTimeBodyToJson(this);
}
