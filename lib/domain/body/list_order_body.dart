import 'package:vcc/domain/entities/code_entity.dart';

class ListOrderBody {
  final String? status;
  final int? page;
  final int? pageSize;
  final String? keyword;
  final CodeEntity? province;
  final List<CodeEntity>? districts;
  final String? startTime;
  final String? endTime;
  final List<String>? participantTypes;
  final List<String>? ordersType;
  final String? specialQueryType;

  ListOrderBody({
    this.participantTypes,
    this.endTime,
    this.districts,
    this.startTime,
    this.pageSize,
    this.page,
    this.status,
    this.keyword,
    this.ordersType,
    this.province,
    this.specialQueryType,
  });
}
