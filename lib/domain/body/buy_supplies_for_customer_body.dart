import 'package:json_annotation/json_annotation.dart';

part 'buy_supplies_for_customer_body.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class BuySuppliesForCustomerBody {
  final bool? hasSelfBuySupply;
  final List<SupplyItemInfo>? supplyItems;

  BuySuppliesForCustomerBody({
    this.hasSelfBuySupply,
    this.supplyItems,
  });

  factory BuySuppliesForCustomerBody.fromJson(Map<String, dynamic> json) =>
      _$BuySuppliesForCustomerBodyFromJson(json);

  Map<String, dynamic> toJson() => _$BuySuppliesForCustomerBodyToJson(this);
}
@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class SupplyItemInfo{
  final String? supplyName;
  final int? price;
  final List<String>? urls;
  final int? dayWarranty;

  SupplyItemInfo({
    this.supplyName,
    this.price,
    this.urls,
    this.dayWarranty,
});
  factory SupplyItemInfo.fromJson(Map<String, dynamic> json) =>
      _$SupplyItemInfoFromJson(json);

  Map<String, dynamic> toJson() => _$SupplyItemInfoToJson(this);
}