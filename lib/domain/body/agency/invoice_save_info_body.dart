import 'package:json_annotation/json_annotation.dart';
import 'package:vcc/domain/entities/order_agency/invoice_info_entity.dart';
part 'invoice_save_info_body.g.dart';

@JsonSerializable(
  explicitToJson: true,
  includeIfNull: false,
)
class InvoiceSaveInfoBody {
  final String? defaultSortField;
  final int? messageColumn;
  final bool? isSize;
  final int? contractId;
  final String? customerName;
  final String? email;
  final String? taxCode;
  final String? companyName;
  final String? address;
  final String? bankAccount;
  final String? bankName;
  final String? customerPhone;
  final String? description;
  final int? totalAmount;
  final int? customerType;
  final int? createdBy;
  final int? invoiceType;
  final int? paymentMethod;
  final String? contractCode;
  final List<AioInvoiceItemDto>? aioInvoiceItemDTOS;
  final List<String>? notes;

  InvoiceSaveInfoBody({
    this.defaultSortField,
    this.messageColumn,
    this.isSize,
    this.contractId,
    this.customerName,
    this.email,
    this.taxCode,
    this.companyName,
    this.address,
    this.bankAccount,
    this.bankName,
    this.customerPhone,
    this.description,
    this.totalAmount,
    this.customerType,
    this.createdBy,
    this.invoiceType,
    this.paymentMethod,
    this.contractCode,
    this.aioInvoiceItemDTOS,
    this.notes,
  });

  InvoiceSaveInfoBody copyWith({
    String? defaultSortField,
    int? messageColumn,
    bool? isSize,
    int? contractId,
    String? customerName,
    String? email,
    String? taxCode,
    String? companyName,
    String? address,
    String? bankAccount,
    String? bankName,
    String? customerPhone,
    String? description,
    int? totalAmount,
    int? customerType,
    int? createdBy,
    int? invoiceType,
    int? paymentMethod,
    String? contractCode,
    List<AioInvoiceItemDto>? aioInvoiceItemDTOS,
    List<String>? notes,
  }) =>
      InvoiceSaveInfoBody(
        defaultSortField: defaultSortField ?? this.defaultSortField,
        messageColumn: messageColumn ?? this.messageColumn,
        isSize: isSize ?? this.isSize,
        contractId: contractId ?? this.contractId,
        customerName: customerName ?? this.customerName,
        email: email ?? this.email,
        taxCode: taxCode ?? this.taxCode,
        companyName: companyName ?? this.companyName,
        address: address ?? this.address,
        bankAccount: bankAccount ?? this.bankAccount,
        bankName: bankName ?? this.bankName,
        customerPhone: customerPhone ?? this.customerPhone,
        description: description ?? this.description,
        totalAmount: totalAmount ?? this.totalAmount,
        customerType: customerType ?? this.customerType,
        createdBy: createdBy ?? this.createdBy,
        invoiceType: invoiceType ?? this.invoiceType,
        paymentMethod: paymentMethod ?? this.paymentMethod,
        contractCode: contractCode ?? this.contractCode,
        aioInvoiceItemDTOS: aioInvoiceItemDTOS ?? this.aioInvoiceItemDTOS,
        notes: notes ?? this.notes,
      );

  factory InvoiceSaveInfoBody.fromJson(Map<String, dynamic> json) =>
      _$InvoiceSaveInfoBodyFromJson(json);

  Map<String, dynamic> toJson() => _$InvoiceSaveInfoBodyToJson(this);
}
