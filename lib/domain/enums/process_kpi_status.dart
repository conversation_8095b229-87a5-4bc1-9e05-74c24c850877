import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';

enum ProcessKpiStatus {
  none,
  deadline,
  expired,
}

extension ProcessKpiStatusExtension on ProcessKpiStatus {
  static ProcessKpiStatus fromString(String? status) {
    if ((status ?? '').isNotEmpty) {
      switch (status) {
        case "NONE":
          return ProcessKpiStatus.none;
        case "DEADLINE":
          return ProcessKpiStatus.deadline;
        case "EXPIRED":
          return ProcessKpiStatus.expired;
        default:
          return ProcessKpiStatus.none;
      }
    }
    return ProcessKpiStatus.none;
  }

  String get title {
    switch (this) {
      case ProcessKpiStatus.none:
        return "";
      case ProcessKpiStatus.deadline:
        return "Sắp tới hạn";
      case ProcessKpiStatus.expired:
        return "Quá hạn";
      default:
        return "";
    }
  }

  Color get borderColor {
    switch (this) {
      case ProcessKpiStatus.deadline:
        return CoreColors.orange;
      case ProcessKpiStatus.expired:
        return CoreColors.red;
      default:
        return CoreColors.white;
    }
  }

  Color get backgroundColor {
    switch (this) {
      case ProcessKpiStatus.deadline:
        return CoreColors.orange01;
      case ProcessKpiStatus.expired:
        return CoreColors.red01;
      default:
        return CoreColors.white;
    }
  }
}
