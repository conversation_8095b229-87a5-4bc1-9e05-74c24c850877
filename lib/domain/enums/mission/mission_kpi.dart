enum MissionKpiEnum {
  onTime(
    label: "Trong hạn",
    keyToServer: 'ON_TIME',
  ),
  overdue(
    label: "Quá hạn",
    keyToServer: 'OVERDUE',
  );

  final String label;
  final String keyToServer;

  const MissionKpiEnum({
    required this.label,
    required this.keyToServer,
  });
}

extension MissionKpiExtension on MissionKpiEnum {
  static MissionKpiEnum? fromString(String? kpi) {
    switch (kpi) {
      case "ON_TIME":
        return MissionKpiEnum.onTime;
      case 'OVERDUE':
        return MissionKpiEnum.overdue;
      default:
        return null;
    }
  }
}
