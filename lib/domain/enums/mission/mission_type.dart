enum MissionTypeEnum {
  plan(
    label: "<PERSON><PERSON> hoạch",
    keyToServer: "PLAN",
  ),
  incident(
    label: "Sự vụ",
    keyToServer: "INCIDENT",
  );

  final String label;
  final String keyToServer;

  const MissionTypeEnum({
    required this.label,
    required this.keyToServer,
  });
}

extension MissionTypeExtension on MissionTypeEnum {
  static MissionTypeEnum? fromString(String? type) {
    switch (type) {
      case "PLAN":
        return MissionTypeEnum.plan;
      case "INCIDENT":
        return MissionTypeEnum.incident;
      default:
        return null;
    }
  }
}
