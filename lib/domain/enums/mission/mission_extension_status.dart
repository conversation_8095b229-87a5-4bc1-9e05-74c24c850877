enum MissionExtensionStatusEnum {
  pending(
    label: '<PERSON><PERSON> duyệ<PERSON>',
    keyToServer: 'PENDING',
  ),
  approved(
    label: 'Đ<PERSON> duyệt',
    keyToServer: 'APPROVED',
  ),
  rejected(
    label: 'Từ chối',
    keyToServer: 'REJECTED',
  );

  final String label;
  final String keyToServer;

  const MissionExtensionStatusEnum({
    required this.label,
    required this.keyToServer,
  });
}

extension MissionExtensionStatusExtension on MissionExtensionStatusEnum {
  static MissionExtensionStatusEnum? fromString(String? reject) {
    switch (reject) {
      case 'PENDING':
        return MissionExtensionStatusEnum.pending;
      case 'APPROVED':
        return MissionExtensionStatusEnum.approved;
      case 'REJECTED':
        return MissionExtensionStatusEnum.rejected;
      default:
        return null;
    }
  }
}
