import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';

enum MissionStatus {
  all(
    label: "Tất cả",
    keyToServer: "ALL",
    count: "totalTaskCount",
  ),
  inProgress(
    label: "<PERSON><PERSON> thực hiện",
    keyToServer: "IN_PROGRESS",
    count: "inProcessTaskCount",
    color: CoreColors.blue,
  ),
  completed(
    label: "Đã thực hiện",
    keyToServer: "COMPLETED",
    count: "completeTaskCount",
    color: CoreColors.green,
  ),
  closed(
    label: "Đã đóng",
    keyToServer: "CLOSED",
    count: "closeTaskCount",
    color: CoreColors.green,
  ),
  canceled(
    label: "Đã hủy",
    keyToServer: "CANCELED",
    count: "canceledTaskCount",
    color: CoreColors.primary,
  );

  final String label;
  final String keyToServer;
  final String? count;
  final Color? color;

  const MissionStatus({
    this.color,
    this.count,
    required this.label,
    required this.keyToServer,
  });
}

extension MissionStatusExtension on MissionStatus {
  static MissionStatus? fromString(String? status) {
    switch (status) {
      case "ALL":
        return MissionStatus.all;
      case "IN_PROGRESS":
        return MissionStatus.inProgress;
      case "COMPLETED":
        return MissionStatus.completed;
      case "CLOSED":
        return MissionStatus.closed;
      case "CANCELED":
        return MissionStatus.canceled;
      default:
        return null;
    }
  }
}
