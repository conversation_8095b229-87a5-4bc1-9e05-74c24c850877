enum SuppliesType {
  normal(
    "<PERSON><PERSON><PERSON> tư thường",
    "NORMAL",
  ),
  oem(
    "Vật tư OEM",
    "OEM",
  ),
  salePoint(
    "Vật tư OEM",
    "SALE_POINT",
  ),
  package(
    "Vật tư gói",
    "PACKAGE",
  );

  const SuppliesType(
    this.display,
    this.keyToServer,
  );

  final String display;
  final String keyToServer;
}

extension SuppliesTypeExtension on SuppliesType {
  static SuppliesType fromString(String? status) {
    if ((status ?? '').isNotEmpty) {
      switch (status) {
        case "NORMAL":
          return SuppliesType.normal;
        case "OEM":
          return SuppliesType.oem;
        case "SALE_POINT":
          return SuppliesType.salePoint;
        default:
          return SuppliesType.normal;
      }
    }
    return SuppliesType.normal;
  }
}
