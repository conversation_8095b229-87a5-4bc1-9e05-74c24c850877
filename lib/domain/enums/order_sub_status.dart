import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';

enum OrderSubStatus {
  paying(
    label: "Chờ thanh toán",
    keyToServer: "PAYING",
    color: Colors.orange,
  ),
  commission(
    label: "Chờ xác nhận hoa hồng",
    keyToServer: "COMMISSION",
    color: CoreColors.green,
  ),
  confirmWaiting(
    label: "Chờ xác nhận đơn hàng",
    keyToServer: "CONFIRM_WAITING",
    color: CoreColors.orange,
  ),
  cnctWaiting(
    label: "Chờ hàng CNCT",
    keyToServer: "CNCT_WAITING",
    color: CoreColors.orange,
  ),
  ttqhWaiting(
    label: "Chờ hàng TTQH",
    keyToServer: "TTQH_WAITING",
    color: CoreColors.orange,
  ),
  processUserWaiting(
    label: "Chưa có nhân sự triển khai",
    keyToServer: "PROCESS_USER_WAITING",
    color: CoreColors.orange,
  ),
  packageWaiting(
    label: "Chờ đóng gói",
    keyToServer: "PACKAGE_WAITING",
    color: CoreColors.orange,
  ),
  processWaiting(
    label: "Chờ thực hiện",
    keyToServer: "PROCESS_WAITING",
    color: CoreColors.orange,
  ),
  scheduleRequest(
    label: "Đề xuất thay đổi lịch hẹn",
    keyToServer: "SCHEDULE_REQUEST",
    color: CoreColors.blue,
  ),
  shipping(
    label: "Đang giao hàng",
    keyToServer: "SHIPPING",
    color: CoreColors.green,
  ),
  processExtendRequest(
    label: "Đề xuất gia hạn triển khai",
    keyToServer: "PROCESS_EXTEND_REQUEST",
    color: CoreColors.blue,
  ),
  processing(
    label: "Đang thực hiện",
    keyToServer: "PROCESSING",
    color: CoreColors.blue,
  ),
  paymentWaiting(
    label: "Đã thực hiện, chờ thanh toán",
    keyToServer: "PAYMENT_WAITING",
    color: CoreColors.green,
  ),
  complete(
    label: "Hoàn thành",
    keyToServer: "COMPLETE",
    color: CoreColors.green,
  ),
  cancelRequest(
    label: "Đề xuất hủy",
    keyToServer: "CANCEL_REQUEST",
    color: CoreColors.primary,
  ),
  cancel(
    label: "Đã hủy",
    keyToServer: "CANCEL",
    color: CoreColors.primary,
  );

  final String label;
  final String keyToServer;
  final Color color;

  const OrderSubStatus({
    required this.label,
    required this.keyToServer,
    required this.color,
  });
}

extension OrderSubStatusExtension on OrderSubStatus {
  static OrderSubStatus? fromString(String? status) {
    if ((status ?? '').isNotEmpty) {
      switch (status) {
        case "PAYING":
          return OrderSubStatus.paying;
        case "COMMISSION":
          return OrderSubStatus.commission;
        case "CONFIRM_WAITING":
          return OrderSubStatus.confirmWaiting;
        case "CNCT_WAITING":
          return OrderSubStatus.cnctWaiting;
        case "TTQH_WAITING":
          return OrderSubStatus.ttqhWaiting;
        case "PROCESS_USER_WAITING":
          return OrderSubStatus.processUserWaiting;
        case "PACKAGE_WAITING":
          return OrderSubStatus.packageWaiting;
        case "PROCESS_WAITING":
          return OrderSubStatus.processWaiting;
        case "SCHEDULE_REQUEST":
          return OrderSubStatus.scheduleRequest;
        case "SHIPPING":
          return OrderSubStatus.shipping;
        case "PROCESS_EXTEND_REQUEST":
          return OrderSubStatus.processExtendRequest;
        case "PAYMENT_WAITING":
          return OrderSubStatus.paymentWaiting;
        case "COMPLETE":
          return OrderSubStatus.complete;
        case "CANCEL_REQUEST":
          return OrderSubStatus.cancelRequest;
        case "CANCEL":
          return OrderSubStatus.cancel;
        case "PROCESSING":
          return OrderSubStatus.processing;
        default:
          return null;
      }
    }
    return null;
  }
}
