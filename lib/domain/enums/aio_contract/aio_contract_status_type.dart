import 'package:base_ui/base_ui.dart';
import 'package:base_ui/theme/colors.dart';
import 'package:flutter/animation.dart';

enum AioContractStatusType {
  notPerformStaff(
    display: "<PERSON><PERSON><PERSON> có nhân viên thực hiện",
    keyToServer: 0,
    color: CoreColors.neutral10,
    bgColor: CoreColors.neutral01,
  ),
  notDone(
    display: "Chưa thực hiện",
    keyToServer: 1,
    color: CoreColors.neutral10,
    bgColor: CoreColors.neutral01,
  ),
  doing(
    display: "<PERSON>ang thực hiện",
    keyToServer: 2,
    color: CoreColors.blue06,
    bgColor: CoreColors.blue01,
  ),
  completed(
    display: "Đã hoàn thành",
    keyToServer: 3,
    color: CoreColors.green01,
    bgColor: CoreColors.green08,
  ),
  canceled(
    display: "Hủy",
    keyToServer: 4,
  ),
  suggestSuspension(
    display: "Đ<PERSON> xuất tạm dừng",
    keyToServer: 5,
    color: CoreColors.primary,
    bgColor: CoreColors.primary01,
  ),
  suggestCanceled(
    display: "Đề xuất hủy",
    keyToServer: 6,
    color: CoreColors.red07,
    bgColor: CoreColors.red01,
  ),
  waitingCreateOrder(
    display: "Chờ tạo đơn HS",
    keyToServer: 7,
  ),
  preparingGoods(
    display: "Đang chuẩn bị hàng hóa",
    keyToServer: 8,
  ),
  waitingDistrict(
    display: "Chờ hàng TTQH",
    keyToServer: 9,
  ),
  waitingGoodsBranch(
    display: "Chờ hàng CNCT",
    keyToServer: 10,
  ),
  nan(
    display: "",
    keyToServer: null,
  );

  final String display;
  final int? keyToServer;
  final Color? color;
  final Color? bgColor;

  const AioContractStatusType({
    required this.display,
    required this.keyToServer,
    this.color = CoreColors.neutral10,
    this.bgColor = CoreColors.neutral01,
  });
}
