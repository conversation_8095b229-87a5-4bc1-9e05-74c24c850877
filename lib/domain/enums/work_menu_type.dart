enum WorkMenuType {
  calendar(
    value: "calendar",
  ),
  orderImplementation(
    value: "orderImplementation",
  ),
  allOrder(
    value: "allOrder",
  ),
  contractSale(
    value: "contractSale",
  ),
  contract(
    value: "contract",
  ),
  deliverOrder(
    value: "deliverOrder",
  ),
  report(
    value: "report",
  ),
  stockExport(
    value: "stockExport",
  ),
  debtStatistics(
    value: "debtStatistics",
  ),
  requestBuyProduct(
    value: "requestBuyProduct",
  ),
  orderSell(
    value: "orderSell",
  ),
  allAssociationWO(
    value: "allAssociationWO",
  ),
  collectionInfoW(
    value: "collectionInfoW",
  ),
  mngAssociation(
    value: "mngAssociation",
  ),
  confirmCommission(
    value: "confirmCommission",
  ),
  bill(
    value: "bill",
  ),
  documentSale(
    value: "documentSale",
  ),
  explanation(
    value: "explanation",
  ),
  salesTeam(
    value: "salesTeam",
  ),
  reportError(
    value: "reportError",
  ),
  feedback(
    value: "feedback",
  ),
  warranty(
    value: "warranty",
  ),
  manageCollaborator(
    value: "manageCollaborator",
  ),
  orderAgency(
    value: "orderAgency",
  ),
  quote(
    value: 'quote',
  ),
  contractDrafting(
    value: 'contractDrafting',
  );

  final String value;

  const WorkMenuType({
    required this.value,
  });
}
