enum WorkScheduleEnum {
  deploymentOrder(
    keyToServer: 'DEPLOYMENT_ORDER',
    display: 'Đơn triển khai',
  ),
  bundleOrder(
    keyToServer: 'BUNDLE_ORDER',
    display: 'Đơn gói giá',
  ),
  customerInformationGather(
    keyToServer: 'CUSTOMER_INFORMATION_GATHER',
    display: 'Thu thập thông tin khách hàng',
  ),
  warrantyRequest(
    keyToServer: 'WARRANTY_REQUEST',
    display: '<PERSON>êu cầu bảo hành',
  ),
  customerClaim(
    keyToServer: 'CUSTOMER_CLAIM',
    display: 'Khiếu nại phản ánh',
  );

  const WorkScheduleEnum({
    required this.keyToServer,
    this.display,
  });

  final String keyToServer;
  final String? display;
}

extension WorkScheduleEnumExtension on WorkScheduleEnum {
  static WorkScheduleEnum fromKey(String? key) {
    switch (key) {
      case "DEPLOYMENT_ORDER":
        return WorkScheduleEnum.deploymentOrder;
      case "BUNDLE_ORDER":
        return WorkScheduleEnum.bundleOrder;
      case "CUSTOMER_INFORMATION_GATHER":
        return WorkScheduleEnum.customerInformationGather;
      case "WARRANTY_REQUEST":
        return WorkScheduleEnum.warrantyRequest;
      case "CUSTOMER_CLAIM":
        return WorkScheduleEnum.customerClaim;
      default:
        return WorkScheduleEnum.deploymentOrder;
    }
  }

  static String? fromKeyToDisplay(String? key) {
    switch (key) {
      case "DEPLOYMENT_ORDER":
        return WorkScheduleEnum.deploymentOrder.display;
      case "BUNDLE_ORDER":
        return WorkScheduleEnum.bundleOrder.display;
      case "CUSTOMER_INFORMATION_GATHER":
        return WorkScheduleEnum.customerInformationGather.display;
      case "WARRANTY_REQUEST":
        return WorkScheduleEnum.warrantyRequest.display;
      case "CUSTOMER_CLAIM":
        return WorkScheduleEnum.customerClaim.display;
      default:
        return WorkScheduleEnum.deploymentOrder.display;
    }
  }
}
