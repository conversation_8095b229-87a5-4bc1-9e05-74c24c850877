enum ServiceType {
  combo("COMBO", "Gói combo"),
  package("PACKAGE", "Dịch vụ gói"),
  single("SINGLE", "<PERSON><PERSON><PERSON> vụ lẻ"),
  supply("SUPPLY", "Đơn vật tư"),
  //3 loai don sau cho phan chon phieu mua hang diem ban ------
  salePoint("SALE_POINT", "Phiếu mua hàng"),
  salePointSingle("SALE_POINT_SINGLE", "Phiếu mua hàng điểm bán lẻ"),
  salePointCombo("SALE_POINT_COMBO", "Phiếu mua hàng điểm bán combo"),
  //--------------------------------------------------
  all("ALL", "Tất cả");

  final String code;
  final String value;

  const ServiceType(
    this.code,
    this.value,
  );
}

extension ServiceTypeExtension on ServiceType? {
  static ServiceType fromString(String? type) {
    switch (type) {
      case "COMBO":
        return ServiceType.combo;
      case "PACKAGE":
        return ServiceType.package;
      case "SERVICE":
        return ServiceType.single;
      case "SALE_POINT":
        return ServiceType.salePoint;
      case "SALE_POINT_COMBO":
        return ServiceType.salePointCombo;
      case "SUPPLY":
        return ServiceType.supply;
      default:
        return ServiceType.single;
    }
  }

  String getTypeName() {
    switch (this) {
      case ServiceType.combo:
        return "Đơn dịch vụ combo";
      case ServiceType.package:
        return "Đơn dịch vụ gói";
      case ServiceType.single:
        return "Đơn dịch vụ lẻ";
      case ServiceType.salePoint:
        return "Đơn điểm bán lẻ";
      case ServiceType.salePointCombo:
        return "Đơn điểm bán combo";
      case ServiceType.supply:
        return "Đơn vật tư";
      default:
        return '';
    }
  }

  String get keyToCreateOrder {
    switch (this) {
      case ServiceType.combo:
        return "COMBO";
      case ServiceType.package:
        return "PACKAGE";
      case ServiceType.single:
        return "SERVICE";
      case ServiceType.salePoint:
        return "SALE_POINT";
      case ServiceType.salePointCombo:
        return "SALE_POINT_COMBO";
      case ServiceType.supply:
        return "SUPPLY";
      default:
        return 'SERVICE';
    }
  }
}
