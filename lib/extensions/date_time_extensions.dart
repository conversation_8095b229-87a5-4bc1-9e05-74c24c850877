import 'package:intl/intl.dart';
import 'package:vcc/app_configs/date_time_formater.dart';

extension StringExtension on DateTime {
  String display({
    String format = DateTimeFormater.dateFormatVi,
  }) {
    return DateFormat(format).format(this);
  }

  bool get isWeekend {
    return weekday == 6 || weekday == 7;
  }

  DateTime setHour(
    int hour, [
    int? minute,
    int? second,
    int? millisecond,
    int? microsecond,
  ]) {
    return DateTime(
      year,
      month,
      day,
      hour,
      minute ?? this.minute,
      second ?? this.second,
      millisecond ?? this.millisecond,
      microsecond ?? this.microsecond,
    );
  }

  static datetimeToString(String? time) {
    if (time == null) {
      return "";
    } else {
      DateTime dt = DateTime.parse(time);

      DateTime now = DateTime.now();
      DateTime startOfWeek = now.subtract(Duration(days: now.weekday - 1));

      DateTime startOfLastWeek = startOfWeek.subtract(const Duration(days: 7));

      DateTime endOfLastWeek = startOfWeek.subtract(const Duration(seconds: 1));

      DateTime yesterday = now.subtract(const Duration(days: 1));

      bool isToday =
          dt.year == now.year && dt.month == now.month && dt.day == now.day;
      bool isYesterday = dt.year == yesterday.year &&
          dt.month == yesterday.month &&
          dt.day == yesterday.day;
      bool isThisWeek = dt.isAfter(startOfWeek) &&
          dt.isBefore(now.add(const Duration(days: 1)));
      bool isLastWeek =
          dt.isAfter(startOfLastWeek) && dt.isBefore(endOfLastWeek);

      if (isToday) {
        return DateFormat('HH:mm').format(dt);
      } else if (isYesterday) {
        return 'Hôm qua, ${DateFormat('HH:mm').format(dt)}';
      } else if (isThisWeek) {
        return 'Thứ ${dt.weekday}, ${DateFormat('HH:mm').format(dt)}';
      } else if (isLastWeek) {
        return DateFormat('dd/MM/yyyy, HH:mm').format(dt);
      } else {
        return DateFormat('dd/MM/yyyy, HH:mm').format(dt);
      }
    }
  }

  String displayView({
    String format = DateTimeFormater.dateTimeFormatView,
  }) {
    return DateFormat(format).format(this);
  }
}
