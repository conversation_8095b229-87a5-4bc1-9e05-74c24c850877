import 'dart:convert';
import 'dart:developer';

import 'package:dio/dio.dart';
import 'package:flutter/foundation.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/app_configs/coordinator.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/local/secure_storage.dart';
import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/remote/base/base_options.dart';
import 'package:vcc/data/remote/base/logging_interceptor.dart';
import 'package:vcc/domain/entities/auth/token_entity.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/utils/log_utils.dart';

bool loadingRefreshToken = false;

class ApiInterceptor extends Interceptor {
  DateTime? startTime;
  DateTime? endTime;

  static const String httpTAG = 'HTTP - LOG';

  @override
  void onRequest(
    RequestOptions options,
    RequestInterceptorHandler handler,
  ) async {
    final token = await SecureStorage.instance.getToken();
    if (token?.accessToken != null) {
      options.headers['Authorization'] = 'Bearer ${token?.accessToken}';
    }

    if (GlobalData.instance.deviceId != null) {
      options.headers['deviceId'] = '${GlobalData.instance.deviceId}';
    }

    startTime = DateTime.now();
    LogUtils.d(' ', tag: httpTAG);
    LogUtils.d(
      '------------------- Start -------------------',
      tag: httpTAG,
    );
    // if (options.queryParameters.isEmpty) {
    //   LogUtils.d(
    //     'Request Url         : '
    //     '${options.method}'
    //     ' '
    //     '${options.baseUrl}'
    //     '${options.path}',
    //     tag: httpTAG,
    //   );
    // } else {
    //   LogUtils.d(
    //     'Request Url         : '
    //     '${options.method}  '
    //     '${options.baseUrl}${options.path}?'
    //     '${Transformer.urlEncodeMap(options.queryParameters)}',
    //     tag: httpTAG,
    //   );
    // }
    //
    // LogUtils.d(
    //   'Request ContentType : ${options.contentType}',
    //   tag: httpTAG,
    // );
    //
    // if (options.data != null) {
    //   LogUtils.d(
    //     'Request Data        : ${options.data.toString()}',
    //     tag: httpTAG,
    //   );
    // }
    //
    // LogUtils.d(
    //   'Request Headers     : ${options.headers.toString()}',
    //   tag: httpTAG,
    // );

    try {
      if (kDebugMode) {
        log('COPY CURL and send it to BE');
        log(cURLRepresentation(options));
      }
    } catch (e) {
      log('Create CURL failure!! - $e');
    }
    LogUtils.d('--', tag: httpTAG);

    handler.next(options);
  }

  @override
  void onResponse(
    Response<dynamic> response,
    ResponseInterceptorHandler handler,
  ) {
    endTime = DateTime.now();
    final int duration =
        endTime!.difference(startTime ?? DateTime.now()).inMilliseconds;
    LogUtils.d(
      'Response_Code       : ${response.statusCode}',
      tag: httpTAG,
    );
    // 输出结果
    LogUtils.d(
      'Response_Data       : ${response.data.toString()}',
      tag: httpTAG,
    );
    LogUtils.d(
      '------------- End: $duration ms -------------',
      tag: httpTAG,
    );
    LogUtils.d('' '', tag: httpTAG);
    handler.next(response);
  }

  @override
  void onError(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    final statusCode = err.response?.statusCode;
    final path = err.requestOptions.path;
    LogUtils.e(
      "⚠️ ERROR[$statusCode] => PATH: $path \n Response: ${err.response?.data}",
    );

    switch (statusCode) {
      case 401:
        handler401Error(err, handler);
      default:
        handler.next(err);
    }
  }

  void handler401Error(
    DioException err,
    ErrorInterceptorHandler handler,
  ) async {
    final uri = err.requestOptions.uri;
    final RequestOptions request = err.requestOptions;

    final savedToken = await SecureStorage.instance.getToken();
    String requestingTokens = err.requestOptions.headers['Authorization']
        .toString()
        .replaceFirst("Bearer ", "");

    //Handle following request with old token
    if (savedToken != null &&
        savedToken.accessToken != null &&
        savedToken.accessToken != requestingTokens) {
      //Clone request with new token
      final cloneReq = await _cloneRequest(
        accessToken: savedToken.accessToken ?? '',
        request: request,
        uri: uri,
      );
      return handler.resolve(cloneReq);
    }

    //Don't have savedToken => return error
    if (savedToken == null) {
      return handler.next(err);
    }

    if (loadingRefreshToken) {
      handler401Error(err, handler);
      return;
    }

    //Refresh token in first request
    try {
      savedToken.accessToken = null;
      await SecureStorage.instance.saveToken(savedToken);
      loadingRefreshToken = true;

      Dio dio = Dio();
      dio.options = OptionRequest.options;
      dio.interceptors.add(LoggingInterceptor());

      if (GlobalData.instance.deviceId != null) {
        dio.options.headers['deviceId'] = '${GlobalData.instance.deviceId}';
      }

      String? userName = await SecureStorage.instance.getUserName();
      final result = await dio.post(
        '$authenticationService/v1/refresh',
        data: {
          "refreshToken": savedToken.refreshToken,
          "username": userName,
        },
      );

      if (result.statusCode != null) {
        final token = TokenEntity.fromJson(result.data as Map<String, dynamic>);

        SecureStorage.instance.saveToken(token);
        loadingRefreshToken = false;
        final cloneReq = await _cloneRequest(
          accessToken: token.accessToken ?? '',
          request: request,
          uri: uri,
        );
        return handler.resolve(cloneReq);
      } else {
        if (loadingRefreshToken && savedToken.accessToken != requestingTokens) {
          loadingRefreshToken = false;
          final cloneReq = await _cloneRequest(
            accessToken: savedToken.accessToken ?? '',
            request: request,
            uri: uri,
          );
          return handler.resolve(cloneReq);
        } else {
          loadingRefreshToken = false;
          _forceSignIn();
          return handler.next(err);
        }
      }
    } catch (e) {
      //Refresh failure => force login
      LogUtils.e(
          "Api refresh token error $e, msg: ${(e as DioException).response}");
      loadingRefreshToken = false;
      _forceSignIn();
      return handler.next(err);
    }
  }

  Future<Response> _cloneRequest({
    String? accessToken,
    required RequestOptions request,
    required Uri uri,
  }) async {
    final newOptions = Options(
      method: request.method,
      headers: request.headers,
    );

    if ((accessToken ?? '').isNotEmpty) {
      newOptions.headers?['Authorization'] = 'Bearer $accessToken';
    }

    Dio dio = Dio();
    dio.options = OptionRequest.options;
    dio.interceptors.add(LoggingInterceptor());

    return await dio.request(
      request.path,
      options: newOptions,
      data: request.data,
      queryParameters: request.queryParameters,
    );
  }

  void _forceSignIn() {
    SecureStorage.instance.removeToken();
    SecureStorage.instance.removeUserName();
    GlobalData.instance.userInfo = null;

    while (AppCoordinator.context.canPop()) {
      AppCoordinator.context.pop();
    }
    AppCoordinator.context.go(RouterPaths.login);
  }

  String cURLRepresentation(RequestOptions options) {
    List<String> components = ['curl -i'];
    //if (options.method.toUpperCase() == 'GET') {
    components.add('-X ${options.method}');
    //}

    options.headers.forEach((k, v) {
      if (k != 'Cookie') {
        components.add('-H \'$k: $v\'');
      }
    });

    if (options.method.toUpperCase() != 'GET') {
      if (options.data is FormData) {
        components.add(extractFormData({}, options.data));
      } else {
        var data = json.encode(options.data);
        data = data.replaceAll('\'', '\\\'');
        components.add('-d \'$data\'');
      }
    }

    components.add('\'${options.uri.toString()}\'');

    return components.join('\\\n\t');
  }

  String extractFormData(Map<String, dynamic> files, FormData fData) {
    for (final element in fData.files) {
      MultipartFile file = element.value;
      files[element.key] = '@${file.filename}';
    }
    for (final element in fData.fields) {
      files[element.key] = element.value;
    }
    if (files.isNotEmpty) {
      return files
          .map((key, value) => MapEntry(key, "-F '$key=$value'"))
          .values
          .join(' ');
    }
    return '';
  }
}


