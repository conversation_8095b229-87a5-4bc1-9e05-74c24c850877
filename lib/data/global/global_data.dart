import 'package:vcc/domain/body/agency/request_detail_body.dart';
import 'package:vcc/domain/entities/auth/role_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';

class GlobalData {
  GlobalData._privateConstructor();

  static final GlobalData instance = GlobalData._privateConstructor();

  String? deviceId;
  InternalStaffEntity? userInfo;
  AddressEntity? addressDefault;
  List<RoleEntity>? listRoleUser;

  bool isShowErrorDialog = false;

  String? version;

  List<RequestDetailBody> orderAgencyDraft = [];

  String? orderAgencyPaymentCurrent = '';

  bool? isCurrentPaymentAgencyScreen = false;
}
