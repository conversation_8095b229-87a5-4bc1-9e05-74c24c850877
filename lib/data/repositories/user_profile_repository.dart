import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/repositories/base/base_repository.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/domain/body/address_body.dart';
import 'package:vcc/domain/body/change_password_body.dart';
import 'package:vcc/domain/body/send_otp_body.dart';
import 'package:vcc/domain/body/sent_otp_body.dart';
import 'package:vcc/domain/body/update_user_info_body.dart';
import 'package:vcc/domain/body/user_info_body.dart';
import 'package:vcc/domain/entities/device_info_entity.dart';
import 'package:vcc/domain/entities/order/telesales_entity.dart';
import 'package:vcc/domain/entities/send_otp_response.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/entities/user/user_info_entity.dart';
import 'package:vcc/domain/responses/list_data_response.dart';
import 'package:vcc/domain/body/check_role_user_body.dart';
import 'package:vcc/domain/entities/check_role_user_entity.dart';
import 'package:vcc/domain/entities/cus360/contract_entity.dart';
import 'package:vcc/domain/entities/cus360/interact_entity.dart';
import 'package:vcc/domain/entities/cus360/survey_entity.dart';
import 'package:vcc/domain/entities/cus360/ticket_entity.dart';
import 'package:vcc/domain/entities/cus360/warranty_entity.dart';
import 'package:vcc/domain/entities/customer_info_entity.dart';
import 'package:vcc/domain/entities/user/user_simple_entity.dart';
import 'package:vcc/domain/enums/user_type.dart';

abstract class UserProfileRepository {
  Future<BaseResult<SendOtpResponse>?> sendOtp({
    required SendOTPBody body,
  });

  Future<BaseResult<SendOtpResponse>?> sendOtpV2({
    required SendOTPBody body,
  });

  Future<BaseResult?> verifyOTP({
    required SendOTPBody body,
  });

  Future<BaseResult?> forgotPassword({
    required SendOTPBody body,
  });

  Future<BaseResult<InternalStaffEntity>?> getProfile();

  Future<BaseResult<UserInfoEntity>?> getProfileV2();

  Future<BaseResult?> updateUserProfile({
    required UpdateUserInfoBody body,
  });

  Future<BaseResult?> registerDevice({
    required DeviceInfoEntity deviceInfo,
  });

  Future<BaseResult> updateAddressDefault({
    required AddressBody address,
  });

  Future<BaseResult<ArrayResponse<TelesalesEntity>>?> getTelesales({
    String? keyword,
  });

  Future<BaseResult?> sentOtp({
    required String orderCode,
  });

  Future<BaseResult<CustomerInfoEntity>?> getCustomerInfo({
    String? phoneNumber,
    String? taxCode,
    required UserType customerType,
  });

  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListStaff({
    int? position,
    String? userType,
    String? phoneNumber,
    int? page,
    int? pageSize,
    String? keyword,
  });

  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListStaffReceiving({
    int? page,
    int? pageSize,
    String? keyword,
    String? provinceCode,
  });

  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListOFT3({
    int? page,
    int? pageSize,
    String? keyword,
    String? schedule,
  });

  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListWorker({
    String? provinceCode,
    String? wardCode,
    String? orderType,
    int? page,
    int? pageSize,
    String? keyword,
    String? schedule,
    String? workerType,
  });

  Future<BaseResult<CustomerInfoEntity>> cus360Info({
    String? customerType,
    String? phoneNumber,
    String? taxCode,
  });

  Future<BaseResult<ArrayResponse<ContractEntity>>> cus360InfoContract({
    String? customerType,
    String? phoneNumber,
    int? customerId,
    int? page,
    int? pageSize,
  });

  Future<BaseResult<InteractEntity>> getInfoInteract({
    String? customerType,
    int? customerId,
  });

  Future<BaseResult<ArrayResponse<SurveyEntity>>> getSurvey({
    String? customerType,
    int? customerId,
  });

  Future<BaseResult<ArrayResponse<TicketEntity>>> getTicket({
    String? customerType,
    int? customerId,
  });

  Future<BaseResult<ArrayResponse<WarrantyEntity>>> getWarranty({
    String? customerType,
    int? customerId,
  });

  Future<BaseResult<ArrayResponse<UserSimpleEntity>>> searchUser({
    String? keyword,
    String? userType,
    int? size,
    int? page,
  });

  Future<BaseResult<CheckRoleUserEntity>?> checkRoleUser({
    CheckRoleUserBody? body,
  });

  Future<BaseResult?> updateUserInfo({
    required UserInfoBody body,
  });

  Future<BaseResult?> changePassword({
    required ChangePasswordBody body,
  });

  Future<BaseResult?> sentOtpRequirementWarranty({
    required SentOtpBody body,
  });

  Future<BaseResult?> verifyWarrantyOTP({
    required SendOTPBody body,
  });
}

class UserProfileRepositoryImpl extends BaseRepository
    implements UserProfileRepository {
  UserProfileRepositoryImpl({
    required ApiClient apiClient,
  }) : _apiClient = apiClient;

  final ApiClient _apiClient;

  @override
  Future<BaseResult<SendOtpResponse>?> sendOtp({
    required SendOTPBody body,
  }) async {
    return await safeApiCall<SendOtpResponse>(
      _apiClient.sendOtp(body: body),
    );
  }

  @override
  Future<BaseResult<SendOtpResponse>?> sendOtpV2({
    required SendOTPBody body,
  }) async {
    return await safeApiCall<SendOtpResponse>(
      _apiClient.sendOtpV2(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<InternalStaffEntity>?> getProfile() async {
    return await safeApiCall<InternalStaffEntity>(
      _apiClient.getProfile(),
    );
  }

  @override
  Future<BaseResult<UserInfoEntity>?> getProfileV2() async {
    return await safeApiCall<UserInfoEntity>(
      _apiClient.getProfileV2(),
    );
  }

  @override
  Future<BaseResult?> updateUserProfile({
    required UpdateUserInfoBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateUserProfile(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult> forgotPassword({
    required SendOTPBody body,
  }) async {
    return await safeApiCall(
      _apiClient.forgotPassword(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult> verifyOTP({
    required SendOTPBody body,
  }) async {
    return await safeApiCall(
      _apiClient.verifyOTP(body: body),
    );
  }

  @override
  Future<BaseResult?> registerDevice({
    required DeviceInfoEntity deviceInfo,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.registerDeviceInfo(deviceInfo),
    );
  }

  @override
  Future<BaseResult> updateAddressDefault({
    required AddressBody address,
  }) async {
    return await safeApiCall(
      _apiClient.updateAddressDefault(
        body: address,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<TelesalesEntity>>?> getTelesales({
    String? keyword,
  }) async {
    return await safeApiCall(
      _apiClient.getTelesales(
        keyword: keyword,
      ),
    );
  }

  @override
  Future<BaseResult?> sentOtp({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.sentOtp(
        body: SentOtpBody(
          orderCode: orderCode,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<CustomerInfoEntity>?> getCustomerInfo({
    String? phoneNumber,
    String? taxCode,
    required UserType customerType,
  }) async {
    return await safeApiCall<CustomerInfoEntity>(
      _apiClient.getCustomerInfo(
        phoneNumber: phoneNumber,
        taxCode: taxCode,
        customerType: customerType.keyToServer,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListStaff({
    int? position,
    String? userType,
    String? phoneNumber,
    int? page,
    int? pageSize,
    String? keyword,
  }) async {
    return await safeApiCall<ArrayResponse<InternalStaffEntity>>(
      _apiClient.getListStaff(
        position: position,
        userType: userType,
        phoneNumber: phoneNumber,
        page: page,
        pageSize: pageSize,
        keyword: keyword,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListStaffReceiving({
    int? page,
    int? pageSize,
    String? keyword,
    String? provinceCode,
  }) async {
    return await safeApiCall<ArrayResponse<InternalStaffEntity>>(
      _apiClient.getListStaffReceiving(
        page: page,
        pageSize: pageSize,
        keyword: keyword,
        userTypes: "INTERNAL",
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListOFT3({
    int? page,
    int? pageSize,
    String? keyword,
    String? schedule,
  }) async {
    return await safeApiCall<ArrayResponse<InternalStaffEntity>>(
      _apiClient.getListOFT3(
        page: page,
        pageSize: pageSize,
        keyword: keyword,
        startTime: schedule,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListWorker({
    int? page,
    int? pageSize,
    String? keyword,
    String? provinceCode,
    String? wardCode,
    String? orderType,
    String? schedule,
    String? workerType,
  }) async {
    return await safeApiCall<ArrayResponse<InternalStaffEntity>>(
      _apiClient.getListWorker(
        page: page,
        pageSize: pageSize,
        keyword: keyword,
        provinceCode: provinceCode,
        wardCode: wardCode,
        orderType: orderType,
        startTime: schedule,
        workerType: workerType,
      ),
    );
  }

  @override
  Future<BaseResult<CustomerInfoEntity>> cus360Info({
    String? customerType,
    String? phoneNumber,
    String? taxCode,
  }) async {
    return await safeApiCall<CustomerInfoEntity>(
      _apiClient.cus360Info(
        customerType: customerType,
        phoneNumber: phoneNumber,
        taxCode: taxCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ContractEntity>>> cus360InfoContract({
    String? customerType,
    String? phoneNumber,
    int? customerId,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<ArrayResponse<ContractEntity>>(
      _apiClient.cus360InfoContract(
        customerType: customerType,
        phoneNumber: phoneNumber,
        customerId: customerId,
        page: page ?? 0,
        size: pageSize ?? 10,
      ),
    );
  }

  @override
  Future<BaseResult<InteractEntity>> getInfoInteract({
    String? customerType,
    String? phoneNumber,
    int? customerId,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<InteractEntity>(
      _apiClient.getInfoInteract(
        customerType: customerType,
        customerId: customerId,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<SurveyEntity>>> getSurvey({
    String? customerType,
    String? phoneNumber,
    int? customerId,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<ArrayResponse<SurveyEntity>>(
      _apiClient.getSurvey(
        customerType: customerType,
        customerId: customerId,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<TicketEntity>>> getTicket({
    String? customerType,
    String? phoneNumber,
    int? customerId,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<ArrayResponse<TicketEntity>>(
      _apiClient.getTicket(
        customerType: customerType,
        customerId: customerId,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<WarrantyEntity>>> getWarranty({
    String? customerType,
    String? phoneNumber,
    int? customerId,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<ArrayResponse<WarrantyEntity>>(
      _apiClient.getWarranty(
        customerType: customerType,
        customerId: customerId,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<UserSimpleEntity>>> searchUser({
    String? keyword,
    String? userType,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<UserSimpleEntity>>(
      _apiClient.searchUser(
        page: page,
        size: size,
        userType: userType,
        keyword: keyword,
      ),
    );
  }

  @override
  Future<BaseResult<CheckRoleUserEntity>?> checkRoleUser({
    CheckRoleUserBody? body,
  }) async {
    return await safeApiCall<CheckRoleUserEntity>(
      isShowError: false,
      _apiClient.checkRoleUser(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> updateUserInfo({
    required UserInfoBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateUserInfo(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> changePassword({
    required ChangePasswordBody body,
  }) async {
    return await safeApiCall(
      _apiClient.changePassword(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> sentOtpRequirementWarranty({
    required SentOtpBody body,
  }) async {
    return await safeApiCall(
      _apiClient.sentOtpRequirementWarranty(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> verifyWarrantyOTP({
    required SendOTPBody body,
  }) async {
    return await safeApiCall(
      _apiClient.verifyWarrantyOTP(
        body: body,
      ),
    );
  }
}
