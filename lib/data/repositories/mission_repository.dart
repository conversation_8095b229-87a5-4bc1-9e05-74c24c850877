import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/domain/responses/list_data_response.dart';
import 'package:vcc/data/repositories/base/base_repository.dart';
import 'package:vcc/domain/entities/mission/mission_entity.dart';
import 'package:vcc/domain/body/mission/complete_mission_body.dart';
import 'package:vcc/domain/body/mission/save_plan_mission_body.dart';
import 'package:vcc/domain/body/mission/extension_implementation_time_body.dart';

abstract class MissionRepository {
  Future<BaseResult<ArrayResponse<MissionEntity>>?> getListMission({
    String? keyword,
    List<String>? types,
    List<String>? status,
    List<String>? kpiTypes,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? size,
  });

  Future<BaseResult<Map<String, int>?>> getCountMission({
    String? keyword,
    List<String>? types,
    List<String>? kpiTypes,
    DateTime? fromDate,
    DateTime? toDate,
  });

  Future<BaseResult<MissionEntity>?> getMissionDetail({
    String? taskCode,
  });

  Future<BaseResult?> extensionImplementationTime({
    String? taskCode,
    required ExtensionImplementationTimeBody body,
  });

  Future<BaseResult?> completeMission({
    String? taskCode,
    required CompleteMissionBody body,
  });

  Future<BaseResult?> savePlanMission({
    String? taskCode,
    required SavePlanMissionBody body,
  });
}

class MissionRepositoryImpl extends BaseRepository
    implements MissionRepository {
  MissionRepositoryImpl({
    required ApiClient apiClient,
  }) : _apiClient = apiClient;

  final ApiClient _apiClient;

  @override
  Future<BaseResult<ArrayResponse<MissionEntity>>?> getListMission({
    String? keyword,
    List<String>? types,
    List<String>? status,
    List<String>? kpiTypes,
    DateTime? fromDate,
    DateTime? toDate,
    int? page,
    int? size,
  }) async {
    return await safeApiCall(
      _apiClient.getListMission(
        keyword: keyword,
        types: types,
        status: status,
        kpiTypes: kpiTypes,
        fromDate: fromDate,
        toDate: toDate,
        page: page,
        size: size,
      ),
    );
  }

  @override
  Future<BaseResult<Map<String, int>?>> getCountMission({
    String? keyword,
    List<String>? types,
    List<String>? kpiTypes,
    DateTime? fromDate,
    DateTime? toDate,
  }) async {
    return await safeApiCall(
      _apiClient.getCountMission(
        keyword: keyword,
        types: types,
        kpiTypes: kpiTypes,
        fromDate: fromDate,
        toDate: toDate,
      ),
    );
  }

  @override
  Future<BaseResult<MissionEntity>?> getMissionDetail({
    String? taskCode,
  }) async {
    return await safeApiCall(
      _apiClient.getMissionDetail(
        taskCode: taskCode,
      ),
    );
  }

  @override
  Future<BaseResult?> extensionImplementationTime({
    String? taskCode,
    required ExtensionImplementationTimeBody body,
  }) async {
    return await safeApiCall(
      _apiClient.extensionImplementationTime(
        taskCode: taskCode,
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> completeMission({
    String? taskCode,
    required CompleteMissionBody body,
  }) async {
    return await safeApiCall(
      _apiClient.completeMission(
        taskCode: taskCode,
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> savePlanMission({
    String? taskCode,
    required SavePlanMissionBody body,
  }) async {
    return await safeApiCall(
      _apiClient.savePlanMission(
        taskCode: taskCode,
        body: body,
      ),
    );
  }
}
