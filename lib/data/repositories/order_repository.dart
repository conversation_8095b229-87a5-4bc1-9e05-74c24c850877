import 'dart:io';

import 'package:dio/dio.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/repositories/base/base_repository.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/domain/body/assign_worker_body.dart';
import 'package:vcc/domain/body/bill_stock_body.dart';
import 'package:vcc/domain/body/buy_supplies_for_customer_body.dart';
import 'package:vcc/domain/body/cancel_extend_kpi_order_body.dart';
import 'package:vcc/domain/body/change_commission_body.dart';
import 'package:vcc/domain/body/checklist_body.dart';
import 'package:vcc/domain/body/distance_cost_body.dart';
import 'package:vcc/domain/body/export_district_product_body.dart';
import 'package:vcc/domain/body/extend_kpi_order_body.dart';
import 'package:vcc/domain/body/image_order_body.dart';
import 'package:vcc/domain/body/items_amount_body.dart';
import 'package:vcc/domain/body/list_order_body.dart';
import 'package:vcc/domain/body/order_request_aio_response.dart';
import 'package:vcc/domain/body/request_branch_product_body.dart';
import 'package:vcc/domain/body/request_buy_product_body.dart';
import 'package:vcc/domain/body/request_buy_product_body_aio.dart';
import 'package:vcc/domain/body/request_cancel_body.dart';
import 'package:vcc/domain/body/request_district_product_body.dart';
import 'package:vcc/domain/body/request_schedule_body.dart';
import 'package:vcc/domain/body/schedule_worker_body.dart';
import 'package:vcc/domain/body/send_otp_body.dart';
import 'package:vcc/domain/body/sign_acceptance_body.dart';
import 'package:vcc/domain/body/service_order_body.dart';
import 'package:vcc/domain/body/sign_item_warranty_body.dart';
import 'package:vcc/domain/body/stock_body.dart';
import 'package:vcc/domain/body/transfer_employee_body.dart';
import 'package:vcc/domain/body/update_commission_body.dart';
import 'package:vcc/domain/body/update_info_customer_body.dart';
import 'package:vcc/domain/body/update_installation_order_product_body.dart';
import 'package:vcc/domain/body/update_order_body.dart';
import 'package:vcc/domain/body/upload_report_order_body.dart';
import 'package:vcc/domain/entities/bill_info_entity.dart';
import 'package:vcc/domain/entities/check_duplicate_order_entity.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/count_schedule_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_entity.dart';
import 'package:vcc/domain/entities/order/bill_entity.dart';
import 'package:vcc/domain/entities/order/checklist_response.dart';
import 'package:vcc/domain/entities/order/count_order_entity.dart';
import 'package:vcc/domain/entities/order/customer_information_entity.dart';
import 'package:vcc/domain/entities/order/debt_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_item_entity.dart';
import 'package:vcc/domain/entities/order/duplicate_order_entity.dart';
import 'package:vcc/domain/entities/order/file_contract_entity.dart';
import 'package:vcc/domain/entities/order/good_impact_history.dart';
import 'package:vcc/domain/entities/order/job_type_entity.dart';
import 'package:vcc/domain/entities/order/list_commission_model.dart';
import 'package:vcc/domain/entities/order/order_info_entity.dart';
import 'package:vcc/domain/entities/order/order_info_short_entity.dart';
import 'package:vcc/domain/entities/order/original_price_supply_entity.dart';
import 'package:vcc/domain/entities/order/other_service_entity.dart';
import 'package:vcc/domain/entities/order/package_entity.dart';
import 'package:vcc/domain/entities/order/reason_schedule_default_entity.dart';
import 'package:vcc/domain/entities/order/reasons_cancel_entity.dart';
import 'package:vcc/domain/entities/order/schedule_worker_entity.dart';
import 'package:vcc/domain/entities/order/serial_info_entity.dart';
import 'package:vcc/domain/entities/order/statistic_order_entity.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/entities/order/telesales_entity.dart';
import 'package:vcc/domain/entities/order/warning_alert_entity.dart';
import 'package:vcc/domain/entities/order/work_count_entity.dart';
import 'package:vcc/domain/entities/order/work_data_entity.dart';
import 'package:vcc/domain/entities/price_order_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/entities/stock/bill_stock_entity.dart';
import 'package:vcc/domain/entities/stock/detail_bill_stock_entity.dart';
import 'package:vcc/domain/entities/stock/inventory_entity.dart';
import 'package:vcc/domain/entities/stock/stock_entity.dart';
import 'package:vcc/domain/entities/stock/supply_of_bill_entity.dart';
import 'package:vcc/domain/entities/work/branch_time_line_entity.dart';
import 'package:vcc/domain/entities/work/request_product_entity.dart';
import 'package:vcc/domain/entities/work/request_time_line_entity.dart';
import 'package:vcc/domain/entities/work/supply_request_product_entity.dart';
import 'package:vcc/domain/entities/work_schedule_event_entity.dart';
import 'package:vcc/domain/params/price_order_param.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:vcc/domain/responses/list_data_response.dart';
import 'package:vcc/domain/responses/order/commission_confirm_response.dart';
import 'package:vcc/domain/responses/order/complete_order_response.dart';
import 'package:vcc/domain/responses/order/warning_order_response.dart';
import 'package:vcc/domain/responses/stock/serial_response.dart';
import 'package:vcc/domain/entities/product/category_entity.dart';
import 'package:vcc/domain/entities/product/inventory_address_info.dart';
import 'package:vcc/domain/entities/product/product_entity.dart';
import 'package:vcc/domain/entities/product/product_info_entity.dart';
import 'package:vcc/domain/enums/menu_type.dart';
import 'package:vcc/domain/params/contact_info_param.dart';
import 'package:vcc/domain/responses/order/order_response.dart';
import 'package:vcc/domain/body/voucher_body.dart';
import 'package:vcc/domain/entities/product/category_package_entity.dart';
import 'package:vcc/domain/entities/service/service_cat_entity.dart';
import 'package:vcc/domain/entities/service/service_entity.dart';
import 'package:vcc/domain/entities/service/service_request_entity.dart';
import 'package:vcc/domain/entities/voucher_entity.dart';
import 'package:vcc/domain/entities/search_store_entity.dart';

abstract class OrderRepository {
  Future<BaseResult<List<SearchStoreEntity>>?> searchStore({
    String? keySearch,
    int? page,
    int? pageSize,
    String? provinceCode,
    String? districtCode,
  });

  Future<BaseResult<ArrayResponse<ProductInfoEntity>>?> getSuggestProduct({
    String? keySearch,
    int? page,
    int? pageSize,
  });

  Future<BaseResult<ArrayResponse<ProductInfoEntity>>?>
      getNoAuthSuggestProductStore({
    String? keySearch,
    int? page,
    int? pageSize,
    String? provinceCode,
  });

  Future<BaseResult<ArrayResponse<CategoryPackageEntity>>?>
      getServiceCategories(
    MenuType menuType, {
    String? tenantId,
  });

  Future<BaseResult<ArrayResponse<CategoryPackageEntity>>?>
      getNoAuthServiceCategories({
    required MenuType menuType,
    String? provinceCode,
  });

  Future<BaseResult<List<ServiceEntity>>?> getServiceByCategory({
    required String parentId,
  });

  Future<BaseResult<List<ServiceEntity>>?> getNoAuthServiceByCategory({
    required String parentId,
    String? provinceCode,
  });

  Future<BaseResult<ServiceEntity>?> getAllService({
    String? categoryId,
    String? type,
    int? page,
    int? pageSize,
    String? keyword,
  });

  Future<BaseResult<ServiceEntity>?> getNoAuthAllService({
    String? categoryId,
    String? type,
    int? page,
    int? pageSize,
    String? keyword,
    String? provinceCode,
  });

  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?> searchService({
    ServiceRequestEntity? request,
  });

  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?> searchNoAuthService({
    ServiceRequestEntity? request,
  });

  Future<BaseResult<ServiceCatEntity>?> getChildService({
    required String parentId,
  });

  Future<BaseResult<ServiceCatEntity>> getNoAuthChildService({
    required String parentId,
    String? provinceCode,
  });

  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?> searchServicePackage({
    required String keyword,
    String? provinceCode,
    int? page,
    int? pageSize,
  });

  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?>
      searchNoAuthServicePackage({
    required String keyword,
    int? page,
    int? pageSize,
    String? provinceCode,
  });

  Future<BaseResult<ServiceInfoEntity>?> getServiceDetail({
    required String code,
  });

  Future<BaseResult<OrderResponse>?> createServiceOrder({
    required OrderParam param,
  });

  Future<BaseResult<ArrayResponse<VoucherEntity>>?> getMyCoupon({
    required VoucherBody body,
  });

  Future<BaseResult<ServiceInfoEntity>?> getServicePackageInfo({
    String? code,
  });

  Future<BaseResult<PackageEntity>?> getDetailSuppliesComboInfo({
    String? comboCode,
  });

  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?>
      getServicePackageAddition({
    String? keyword,
    required String parentCode,
    String? serviceType,
    String? startTime,
    String? provinceCode,
    String? districtCode,
    String? wardCode,
    int? page,
    int? size,
    List<String>? sort,
  });

  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?> getSuppliesCombo({
    required String keyword,
    int? page,
    int? pageSize,
  });

  Future<BaseResult<ArrayResponse<CategoryEntity>>?> getProductCategories(
    MenuType menuType,
  );

  Future<BaseResult<ArrayResponse<CategoryEntity>>?>
      getNoAuthProductCategories({
    required MenuType menuType,
    String? provinceCode,
  });

  Future<BaseResult<ArrayResponse<ProductEntity>>?> getProductByCategory({
    required String parentId,
  });

  Future<BaseResult<ArrayResponse<ProductEntity>>?> getNoAuthProductByCategory({
    required String parentId,
    String? provinceCode,
  });

  Future<BaseResult<ProductEntity>?> getAllProduct({
    String? keyword,
    int? page,
    int? pageSize,
  });

  Future<BaseResult<ProductEntity>?> getNoAuthAllProduct({
    String? keyword,
    int? page,
    int? pageSize,
    String? provinceCode,
  });

  Future<BaseResult<ArrayResponse<InventoryAddressInfo>>?> getInventoryAddress({
    required String provinceCode,
    required String productCode,
    String? keySearch,
  });

  Future<BaseResult<ProductEntity>?> searchProductByCategory({
    required String categoryId,
    String? keyword,
    int? page,
    int? pageSize,
    int? minPrice,
    int? maxPrice,
  });

  Future<BaseResult<ProductEntity>?> searchNoAuthProductByCategory({
    required String categoryId,
    String? keyword,
    int? page,
    int? pageSize,
    int? minPrice,
    int? maxPrice,
    String? provinceCode,
  });

  Future<BaseResult<ProductInfoEntity>?> getProductDetail({
    required String code,
  });

  Future<BaseResult<ProductInfoEntity>?> getNoAuthProductDetail({
    required String code,
    String? provinceCode,
  });

  Future<BaseResult?> saveContactInfo({
    required ContactInfoParam param,
  });

  Future<BaseResult<OrderResponse>?> createProductOrder({
    required OrderParam param,
  });

  Future<BaseResult<ArrayResponse<ProductInfoEntity>>?> getProductByMenu({
    String? menuId,
    String? provinceCode,
    String? districtCode,
    String? wardCode,
    String? startTime,
    String? keyword,
    int? page,
    int? pageSize,
  });

  Future<BaseResult<ArrayResponse<OrderInfoShortEntity>>?> getShortOrders({
    ListOrderBody? body,
  });

  Future<BaseResult<PriceOrderEntity>?> getPriceOrder({
    required PriceOrderParam param,
  });

  Future<BaseResult<ArrayResponse<SupplyEntity>>?> searchSupply({
    String? keyword,
    int? page,
    int? pageSize,
    String? supplyTypes,
    String? wmsTypes,
    String? inventoryTypes,
    String? tenantId,
  });

  Future<BaseResult<ArrayResponse<SupplyEntity>>?> searchNoAuthSupply({
    String? keyword,
    int? page,
    int? pageSize,
    String? supplyTypes,
    String? wmsTypes,
    String? inventoryTypes,
    String? provinceCode,
  });

  Future<BaseResult<ArrayResponse<OriginalPriceSupplyEntity>>?>
      searchSupplyOrder({
    String? keyword,
    int? page,
    int? pageSize,
    String? goodsCode,
    String? listStockType,
    String? orderCode,
  });

  Future<BaseResult<PackageEntity>?> getPackageConfig({
    required String code,
    required String startTime,
    required String provinceCode,
    String? districtCode,
  });

  Future<BaseResult<ArrayResponse<OtherServiceEntity>>?> getOtherService({
    required String menuCode,
    required String serviceCode,
  });

  Future<BaseResult<OrderInfoEntity>?> getItemUpdated({
    required ItemsAmountBody body,
  });

  Future<BaseResult<ArrayResponse<BillEntity>>?> billSearch({
    String? keySearch,
    String? status,
    int? page,
    int? size,
  });

  Future<BaseResult<BillEntity>?> billReport({
    String? orderCode,
  });

  Future<BaseResult<BillDetailEntity>?> billDetail({
    String? orderCode,
  });

  Future<BaseResult<BillDetailEntity>?> billPreview({
    BillDetailEntity? data,
  });

  Future<BaseResult<DebtEntity>?> getDebts({
    String? queryType,
  });

  Future<BaseResult?> updateBill({
    BillDetailEntity? data,
  });

  Future<BaseResult<CheckDuplicateOrderEntity>?> checkExistOrder({
    required String customerId,
  });

  Future<BaseResult<List<StockEntity>>?> getListStock({
    required StockBody body,
  });

  Future<BaseResult<List<StockEntity>>?> getListStockRequestOrder({
    List<String>? listType,
    String? orderCode,
    List<String>? levelStock,
  });

  Future<BaseResult?> createRequestDistrictProduct({
    required RequestDistrictProductBody body,
  });

  Future<BaseResult?> exportDistrictProduct({
    required ExportDistrictProductBody body,
  });

  Future<BaseResult?> createRequestBranchProduct({
    required RequestBranchProductBody body,
  });

  Future<BaseResult?> exportBranchProduct({
    required RequestBranchProductBody body,
  });

  Future<BaseResult?> transferEmployee({
    required TransferEmployeeBody body,
  });

  Future<BaseResult?> transferDeploymentEmployee({
    required TransferEmployeeBody body,
  });

  Future<BaseResult?> confirmTransferDeploymentEmployee({
    required int stockTransId,
  });

  Future<BaseResult<ArrayResponse<SupplyEntity>>?> getListSupply({
    String? keyword,
    String? status,
    int? page,
    int? size,
    String? stockType,
    String? tenantId,
  });

  Future<BaseResult<ArrayResponse<SupplyEntity>>?> getListSupplyDistrict({
    String? keyword,
    String? status,
    int? page,
    int? size,
    String? stockType,
  });

  Future<BaseResult<List<SerialInfoEntity>>?> getListSerial({
    String? goodsCode,
    String? listStockType,
    String? keyword,
  });

  Future<BaseResult<SerialResponse>?> getSerialsOfOrder({
    int? stockTransDetailId,
  });

  Future<BaseResult<ArrayResponse<BillStockEntity>>?> getListConfirmationBill({
    String? keyword,
    String? businessType,
    String? status,
    String? listTypeStock,
    int? page,
    int? size,
  });

  Future<BaseResult<List<SupplyOfBillEntity>>?> getSupplyOfStockBill({
    int? id,
  });

  Future<BaseResult<ArrayResponse<BillStockEntity>>?> getListMyCreatedBill({
    String? keyword,
    String? businessType,
    String? status,
    String? listTypeStock,
    int? page,
    int? size,
  });

  Future<BaseResult<ArrayResponse<InventoryEntity>>?> getPersonalInventory({
    String? keyword,
    String? goodsCode,
    String? type,
    int? page,
    int? size,
  });

  Future<BaseResult?> updateBillStatus({
    required BillStockBody body,
  });

  Future<BaseResult<ArrayResponse<RequestProductEntity>>?>
      getListRequestProduct({
    String? keyword,
    int? page,
    int? size,
    String? type,
    String? status,
  });

  Future<BaseResult<ArrayResponse<SupplyRequestProductEntity>>?>
      getListSupplyRequestProduct({
    int? id,
    String? type,
  });

  Future<BaseResult<ArrayResponse<BranchTimeLineEntity>>?>
      getTimeLineBranchRequest({
    int? id,
  });

  Future<BaseResult<RequestTimeLineEntity>?> getTimeLineCompanyRequest({
    int? id,
  });

  Future<BaseResult?> createRequestBuyProduct({
    required RequestBuyProductBody body,
  });

  Future<BaseResult<OrderRequestAioResponse?>> saveOrderRequestAio({
    required RequestBuyProductBodyAio body,
  });

  Future<BaseResult<ArrayResponse<SupplyRequestProductEntity>>?>
      getListSupplyOrderRequest({
    String? keyword,
    int? page,
    int? size,
    String? type,
    List<int>? modemIds,
    List<int>? manufacturerIds,
    List<int>? goodsGroupIds,
  });

  Future<BaseResult<ArrayResponse<CodeEntity>>?> getProductsManufacturers({
    String? keyword,
    int? page,
    int? size,
  });

  Future<BaseResult<ArrayResponse<CodeEntity>>?> getProductModerns({
    String? keyword,
    int? page,
    int? size,
  });

  Future<BaseResult<ArrayResponse<CodeEntity>>?> getProductGroup({
    String? keyword,
    int? page,
    int? size,
  });

  Future<BaseResult<DetailOrderEntity>?> getDetailOrder({
    String? orderCode,
    bool? isGetOldOrder,
  });

  Future<BaseResult?> assignWorker({
    required AssignWorkerBody body,
  });

  Future<BaseResult<CountOrderEntity>?> getCountOrder({
    ListOrderBody? body,
  });

  Future<BaseResult<CompleteOrderResponse>?> getTotalCompleteOrders({
    List<String>? orderTypes,
    String? startTime,
  });

  Future<BaseResult<CustomerInformationEntity>?> getCustomerInformation({
    String? orderCode,
  });

  Future<BaseResult<ArrayResponse<GoodImpactHistory>>?> getGoodImpactHistory({
    required String orderCode,
  });

  Future<BaseResult<ListCommissionModel>?> getCommissionList({
    String? status,
    String? from,
    String? to,
    List<String>? orderType,
    String? keyword,
  });

  Future<BaseResult?> changeConfirmCommission({
    required ChangeCommissionBody body,
  });

  Future<BaseResult?> updateCommission({
    required UpdateCommissionBody body,
  });

  Future<BaseResult?> updateInfoCustomer({
    required UpdateInfoCustomerBody body,
  });

  Future<BaseResult?> updateOrder({
    required UpdateOrderBody body,
  });

  Future<BaseResult<ArrayResponse<JobTypeEntity>>?> getJobType({
    String? keyword,
  });

  Future<BaseResult<ArrayResponse<TelesalesEntity>>?> assignTelesales({
    String? userCode,
    String? orderCode,
  });

  Future<BaseResult<ArrayResponse<ReasonCancelEntity>>?> getReasonsCancel({
    String? parentId,
    String? reasonQueryType,
  });

  Future<BaseResult?> updateCall({
    required String orderCode,
  });

  Future<BaseResult<DuplicateOrderEntity>?> getOrderDuplicates({
    required String customerId,
  });

  Future<BaseResult?> requestCancel({
    required RequestCancelBody body,
  });

  Future<BaseResult?> ignoreRequestCancel({
    required String requestCancelOrderId,
  });

  Future<BaseResult?> scheduleCustomer({
    required String orderCode,
    required String scheduleTime,
    String? reason,
    String? reasonDetail,
    String? note,
    List<String>? imageUrls,
    String? reasonId,
  });

  Future<BaseResult?> cancelReceive({
    required String orderCode,
  });

  Future<BaseResult?> receiveOrder({
    required String orderCode,
  });

  Future<BaseResult?> ignoreOrder({
    required String orderCode,
  });

  Future<BaseResult?> extendKpiOrder({
    required ExtendKpiOrderBody body,
  });

  Future<BaseResult?> cancelExtendKpiOrder({
    required CancelExtendKpiOrderBody body,
  });

  Future<BaseResult<ArrayResponse<int>>?> getListPrice({
    String? keyword,
    int? page,
    int? pageSize,
  });

  Future<BaseResult?> calculateShippingCost({
    int? distance,
    required String orderCode,
    bool? hasWarranty,
  });

  Future<BaseResult<SignContractEntity>?> getFileContract({
    required String orderCode,
  });

  Future<BaseResult?> sentOtpUpdateOrder({
    required String orderCode,
  });

  Future<BaseResult?> verifyOtpUpdateOrder({
    required SendOTPBody body,
  });

  Future<BaseResult?> signAcceptance({
    required String orderCode,
    String? otp,
    String? customerSignature,
    String? staffSignature,
  });

  Future<BaseResult?> uploadReportOrder({
    required UploadReportOrderBody body,
  });

  Future<BaseResult?> signAcceptanceV2({
    required String orderCode,
    required List<String> images,
  });

  Future<BaseResult?> materialRequest({
    required String orderCode,
  });

  Future<BaseResult<List<String>>?> uploadOrderImage({
    required File file,
    required String orderCode,
  });

  Future<BaseResult?> deleteOrderImage({
    required String imgUrl,
    required String orderCode,
  });

  Future<BaseResult<ReasonScheduleDefaultEntity>?> getReasonsSchedule({
    String? parentId,
    String? orderCode,
    String? scheduleTime,
  });

  Future<BaseResult<ArrayResponse<ReasonCancelEntity>>?>
      getCancelReasonsSchedule({
    String? parentId,
  });

  Future<BaseResult?> cancelSchedule({
    required String orderCode,
  });

  Future<BaseResult?> editServiceOrder({
    required ServiceOrderBody body,
    bool isShowError = true,
  });

  Future<BaseResult<BillInfoEntity>?> getBillCustomerInfo({
    required String orderCode,
  });

  Future<BaseResult?> updateBillCustomerInfo({
    BillInfoParam? body,
  });

  Future<BaseResult?> cancelBillOrder({
    String? orderCode,
  });

  Future<BaseResult<DetailBillStockEntity>?> getDetailStockBill({
    int? stockTransId,
  });

  Future<BaseResult<DetailBillStockEntity>?> validateCancelReceive({
    String? orderCode,
  });

  Future<BaseResult<List<ChecklistResponse>>?> getCheckList({
    String? orderCode,
    String? serviceCode,
  });

  Future<BaseResult?> updateCheckList({
    required ChecklistBody body,
  });

  Future<BaseResult?> updateInstallationOrderProduct({
    required UpdateInstallationOrderProductBody body,
  });

  Future<BaseResult<CountEntity>?> countSchedule({
    required String orderCode,
  });

  Future<BaseResult<CountEntity>?> countRequestCancelOrder({
    required String orderCode,
  });

  Future<BaseResult<CountEntity>?> countRequestExtendKpiOrder({
    required String orderCode,
  });

  Future<BaseResult<CountEntity>?> countWorkerAssignOrder({
    required String orderCode,
  });

  Future<BaseResult?> completeOrder({
    required String orderCode,
  });

  Future<BaseResult<WarningOrderResponse>?> getWarningOrder({
    String? expiredAlertTypes,
  });

  Future<BaseResult<WarningAlertEntity>?> checkShowWarningOrder();

  Future<BaseResult<ArrayResponse<DetailOrderItemEntity>>?>
      getServiceByproduct({
    String? productCode,
  });

  Future<BaseResult?> validateMultiPayment({
    required OrderParam body,
  });

  Future<BaseResult?> validateOrder({
    required String orderCode,
    bool isShowError = true,
  });

  Future<BaseResult<ArrayResponse<String>>?> getOrderType({
    String? orderTypes,
    String? customerTypes,
  });

  Future<BaseResult<List<ScheduleWorkerEntity>>> checkScheduleWorker({
    required ScheduleWorkerBody body,
  });

  Future<BaseResult<ServiceInfoEntity>?> getServiceInfo({
    required String code,
  });

  Future<BaseResult<ScheduleWorkerEntity>?> checkHotOrder({
    required String orderType,
    required String wardCode,
    required String startTime,
    String? packageCode,
  });

  Future<BaseResult<WarningAlertEntity>?> showWarningMobileCall({
    required String orderCode,
  });

  Future<BaseResult<CommissionConfirmResponse>?> checkCommissionConfirm({
    required String customerPhone,
  });

  Future<BaseResult<SignContractEntity>?> getRequirementWarrantyFileSign({
    required SignItemWarrantyBody signItemWarrantyBody,
  });

  Future<BaseResult<SignContractEntity>?> getDetailRequirementWarrantyFileSign({
    required SignItemWarrantyBody signItemWarrantyBody,
  });

  Future<BaseResult?> buySuppliesForCustomer({
    required BuySuppliesForCustomerBody body,
    required String orderCode,
  });

  Future<BaseResult<StatisticOrderEntity>?> getDataStatistic({
    String? dateTime,
  });

  Future<BaseResult<WorkCountEntity>?> getWorkCount({
    String? startTime,
    String? endTime,
  });

  Future<BaseResult<ArrayResponse<WorkDataEntity>>?> getWorkData({
    String? startTime,
    String? endTime,
    String? dataTypeEnum,
  });

  Future<BaseResult<WorkScheduleEventEntity>?> getEventDay({
    String? date,
  });
}

class OrderRepositoryImpl extends BaseRepository implements OrderRepository {
  OrderRepositoryImpl({
    required ApiClient apiClient,
  }) : _apiClient = apiClient;

  final ApiClient _apiClient;

  @override
  Future<BaseResult<List<SearchStoreEntity>>?> searchStore({
    String? keySearch,
    int? page,
    int? pageSize,
    String? provinceCode,
    String? districtCode,
  }) async {
    return await safeApiCall<List<SearchStoreEntity>>(
      _apiClient.searchStore(
        keySearch: keySearch,
        page: page ?? 0,
        pageSize: pageSize ?? 5,
        provinceCode: provinceCode,
        districtCode: districtCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ProductInfoEntity>>?> getSuggestProduct({
    String? keySearch,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<ArrayResponse<ProductInfoEntity>>(
      _apiClient.getSuggestProductStore(
        keySearch: keySearch,
        page: page ?? BaseConstant.defaultPage,
        pageSize: pageSize ?? 5,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ProductInfoEntity>>?>
      getNoAuthSuggestProductStore({
    String? keySearch,
    int? page,
    int? pageSize,
    String? provinceCode,
  }) async {
    return await safeApiCall<ArrayResponse<ProductInfoEntity>>(
      _apiClient.getNoAuthSuggestProductStore(
        keySearch: keySearch,
        page: page ?? BaseConstant.defaultPage,
        pageSize: pageSize ?? 5,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CategoryPackageEntity>>> getServiceCategories(
    MenuType menuType, {
    String? tenantId,
  }) async {
    return await safeApiCall<ArrayResponse<CategoryPackageEntity>>(
      isShowError: false,
      _apiClient.getServiceCategories(
        type: menuType.keyToServer ?? '',
        tenantId: tenantId,
      ),
    );
  }

  @override
  Future<BaseResult<ServiceEntity>?> getAllService({
    String? categoryId,
    String? type,
    int? page,
    int? pageSize,
    String? keyword,
  }) async {
    return await safeApiCall<ServiceEntity>(
      _apiClient.getAllService(
        type: type ?? "SERVICE",
        categoryId: categoryId,
        page: page ?? 0,
        pageSize: pageSize ?? 20,
        keyword: keyword,
      ),
    );
  }

  @override
  Future<BaseResult<ServiceEntity>?> getNoAuthAllService({
    String? categoryId,
    String? type,
    int? page,
    int? pageSize,
    String? keyword,
    String? provinceCode,
  }) async {
    return await safeApiCall<ServiceEntity>(
      _apiClient.getNoAuthAllService(
        type: type ?? "SERVICE",
        categoryId: categoryId,
        page: page ?? 0,
        pageSize: pageSize ?? 20,
        keyword: keyword,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ServiceCatEntity>> getChildService({
    required String parentId,
  }) async {
    return await safeApiCall<ServiceCatEntity>(
      _apiClient.getChildService(parentId),
    );
  }

  @override
  Future<BaseResult<ServiceCatEntity>> getNoAuthChildService({
    required String parentId,
    String? provinceCode,
  }) async {
    return await safeApiCall<ServiceCatEntity>(
      _apiClient.getNoAuthChildService(
        parentId: parentId,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?> searchService({
    ServiceRequestEntity? request,
  }) async {
    final result = await safeApiCall<ArrayResponse<ServiceInfoEntity>>(
      _apiClient.searchService(
        keyword: request?.keySearch,
        categoryId: request?.categoryId,
        menuId: request?.menuId,
        serviceType: request?.serviceType,
        startTime: request?.startTime,
        provinceCode: request?.provinceCode,
        districtCode: request?.districtCode,
        tenantId: request?.tenantId,
        page: 0,
        size: 20,
      ),
    );

    return result;
  }

  @override
  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?> searchNoAuthService({
    ServiceRequestEntity? request,
  }) async {
    final result = await safeApiCall<ArrayResponse<ServiceInfoEntity>>(
      _apiClient.searchNoAuthService(
        keyword: request?.keySearch,
        categoryId: request?.categoryId,
        menuId: request?.menuId,
        serviceType: request?.serviceType,
        startTime: request?.startTime,
        provinceCode: request?.provinceCode,
        districtCode: request?.districtCode,
        page: 0,
        size: 20,
      ),
    );

    return result;
  }

  @override
  Future<BaseResult<List<ServiceEntity>>> getServiceByCategory({
    required String parentId,
  }) async {
    return await safeApiCall<List<ServiceEntity>>(
      isShowError: false,
      _apiClient.getServiceByCategory(parentId: parentId),
    );
  }

  @override
  Future<BaseResult<ServiceInfoEntity>> getServiceDetail({
    required String code,
  }) async {
    return await safeApiCall<ServiceInfoEntity>(
      _apiClient.getServiceDetail(code: code),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?> searchServicePackage({
    required String keyword,
    int? page,
    int? pageSize,
    String? provinceCode,
  }) async {
    return await safeApiCall<ArrayResponse<ServiceInfoEntity>>(
      _apiClient.searchServicePackage(
        keyword: keyword,
        provinceCode: provinceCode,
        page: page ?? BaseConstant.defaultPage,
        pageSize: pageSize ?? BaseConstant.defaultLimitSize,
      ),
    );
  }

  @override
  Future<BaseResult<OrderResponse>?> createServiceOrder({
    required OrderParam param,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.createOrder(
        param: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<VoucherEntity>>?> getMyCoupon({
    required VoucherBody body,
  }) async {
    return await safeApiCall(
      _apiClient.getMyCoupon(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<ServiceInfoEntity>?> getServicePackageInfo({
    String? code,
  }) async {
    return await safeApiCall(
      _apiClient.getServicePackageInfo(
        code: code ?? '',
      ),
    );
  }

  @override
  Future<BaseResult<PackageEntity>?> getDetailSuppliesComboInfo({
    String? comboCode,
  }) async {
    return await safeApiCall(
      _apiClient.getDetailSuppliesComboInfo(
        comboCode: comboCode ?? '',
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?>
      getServicePackageAddition({
    String? keyword,
    required String parentCode,
    String? serviceType,
    String? startTime,
    String? provinceCode,
    String? districtCode,
    String? wardCode,
    int? page,
    int? size,
    List<String>? sort,
  }) async {
    return await safeApiCall<ArrayResponse<ServiceInfoEntity>>(
      _apiClient.getServicePackageAddition(
        keyword: keyword,
        parentCode: parentCode,
        serviceType: serviceType,
        startTime: startTime,
        provinceCode: provinceCode,
        districtCode: districtCode,
        wardCode: wardCode,
        page: page,
        size: size,
        sort: sort,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CategoryPackageEntity>>?>
      getNoAuthServiceCategories({
    required MenuType menuType,
    String? provinceCode,
  }) async {
    return await safeApiCall<ArrayResponse<CategoryPackageEntity>>(
      isShowError: false,
      _apiClient.getNoAuthServiceCategories(
        type: menuType.keyToServer ?? '',
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?>
      searchNoAuthServicePackage({
    required String keyword,
    int? page,
    int? pageSize,
    String? provinceCode,
  }) async {
    return await safeApiCall<ArrayResponse<ServiceInfoEntity>>(
      _apiClient.searchNoAuthServicePackage(
        keyword: keyword,
        page: page ?? BaseConstant.defaultPage,
        pageSize: pageSize ?? BaseConstant.defaultLimitSize,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<List<ServiceEntity>>?> getNoAuthServiceByCategory({
    required String parentId,
    String? provinceCode,
  }) async {
    return await safeApiCall<List<ServiceEntity>>(
      isShowError: false,
      _apiClient.getNoAuthServiceByCategory(
        parentId: parentId,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ServiceInfoEntity>>?> getSuppliesCombo({
    required String keyword,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<ArrayResponse<ServiceInfoEntity>>(
      _apiClient.getSuppliesCombo(
        keyword: keyword,
        page: page ?? BaseConstant.defaultPage,
        pageSize: pageSize ?? BaseConstant.defaultLimitSize,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CategoryEntity>>?> getProductCategories(
    MenuType menuType,
  ) async {
    return await safeApiCall<ArrayResponse<CategoryEntity>>(
      isShowError: false,
      _apiClient.getProductCategories(
        type: menuType.keyToServer ?? '',
      ),
    );
  }

  @override
  Future<BaseResult<ProductEntity>?> getAllProduct({
    String? keyword,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<ProductEntity>(
      _apiClient.getAllProduct(
        keyword: keyword,
        page: page ?? BaseConstant.defaultPage,
        pageSize: pageSize ?? BaseConstant.defaultPage,
      ),
    );
  }

  @override
  Future<BaseResult<ProductEntity>?> getNoAuthAllProduct({
    String? keyword,
    int? page,
    int? pageSize,
    String? provinceCode,
  }) async {
    return await safeApiCall<ProductEntity>(
      _apiClient.getNoAuthAllProduct(
        keyword: keyword,
        page: page ?? BaseConstant.defaultPage,
        pageSize: pageSize ?? BaseConstant.defaultPage,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ProductEntity>>?> getProductByCategory({
    required String parentId,
  }) async {
    return await safeApiCall<ArrayResponse<ProductEntity>>(
      isShowError: false,
      _apiClient.getProductByCategory(parentId),
    );
  }

  @override
  Future<BaseResult<ProductInfoEntity>?> getProductDetail({
    required String code,
  }) async {
    return await safeApiCall<ProductInfoEntity>(
      _apiClient.getProductDetail(code),
    );
  }

  @override
  Future<BaseResult<ProductInfoEntity>?> getNoAuthProductDetail({
    required String code,
    String? provinceCode,
  }) async {
    return await safeApiCall<ProductInfoEntity>(
      _apiClient.getNoAuthProductDetail(
        code: code,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult?> saveContactInfo({
    required ContactInfoParam param,
  }) async {
    return await safeApiCall(
      _apiClient.saveContactInfo(param),
    );
  }

  @override
  Future<BaseResult<ProductEntity>?> searchProductByCategory({
    required String categoryId,
    String? keyword,
    int? page,
    int? pageSize,
    int? minPrice,
    int? maxPrice,
    bool? isAgentSelect,
  }) async {
    return await safeApiCall<ProductEntity>(
      _apiClient.searchProductByCategory(
          categoryId: categoryId,
          keyword: keyword,
          page: page,
          pageSize: pageSize,
          minPrice: minPrice,
          maxPrice: maxPrice,
          isAgentSelect: isAgentSelect),
    );
  }

  @override
  Future<BaseResult<ProductEntity>?> searchNoAuthProductByCategory({
    required String categoryId,
    String? keyword,
    int? page,
    int? pageSize,
    int? minPrice,
    int? maxPrice,
    String? provinceCode,
  }) async {
    return await safeApiCall<ProductEntity>(
      _apiClient.searchNoAuthProductByCategory(
        categoryId: categoryId,
        keyword: keyword,
        page: page,
        pageSize: pageSize,
        minPrice: minPrice,
        maxPrice: maxPrice,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<OrderResponse>?> createProductOrder({
    required OrderParam param,
  }) async {
    return await safeApiCall(
      _apiClient.createOrderProduct(
        param: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InventoryAddressInfo>>?> getInventoryAddress({
    required String provinceCode,
    required String productCode,
    String? keySearch,
  }) async {
    return await safeApiCall(
      _apiClient.getInventoryAddress(
        provinceCode: provinceCode,
        productCode: productCode,
        keySearch: keySearch,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ProductInfoEntity>>?> getProductByMenu({
    String? menuId,
    String? provinceCode,
    String? districtCode,
    String? wardCode,
    String? startTime,
    String? keyword,
    int? page,
    int? pageSize,
  }) {
    return safeApiCall(
      _apiClient.getProductByMenu(
        menuId: menuId,
        keyword: keyword,
        page: page,
        size: pageSize,
        provinceCode: provinceCode,
        districtCode: districtCode,
        wardCode: wardCode,
        startTime: startTime,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CategoryEntity>>?>
      getNoAuthProductCategories({
    required MenuType menuType,
    String? provinceCode,
  }) async {
    return await safeApiCall<ArrayResponse<CategoryEntity>>(
      isShowError: false,
      _apiClient.getNoAuthProductCategories(
        type: menuType.keyToServer ?? '',
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ProductEntity>>?> getNoAuthProductByCategory({
    required String parentId,
    String? provinceCode,
  }) async {
    return await safeApiCall<ArrayResponse<ProductEntity>>(
      _apiClient.getNoAuthProductByCategory(
        parentId: parentId,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<OrderInfoShortEntity>>?> getShortOrders({
    ListOrderBody? body,
  }) async {
    return await safeApiCall<ArrayResponse<OrderInfoShortEntity>>(
      _apiClient.getShortOrders(
        status: body?.status,
        page: body?.page,
        pageSize: body?.pageSize,
        keyword: body?.keyword,
        startTime: body?.startTime,
        endTime: body?.endTime,
        participantTypes: body?.participantTypes,
        orderTypes: body?.ordersType,
        provinceCode: body?.province?.code,
        specialQueryType: body?.specialQueryType,
        districtCode: body?.districts?.map((e) => e.code!).toList(),
      ),
    );
  }

  @override
  Future<BaseResult<PriceOrderEntity>?> getPriceOrder({
    required PriceOrderParam param,
  }) async {
    return await safeApiCall<PriceOrderEntity>(
      isShowError: false,
      _apiClient.getPriceOrder(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<SupplyEntity>>?> searchSupply({
    String? keyword,
    int? page,
    int? pageSize,
    String? supplyTypes,
    String? wmsTypes,
    String? inventoryTypes,
    String? tenantId,
  }) async {
    return await safeApiCall<ArrayResponse<SupplyEntity>>(
      _apiClient.searchSupply(
        keyword: keyword,
        page: page,
        pageSize: pageSize,
        supplyTypes: supplyTypes,
        wmsTypes: wmsTypes,
        inventoryTypes: inventoryTypes,
        tenantId: tenantId,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<SupplyEntity>>?> searchNoAuthSupply({
    String? keyword,
    int? page,
    int? pageSize,
    String? supplyTypes,
    String? wmsTypes,
    String? inventoryTypes,
    String? provinceCode,
  }) async {
    return await safeApiCall<ArrayResponse<SupplyEntity>>(
      _apiClient.searchNoAuthSupply(
        keyword: keyword,
        page: page,
        pageSize: pageSize,
        supplyTypes: supplyTypes,
        wmsTypes: wmsTypes,
        inventoryTypes: inventoryTypes,
        provinceCode: provinceCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<OriginalPriceSupplyEntity>>?>
      searchSupplyOrder({
    String? keyword,
    int? page,
    int? pageSize,
    String? goodsCode,
    String? listStockType,
    String? orderCode,
  }) async {
    return await safeApiCall<ArrayResponse<OriginalPriceSupplyEntity>>(
      _apiClient.searchSupplyOrder(
        keyword: keyword,
        page: page,
        pageSize: pageSize,
        goodsCode: goodsCode,
        listStockType: listStockType,
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<PackageEntity>?> getPackageConfig({
    required String code,
    required String startTime,
    required String provinceCode,
    String? districtCode,
  }) async {
    return await safeApiCall<PackageEntity>(
      _apiClient.getPackageDetail(
        code: code,
        startTime: startTime,
        provinceCode: provinceCode,
        districtCode: districtCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<OtherServiceEntity>>?> getOtherService({
    required String menuCode,
    required String serviceCode,
  }) async {
    return await safeApiCall<ArrayResponse<OtherServiceEntity>>(
      _apiClient.getOtherService(
        menuCode: menuCode,
        serviceCode: serviceCode,
      ),
    );
  }

  @override
  Future<BaseResult<OrderInfoEntity>?> getItemUpdated({
    required ItemsAmountBody body,
  }) async {
    return await safeApiCall<OrderInfoEntity>(
      isShowError: false,
      _apiClient.getItemUpdated(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<BillEntity>>?> billSearch({
    String? keySearch,
    String? status,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<BillEntity>>(
      isShowError: false,
      _apiClient.billSearch(
        keyword: keySearch,
        status: status,
        page: page ?? 0,
        size: size ?? 10,
      ),
    );
  }

  @override
  Future<BaseResult<BillEntity>?> billReport({
    String? orderCode,
  }) async {
    return await safeApiCall<BillEntity>(
      _apiClient.billReport(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<BillDetailEntity>?> billDetail({
    String? orderCode,
  }) async {
    return await safeApiCall<BillDetailEntity>(
      _apiClient.billDetail(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<BillDetailEntity>?> billPreview({
    BillDetailEntity? data,
  }) async {
    return await safeApiCall<BillDetailEntity>(
      _apiClient.billPreview(
        data: data,
      ),
    );
  }

  @override
  Future<BaseResult<DebtEntity>?> getDebts({
    String? queryType,
  }) async {
    return await safeApiCall<DebtEntity>(
      _apiClient.getDebts(
        queryType: queryType,
      ),
    );
  }

  @override
  Future<BaseResult<CheckDuplicateOrderEntity>?> checkExistOrder({
    required String customerId,
  }) async {
    return await safeApiCall<CheckDuplicateOrderEntity>(
      isShowError: false,
      _apiClient.checkExistOrder(
        customerId: customerId,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<BillStockEntity>>?> getListConfirmationBill({
    String? keyword,
    String? businessType,
    String? status,
    String? listTypeStock,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<BillStockEntity>>(
      _apiClient.getListConfirmationBill(
        keyword: keyword,
        businessType: businessType,
        status: status,
        listTypeStock: listTypeStock,
        page: page,
        size: size,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<BillStockEntity>>?> getListMyCreatedBill({
    String? keyword,
    String? businessType,
    String? status,
    String? listTypeStock,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<BillStockEntity>>(
      _apiClient.getListMyCreatedBill(
        keyword: keyword,
        businessType: businessType,
        status: status,
        listTypeStock: listTypeStock,
        page: page,
        size: size,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InventoryEntity>>?> getPersonalInventory({
    String? keyword,
    String? goodsCode,
    String? type,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<InventoryEntity>>(
      _apiClient.getPersonalInventory(
        keyword: keyword,
        goodsCode: goodsCode,
        type: type,
        page: page,
        size: size,
      ),
    );
  }

  @override
  Future<BaseResult<List<SerialInfoEntity>>?> getListSerial({
    String? goodsCode,
    String? listStockType,
    String? keyword,
  }) async {
    return await safeApiCall(
      _apiClient.getListSerial(
        goodsCode: goodsCode,
        listStockType: listStockType,
        keyword: keyword,
      ),
    );
  }

  @override
  Future<BaseResult<SerialResponse>?> getSerialsOfOrder({
    int? stockTransDetailId,
  }) async {
    return await safeApiCall<SerialResponse>(
      _apiClient.getSerialsOfOrder(
        stockTransDetailId: stockTransDetailId,
      ),
    );
  }

  @override
  Future<BaseResult<List<StockEntity>>?> getListStock({
    required StockBody body,
  }) async {
    return await safeApiCall<List<StockEntity>>(
      _apiClient.getListStock(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<List<StockEntity>>?> getListStockRequestOrder({
    List<String>? listType,
    String? orderCode,
    List<String>? levelStock,
  }) async {
    return await safeApiCall<List<StockEntity>>(
      _apiClient.getListStockRequestOrder(
        listType: listType,
        orderCode: orderCode,
        levelStock: levelStock,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<SupplyEntity>>?> getListSupply({
    String? keyword,
    String? status,
    int? page,
    int? size,
    String? stockType,
    String? tenantId,
  }) async {
    return await safeApiCall<ArrayResponse<SupplyEntity>>(
      _apiClient.getListSupply(
        keyword: keyword,
        status: status,
        page: page,
        size: size,
        stockType: stockType,
        tenantId: tenantId,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<SupplyEntity>>?> getListSupplyDistrict({
    String? keyword,
    String? status,
    int? page,
    int? size,
    String? stockType,
  }) async {
    return await safeApiCall<ArrayResponse<SupplyEntity>>(
      _apiClient.getListSupplyDistrict(
        keyword: keyword,
        status: status,
        page: page,
        size: size,
        stockType: stockType,
      ),
    );
  }

  @override
  Future<BaseResult<List<SupplyOfBillEntity>>?> getSupplyOfStockBill({
    int? id,
  }) async {
    return await safeApiCall<List<SupplyOfBillEntity>>(
      _apiClient.getSupplyOfStockBill(
        id: id,
      ),
    );
  }

  @override
  Future<BaseResult?> updateBill({
    BillDetailEntity? data,
  }) async {
    return await safeApiCall(
      _apiClient.updateBill(
        data: data,
      ),
    );
  }

  @override
  Future<BaseResult?> createRequestBuyProduct({
    required RequestBuyProductBody body,
  }) async {
    return await safeApiCall(
      _apiClient.createRequestBuyProduct(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<OrderRequestAioResponse?>> saveOrderRequestAio({
    required RequestBuyProductBodyAio body,
  }) async {
    return await safeApiCall(
      _apiClient.saveOrderRequestAio(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<RequestProductEntity>>?>
      getListRequestProduct({
    String? keyword,
    int? page,
    int? size,
    String? type,
    String? status,
  }) async {
    return await safeApiCall<ArrayResponse<RequestProductEntity>>(
      _apiClient.getListRequestProduct(
        keyword: keyword,
        page: page,
        size: size,
        type: type,
        status: status,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<SupplyRequestProductEntity>>?>
      getListSupplyRequestProduct({
    int? id,
    String? type,
  }) async {
    return await safeApiCall<ArrayResponse<SupplyRequestProductEntity>>(
      _apiClient.getListSupplyRequestProduct(
        id: id,
        type: type,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<BranchTimeLineEntity>>?>
      getTimeLineBranchRequest({
    int? id,
  }) async {
    return await safeApiCall<ArrayResponse<BranchTimeLineEntity>>(
      _apiClient.getTimeLineBranchRequest(
        id: id,
      ),
    );
  }

  @override
  Future<BaseResult<RequestTimeLineEntity>?> getTimeLineCompanyRequest({
    int? id,
  }) async {
    return await safeApiCall<RequestTimeLineEntity>(
      _apiClient.getTimeLineCompanyRequest(
        id: id,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<SupplyRequestProductEntity>>?>
      getListSupplyOrderRequest({
    String? keyword,
    int? page,
    int? size,
    String? type,
    List<int>? modemIds,
    List<int>? manufacturerIds,
    List<int>? goodsGroupIds,
  }) async {
    return await safeApiCall<ArrayResponse<SupplyRequestProductEntity>>(
      _apiClient.getListSupplyOrderRequest(
        keyword: keyword,
        page: page,
        size: size,
        type: type,
        modemIds: modemIds,
        manufacturerIds: manufacturerIds,
        goodsGroupIds: goodsGroupIds,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CodeEntity>>?> getProductModerns({
    String? keyword,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<CodeEntity>>(
      _apiClient.getProductModerns(
        keyword: keyword,
        page: page,
        size: size,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CodeEntity>>?> getProductGroup({
    String? keyword,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<CodeEntity>>(
      _apiClient.getProductGroup(
        keyword: keyword,
        page: page,
        size: size,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CodeEntity>>?> getProductsManufacturers({
    String? keyword,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<CodeEntity>>(
      _apiClient.getProductsManufacturers(
        keyword: keyword,
        page: page,
        size: size,
      ),
    );
  }

  @override
  Future<BaseResult?> updateBillStatus({
    required BillStockBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateBillStatus(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> exportBranchProduct({
    required RequestBranchProductBody body,
  }) async {
    return await safeApiCall(
      _apiClient.exportBranchProduct(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> exportDistrictProduct({
    required ExportDistrictProductBody body,
  }) async {
    return await safeApiCall(
      _apiClient.exportDistrictProduct(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> createRequestBranchProduct({
    required RequestBranchProductBody body,
  }) async {
    return await safeApiCall(
      _apiClient.createRequestBranchProduct(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> createRequestDistrictProduct({
    required RequestDistrictProductBody body,
  }) async {
    return await safeApiCall(
      _apiClient.createRequestDistrictProduct(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> transferEmployee({
    required TransferEmployeeBody body,
  }) async {
    return await safeApiCall(
      _apiClient.transferEmployee(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> transferDeploymentEmployee({
    required TransferEmployeeBody body,
  }) async {
    return await safeApiCall(
      _apiClient.transferDeploymentEmployee(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<DetailOrderEntity>?> getDetailOrder({
    String? orderCode,
    bool? isGetOldOrder,
  }) async {
    return await safeApiCall(
      _apiClient.getOrderDetail(
        orderCode: orderCode,
        isGetOldOrder: isGetOldOrder,
      ),
    );
  }

  @override
  Future<BaseResult?> assignWorker({
    required AssignWorkerBody body,
  }) async {
    return await safeApiCall(
      _apiClient.assignWorker(
        orderCode: body.orderCode ?? "",
        userCode: body.userCode ?? "",
        type: body.type ?? "",
      ),
    );
  }

  @override
  Future<BaseResult<CountOrderEntity>?> getCountOrder({
    ListOrderBody? body,
  }) async {
    return await safeApiCall(
      _apiClient.getCountOrders(
        keyword: body?.keyword,
        startTime: body?.startTime,
        endTime: body?.endTime,
        participantTypes: body?.participantTypes,
        orderTypes: body?.ordersType,
        provinceCode: body?.province?.code,
        districtCode: body?.districts?.map((e) => e.code!).toList(),
      ),
    );
  }

  @override
  Future<BaseResult<CompleteOrderResponse>?> getTotalCompleteOrders({
    List<String>? orderTypes,
    String? startTime,
  }) async {
    return await safeApiCall(
      _apiClient.getTotalCompleteOrders(
        orderTypes: orderTypes,
        startTime: startTime,
      ),
    );
  }

  @override
  Future<BaseResult<CustomerInformationEntity>?> getCustomerInformation({
    String? orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.getDetailInfoCustomer(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<GoodImpactHistory>>?> getGoodImpactHistory({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.getGoodImpactHistory(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<ListCommissionModel>?> getCommissionList({
    String? status,
    String? from,
    String? to,
    List<String>? orderType,
    String? keyword,
  }) async {
    return await safeApiCall(
      _apiClient.getCommissionList(
        status: status,
        from: from,
        to: to,
        orderType: orderType,
        keyword: keyword,
      ),
    );
  }

  @override
  Future<BaseResult?> changeConfirmCommission({
    required ChangeCommissionBody body,
  }) async {
    return await safeApiCall(
      _apiClient.changeConfirmCommission(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> updateCommission({
    required UpdateCommissionBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateCommission(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> updateInfoCustomer({
    required UpdateInfoCustomerBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateInfoCustomer(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> updateOrder({
    required UpdateOrderBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateOrder(orderCode: body.orderCode),
    );
  }

  @override
  Future<BaseResult?> confirmTransferDeploymentEmployee({
    required int stockTransId,
  }) async {
    return await safeApiCall(
      _apiClient.confirmTransferDeploymentEmployee(
        stockTransId: stockTransId,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<JobTypeEntity>>?> getJobType({
    String? keyword,
  }) async {
    return await safeApiCall(
      _apiClient.getJobType(
        keyword: keyword,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<TelesalesEntity>>?> assignTelesales({
    String? userCode,
    String? orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.assignTelesales(
        userCode: userCode,
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ReasonCancelEntity>>?> getReasonsCancel({
    String? parentId,
    String? reasonQueryType,
  }) async {
    return await safeApiCall(
      _apiClient.getReasonsCancel(
        parentId: parentId,
        queryType: reasonQueryType,
      ),
    );
  }

  @override
  Future<BaseResult?> updateCall({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.updateCall(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<DuplicateOrderEntity>?> getOrderDuplicates(
      {required String customerId}) async {
    return await safeApiCall<DuplicateOrderEntity>(
      _apiClient.getOrderDuplicates(
        customerId: customerId,
      ),
    );
  }

  @override
  Future<BaseResult?> requestCancel({
    required RequestCancelBody body,
  }) async {
    return await safeApiCall(
      _apiClient.requestCancel(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> ignoreRequestCancel({
    required String requestCancelOrderId,
  }) async {
    return await safeApiCall(
      _apiClient.ignoreRequestCancel(
        requestCancelOrderId: requestCancelOrderId,
      ),
    );
  }

  @override
  Future<BaseResult?> scheduleCustomer({
    required String orderCode,
    required String scheduleTime,
    String? reason,
    String? reasonDetail,
    String? note,
    List<String>? imageUrls,
    String? reasonId,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.scheduleCustomer(
        body: RequestScheduleBody(
          orderCode: orderCode,
          scheduleTime: scheduleTime,
          reason: reason,
          reasonDetail: reasonDetail,
          note: note,
          imageUrls: imageUrls,
          reasonId: reasonId,
        ),
      ),
    );
  }

  @override
  Future<BaseResult?> cancelReceive({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.cancelReceive(
        body: RequestCancelBody(
          orderCode: orderCode,
        ),
      ),
    );
  }

  @override
  Future<BaseResult?> receiveOrder({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.receiveOrder(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult?> ignoreOrder({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.ignoreOrder(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult?> extendKpiOrder({
    required ExtendKpiOrderBody body,
  }) async {
    return await safeApiCall(
      _apiClient.extendKpiOrder(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> cancelExtendKpiOrder({
    required CancelExtendKpiOrderBody body,
  }) async {
    return await safeApiCall(
      _apiClient.cancelExtendKpiOrder(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<int>>?> getListPrice({
    String? keyword,
    int? page,
    int? pageSize,
  }) async {
    // return await safeApiCall(
    //   _apiClient.getListPrice(
    //     keyword: keyword,
    //     page: page,
    //     pageSize: pageSize,
    //   ),
    // );
    throw ();
  }

  @override
  Future<BaseResult?> calculateShippingCost({
    int? distance,
    required String orderCode,
    bool? hasWarranty,
  }) async {
    return await safeApiCall(
      _apiClient.calculateShippingCost(
        body: DistanceCostBody(
          distance: distance,
          orderCode: orderCode,
          hasWarranty: hasWarranty,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<SignContractEntity>?> getFileContract({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.getFileContract(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult?> sentOtpUpdateOrder({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.sentOtpUpdateOrder(
        body: SendOTPBody(
          orderCode: orderCode,
        ),
      ),
    );
  }

  @override
  Future<BaseResult?> verifyOtpUpdateOrder({
    required SendOTPBody body,
  }) async {
    return await safeApiCall(
      _apiClient.verifyOtpUpdateOrder(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> signAcceptance({
    required String orderCode,
    String? otp,
    String? customerSignature,
    String? staffSignature,
  }) async {
    return await safeApiCall(
      _apiClient.signAcceptance(
        body: SignAcceptanceBody(
          orderCode: orderCode,
          otp: otp,
          customerSignature: customerSignature,
          staffSignature: staffSignature,
        ),
      ),
    );
  }

  @override
  Future<BaseResult?> signAcceptanceV2({
    required String orderCode,
    required List<String> images,
  }) async {
    return await safeApiCall(
      _apiClient.signAcceptanceV2(
        body: SignAcceptanceBody(orderCode: orderCode, images: images),
      ),
    );
  }

  @override
  Future<BaseResult?> materialRequest({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.materialRequest(
        body: SignAcceptanceBody(
          orderCode: orderCode,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<List<String>>?> uploadOrderImage({
    required File file,
    required String orderCode,
  }) async {
    FormData formData = FormData.fromMap(
      {
        "files": [
          await MultipartFile.fromFile(
            file.path,
          ),
        ],
        "orderCode": orderCode,
      },
    );

    return await safeApiCall(
      _apiClient.uploadOrderImage(
        body: formData,
      ),
    );
  }

  @override
  Future<BaseResult?> deleteOrderImage({
    required String imgUrl,
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.deleteOrderImage(
        body: ImageOrderBody(
          orderCode: orderCode,
          imageUrls: [imgUrl],
        ),
      ),
    );
  }

  @override
  Future<BaseResult<ReasonScheduleDefaultEntity>?> getReasonsSchedule({
    String? parentId,
    String? orderCode,
    String? scheduleTime,
  }) async {
    return await safeApiCall(
      _apiClient.getReasonsSchedule(
        parentId: parentId,
        orderCode: orderCode,
        scheduleTime: scheduleTime,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ReasonCancelEntity>>?>
      getCancelReasonsSchedule({
    String? parentId,
  }) async {
    return await safeApiCall(
      _apiClient.getCancelReasonsSchedule(
        parentId: parentId,
      ),
    );
  }

  @override
  Future<BaseResult?> cancelSchedule({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.cancelSchedule(
        body: RequestScheduleBody(
          orderCode: orderCode,
        ),
      ),
    );
  }

  @override
  Future<BaseResult?> editServiceOrder({
    required ServiceOrderBody body,
    bool isShowError = true,
  }) async {
    return await safeApiCall(
      isShowError: isShowError,
      _apiClient.editServiceOrder(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<BillInfoEntity>?> getBillCustomerInfo({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.getBillCustomerInfo(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult?> updateBillCustomerInfo({
    BillInfoParam? body,
  }) async {
    return await safeApiCall(
      _apiClient.updateBillCustomerInfo(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> cancelBillOrder({
    String? orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.cancelBillOrder(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<DetailBillStockEntity>?> getDetailStockBill({
    int? stockTransId,
  }) async {
    return await safeApiCall<DetailBillStockEntity>(
      _apiClient.getDetailStockBill(
        stockTransId: stockTransId,
      ),
    );
  }

  @override
  Future<BaseResult<DetailBillStockEntity>?> validateCancelReceive({
    String? orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.validateCancelReceive(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<List<ChecklistResponse>>?> getCheckList({
    String? orderCode,
    String? serviceCode,
  }) async {
    return await safeApiCall(
      _apiClient.getCheckList(
        orderCode: orderCode,
        serviceCode: serviceCode,
      ),
    );
  }

  @override
  Future<BaseResult?> updateCheckList({
    required ChecklistBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateCheckList(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> updateInstallationOrderProduct({
    required UpdateInstallationOrderProductBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateInstallationOrderProduct(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<CountEntity>?> countSchedule({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.countSchedule(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<CountEntity>?> countRequestCancelOrder({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.countRequestCancelOrder(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<CountEntity>?> countRequestExtendKpiOrder({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.countRequestExtendKpiOrder(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<CountEntity>?> countWorkerAssignOrder({
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.countWorkerAssignOrder(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult?> completeOrder({
    String? orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.completeOrder(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<DetailOrderItemEntity>>?>
      getServiceByproduct({
    String? productCode,
  }) async {
    try {
      final result = await safeApiCall(
        _apiClient.getServiceByproduct(
          productCode: productCode,
        ),
      );
      return result;
    } catch (e) {
      return null;
    }
  }

  @override
  Future<BaseResult?> validateMultiPayment({
    required OrderParam body,
  }) async {
    return await safeApiCall(
      _apiClient.validateMultiPayment(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> validateOrder({
    required String orderCode,
    bool isShowError = true,
  }) async {
    return await safeApiCall(
      isShowError: isShowError,
      _apiClient.validateOrder(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult?> uploadReportOrder({
    required UploadReportOrderBody body,
  }) async {
    return await safeApiCall(
      _apiClient.uploadReportOrder(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<List<ScheduleWorkerEntity>>> checkScheduleWorker({
    required ScheduleWorkerBody body,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.checkScheduleWorker(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<String>>?> getOrderType({
    String? orderTypes,
    String? customerTypes,
  }) async {
    return await safeApiCall(
      _apiClient.getOrderType(
        orderTypes: orderTypes,
        customerTypes: customerTypes,
      ),
    );
  }

  @override
  Future<BaseResult<ServiceInfoEntity>?> getServiceInfo({
    required String code,
  }) async {
    return await safeApiCall(
      _apiClient.getServiceInfo(
        code: code,
      ),
    );
  }

  @override
  Future<BaseResult<ScheduleWorkerEntity>?> checkHotOrder({
    required String orderType,
    required String wardCode,
    required String startTime,
    String? packageCode,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.checkHotOrder(
        orderType: orderType,
        wardCode: wardCode,
        startTime: startTime,
        packageCode: packageCode,
      ),
    );
  }

  @override
  Future<BaseResult<WarningAlertEntity>?> showWarningMobileCall({
    required String orderCode,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.showWarningMobileCall(
        orderCode: orderCode,
      ),
    );
  }

  @override
  Future<BaseResult<CommissionConfirmResponse>?> checkCommissionConfirm({
    required String customerPhone,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.checkCommissionConfirm(
        customerPhone: customerPhone,
      ),
    );
  }

  @override
  Future<BaseResult<WarningOrderResponse>?> getWarningOrder({
    String? expiredAlertTypes,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.getWarningOrder(
        expiredAlertTypes: expiredAlertTypes,
      ),
    );
  }

  @override
  Future<BaseResult<WarningAlertEntity>?> checkShowWarningOrder() async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.checkShowWarningOrder(),
    );
  }

  @override
  Future<BaseResult<SignContractEntity>?> getRequirementWarrantyFileSign({
    required SignItemWarrantyBody signItemWarrantyBody,
  }) async {
    return await safeApiCall(
      _apiClient.getRequirementWarrantyFileSign(
        signItemWarrantyBody: signItemWarrantyBody,
      ),
    );
  }

  @override
  Future<BaseResult<SignContractEntity>?> getDetailRequirementWarrantyFileSign({
    required SignItemWarrantyBody signItemWarrantyBody,
  }) async {
    return await safeApiCall(
      _apiClient.getDetailRequirementWarrantyFileSign(
        code: signItemWarrantyBody.orderCode ?? '',
      ),
    );
  }

  @override
  Future<BaseResult?> buySuppliesForCustomer({
    required BuySuppliesForCustomerBody body,
    required String orderCode,
  }) async {
    return await safeApiCall(
      _apiClient.buySuppliesForCustomer(body: body, orderCode: orderCode),
    );
  }

  @override
  Future<BaseResult<StatisticOrderEntity>?> getDataStatistic({
    String? dateTime,
  }) async {
    return await safeApiCall(
      _apiClient.getStatistic(
        dateTime: dateTime,
      ),
    );
  }

  @override
  Future<BaseResult<WorkCountEntity>?> getWorkCount({
    String? startTime,
    String? endTime,
  }) async {
    return await safeApiCall(
      _apiClient.getWorkCount(
        startTime: startTime,
        endTime: endTime,
      ),
    );
  }

  @override
  Future<BaseResult<WorkScheduleEventEntity>?> getEventDay({
    String? date,
  }) async {
    return await safeApiCall(
      _apiClient.getEventDay(date: date),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<WorkDataEntity>>?> getWorkData({
    String? startTime,
    String? endTime,
    String? dataTypeEnum,
  }) async {
    return await safeApiCall(
      _apiClient.getWorkData(
        startTime: startTime,
        endTime: endTime,
        dataTypeEnum: dataTypeEnum,
      ),
    );
  }
}
