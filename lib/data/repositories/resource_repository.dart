import 'dart:io';

import 'package:dio/dio.dart';
import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/repositories/base/base_repository.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/domain/body/mobile_call_body.dart';
import 'package:vcc/domain/entities/action_history_entity.dart';
import 'package:vcc/domain/entities/call_history_entity.dart';
import 'package:vcc/domain/entities/config_document_entity.dart';
import 'package:vcc/domain/entities/logs_entity.dart';
import 'package:vcc/domain/entities/order/file_entity.dart';
import 'package:vcc/domain/responses/general/version_response.dart';
import 'package:vcc/domain/responses/list_data_response.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/enums/address_type.dart';

import '../../domain/entities/order/file_v2_entity.dart';

abstract class ResourceRepository {
  Future<BaseResult<ArrayResponse<ActionHistoryEntity>?>> getActionHistory({
    String? referenceCode,
    String? functional,
    int? page,
    int? pageSize,
    List<String>? sort,
  });

  Future<BaseResult<ActionHistoryEntity>?> getDetailActionHistory({
    String? id,
  });

  Future<BaseResult<ArrayResponse<CallHistoryEntity>>?> getCallHistory({
    required String referenceId,
    String? functional,
  });

  Future<BaseResult<FileEntity>?> getOtherServicePDF();

  Future<BaseResult<List<CodeEntity>>?> getAddressInfo({
    String? keyword,
    required AddressType type,
    String? parentCode,
  });

  Future<BaseResult<List<String>>?> uploadFile({
    required File file,
  });

  Future<BaseResult<List<String>>?> uploadImage({
    required File file,
  });

  Future<BaseResult<List<FileV2Entity>>?> uploadFileV2({
    required File file,
  });

  Future<BaseResult?> saveLogMobileCall({
    required MobileCallBody body,
  });

  Future saveLogs({
    String? path,
    String? startAt,
    String? endAt,
  });

  Future<BaseResult<VersionResponse>?> getVersionInfo();

  Future<BaseResult<List<ConfigDocumentEntity>>?> getConfigDocument();
}

class ResourceRepositoryImpl extends BaseRepository
    implements ResourceRepository {
  ResourceRepositoryImpl({
    required ApiClient apiClient,
  }) : _apiClient = apiClient;

  final ApiClient _apiClient;

  @override
  Future<BaseResult<ArrayResponse<ActionHistoryEntity>?>> getActionHistory({
    String? referenceCode,
    String? functional,
    int? page,
    int? pageSize,
    List<String>? sort,
  }) async {
    return await safeApiCall(
      _apiClient.getActionHistory(
        referenceCode: referenceCode,
        functional: functional,
        page: page,
        pageSize: pageSize,
        sort: sort,
      ),
    );
  }

  @override
  Future<BaseResult<ActionHistoryEntity>?> getDetailActionHistory({
    String? id,
  }) async {
    return await safeApiCall(
      _apiClient.getDetailActionHistory(
        id: id,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CallHistoryEntity>>?> getCallHistory({
    required String referenceId,
    String? functional,
  }) async {
    return await safeApiCall(
      _apiClient.getCallHistory(
        referenceId: referenceId,
        functional: functional,
      ),
    );
  }

  @override
  Future<BaseResult<FileEntity>?> getOtherServicePDF() async {
    return await safeApiCall(
      _apiClient.getOtherServicePDF(),
    );
  }

  @override
  Future<BaseResult<List<CodeEntity>>?> getAddressInfo({
    String? keyword,
    required AddressType type,
    String? parentCode,
  }) async {
    return await safeApiCall<List<CodeEntity>>(
      _apiClient.getAddressInfo(
        keyword: keyword,
        type: type.keyToServer,
        parentCode: parentCode,
      ),
    );
  }

  @override
  Future<BaseResult<List<String>>?> uploadFile({
    required File file,
  }) async {
    FormData formData = FormData.fromMap(
      {
        "files": [
          await MultipartFile.fromFile(
            file.path,
          ),
        ],
      },
    );

    return await safeApiCall(
      _apiClient.uploadFile(
        body: formData,
      ),
    );
  }

  @override
  Future<BaseResult<List<String>>?> uploadImage({
    required File file,
  }) async {
    FormData formData = FormData.fromMap(
      {
        "files": [
          await MultipartFile.fromFile(
            file.path,
          ),
        ],
      },
    );

    return await safeApiCall(
      _apiClient.uploadImage(
        body: formData,
      ),
    );
  }

  @override
  Future<BaseResult<List<FileV2Entity>>?> uploadFileV2({
    required File file,
  }) async {
    FormData formData = FormData.fromMap(
      {
        "files": [
          await MultipartFile.fromFile(
            file.path,
          ),
        ],
      },
    );

    return await safeApiCall(
      _apiClient.uploadFileV2(
        body: formData,
      ),
    );
  }

  @override
  Future<BaseResult?> saveLogMobileCall({
    required MobileCallBody body,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.createLogMobileCall(body: body),
    );
  }

  @override
  Future saveLogs({
    String? path,
    String? startAt,
    String? endAt,
  }) async {
    await safeApiCall(
      isShowError: false,
      _apiClient.saveLogs(
        body: LogsEntity(
          path: path,
          startAt: startAt,
          endAt: endAt,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<VersionResponse>?> getVersionInfo() async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.getVersionInfo(),
    );
  }

  @override
  Future<BaseResult<List<ConfigDocumentEntity>>?> getConfigDocument() async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.getConfigDocument(),
    );
  }
}
