import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/repositories/base/base_repository.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/domain/body/search_warranty_body.dart';
import 'package:vcc/domain/entities/aio_contract/aio_commission_rate_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_config_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_detail_request.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_actual_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_invoice_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_log_inventory_response.dart';
import 'package:vcc/domain/entities/aio_contract/aio_method_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_qr_one_pay_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_collection_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_param_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_param_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_supply_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_survey_customer_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_survey_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_contract_detail_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_request_cancel_body.dart';
import 'package:vcc/domain/entities/app_param/app_param.dart';
import 'package:vcc/domain/entities/order/drop_down_list_entity.dart';
import 'package:vcc/domain/entities/order/list_good_entity.dart';
import 'package:vcc/domain/entities/order/order_info_short_entity.dart';
import 'package:vcc/domain/entities/price_order_entity.dart';
import 'package:vcc/domain/entities/stock/list_stock_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/entities/warranty/contract_warranty_entity.dart';
import 'package:vcc/domain/entities/warranty/warranty_center_entity.dart';
import 'package:vcc/domain/params/aio_contract/aio_check_deployment.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_actual.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_digital_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_update_param.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';
import 'package:vcc/domain/params/price_order_param.dart';
import 'package:vcc/domain/responses/aio/list_data_aio_package_response.dart';
import 'package:vcc/domain/responses/aio_array_response.dart';
import 'package:vcc/domain/responses/aio_data_response.dart';
import 'package:vcc/domain/responses/list_data_response.dart';

abstract class AioOrderRepository {
  Future<BaseResult<ArrayResponse<OrderInfoShortEntity>>?> getShortOrders();

  Future<BaseResult<PriceOrderEntity>?> getPriceOrder({
    required PriceOrderParam param,
  });

  Future<BaseResult<ArrayResponse<AioCollectionInfoEntity>>?>
      searchCollectionInfo({
    String? keySearch,
    int? performerId,
  });

  Future<BaseResult<ArrayResponse<AioSurveyEntity>>?> getAutoCompleteSurvey({
    String? surveyContent,
    int? sysUserId,
  });

  Future<BaseResult<ArrayResponse<AioSurveyCustomerEntity>>?>
      getAutoCompleteSurveyCustomer({
    int? surveyId,
  });

  Future<BaseResult<ArrayResponse<DropDownListEntity>>?> findDropDownData({
    int? surveyId,
  });

  Future<BaseResult<ListDataAioPackageResponse<AioPackageEntity>>?>
      getDataPackageDetail({required AioPackageParamEntity? params});

  Future<BaseResult<ArrayResponse<AioServiceEntity>>?> getServiceHs(
      {required AioServiceParamEntity params});

  Future<BaseResult<AioConfigEntity>?> getCodeAioConfigService(
      {required InternalStaffEntity params});

  Future<BaseResult<AioContractResponseEntity>?> saveNewContract(
      {required AioContractParam params});

  Future<BaseResult<ArrayResponse<DropDownListEntity>>?> getListGoodsType(
      {required DropDownListEntity params});

  Future<BaseResult<ArrayResponse<AioContractEntity>>?> findListTotalContract(
      {required AioContractEntity params});

  Future<BaseResult<ArrayResponse<AioContractDetailEntity>>?>
      getListContractDetailByContractId({required AioContractEntity params});

  Future<BaseResult<ArrayResponse<AioContractDetailEntity>>?>
      getListContractSellTaskDetail({required AioContractParam params});

  Future<BaseResult<ArrayResponse<AioPackageEntity>>?> getListPackageGood(
      {required AioContractParam params});

  Future<BaseResult<AioCommissionRateEntity>?> getNewCommissionRate(
      {required AioContractParam params});

  Future<BaseResult<ArrayResponse<AioInvoiceItemEntity>>?> getListInvoiceItem(
      {required List<int?> listPackageId});

  Future<BaseResult<ArrayResponse<AioInvoiceItemEntity>>?>
      dataOrderRequestForContract({required AioContractEntity param});

  Future<BaseResult<ArrayResponse<String>>?> getReasonExpired();

  Future<BaseResult<AioContractResponseEntity>?> updateContractHold({
    required AioContractParam param,
  });

  Future<BaseResult<AioContractResponseEntity>?> startContractService({
    required AioContractParam param,
  });

  Future<BaseResult<ArrayResponse<DropDownListEntity>>?>
      getListAppParamByParType({required DropDownListEntity params});

  Future<BaseResult<AioContractResponseEntity>?> aioRequestCancel(
      {required AioRequestCancelBody params});

  Future<BaseResult<AioContractResponseEntity>?> deleteCancelOrder(
      {required AioRequestCancelBody params});

  Future<BaseResult<ArrayResponse<AioSupplyEntity>>?> getGoodsPersonalWarehouse(
      {required AioContractParam param});

  Future<BaseResult<ArrayResponse<AioSupplyEntity>>?> getQuantityInstockTtqh(
      {required AioContractParam param});

  Future<BaseResult<ArrayResponse<AioSupplyEntity>>?> getGoodsProvinceWarehouse(
      {required AioContractParam param});

  Future<BaseResult<ListStockEntity>?> getListStockAIO(
      {required SysUserRequest param});

  Future<BaseResult<ListGoodEntity>?> getListGoodAio(
      {required AioContractEntity param});

  Future<BaseResult<AioContractResponseEntity>?> updatePayTypeContract(
      {required AioContractParam param});

  Future<BaseResult<AioInvoiceResponseEntity>?> getDataForInvoice(
      {required AioContractEntity param});

  Future<BaseResult<AioInvoiceResponseEntity>?> getInfoInvoice(
      {required AioContractEntity param});

  Future<BaseResult<AioInvoiceResponseEntity>?> saveInvoice(
      {required AioInvoiceEntity? param});

  Future<BaseResult<AioInvoiceResponseEntity?>?> findDigitalContractPath(
      {required AioContractDigitalParam? param});

  Future<BaseResult<AioContractResponseEntity?>?> getUrlToRequestSubmitMoney(
      {required AioContractDigitalParam? param});

  Future<BaseResult<AioInvoiceResponseEntity>?> signDigitalContract(
      {required AioContractDigitalParam? param});

  Future<BaseResult<AioContractResponseEntity>?>
      updateStatusAndPerformerContract({required AioContractEntity? param});

  Future<BaseResult<AioContractInfoEntity?>?> getFullContractInfo(
      {required AioContractDetailRequest? param});

  Future<BaseResult<ArrayResponse<AioContractEntity>>?> getContractOfPerformer(
      {required AioContractParam params});

  Future<BaseResult<AioContractResponseEntity>?> editContractOfPerformer(
      {required AioContractUpdateParam params});

  Future<BaseResult<AioMethodResponseEntity>?> getTransportMethod(
      {required AioContractUpdateParam params});

  Future<BaseResult<AioContractResponseEntity>?> checkEligibleToApply(
      {required AioContractParam params});

  Future<BaseResult<AioContractResponseEntity>?> endContractSell(
      {required AioContractDigitalParam param});

  Future<BaseResult<AioContractResponseEntity?>> checkDeploymentMethod(
      {required AioCheckDeployment param});

  Future<BaseResult<AioLogInventoryResponse?>> getLogInventoryContract(
      {required AioContractParam param});

  Future<BaseResult<AioContractResponseActualEntity?>> getStatusActual(
      {required AioContractParam param});

  Future<BaseResult<AioContractResponseEntity?>> updateStatusActual(
      {required List<AioContractActual> param});

  Future<BaseResult<AioContractResponseEntity?>> calculatorCostTechnicalBranch(
      {required AioContractParam? param});

  Future<BaseResult<AioContractResponseEntity?>> validOtpSignContract(
      {required AioContractEntity? param});

  Future<BaseResult<AioContractResponseEntity?>> sendSmsOtp(
      {required AioContractEntity? param});

  Future<BaseResult<AioContractResponseEntity?>> validOTPSignContract(
      {required AioContractEntity? param});

  Future<BaseResult<ArrayResponse<AppParam>?>> getListAppParamSolarCare(
      {required AioContractEntity? param});

  Future<BaseResult<ArrayResponse<InternalStaffEntity>>>
      getStaffByEmployeeCode({
    String? keySearch,
  });

  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListUserStock({
    String? keySearch,
    String? areaCode,
  });

  Future<BaseResult<ArrayResponse<InternalStaffEntity>>>
      getForAutoCompleteSysUser({
    String? keySearch,
  });

  Future<BaseResult<AioArrayResponse<ContractWarrantyEntity>>>
      getListContractWarranty({
    required SearchWarrantyBody body,
  });

  Future<BaseResult<AioDataResponse<ContractWarrantyEntity>>>
      getDetailContractWarranty({
    required SearchWarrantyBody body,
  });

  Future<BaseResult<AioArrayResponse<WarrantyCenterEntity>>>
      getListWarrantyCenter({
    required SearchWarrantyBody body,
  });

  Future<BaseResult<AioQrOnePayEntity>?> getQrOnePay(
      {required AioContractParam param});
}

class AioOrderRepositoryImpl extends BaseRepository
    implements AioOrderRepository {
  AioOrderRepositoryImpl({
    required ApiClient apiClient,
  }) : _apiClient = apiClient;

  final ApiClient _apiClient;

  @override
  Future<BaseResult<ArrayResponse<OrderInfoShortEntity>>?>
      getShortOrders() async {
    return await safeApiCall<ArrayResponse<OrderInfoShortEntity>>(
      _apiClient.getShortOrders(),
    );
  }

  @override
  Future<BaseResult<PriceOrderEntity>?> getPriceOrder({
    required PriceOrderParam param,
  }) async {
    return await safeApiCall<PriceOrderEntity>(
      isShowError: false,
      _apiClient.getPriceOrder(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioCollectionInfoEntity>>?>
      searchCollectionInfo({
    String? keySearch,
    int? performerId,
  }) async {
    return await safeApiCall<ArrayResponse<AioCollectionInfoEntity>>(
      _apiClient.searchCollectionInfo(
        body: AioCollectionInfoEntity(
            keySearch: keySearch, performerId: performerId),
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioSurveyEntity>>?> getAutoCompleteSurvey({
    String? surveyContent,
    int? sysUserId,
  }) async {
    return await safeApiCall<ArrayResponse<AioSurveyEntity>>(
      _apiClient.getAutoCompleteSurvey(
        body: AioSurveyEntity(
          surveyContent: surveyContent,
          sysUserId: sysUserId,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioSurveyCustomerEntity>>?>
      getAutoCompleteSurveyCustomer({
    int? surveyId,
  }) async {
    return await safeApiCall<ArrayResponse<AioSurveyCustomerEntity>>(
      _apiClient.getAutoCompleteSurveyCustomer(
        body: AioSurveyCustomerEntity(
          surveyId: surveyId,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<DropDownListEntity>>?> findDropDownData({
    int? surveyId,
  }) async {
    return await safeApiCall<ArrayResponse<DropDownListEntity>>(
      _apiClient.findDropDownData(
        body: DropDownListEntity(),
      ),
    );
  }

  @override
  Future<BaseResult<ListDataAioPackageResponse<AioPackageEntity>>?>
      getDataPackageDetail({required AioPackageParamEntity? params}) async {
    return await safeApiCall<ListDataAioPackageResponse<AioPackageEntity>>(
      _apiClient.getDataPackageDetail(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioServiceEntity>>?> getServiceHs(
      {required AioServiceParamEntity params}) async {
    return await safeApiCall<ArrayResponse<AioServiceEntity>>(
      _apiClient.getServiceHs(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<AioConfigEntity>?> getCodeAioConfigService(
      {required InternalStaffEntity params}) async {
    return await safeApiCall<AioConfigEntity>(
      _apiClient.getCodeAioConfigService(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<DropDownListEntity>>?> getListGoodsType(
      {required DropDownListEntity params}) async {
    return await safeApiCall<ArrayResponse<DropDownListEntity>>(
      _apiClient.getListGoodsType(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> saveNewContract(
      {required AioContractParam params}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.saveNewContract(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioContractEntity>>?> findListTotalContract(
      {required AioContractEntity params}) async {
    return await safeApiCall<ArrayResponse<AioContractEntity>>(
      _apiClient.findListTotalContract(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioContractDetailEntity>>?>
      getListContractDetailByContractId(
          {required AioContractEntity params}) async {
    return await safeApiCall<ArrayResponse<AioContractDetailEntity>>(
      _apiClient.getListContractDetailByContractId(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioContractDetailEntity>>?>
      getListContractSellTaskDetail({required AioContractParam params}) async {
    return await safeApiCall<ArrayResponse<AioContractDetailEntity>>(
      _apiClient.getListContractSellTaskDetail(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioPackageEntity>>?> getListPackageGood(
      {required AioContractParam params}) async {
    return await safeApiCall<ArrayResponse<AioPackageEntity>>(
      _apiClient.getListPackageGood(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioInvoiceItemEntity>>?> getListInvoiceItem(
      {required List<int?> listPackageId}) async {
    return await safeApiCall<ArrayResponse<AioInvoiceItemEntity>>(
      _apiClient.getListInvoiceItem(
        param: AioInvoiceItemEntity(
          aioPackageIdList: listPackageId,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioInvoiceItemEntity>>?>
      dataOrderRequestForContract({required AioContractEntity param}) async {
    return await safeApiCall<ArrayResponse<AioInvoiceItemEntity>>(
      _apiClient.dataOrderRequestForContract(
        param: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioQrOnePayEntity>?> getQrOnePay(
      {required AioContractParam param}) async {
    return await safeApiCall<AioQrOnePayEntity>(
      _apiClient.getQrOnePay(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioCommissionRateEntity>?> getNewCommissionRate(
      {required AioContractParam params}) async {
    return await safeApiCall<AioCommissionRateEntity>(
      _apiClient.getNewCommissionRate(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<String>>?> getReasonExpired() async {
    return await safeApiCall<ArrayResponse<String>>(
      _apiClient.getReasonExpired(),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> updateContractHold({
    required AioContractParam param,
  }) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.updateContractHold(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> startContractService({
    required AioContractParam param,
  }) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.startContractService(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<DropDownListEntity>>?>
      getListAppParamByParType({required DropDownListEntity params}) async {
    return await safeApiCall<ArrayResponse<DropDownListEntity>>(
      _apiClient.getListAppParamByParType(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> aioRequestCancel(
      {required AioRequestCancelBody params}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.createCancelOrder(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> deleteCancelOrder(
      {required AioRequestCancelBody params}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.deleteCancelOrder(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioSupplyEntity>>?> getGoodsPersonalWarehouse(
      {required AioContractParam param}) async {
    return await safeApiCall<ArrayResponse<AioSupplyEntity>>(
      _apiClient.getGoodsPersonalWarehouse(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioSupplyEntity>>?> getQuantityInstockTtqh(
      {required AioContractParam param}) async {
    return await safeApiCall<ArrayResponse<AioSupplyEntity>>(
      _apiClient.getQuantityInstockTtqh(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioSupplyEntity>>?> getGoodsProvinceWarehouse(
      {required AioContractParam param}) async {
    return await safeApiCall<ArrayResponse<AioSupplyEntity>>(
      _apiClient.getGoodsProvinceWarehouse(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ListGoodEntity>?> getListGoodAio(
      {required AioContractEntity param}) async {
    return await safeApiCall<ListGoodEntity>(
      _apiClient.getListGoodAio(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ListStockEntity>?> getListStockAIO(
      {required SysUserRequest param}) async {
    return await safeApiCall<ListStockEntity>(
      _apiClient.getListStockAIO(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> updatePayTypeContract(
      {required AioContractParam param}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.updatePayTypeContract(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioInvoiceResponseEntity>?> getDataForInvoice(
      {required AioContractEntity param}) async {
    return await safeApiCall<AioInvoiceResponseEntity>(
      _apiClient.getDataForInvoice(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioInvoiceResponseEntity>?> getInfoInvoice(
      {required AioContractEntity param}) async {
    return await safeApiCall<AioInvoiceResponseEntity>(
      _apiClient.getInfoInvoice(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioInvoiceResponseEntity>?> saveInvoice(
      {required AioInvoiceEntity? param}) async {
    return await safeApiCall<AioInvoiceResponseEntity>(
      _apiClient.saveInvoice(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioInvoiceResponseEntity>?> findDigitalContractPath(
      {required AioContractDigitalParam? param}) async {
    return await safeApiCall<AioInvoiceResponseEntity>(
      _apiClient.findDigitalContractPath(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioInvoiceResponseEntity>?> signDigitalContract(
      {required AioContractDigitalParam? param}) async {
    return await safeApiCall<AioInvoiceResponseEntity>(
      _apiClient.signDigitalContract(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> getUrlToRequestSubmitMoney(
      {required AioContractDigitalParam? param}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.getUrlToRequestSubmitMoney(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?>
      updateStatusAndPerformerContract(
          {required AioContractEntity? param}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.updateStatusAndPerformerContract(
        param: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractInfoEntity?>?> getFullContractInfo(
      {required AioContractDetailRequest? param}) async {
    return await safeApiCall<AioContractInfoEntity?>(
      _apiClient.getFullContractInfo(
        param: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AioContractEntity>>?> getContractOfPerformer(
      {required AioContractParam params}) async {
    return await safeApiCall<ArrayResponse<AioContractEntity>>(
      _apiClient.getContractOfPerformer(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> editContractOfPerformer(
      {required AioContractUpdateParam params}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.editContractOfPerformer(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<AioMethodResponseEntity>?> getTransportMethod(
      {required AioContractUpdateParam params}) async {
    return await safeApiCall<AioMethodResponseEntity>(
      _apiClient.getTransportMethod(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> checkEligibleToApply(
      {required AioContractParam params}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.checkEligibleToApply(
        body: params,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity>?> endContractSell(
      {required AioContractDigitalParam param}) async {
    return await safeApiCall<AioContractResponseEntity>(
      _apiClient.endContractSell(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity?>> checkDeploymentMethod(
      {required AioCheckDeployment param}) async {
    return await safeApiCall<AioContractResponseEntity?>(
      _apiClient.checkDeploymentMethod(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioLogInventoryResponse?>> getLogInventoryContract(
      {required AioContractParam param}) async {
    return await safeApiCall<AioLogInventoryResponse?>(
      _apiClient.getLogInventoryContract(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseActualEntity?>> getStatusActual(
      {required AioContractParam param}) async {
    return await safeApiCall<AioContractResponseActualEntity?>(
      _apiClient.getStatusActual(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity?>> updateStatusActual(
      {required List<AioContractActual> param}) async {
    return await safeApiCall<AioContractResponseEntity?>(
      _apiClient.updateStatusActual(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity?>> calculatorCostTechnicalBranch(
      {required AioContractParam? param}) async {
    return await safeApiCall<AioContractResponseEntity?>(
      _apiClient.calculatorCostTechnicalBranch(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity?>> validOtpSignContract(
      {required AioContractEntity? param}) async {
    return await safeApiCall<AioContractResponseEntity?>(
      _apiClient.validOtpSignContract(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity?>> sendSmsOtp(
      {required AioContractEntity? param}) async {
    return await safeApiCall<AioContractResponseEntity?>(
      _apiClient.sendSmsOtp(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<AioContractResponseEntity?>> validOTPSignContract(
      {required AioContractEntity? param}) async {
    return await safeApiCall<AioContractResponseEntity?>(
      _apiClient.validOTPSignContract(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AppParam>?>> getListAppParamSolarCare(
      {required AioContractEntity? param}) async {
    return await safeApiCall<ArrayResponse<AppParam>?>(
      _apiClient.getListAppParamSolarCare(
        body: param,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InternalStaffEntity>>>
      getStaffByEmployeeCode({
    String? keySearch,
  }) async {
    return await safeApiCall<ArrayResponse<InternalStaffEntity>>(
      _apiClient.getStaffByEmployeeCode(
        body: AioCollectionInfoEntity(keySearch: keySearch),
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InternalStaffEntity>>> getListUserStock({
    String? keySearch,
    String? areaCode,
  }) async {
    return await safeApiCall<ArrayResponse<InternalStaffEntity>>(
      _apiClient.getListUserStock(
        body: InternalStaffEntity(
          fullName: keySearch,
          // flag: 0,
          keySearch: keySearch,
          areaCode: areaCode,
          sysUserId: GlobalData.instance.userInfo?.sysUserId,
          sysGroupId: GlobalData.instance.userInfo?.sysGroupId,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<InternalStaffEntity>>>
      getForAutoCompleteSysUser({
    String? keySearch,
  }) async {
    return await safeApiCall<ArrayResponse<InternalStaffEntity>>(
      _apiClient.getForAutoCompleteSysUser(
        body: InternalStaffEntity(
          fullName: keySearch,
          sysUserId: GlobalData.instance.userInfo?.sysUserId,
        ),
      ),
    );
  }

  @override
  Future<BaseResult<AioArrayResponse<ContractWarrantyEntity>>>
      getListContractWarranty({
    required SearchWarrantyBody body,
  }) async {
    return await safeApiCall<AioArrayResponse<ContractWarrantyEntity>>(
      _apiClient.getListContractWarranty(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<AioDataResponse<ContractWarrantyEntity>>>
      getDetailContractWarranty({
    required SearchWarrantyBody body,
  }) async {
    return await safeApiCall<AioDataResponse<ContractWarrantyEntity>>(
      _apiClient.getDetailContractWarranty(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<AioArrayResponse<WarrantyCenterEntity>>>
      getListWarrantyCenter({
    required SearchWarrantyBody body,
  }) async {
    return await safeApiCall<AioArrayResponse<WarrantyCenterEntity>>(
      _apiClient.getListWarrantyCenter(
        body: body,
      ),
    );
  }
}
