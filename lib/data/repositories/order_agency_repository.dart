import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/repositories/base/base_repository.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/domain/body/agency/agency_user_company_body.dart';
import 'package:vcc/domain/body/agency/approved_order_agency_body.dart';
import 'package:vcc/domain/body/agency/confirm_issue_note_body.dart';
import 'package:vcc/domain/body/agency/create_payment_body.dart';
import 'package:vcc/domain/body/agency/dealer_order_detail_body.dart';
import 'package:vcc/domain/body/agency/duplicate_order_agency_body.dart';
import 'package:vcc/domain/body/agency/history_order_agency_body.dart';
import 'package:vcc/domain/body/agency/invoice_info_body.dart';
import 'package:vcc/domain/body/agency/invoice_info_file_body.dart';
import 'package:vcc/domain/body/agency/invoice_save_info_body.dart';
import 'package:vcc/domain/body/agency/order_agency_draft_body.dart';
import 'package:vcc/domain/body/agency/order_request_cart_body.dart';
import 'package:vcc/domain/body/agency/refuse_order_agent_body.dart';
import 'package:vcc/domain/body/agency/reject_issue_note_body.dart';
import 'package:vcc/domain/body/agency/request_cancel_order_agency_body.dart';
import 'package:vcc/domain/body/agency/request_order_body.dart';
import 'package:vcc/domain/body/agency/sign_contract_invoice_body.dart';
import 'package:vcc/domain/body/agency/sync_stock_trans_body.dart';
import 'package:vcc/domain/body/agency/work_agency_body.dart';
import 'package:vcc/domain/entities/aio_contract/aio_invoice_response_entity.dart';
import 'package:vcc/domain/entities/order_agency/agency_user_company_entity.dart';
import 'package:vcc/domain/entities/order_agency/dealer_order_detail_entity.dart';
import 'package:vcc/domain/entities/order_agency/duplicate_order_agency_entity.dart';
import 'package:vcc/domain/entities/order_agency/history_order_agency_entity.dart';
import 'package:vcc/domain/entities/order_agency/invoice_info_entity.dart';
import 'package:vcc/domain/entities/order_agency/invoice_info_file_entity.dart';
import 'package:vcc/domain/entities/order_agency/order_agency_draft_entity.dart';
import 'package:vcc/domain/entities/order_agency/order_request_cart_entity.dart';
import 'package:vcc/domain/entities/order_agency/request_order_entity.dart';
import 'package:vcc/domain/entities/order_agency/save_order_agency_entity.dart';
import 'package:vcc/domain/entities/order_agency/sign_contract_invoice_entity.dart';
import 'package:vcc/domain/entities/order_agency/trans_export_entity.dart';
import 'package:vcc/domain/entities/order_agency/work_agency_entity.dart';
import 'package:vcc/domain/entities/product/category_entity.dart';
import 'package:vcc/domain/entities/product/product_entity.dart';
import 'package:vcc/domain/entities/product/product_info_entity.dart';
import 'package:vcc/domain/enums/menu_type.dart';
import 'package:vcc/domain/responses/list_data_response.dart';

abstract class OrderAgencyRepository {
  Future<BaseResult<ArrayResponse<ProductEntity>>?> getProductByCategory({
    required String parentId,
  });

  Future<BaseResult<ArrayResponse<CategoryEntity>>?> getProductCategories(
    MenuType menuType,
  );

  Future<BaseResult<OrderRequestCartEntity>?> getInfoLevelCategoryForOrder(
    OrderRequestCartBody orderRequestCartBody,
  );

  Future<BaseResult<SaveOrderAgencyEntity>?> createOrderAgencyRequest(
    OrderRequestCartBody orderRequestCartBody,
  );

  Future<BaseResult?> saveOrderAgencyDraft(
    OrderAgencyDraftBody orderAgencyDraftBody,
  );

  Future<BaseResult<WorkAgencyEntity>?> getWorkAgency(
    WorkAgencyBody workAgencyBody,
  );

  Future<BaseResult<ProductInfoEntity>?> getProductDetail({
    required String code,
  });

  Future<BaseResult<DealerOrderDetailEntity>?> getDealerOrderDetail({
    required DealerOrderDetailBody body,
  });

  Future<BaseResult<ProductEntity>?> searchProductAgencyByCategory({
    required String categoryId,
    String? keyword,
    int? page,
    int? pageSize,
    int? minPrice,
    int? maxPrice,
  });

  Future<BaseResult<DealerOrderDetailEntity>?> refuseOrderAgent({
    required CancelOrderAgentBody body,
  });

  Future<BaseResult<DealerOrderDetailEntity>?> confirmIssueNote({
    required ConfirmIssueNoteBody body,
  });

  Future<BaseResult<TransExportEntity>?> getListTransExport({
    required SynStockTransBody body,
  });

  Future<BaseResult<DealerOrderDetailEntity>?> rejectIssueNote({
    required RejectIssueNoteBody body,
  });

  Future<BaseResult<DealerOrderDetailEntity>?> rejectOrderAgent({
    required CancelOrderAgentBody body,
  });

  Future<BaseResult<DealerOrderDetailEntity>?> approvedOrderAgent({
    required ApprovedOrderAgencyBody body,
  });

  Future<BaseResult<InvoiceInfoEntity>?> getInvoiceData({
    required InvoiceInfoBody body,
  });

  Future<BaseResult<InvoiceInfoFileEntity>?> getInvoicePdf({
    required InvoiceInfoFileBody body,
  });

  Future<BaseResult<AioInvoiceResponseEntity>?> updateInvoice({
    required InvoiceSaveInfoBody body,
  });

  Future<BaseResult<SignContractInvoiceEntity>?> signContractInvoice({
    required SignContractInvoiceBody body,
  });

  Future<BaseResult<SignContractInvoiceEntity>?> findContractPathInvoice({
    required SignContractInvoiceBody body,
  });

  Future<BaseResult<SignContractInvoiceEntity>?> createViettelPayQRCode({
    required CreatePaymentBody body,
  });

  Future<BaseResult<SignContractInvoiceEntity>?> createOnePayQRCodeAgency({
    required CreatePaymentBody body,
  });

  Future<BaseResult<OrderAgencyDraftEntity>?> getListProductInCart();

  Future<BaseResult<ProductEntity>?> getAllProductManage({
    String? keyword,
    bool? isAgentSelect,
    int? page,
    int? pageSize,
  });

  Future<BaseResult<RequestOrderEntity>?> getDataAddOrder({
    required RequestOrderBody body,
  });

  Future<BaseResult<AgencyUserCompanyEntity>?> getAgencyByUserCompany({
    required AgencyUserCompanyBody body,
  });

  Future<BaseResult<ArrayResponse<ProductInfoEntity>>?> getSuggestProduct({
    String? keySearch,
    int? page,
    int? pageSize,
    bool? isAgentSelect,
  });

  Future<BaseResult<DuplicateOrderAgencyEntity>?> getOrderAgencyDuplicates({
    required DuplicateOrderAgencyBody? body,
  });

  Future<BaseResult<DealerOrderDetailEntity>?> requestCancelAgency({
    required RequestCancelOrderAgencyBody body,
  });

  Future<BaseResult<HistoryOrderAgencyEntity>?> getHistoryOrderAgency({
    required HistoryOrderAgencyBody body,
  });
}

class OrderAgencyRepositoryImpl extends BaseRepository
    implements OrderAgencyRepository {
  OrderAgencyRepositoryImpl({
    required ApiClient apiClient,
  }) : _apiClient = apiClient;

  final ApiClient _apiClient;

  @override
  Future<BaseResult<ArrayResponse<ProductEntity>>?> getProductByCategory({
    required String parentId,
  }) async {
    return await safeApiCall<ArrayResponse<ProductEntity>>(
      isShowError: false,
      _apiClient.getProductByCategory(
        parentId,
        isAgentSelect: true,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CategoryEntity>>?> getProductCategories(
      MenuType menuType) async {
    return await safeApiCall<ArrayResponse<CategoryEntity>>(
      isShowError: false,
      _apiClient.getProductCategories(
        type: menuType.keyToServer ?? '',
        isAgentSelect: true,
      ),
    );
  }

  @override
  Future<BaseResult<OrderRequestCartEntity>?> getInfoLevelCategoryForOrder(
      OrderRequestCartBody orderRequestCartBody) async {
    return await safeApiCall<OrderRequestCartEntity>(
      _apiClient.getInfoLevelCategoryForOrder(
        body: orderRequestCartBody,
      ),
    );
  }

  @override
  Future<BaseResult<SaveOrderAgencyEntity>?> createOrderAgencyRequest(
      OrderRequestCartBody orderRequestCartBody) async {
    return await safeApiCall<SaveOrderAgencyEntity>(
      _apiClient.createOrderAgencyRequest(
        body: orderRequestCartBody,
      ),
    );
  }

  @override
  Future<BaseResult?> saveOrderAgencyDraft(
      OrderAgencyDraftBody orderAgencyDraftBody) async {
    return await safeApiCall(
      _apiClient.saveOrderAgencyDraft(
        body: orderAgencyDraftBody,
      ),
    );
  }

  @override
  Future<BaseResult<WorkAgencyEntity>?> getWorkAgency(
      WorkAgencyBody workAgencyBody) async {
    return await safeApiCall<WorkAgencyEntity>(
      _apiClient.getWorkAgency(
        body: workAgencyBody,
      ),
    );
  }

  @override
  Future<BaseResult<ProductInfoEntity>?> getProductDetail({
    required String code,
  }) async {
    return await safeApiCall<ProductInfoEntity>(
      _apiClient.getProductDetail(
        code,
        isAgentSelect: true,
      ),
    );
  }

  @override
  Future<BaseResult<DealerOrderDetailEntity>?> getDealerOrderDetail({
    required DealerOrderDetailBody body,
  }) async {
    return await safeApiCall<DealerOrderDetailEntity>(
      _apiClient.getDealerOrderDetail(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<ProductEntity>?> searchProductAgencyByCategory({
    required String categoryId,
    String? keyword,
    int? page,
    int? pageSize,
    int? minPrice,
    int? maxPrice,
  }) async {
    return await safeApiCall<ProductEntity>(
      _apiClient.searchProductByCategory(
        categoryId: categoryId,
        keyword: keyword,
        page: page,
        pageSize: pageSize,
        minPrice: minPrice,
        maxPrice: maxPrice,
        isAgentSelect: true,
      ),
    );
  }

  @override
  Future<BaseResult<DealerOrderDetailEntity>?> refuseOrderAgent({
    required CancelOrderAgentBody body,
  }) async {
    return await safeApiCall<DealerOrderDetailEntity>(
      _apiClient.cancelOrderAgent(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<DealerOrderDetailEntity>?> confirmIssueNote({
    required ConfirmIssueNoteBody body,
  }) async {
    return await safeApiCall<DealerOrderDetailEntity>(
      _apiClient.confirmIssueNote(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<TransExportEntity>?> getListTransExport({
    required SynStockTransBody body,
  }) async {
    return await safeApiCall<TransExportEntity>(
      _apiClient.getListTransExport(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<DealerOrderDetailEntity>?> rejectIssueNote({
    required RejectIssueNoteBody body,
  }) async {
    return await safeApiCall<DealerOrderDetailEntity>(
      _apiClient.rejectIssueNote(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<DealerOrderDetailEntity>?> rejectOrderAgent({
    required CancelOrderAgentBody body,
  }) async {
    return await safeApiCall<DealerOrderDetailEntity>(
      _apiClient.rejectOrderAgent(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<DealerOrderDetailEntity>?> approvedOrderAgent({
    required ApprovedOrderAgencyBody body,
  }) async {
    return await safeApiCall<DealerOrderDetailEntity>(
      _apiClient.approvedOrderAgent(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<InvoiceInfoEntity>?> getInvoiceData({
    required InvoiceInfoBody body,
  }) async {
    return await safeApiCall<InvoiceInfoEntity>(
      _apiClient.getInvoiceData(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<InvoiceInfoFileEntity>?> getInvoicePdf({
    required InvoiceInfoFileBody body,
  }) async {
    return await safeApiCall<InvoiceInfoFileEntity>(
      _apiClient.getInvoicePdf(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<AioInvoiceResponseEntity>?> updateInvoice({
    required InvoiceSaveInfoBody body,
  }) async {
    return await safeApiCall<AioInvoiceResponseEntity>(
      _apiClient.updateInvoice(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<SignContractInvoiceEntity>?> signContractInvoice({
    required SignContractInvoiceBody body,
  }) async {
    return await safeApiCall(
      _apiClient.signContractInvoice(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<SignContractInvoiceEntity>?> findContractPathInvoice({
    required SignContractInvoiceBody body,
  }) async {
    return await safeApiCall(
      _apiClient.findContractPathInvoice(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<SignContractInvoiceEntity>?> createViettelPayQRCode({
    required CreatePaymentBody body,
  }) async {
    return await safeApiCall(
      _apiClient.createViettelPayQRCodeAgency(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<SignContractInvoiceEntity>?> createOnePayQRCodeAgency(
      {required CreatePaymentBody body}) async {
    return await safeApiCall(
      _apiClient.createOnePayQRCodeAgency(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<OrderAgencyDraftEntity>?> getListProductInCart() async {
    return await safeApiCall(
      _apiClient.getListProductInCart(),
    );
  }

  @override
  Future<BaseResult<ProductEntity>?> getAllProductManage({
    String? keyword,
    bool? isAgentSelect,
    int? page,
    int? pageSize,
  }) async {
    return await safeApiCall<ProductEntity>(
      _apiClient.getAllProduct(
        keyword: keyword,
        isAgentSelect: isAgentSelect,
        page: page,
        pageSize: pageSize,
      ),
    );
  }

  @override
  Future<BaseResult<RequestOrderEntity>?> getDataAddOrder({
    required RequestOrderBody body,
  }) async {
    return await safeApiCall(
      _apiClient.getDataAddOrder(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<AgencyUserCompanyEntity>?> getAgencyByUserCompany({
    required AgencyUserCompanyBody body,
  }) async {
    return await safeApiCall(
      _apiClient.getAgencyByUserCompany(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ProductInfoEntity>>?> getSuggestProduct({
    String? keySearch,
    int? page,
    int? pageSize,
    bool? isAgentSelect,
  }) async {
    return await safeApiCall<ArrayResponse<ProductInfoEntity>>(
      _apiClient.getSuggestProductStore(
        keySearch: keySearch,
        page: page ?? BaseConstant.defaultPage,
        pageSize: pageSize ?? 5,
        isAgentSelect: isAgentSelect ?? true,
      ),
    );
  }

  @override
  Future<BaseResult<DuplicateOrderAgencyEntity>?> getOrderAgencyDuplicates({
    DuplicateOrderAgencyBody? body,
  }) async {
    return await safeApiCall<DuplicateOrderAgencyEntity>(
      _apiClient.getOrderAgencyDuplicates(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<HistoryOrderAgencyEntity>?> getHistoryOrderAgency({
    required HistoryOrderAgencyBody body,
  }) async {
    return await safeApiCall<HistoryOrderAgencyEntity>(
      _apiClient.getHistoryOrderAgency(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<DealerOrderDetailEntity>?> requestCancelAgency({
    required RequestCancelOrderAgencyBody body,
  }) async {
    return await safeApiCall<DealerOrderDetailEntity>(
      _apiClient.requestCancelAgency(
        body: body,
      ),
    );
  }
}
