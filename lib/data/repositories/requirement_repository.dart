import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/repositories/base/base_repository.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/domain/body/save_solar_energy_body.dart';
import 'package:vcc/domain/body/solar_energy_body.dart';
import 'package:vcc/domain/entities/association/act_customer_source_entity.dart';
import 'package:vcc/domain/entities/association/solar_energy_entity.dart';
import 'package:vcc/domain/entities/base64_entity.dart';
import 'package:vcc/domain/entities/config_entity.dart';
import 'package:vcc/domain/responses/list_data_response.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/body/answer_question_body.dart';
import 'package:vcc/domain/body/cancel_collection_body.dart';
import 'package:vcc/domain/body/collection_body.dart';
import 'package:vcc/domain/body/image_collection_body.dart';
import 'package:vcc/domain/body/survey_info_collection_body.dart';
import 'package:vcc/domain/entities/association/association_entity.dart';
import 'package:vcc/domain/entities/campaign_entity.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/entities/count_collection_entity.dart';
import 'package:vcc/domain/entities/question_campaign_entity.dart';
import 'package:vcc/domain/body/assign_association_body.dart';
import 'package:vcc/domain/body/assign_wo_site_survey_body.dart';
import 'package:vcc/domain/body/association_customer_demand_body.dart';
import 'package:vcc/domain/body/association_customer_info_body.dart';
import 'package:vcc/domain/body/save_act_product_service_body.dart';
import 'package:vcc/domain/body/save_association_body.dart';
import 'package:vcc/domain/body/save_demand_wo_body.dart';
import 'package:vcc/domain/body/save_wo_body.dart';
import 'package:vcc/domain/entities/association/act_channel_source_entity.dart';
import 'package:vcc/domain/entities/association/act_demand_filter_entity.dart';
import 'package:vcc/domain/entities/association/act_service_supply_entity.dart';
import 'package:vcc/domain/entities/association/act_wo_detail.dart';

abstract class RequirementRepository {
  Future<BaseResult<ArrayResponse<ConfigEntity>>?> getConfig({
    String? type,
    int? page,
    int? size,
  });

  Future<BaseResult<ArrayResponse<CampaignEntity>>?> getListCampaign({
    int? page,
    int? pageSize,
    String? keyword,
    String? targetCustomerType,
  });

  Future<BaseResult<ArrayResponse<QuestionCampaignEntity>>?>
      getListQuestionOfCampaign({
    String? campaignId,
  });

  Future<BaseResult?> updateCollectionInfo({
    required SurveyCollectionBody body,
  });

  Future<BaseResult?> receiveCollection({
    required List<String> body,
  });

  Future<BaseResult?> completeCollection({
    required AnswerQuestionBody body,
  });

  Future<BaseResult?> cancelCollection({
    required CancelCollectionBody body,
  });

  Future<BaseResult?> deleteImageCollection({
    required ImageCollectionBody body,
  });

  Future<BaseResult<CollectionInfoEntity>?> createCollection(
    CollectionBody body,
  );

  Future<BaseResult<ArrayResponse<CollectionInfoEntity>>?> getListCollection({
    String? keySearch,
    String? campaignId,
    List<String>? status,
    String? customerType,
    List<String>? districtCodes,
    bool? isGetAll,
    String? assignment,
    int? page,
    int? size,
  });

  Future<BaseResult<CollectionInfoEntity>?> getDetailCollection(
    String code,
  );

  Future<BaseResult<CountCollectionEntity>?> countCollectionStatus({
    String? keySearch,
    String? campaignId,
    List<String>? status,
    String? customerType,
    List<String>? districtCodes,
    bool? isGetAll,
  });

  Future<BaseResult<AssociationEntity>?> getDetailAssociation(
    String code,
  );

  Future<BaseResult<ArrayResponse<AssociationEntity>?>> getListAssociation({
    String? from,
    String? to,
    List<String>? demandCode,
    List<String>? listStatus,
    List<String>? types,
    List<String>? processStatus,
    String? keyword,
    int? page,
    int? size,
    List<String>? sort,
  });

  Future<BaseResult<ArrayResponse<ActCustomerSourceEntity>>?>
      getAssociationCustomerSource({
    int? page,
    int? size,
    List<String>? sort,
  });

  Future<BaseResult<ArrayResponse<ActServiceSupplyEntity>>?>
      getListAssociationSupply({
    int? page,
    int? size,
    List<String>? sort,
    String? keySearch,
  });

  Future<BaseResult<ArrayResponse<ActServiceSupplyEntity>>?>
      getListAssociationService({
    int? page,
    int? size,
    List<String>? sort,
    String? keySearch,
  });

  Future<BaseResult<ArrayResponse<ActChannelSourceEntity>>?>
      getAssociationChannel({
    int? page,
    int? size,
    List<String>? sort,
  });

  Future<BaseResult?> receiveAssociation({
    required String code,
  });

  Future<BaseResult?> updateCustomerDemand({
    required AssociationCustomerDemandBody body,
  });

  Future<BaseResult?> updateCustomerInfo({
    required AssociationCustomerInfoBody body,
  });

  Future<BaseResult?> completeAssociation({
    required String code,
  });

  Future<BaseResult<ArrayResponse<ActDemandFilterEntity>?>> getDemandWoSurvey({
    required String code,
    int? page,
    int? pageSize,
    String? keySearch,
  });

  Future<BaseResult?> receiveWoSurvey({
    required String code,
  });

  Future<BaseResult?> assignWOSurvey({
    required AssignAssociationBody body,
  });

  Future<BaseResult?> assignWoSiteSurvey({
    required AssignWoSiteSurveyBody body,
  });

  Future<BaseResult?> aggregateStatusWoSurvey({
    String? keyword,
    String? from,
    String? to,
    String? processStatus,
    String? status,
  });

  Future<BaseResult<AssociationEntity>?> createWoSurvey({
    required SaveWoBody body,
  });

  Future<BaseResult<ActWoDetail>?> getDetailAssociationWo({
    required String code,
  });

  Future<BaseResult?> createAssociation({
    required SaveAssociationBody body,
  });

  Future<BaseResult?> assignStaffAssociation({
    required AssignAssociationBody body,
  });

  Future<BaseResult?> getAssociationByStatus({
    String? keyword,
    String? from,
    String? to,
    List<String>? demandCode,
    List<String>? processStatus,
    List<String>? types,
  });

  Future<BaseResult?> insertProductService({
    required SaveActProductServiceBody body,
  });

  Future<BaseResult<ArrayResponse<ActWoDetail>?>> getListWoSurvey({
    String? keyword,
    int? page,
    int? pageSize,
    String? from,
    String? to,
    String? processStatus,
    String? status,
  });

  Future<BaseResult?> updateWoSiteSurvey({
    required SaveDemandWoBody body,
  });

  Future<BaseResult?> goToWorkSurvey({
    required String code,
  });

  Future<BaseResult<ArrayResponse<ActDemandFilterEntity>?>> getDemandFilter();

  Future<BaseResult?> getConfigOverdue({
    required String code,
  });

  Future<BaseResult<SolarEnergyEntity>?> getInfoSolarEnergy({
    required SolarEnergyBody body,
  });

  Future<BaseResult<List<SolarEnergyEntity>>?> compareSolarEnergy({
    required SolarEnergyBody body,
  });

  Future<BaseResult?> saveSolarEnergy({
    required SaveSolarEnergyBody body,
  });

  Future<BaseResult<Base64Entity>?> downloadSolarEnergy({
    required SaveSolarEnergyBody body,
  });

  Future<BaseResult?> updateAssociationCall({
    required String associationCode,
  });
}

class RequirementRepositoryImpl extends BaseRepository
    implements RequirementRepository {
  RequirementRepositoryImpl({
    required ApiClient apiClient,
  }) : _apiClient = apiClient;

  final ApiClient _apiClient;

  @override
  Future<BaseResult<ArrayResponse<ConfigEntity>>?> getConfig({
    String? type,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<ConfigEntity>>(
      _apiClient.getConfigEntity(
        type: type,
        page: page,
        size: size,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CampaignEntity>>> getListCampaign({
    int? page,
    int? pageSize,
    String? keyword,
    String? targetCustomerType,
  }) async {
    return await safeApiCall<ArrayResponse<CampaignEntity>>(
      _apiClient.getListCampaign(
        page: page,
        pageSize: pageSize,
        keyword: keyword,
        targetCustomerType: targetCustomerType,
      ),
    );
  }

  @override
  Future<BaseResult?> cancelCollection({
    required CancelCollectionBody body,
  }) async {
    return await safeApiCall(
      _apiClient.cancelCollection(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> completeCollection({
    required AnswerQuestionBody body,
  }) async {
    return await safeApiCall(
      _apiClient.completeCollection(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<CollectionInfoEntity>?> createCollection(
    CollectionBody body,
  ) async {
    return await safeApiCall<CollectionInfoEntity>(
      _apiClient.createCollection(body),
    );
  }

  @override
  Future<BaseResult<CollectionInfoEntity>?> getDetailCollection(
    String code,
  ) async {
    return await safeApiCall<CollectionInfoEntity>(
      _apiClient.getDetailCollection(code),
    );
  }

  @override
  Future<BaseResult<AssociationEntity>?> getDetailAssociation(
    String code,
  ) async {
    return await safeApiCall<AssociationEntity>(
      _apiClient.getDetailAssociation(code),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<QuestionCampaignEntity>>?>
      getListQuestionOfCampaign({
    String? campaignId,
  }) async {
    return await safeApiCall<ArrayResponse<QuestionCampaignEntity>>(
      _apiClient.getListQuestionOfCampaign(campaignId: campaignId),
    );
  }

  @override
  Future<BaseResult?> receiveCollection({
    required List<String> body,
  }) async {
    return await safeApiCall(
      _apiClient.receiveCollection(body: body),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<CollectionInfoEntity>>?> getListCollection({
    String? keySearch,
    String? campaignId,
    List<String>? status,
    String? customerType,
    List<String>? districtCodes,
    bool? isGetAll,
    String? assignment,
    int? page,
    int? size,
  }) async {
    return await safeApiCall<ArrayResponse<CollectionInfoEntity>>(
      _apiClient.getListCollection(
        keySearch: keySearch,
        page: page ?? BaseConstant.defaultPage,
        size: size ?? BaseConstant.defaultLimitSize,
        assignment: assignment,
        isGetAll: isGetAll ?? true,
        campaignId: campaignId,
        status: status,
        customerType: customerType,
        districtCodes: districtCodes,
      ),
    );
  }

  @override
  Future<BaseResult?> updateCollectionInfo({
    required SurveyCollectionBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateCollectionInfo(body: body),
    );
  }

  @override
  Future<BaseResult?> deleteImageCollection({
    required ImageCollectionBody body,
  }) async {
    return await safeApiCall(
      _apiClient.deleteImageCollection(body),
    );
  }

  @override
  Future<BaseResult<CountCollectionEntity>?> countCollectionStatus({
    String? keySearch,
    String? campaignId,
    List<String>? status,
    String? customerType,
    List<String>? districtCodes,
    bool? isGetAll,
  }) async {
    return await safeApiCall<CountCollectionEntity>(
      _apiClient.countCollectionStatus(
        keySearch: keySearch,
        campaignId: campaignId,
        status: status,
        customerType: customerType,
        districtCodes: districtCodes,
        isGetAll: isGetAll,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<AssociationEntity>?>> getListAssociation({
    String? from,
    String? to,
    List<String>? demandCode,
    List<String>? listStatus,
    List<String>? types,
    List<String>? processStatus,
    String? keyword,
    int? page,
    int? size,
    List<String>? sort,
  }) async {
    return await safeApiCall<ArrayResponse<AssociationEntity>?>(
      _apiClient.searchAssociation(
        from: from,
        to: to,
        demandCode: demandCode,
        listStatus: listStatus,
        types: types,
        processStatus: processStatus,
        keyword: keyword,
        page: page,
        size: size,
        sort: sort,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ActCustomerSourceEntity>>?>
      getAssociationCustomerSource({
    int? page,
    int? size,
    List<String>? sort,
  }) async {
    return await safeApiCall<ArrayResponse<ActCustomerSourceEntity>>(
      _apiClient.getAssociationCustomerSource(
        page: page,
        size: size,
        sort: sort,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ActServiceSupplyEntity>>?>
      getListAssociationService({
    int? page,
    int? size,
    List<String>? sort,
    String? keySearch,
  }) async {
    return await safeApiCall<ArrayResponse<ActServiceSupplyEntity>>(
      _apiClient.getAssociationService(
        page: page,
        size: size,
        sort: sort,
        keyword: keySearch,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ActServiceSupplyEntity>>?>
      getListAssociationSupply({
    int? page,
    int? size,
    List<String>? sort,
    String? keySearch,
  }) async {
    return await safeApiCall<ArrayResponse<ActServiceSupplyEntity>>(
      _apiClient.getAssociationProduct(
        page: page,
        size: size,
        sort: sort,
        keyword: keySearch,
      ),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ActChannelSourceEntity>>?>
      getAssociationChannel({
    int? page,
    int? size,
    List<String>? sort,
  }) async {
    return await safeApiCall<ArrayResponse<ActChannelSourceEntity>>(
      _apiClient.getAssociationChannel(
        page: page,
        size: size,
        sort: sort,
      ),
    );
  }

  @override
  Future<BaseResult?> receiveAssociation({
    required String code,
  }) async {
    return await safeApiCall(
      _apiClient.receiveAssociation(
        code: code,
      ),
    );
  }

  @override
  Future<BaseResult?> completeAssociation({
    required String code,
  }) async {
    return await safeApiCall(
      _apiClient.completeAssociation(
        code: code,
      ),
    );
  }

  @override
  Future<BaseResult?> updateCustomerDemand({
    required AssociationCustomerDemandBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateCustomerDemand(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> updateCustomerInfo({
    required AssociationCustomerInfoBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateCustomerInfo(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> receiveWoSurvey({
    required String code,
  }) async {
    return await safeApiCall(
      _apiClient.receiveWoSurvey(
        code: code,
      ),
    );
  }

  @override
  Future<BaseResult?> assignWOSurvey({
    required AssignAssociationBody body,
  }) async {
    return await safeApiCall(
      _apiClient.assignWOSurvey(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> assignWoSiteSurvey({
    required AssignWoSiteSurveyBody body,
  }) async {
    return await safeApiCall(
      _apiClient.assignWoSiteSurvey(body: body),
    );
  }

  @override
  Future<BaseResult?> aggregateStatusWoSurvey({
    String? keyword,
    String? from,
    String? to,
    String? processStatus,
    String? status,
  }) async {
    return await safeApiCall(
      _apiClient.aggregateStatusWoSurvey(
        keyword: keyword,
        from: from,
        to: to,
        processStatus: processStatus,
        status: status,
      ),
    );
  }

  @override
  Future<BaseResult<AssociationEntity>?> createWoSurvey({
    required SaveWoBody body,
  }) async {
    return await safeApiCall(
      _apiClient.createWoSurvey(body: body),
    );
  }

  @override
  Future<BaseResult<ActWoDetail>?> getDetailAssociationWo({
    required String code,
  }) async {
    return await safeApiCall(
      _apiClient.getDetailAssociationWo(code: code),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ActDemandFilterEntity>?>> getDemandWoSurvey({
    required String code,
    int? page,
    int? pageSize,
    String? keySearch,
  }) async {
    return await safeApiCall<ArrayResponse<ActDemandFilterEntity>?>(
      _apiClient.getDemandWoSurvey(
        code: code,
        page: page,
        size: pageSize,
        keyword: keySearch,
      ),
    );
  }

  @override
  Future<BaseResult?> createAssociation({
    required SaveAssociationBody body,
  }) async {
    return await safeApiCall<AssociationEntity>(
      _apiClient.createAssociation(body: body),
    );
  }

  @override
  Future<BaseResult?> assignStaffAssociation({
    required AssignAssociationBody body,
  }) async {
    return await safeApiCall(
      _apiClient.assignAssociation(body: body),
    );
  }

  @override
  Future<BaseResult?> getAssociationByStatus({
    String? keyword,
    String? from,
    String? to,
    List<String>? demandCode,
    List<String>? processStatus,
    List<String>? types,
  }) async {
    return await safeApiCall(
      _apiClient.getAssociationStatus(
        keyword: keyword,
        from: from,
        to: to,
        demandCode: demandCode,
        processStatus: processStatus,
        types: types,
      ),
    );
  }

  @override
  Future<BaseResult?> insertProductService({
    required SaveActProductServiceBody body,
  }) async {
    return await safeApiCall(
      _apiClient.insertProductService(body: body),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ActWoDetail>?>> getListWoSurvey({
    String? keyword,
    int? page,
    int? pageSize,
    String? from,
    String? to,
    String? processStatus,
    String? status,
  }) async {
    return await safeApiCall(
      _apiClient.getListWoSurvey(
        keyword: keyword,
        page: page,
        size: pageSize,
        from: from,
        to: to,
        processStatus: processStatus,
        status: status,
      ),
    );
  }

  @override
  Future<BaseResult?> updateWoSiteSurvey({
    required SaveDemandWoBody body,
  }) async {
    return await safeApiCall(
      _apiClient.updateWoSiteSurvey(body: body),
    );
  }

  @override
  Future<BaseResult?> goToWorkSurvey({
    required String code,
  }) async {
    return await safeApiCall(
      _apiClient.startSiteWOSurvey(code: code),
    );
  }

  @override
  Future<BaseResult<ArrayResponse<ActDemandFilterEntity>?>>
      getDemandFilter() async {
    return await safeApiCall(
      _apiClient.getDemandFilter(),
    );
  }

  @override
  Future<BaseResult<ConfigEntity>?> getConfigOverdue({
    required String code,
  }) async {
    return await safeApiCall(
      isShowError: false,
      _apiClient.getConfigOverdue(code: code),
    );
  }

  @override
  Future<BaseResult<SolarEnergyEntity>?> getInfoSolarEnergy({
    required SolarEnergyBody body,
  }) async {
    return await safeApiCall(
      _apiClient.getInfoSolarEnergy(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<List<SolarEnergyEntity>>?> compareSolarEnergy({
    required SolarEnergyBody body,
  }) async {
    return await safeApiCall(
      _apiClient.compareSolarEnergy(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> saveSolarEnergy({
    required SaveSolarEnergyBody body,
  }) async {
    return await safeApiCall(
      _apiClient.saveSolarEnergy(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult<Base64Entity>?> downloadSolarEnergy({
    required SaveSolarEnergyBody body,
  }) async {
    return await safeApiCall(
      _apiClient.downloadSolarEnergy(
        body: body,
      ),
    );
  }

  @override
  Future<BaseResult?> updateAssociationCall({
    required String associationCode,
  }) async {
    return await safeApiCall(
      _apiClient.updateAssociationCall(
        associationCode: associationCode,
      ),
    );
  }
}
