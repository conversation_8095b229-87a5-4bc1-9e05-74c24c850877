import 'dart:convert';
import 'dart:io';
import 'package:crypto/crypto.dart';
import 'package:diacritic/diacritic.dart';
import 'package:intl/intl.dart';

import '../app_configs/constants.dart';

class StringUtils {
  static final _formatter = NumberFormat.currency(
    symbol: "đ",
    decimalDigits: 0,
    customPattern: "##,###,###¤",
  );

  static final _numberFormatter = NumberFormat.currency(
    symbol: "",
    decimalDigits: 0,
    customPattern: "##,###,###",
  );

  static String formatMoney(num value) {
    return _formatter.format(value);
  }

  static String displayMoney(num value) {
    return NumberFormat.currency(
      symbol: "",
      decimalDigits: 0,
      customPattern: "##,###,###¤",
    ).format(value);
  }

  static String formatNumber(num value) {
    return _numberFormatter.format(value);
  }

  static String formatNumberFlexible(num value) {
    final formatter = NumberFormat("#,##0.##", "vi_VN");
    return "${formatter.format(value)}đ";
  }

  static String formatDiscountMoney(int value) {
    if ((value / 1000).round() > 0) {
      return "-đ${(value / 1000).round()}K";
    }
    return "-đ$value";
  }

  static String formatFlexibleDouble(double value) {
    final number = NumberFormat("#,##0.###");
    return "${number.format(value)}đ";
  }

  static DateTime formatDate(String? val, String format) {
    if (val == null) return DateTime.now();
    return DateFormat(format).parse(val);
  }

  static String dateToStringFormat(DateTime val, String format) {
    final DateFormat formatter = DateFormat(format);
    return formatter.format(val);
  }

  static String dateToStringFormatWithJms(DateTime val, String format) {
    final DateFormat formatter = DateFormat(format);
    return formatter.add_jms().format(val);
  }

  static String dateToStringFormatWithUTC(DateTime val, String format) {
    final DateFormat formatter = DateFormat(format);
    return formatter.format(val.toLocal());
  }

  static bool validateFile(fileName) {
    bool fileAccepted;
    // or check mimeType
    final rgx = RegExp(r'[-_. A-Za-z\d]');

    if (!rgx.hasMatch(fileName)) {
      fileAccepted = false;
    } else {
      if (fileName!.endsWith("png") ||
          fileName.endsWith("gif") ||
          fileName.endsWith("jpg") ||
          fileName.endsWith("jpeg") ||
          fileName.endsWith("webp") ||
          fileName.endsWith("mp4") ||
          fileName.endsWith("mov")) {
        fileAccepted = true;
      } else {
        fileAccepted = false;
      }
    }

    return fileAccepted;
  }

  static String transformImageLink(
      {required String img, required String relative}) {
    if (img.startsWith("https://") || img.startsWith("http://")) {
      return img.replaceAll("///", "/");
    }

    return relative;
  }

  static String getNameOfDay(DateTime value) {
    final day = DateTime(value.year, value.month, value.day);
    final timeNow = DateTime.now();
    final dayCompare = DateTime(timeNow.year, timeNow.month, timeNow.day);
    final different = dayCompare.difference(day).inDays;
    if (different == 0) {
      return "Hôm nay";
    } else if (different == 1) {
      return "Hôm qua";
    } else if (different == -1) {
      return "Ngày mai";
    } else {
      return DateFormat.yMEd().add_jms().format(value);
    }
  }

  static String paddingZero(String val, int? length) {
    return val.toString().padLeft(length ?? 2, '0');
  }

  static String maskPhoneNumber(String phoneNumber) {
    if (phoneNumber.length < 3) {
      return phoneNumber;
    }
    String lastThreeDigits = phoneNumber.substring(phoneNumber.length - 3);
    String maskedPart = '*' * (phoneNumber.length - 3);
    return maskedPart + lastThreeDigits;
  }

  static String maskAddress(String address) {
    String rs = address;
    List<String> parts = address.split(',');
    if (parts.length > 4) {
      rs = "***, ${parts.sublist(0, parts.length - 3).join(',')}";
    } else {
      int firstCommaIndex = address.indexOf(',');
      rs = firstCommaIndex != -1
          ? "***${address.substring(firstCommaIndex)}"
          : "***";
    }
    return rs;
  }

  static String dateStringWithDay({
    required String date,
    required String oldFormat,
    String? newFormat = 'yyyy-MM-dd',
  }) {
    if (date.isEmpty) {
      return "";
    }
    var inputFormat = DateFormat(oldFormat);
    return DateFormat(newFormat, BaseConstant.localeVi)
        .format(inputFormat.parse(date));
  }

  static String dateToString({
    required String timestamp,
    String? format = "dd/MM/yyyy HH:mm",
  }) {
    DateTime dateTime = DateTime.parse(timestamp);
    String formattedDate = DateFormat(format).format(dateTime);
    return formattedDate;
  }

  static String capitalizeEachWord(String text) {
    if (text.isEmpty) return text;
    text = text.replaceAll(RegExp(r'\s+'), ' ');
    return text
        .trim()
        .split(' ')
        .map((word) => toBeginningOfSentenceCase(word))
        .join(' ');
  }

  static String truncate(String text, int length) {
    String rs = "${text.substring(0, length)}...";
    return rs;
  }

  static fileToBase64(File file) {}

  static String formatInputNumber(String input) {
    return input.replaceAll(RegExp(r'[^0-9]'), '');
  }

  static String formatRegister(String input) {
    String cleaned = input.replaceAll(RegExp(r'\s+'), ' ');
    List<String> parts = cleaned.split(' ');
    return parts.map((e) {
      if (e.isEmpty) return '';
      return e[0].toUpperCase() + e.substring(1);
    }).join(' ');
  }

  static String removeSpecialCharacters(String input) {
    // Loại bỏ tất cả ký tự đặc biệt, bao gồm cả dấu gạch dưới (_)
    final regex = RegExp(r'[^\w\s]|_');
    return input.replaceAll(regex, '');
  }

  static String formatCustomerName(String name) {
    return removeDiacritics(name).replaceAll(' ', '').toUpperCase();
  }

  static String encodeCharacter(String text) {
    var bytes = utf8.encode(text);
    var digest = sha256.convert(bytes);
    return digest.toString();
  }

  static String formatFullName(String input) {
    String cleaned = input.replaceAll(RegExp(r'\s+'), ' ');
    List<String> parts = cleaned.split(' ');
    return parts.map((e) {
      if (e.isEmpty) return '';
      return e[0].toUpperCase() + e.substring(1);
    }).join(' ');
  }
}
