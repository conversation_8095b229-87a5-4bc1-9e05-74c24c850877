abstract class RouterPaths {
  static const String splash = '/';
  static const String root = '/root';

  static const String webView = '/webView';
  static const String webViewDocument = '/webViewDocument';
  static const String imagesPreview = '/imagesPreview';
  static const String imagePreview = '/imagePreview';

  static const String login = '/login';
  static const String register = '/register';
  static const String forgotPassword = '/forgotPassword';
  static const String setupAccount = '/setupAccount';
  static const String verifyOtp = '/verifyOtp';
  static const String changePassword = '/changePassword';
  static const String userInfo = '/userInfo';
  static const String chatBot = '/chatBot';
  static const String permissionSetting = '/permissionSetting';

  static const String allProducts = '/allProducts';
  static const String allProductsAgency = '/allProductsAgency';
  static const String productDetail = '/productDetail';
  static const String productInfo = '/productInfo';
  static const String checkInventory = '/checkInventory';
  static const String searchStore = '/searchStore';
  static const String createProductOrder = '/createProductOrder';
  static const String createServiceOrder = '/createServiceOrder';
  static const String saveContactInfo = '/saveContactInfo';
  static const String allServices = '/allServices';
  static const String orderSuccess = '/orderSuccess';
  static const String paymentSuccess = '/paymentSuccess';

  static const String selectVoucher = '/selectVoucher';
  static const String detailVoucher = '/detailVoucher';

  static const String listSingleService = '/listSingleService';
  static const String listPartnerService = '/listPartnerService';
  static const String searchSingleService = '/searchSingleService';
  static const String historyService = '/historyService';
  static const String listPackageService = '/listPackageService';
  static const String searchPackageService = '/searchPackageService';

  static const String serviceInfo = '/serviceInfo';
  static const String selectAddress = '/selectAddress';
  static const String managementUnit = '/managementUnit';
  static const String addSeller = '/addSeller';
  static const String addProduct = '/addProduct';
  static const String choosePaymentProvider = '/choosePaymentProvider';
  static const String allOrder = '/allOrder';
  static const String notification = '/notification';

  static const String createCustomerInfo = '/createCustomerInfo';
  static const String createCustomerInfoSuccess = '/createCustomerInfoSuccess';
  static const String selectCampaign = '/selectCampaign';
  static const String detailInfoCustomerCollection =
      '/detailInfoCustomerCollection';
  static const String collectionInfoWarehouse = '/collectionInfoWarehouse';
  static const String customerSurveyForm = '/customerSurveyForm';
  static const String impactHistory = '/impactHistory';
  static const String chooseDistrict = '/chooseDistrict';
  static const String chooseGoods = '/chooseGoods';

  static const String detailNotification = '/detailNotification';

  static const String allAssociation = '/allAssociation';
  static const String createAssociation = '/createAssociation';
  static const String associationDetail = '/associationDetail';
  static const String cus360 = '/cus360';
  static const String detailContract = '/detailContract';
  static const String selectTypeAssociation = '/selectTypeAssociation';
  static const String selectAssociationCustomer = '/selectAssociationCustomer';
  static const String createAssociationSuccess = '/createAssociationSuccess';
  static const String selectAssociationSupply = '/selectAssociationSupply';
  static const String selectAssociationService = '/selectAssociationService';
  static const String selectPriceSolarEnergy = '/selectPriceSolarEnergy';
  static const String priceSolarEnergyDetail = '/priceSolarEnergyDetail';
  static const String allAssociationWO = '/allAssociationWO';
  static const String associationWODetail = '/associationWODetail';
  static const String selectDemandAssociation = '/selectDemandAssociation';
  static const String editAssociationSuccess = '/editAssociationSuccess';
  static const String onePayView = '/onePayView';

  static const String debtStatistics = '/debtStatistics';
  static const String createRequestBuyProduct = '/createRequestBuyProduct';
  static const String requestBuyProduct = '/requestBuyProduct';
  static const String addSupplyRequest = '/addSupplyRequest';
  static const String detailRequestBuyProduct = '/detailRequestBuyProduct';
  static const String confirmCommission = '/confirmCommission';
  static const String bill = '/bill';
  static const String billDetail = '/billDetail';
  static const String billUpdate = '/billUpdate';
  static const String calendar = '/calendar';
  static const String explanation = '/explanation';
  static const String stockExport = '/stockExport';
  static const String myStockExport = '/myStockExport';
  static const String personalInventory = '/personalInventory';

  // static const String detailOrder = '/detailOrder';
  static const String searchSupplies = '/searchSupplies';
  static const String addSupplies = '/addSupplies';
  static const String addSuppliesSalePoint = '/addSuppliesSalePoint';
  static const String addSuppliesSalePointCombo = '/addSuppliesSalePointCombo';
  static const String warehousePersonal = '/warehousePersonal';
  static const String noteWarehouse = '/noteWarehouse';
  static const String confirmationBill = '/confirmationBill';
  static const String detailStock = '/detailStock';
  static const String supplyInfo = '/supplyInfo';
  static const String requestDistrictWarehouse = '/requestDistrictWarehouse';
  static const String exportDistrictWarehouse = '/exportDistrictWarehouse';
  static const String requestBranchWarehouse = '/requestBranchWarehouse';
  static const String exportBranchWarehouse = '/exportBranchWarehouse';
  static const String transferEmployee = '/transferEmployee';
  static const String transferEmployeeImplement = '/transferEmployeeImplement';
  static const String addSupply = '/addSupply';
  static const String returnSupply = '/returnSupply';
  static const String selectSerial = '/selectSerial';
  static const String orderShippingDetail = '/orderShippingDetail';
  static const String orderSingleServiceDetail = '/orderSingleServiceDetail';
  static const String orderInstallationDetail = '/orderInstallationDetail';
  static const String selectFieldOfWorkPage = '/selectFieldOfWorkPage';
  static const String requestCancelPage = '/requestCancelPage';
  static const String requestCancelReceivePage = '/requestCancelReceivePage';
  static const String requestSchedulePage = '/requestSchedulePage';
  static const String selectOriginalPricePage = '/selectOriginalPricePage';
  static const String selectPriceAndSerialPage = '/selectPriceAndSerialPage';
  static const String checklistTaskPage = '/checklistTaskPage';
  static const String signContractOrder = '/signContractOrder';
  static const String signContractRegister = '/signContractRegister';
  static const String viewFileContract = '/viewFileContract';
  static const String viewPdf = '/viewPdf';
  static const String billOrder = '/billOrder';
  static const String selectPackageService = '/selectPackageService';
  static const String detailBranch = '/detailBranch';
  static const String checklistService = '/checklistService';
  static const String selectServicePage = '/selectServicePage';
  static const String editOrderItemPage = '/editOrderItemPage';
  static const String orderServicePartnerDetail = '/orderServicePartnerDetail';
  static const String warningOrder = '/warningOrder';

  static const String aioAddSeller = '/aioAddSeller';
  static const String aioContract = '/aioContract';
  static const String aioContractCreate = '/aioContractCreate';
  static const String aioContractDetail = '/aioContractDetail';
  static const String aioContractDetailPerformer =
      '/aioContractDetailPerformer';
  static const String aioAddPackage = '/aioAddPackage';
  static const String aioAddService = '/aioAddService';
  static const String aioContractPackage = '/aioContractPackage';
  static const String aioRequestCancelPage = '/aioRequestCancelPage';
  static const String aioCheckInventory = '/aioCheckInventory';
  static const String aioBillDetail = '/aioBillDetail';
  static const String listStockGoodPage = '/listStockGoodPage';
  static const String updateContractRealPage = '/updateContractRealPage';
  static const String createReportError = '/create_report_error';
  static const String editReportError = '/edit_report_error';
  static const String allReportError = '/all_report_error';
  static const String detailReportError = '/detail_report_error';
  static const String duplicateReportError = '/duplicate_report_error';
  static const String listErrorInstructions = '/list_error_instruction';
  static const String detailErrorInstructions = '/detail_error_instruction';
  static const String selectGroupError = '/select_group_error';
  static const String viewDocumentCim = '/viewDocumentCim';
  static const String feedbackError = '/feedbackError';
  static const String explanationDetail = '/explanationDetail';
  static const String explanationHistory = '/explanationHistory';

  // warranty
  static const String searchWarranty = '/searchWarranty';
  static const String listWarranty = '/listWarranty';
  static const String detailWarranty = '/detailWarranty';
  static const String listWarrantyCenter = '/listWarrantyCenter';
  static const String scanQRCode = '/scanQRCode';

  // complain
  static const String complain = '/complain';
  static const String createComplain = '/create_complain';
  static const String detailComplain = '/detail_complain';
  static const String contractComplain = '/contractComplain';
  static const String historyComplain = '/historyComplain';
  static const String serialContractComplain = '/serialContractComplain';
  static const String interactCustomerComplain = '/interactCustomerComplain';
  static const String signContractComplain = '/signContractComplain';

  //sales team
  static const String addSaleMember = '/addSaleMember';
  static const String confirmSaleMember = '/confirmSaleMember';
  static const String saleTeamDetail = '/saleTeamDetail';
  static const String saleTeamHome = '/saleTeamHome';
  static const String takeImageIdentifier = '/takeImageIdentifier';
  static const String sendRequestTeam = '/sendRequestTeam';
  static const String saleMemberInfo = '/saleMemberInfo';

  // requirement warranty
  static const String requirementWarranty = '/requirementWarranty';
  static const String appointmentSuggestion = '/appointmentSuggestion';
  static const String createRequirementWarranty = '/createRequirementWarranty';
  static const String addProductRequirementWarranty =
      '/addProductRequirementWarranty';
  static const String detailRequirementWarrantyClaim =
      '/detailRequirementWarrantyClaim';
  static const String addSuppliesRequirementWarrantyClaim =
      '/addSuppliesRequirementWarrantyClaim';
  static const String requestWarehouseMaterials = '/requestWarehouseMaterials';
  static const String historyRequirementWarranty =
      '/historyRequirementWarranty';
  static const String historyOrderAgency = '/historyOrderAgency';
  static const String manageCollaborator = '/manageCollaborator';

  // shopping cart
  static const String shoppingCart = '/shoppingCart';
  static const String productDetailAgency = '/productDetailAgency';
  static const String requestOrder = '/requestOrder';
  static const String addProductAgency = '/addProductAgency';
  static const String orderAgencyManagementDetail =
      '/orderAgencyManagementDetail';
  static const String searchProductAgency = '/searchProductAgency';
  static const String createSuccessOrderInfo = '/createSuccessOrderInfo';
  static const String workAgencyPage = '/workAgencyPage';
  static const String updateInvoice = '/updateInvoice';
  static const String signContractInvoice = '/signContractInvoice';

  // Customer Quote
  static const String customerQuoteHome = '/customerQuoteHome';
  static const String customerQuoteDetail = '/customerQuoteDetail';
  static const String customerQuoteHistory = '/customerQuoteHistory';
  static const String customerQuoteCreate = '/customerQuoteCreate';
  static const String selectPaymentQuote = '/selectPaymentQuote';
  static const String editProductQuote = '/editProductQuote';
  static const String availableProductsQuote = '/availableProductsQuote';

  //feedback
  static const String createFeedback = '/createFeedback';
  static const String listFeedback = '/listFeedback';
  static const String detailFeedback = '/detailFeedback';
  static const String customerFeedback = '/customerFeedback';

  // contract draft
  static const String contractDraftPage = '/contractDraftPage';
  static const String contractDraftDetailPage = '/contractDraftDetailPage';
  static const String createContractDraftPage = '/createContractDraftPage';
  static const String addProductServicePage = '/addProductServicePage';
  static const String changeApprovedLeader = '/changeApprovedLeader';
  static const String createBusinessPlanPage = '/createBusinessPlanPage';
  static const String changeApproveLeader = '/changeApproveLeader';
  static const String previewDraftContract = '/previewDraftContract';
  static const String signDraftContractRegister = '/signDraftContractRegister';
  static const String previewContractAppendix = '/previewContractAppendix';
  static const String historyContractDraft = '/historyContractDraft';

  // Timekeeping
  static const String timekeeping = '/timekeeping';
  static const String detailRequestTimekeeping = '/detailRequestTimekeeping';
  static const String createOrUpdateRequestTimekeeping =
      '/createOrUpdateRequestTimekeeping';
}
