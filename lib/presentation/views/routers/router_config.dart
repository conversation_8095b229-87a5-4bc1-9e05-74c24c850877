import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/app_configs/coordinator.dart';
import 'package:vcc/app_configs/time_tracking_observer.dart';
import 'package:vcc/presentation/views/bottom_sheet/filer_collection/widgets/choose_district/choose_district_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_association_channel/select_association_channel_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_association_customer/select_association_customer_view.dart';
import 'package:vcc/presentation/views/features/agency/add_product_manage/add_product_manage_page.dart';
import 'package:vcc/presentation/views/features/agency/all_products_agency/all_products_agency_page.dart';
import 'package:vcc/presentation/views/features/agency/history_order_agency/history_order_agency_page.dart';
import 'package:vcc/presentation/views/features/agency/manage/detail_order_agency_manage/order_agency_management_detail_page.dart';
import 'package:vcc/presentation/views/features/agency/product_detail_agency/product_detail_agency_page.dart';
import 'package:vcc/presentation/views/features/agency/search_store_agency/search_store_agency_page.dart';
import 'package:vcc/presentation/views/features/agency/shopping_cart/shopping_cart_page.dart';
import 'package:vcc/presentation/views/features/agency/sign_contract_invoice/sign_contract_invoice_page.dart';
import 'package:vcc/presentation/views/features/agency/work_order_agency/work_agency_page.dart';
import 'package:vcc/presentation/views/features/agency/update_invoice/update_invoice_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/add_package/add_package_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/add_service/add_service_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_add_seller/aio_add_seller_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_bill_detail/aio_bill_detail_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_check_inventory/aio_check_inventory_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_create/aio_contract_create_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_detail/aio_contract_detail_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_detail_performer/aio_contract_detail_performer_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_package/aio_contract_package_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_request_cancel/aio_request_cancel_screen.dart';
import 'package:vcc/presentation/views/features/aio_contract/list_stock_good/list_stock_good_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/update_contract_real/update_contract_real_page.dart';
import 'package:vcc/presentation/views/features/authentication/change_password/change_password_page.dart';
import 'package:vcc/presentation/views/features/authentication/forgot_password/forgot_password_page.dart';
import 'package:vcc/presentation/views/features/authentication/login/login_page.dart';
import 'package:vcc/presentation/views/features/authentication/setup_account/widget/select_goods/select_goods.dart';
import 'package:vcc/presentation/views/features/authentication/setup_account/setup_account_page.dart';
import 'package:vcc/presentation/views/features/authentication/sign_contract_register/sign_contract_register.dart';
import 'package:vcc/presentation/views/features/authentication/sign_up/register_page.dart';
import 'package:vcc/presentation/views/features/authentication/verify_otp/verify_otp_page.dart';
import 'package:vcc/presentation/views/features/chatbot/chatbot_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/add_product_service/add_product_service_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/change_approved_leader/change_approve_leader_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/contract_draft_detail_page/contract_draft_detail_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/contract_draft_page/contract_draft_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/create_business_plan_page/create_business_plan_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/create_contract_draft_page/create_contract_draft_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/history_draft_contract/history_draft_contract_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/preview_draft_contract_customer/preview_draft_contract_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/preview_contract_appendix/preview_contract_appendix_page.dart';
import 'package:vcc/presentation/views/features/cus360/cus360_page.dart';
import 'package:vcc/presentation/views/features/cus360/customer_rating_feedback/feedback_widget.dart';
import 'package:vcc/presentation/views/features/cus360/detail_contract/detail_contract_page.dart';
import 'package:vcc/presentation/views/features/document/document_screen.dart';
import 'package:vcc/presentation/views/features/feedback/create_feedback/create_feedback_page.dart';
import 'package:vcc/presentation/views/features/feedback/detail_feedback/detail_feedback_page.dart';
import 'package:vcc/presentation/views/features/feedback/list_feedback/list_feedback_page.dart';
import 'package:vcc/presentation/views/features/notification/detail_notification/detail_notification_page.dart';
import 'package:vcc/presentation/views/features/notification/notification_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/add_seller/add_seller_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_product_order/add_product/add_product_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_product_order/create_product_order_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/add_supplies/add_supplies_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/create_service_order_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/detail_voucher/detail_voucher_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/search_supplies/search_supplies_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/select_voucher/select_voucher_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/order_success/order_success_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment/one_pay_view.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment_provider/payment_provider_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment_success/payment_success_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/add_supply_combo_sale_point/add_supply_sale_point_combo_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/add_supply_sale_point/add_supplies_sale_point_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/bill_order/bill_order_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/cancel_reveive/cancel_receive_screen.dart';
import 'package:vcc/presentation/views/features/order/detail_order/checklist_service/checklist_service_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/checklist_task/checklist_task_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/edit_order_item/edit_order_item_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_installation/order_installation_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_shipping_detail/order_shipping_detail_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_shipping_detail/widgets/select_field_of_work_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/order_single_service_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/request_cancel/request_cancel_screen.dart';
import 'package:vcc/presentation/views/features/order/detail_order/request_schedule/request_schedule_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/select_original_price/select_original_price_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/select_package_service/select_package_service_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/select_price_and_serial/select_price_and_serial_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/select_service/select_service_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/sign_contract_order/sign_contract_order_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/view_contract_file/view_contract_file.dart';
import 'package:vcc/presentation/views/features/order/detail_order/warning_order/warning_order_page.dart';
import 'package:vcc/presentation/views/features/order/request_order/request_order_page.dart';
import 'package:vcc/presentation/views/features/profile/settings/permission_setting/permission_setting_page.dart';
import 'package:vcc/presentation/views/features/profile/user_info/user_info_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/add_product/add_product_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/add_supplies_requirement_warranty/add_supplies_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/create_requirement_warranty/create_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/detail_requirement_warranty/appointment_suggestion_page/appointment_suggestion_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/detail_requirement_warranty/detail_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/history_requirement_warranty/history_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/request_warehouse_materials/request_warehouse_materials_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/requirement_warranty_home/requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/root/root_page.dart';
import 'package:vcc/presentation/views/features/store/product/all_products/all_products_page.dart';
import 'package:vcc/presentation/views/features/store/product/product_detail/product_detail_page.dart';
import 'package:vcc/presentation/views/features/store/product/product_info/product_info_page.dart';
import 'package:vcc/presentation/views/features/store/service/list_package_service/list_package_service_page.dart';
import 'package:vcc/presentation/views/features/store/service/partner_service/partner_service_page.dart';
import 'package:vcc/presentation/views/features/store/service/single_service/single_service_page.dart';
import 'package:vcc/presentation/views/features/timekeeping/create_or_update_request_timekeeping/create_or_update_request_timekeeping_page.dart';
import 'package:vcc/presentation/views/features/timekeeping/detail_request_timekeeping/detail_request_timekeeping_page.dart';
import 'package:vcc/presentation/views/features/timekeeping/timekeeping_page.dart';
import 'package:vcc/presentation/views/features/work/association/association_create/association_create_page.dart';
import 'package:vcc/presentation/views/features/work/association/association_detail/association_detail_page.dart';
import 'package:vcc/presentation/views/features/work/association/association_wo_detail/association_wo_detail_page.dart';
import 'package:vcc/presentation/views/features/work/association/association_wo_list/association_wo_list_page.dart';
import 'package:vcc/presentation/views/features/work/association/create_association_success/create_association_success_page.dart';
import 'package:vcc/presentation/views/features/work/association/edit_association_success/edit_association_success_page.dart';
import 'package:vcc/presentation/views/features/work/association/list_association/list_association_page.dart';
import 'package:vcc/presentation/views/features/work/association/price_solar_energy_detail/price_solar_energy_detail_page.dart';
import 'package:vcc/presentation/views/features/work/association/select_association_demand/select_association_demand_view.dart';
import 'package:vcc/presentation/views/features/work/association/select_association_service/select_association_service_view.dart';
import 'package:vcc/presentation/views/features/work/association/select_association_supply/select_association_supply_view.dart';
import 'package:vcc/presentation/views/features/work/association/select_price_solar_energy/select_price_solar_energy_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/collection_info_warehouse/collection_info_warehouse_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/create_customer_info/create_customer_info_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/create_customer_info_success/create_customer_info_success_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/customer_survey_form/customer_survey_form_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/detail_info_customer_collection/detail_info_customer_collection_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/select_campaign/select_campaign_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/available_products_quote/available_products_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/create_customer_quote/create_customer_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/customer_quote_home/customer_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/detail_customer_quote/detail_customer_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/edit_product_quote/edit_product_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/history_customer_quote/history_customer_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/select_payment_quote/select_payment_quote_page.dart';
import 'package:vcc/presentation/views/features/work/explanation/explanation_history/explanation_history_page.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/complain_detail_page.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/interact_customer_page/interact_customer_page.dart';
import 'package:vcc/presentation/views/features/work/manage_collaborator/manage_collaborator_page/manage_collaborator_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/create_report_error/create_report_error_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/detail_error_instruction/detail_error_instructions_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/detail_report_error/detail_report_error_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/duplicate_error/duplicate_report_error_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/edit_report_error/edit_report_error_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/feedback_error/feedback_error_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/list_error_handling_instructions/list_error_handling_instructions_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/list_report_error/list_report_error_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/select_group_error/select_group_error_page.dart';
import 'package:vcc/presentation/views/features/work/report_error/view_document_cim/view_document_cim.dart';
import 'package:vcc/presentation/views/features/work/sales_team/add_sale_member/add_sale_member_page.dart';
import 'package:vcc/presentation/views/features/work/sales_team/confirm_sale_member/confirm_sale_member_page.dart';
import 'package:vcc/presentation/views/features/work/sales_team/sale_member_info/sale_member_info_page.dart';
import 'package:vcc/presentation/views/features/work/sales_team/sale_team_detail/sale_team_detail_page.dart';
import 'package:vcc/presentation/views/features/work/sales_team/sale_team_home/sale_team_home_page.dart';
import 'package:vcc/presentation/views/features/work/sales_team/send_request_team/send_request_team_page.dart';
import 'package:vcc/presentation/views/features/work/sales_team/take_image_identifier/take_image_identifier_page.dart';
import 'package:vcc/presentation/views/features/work/warranty/detail_warranty/detail_warranty_page.dart';
import 'package:vcc/presentation/views/features/work/warranty/list_warranty/list_warranty_page.dart';
import 'package:vcc/presentation/views/features/work/warranty/list_warranty_center/list_warranty_center_page.dart';
import 'package:vcc/presentation/views/features/work/warranty/search_warranty/search_warranty_page.dart';
import 'package:vcc/presentation/views/widgets/create_order_success_page.dart';
import 'package:vcc/presentation/views/widgets/pages/pdf_view_screen.dart';
import 'package:vcc/presentation/views/widgets/pages/scan_qr_code_screen.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/features/splash/splash_page.dart';
import 'package:vcc/presentation/views/features/store/save_contact_info/save_contact_info_page.dart';
import 'package:vcc/presentation/views/features/store/search_store/search_store_page.dart';
import 'package:vcc/presentation/views/widgets/pages/select_management_unit_page.dart';
import 'package:vcc/presentation/views/widgets/pages/web_view_screen.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_detail/bill_detail_page.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_page.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_update/bill_update_page.dart';
import 'package:vcc/presentation/views/features/work/calendar/calendar_page.dart';
import 'package:vcc/presentation/views/features/work/confirm_commission/confirm_commission_page.dart';
import 'package:vcc/presentation/views/features/work/debt_statistics/debt_statistics_page.dart';
import 'package:vcc/presentation/views/features/work/debt_statistics/detail_branch/detail_branch_page.dart';
import 'package:vcc/presentation/views/features/work/explanation/explanation_page.dart';
import 'package:vcc/presentation/views/features/work/impact_history/impact_history_page.dart';
import 'package:vcc/presentation/views/features/order/list_order/list_order_page.dart';
import 'package:vcc/presentation/views/features/work/request_buy_product/add_supply_request/add_supply_request_page.dart';
import 'package:vcc/presentation/views/features/work/request_buy_product/create_request_buy_product/create_request_buy_product_page.dart';
import 'package:vcc/presentation/views/features/work/request_buy_product/detail_request_buy_product/detail_request_buy_product_page.dart';
import 'package:vcc/presentation/views/features/work/request_buy_product/request_buy_product_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/confirmation_bill/confirmation_bill_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/detail_stock/detail_stock_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/add_supply/add_supply_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/export_branch_warehouse/export_branch_warehouse_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/export_district_warehouse/export_district_warehouse_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/my_stock_export/my_stock_export_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/request_branch_warehouse/request_branch_warehouse_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/request_district_warehouse/request_district_warehouse_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/return_supply/return_supply_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/select_serial/select_serial_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/stock_export_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/supply_info/supply_info_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/transfer_employee/transfer_employee_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/transfer_employee_impl/transfer_employee_implement_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/warehouse_personal/warehouse_personal_page.dart';
import 'package:vcc/presentation/views/widgets/image_preview.dart';
import 'package:vcc/presentation/views/widgets/image_view/images_preview_page.dart';

import '../features/work/explanation/explanation_detail/explanation_detail_page.dart';
import '../features/work/complain/complain_page.dart';
import '../features/work/complain/create_complain/create_complain_page.dart';
import '../features/work/complain/detail_complain/sign_contract_complain/sign_contract_complain_page.dart';
import '../features/work/complain/history_complain/complain_history_page.dart';
import '../features/work/complain/select_serial_complain/select_serial_complain_page.dart';
import '../features/work/complain/select_serial_complain/select_serial_complain_view_model.dart';
import 'paths.dart';

final TimeTrackingObserver timeTrackingObserver = TimeTrackingObserver();

final goRouterConfiguration = GoRouter(
  initialLocation: RouterPaths.splash,
  debugLogDiagnostics: true,
  redirect: _guard,
  navigatorKey: AppCoordinator.navigatorKey,
  routes: [
    ///========== start =========//
    GoRoute(
      path: RouterPaths.splash,
      builder: (context, state) => const SplashPage(),
    ),
    GoRoute(
      path: RouterPaths.webView,
      builder: (context, state) => WebViewScreen(
        arguments: state.extra as WebViewArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.webViewDocument,
      builder: (context, state) => DocumentScreen(
        arguments: state.extra as DocumentArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.imagesPreview,
      builder: (context, state) => ImagesPreviewPage(
        arguments: state.extra as ImagesPreviewArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.imagePreview,
      builder: (context, state) => ImagePreview(
        url: state.extra as String,
      ),
    ),
    GoRoute(
      path: RouterPaths.login,
      builder: (context, state) => LoginPage(
        arguments: state.extra as LoginArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.register,
      builder: (context, state) => const RegisterPage(),
    ),
    GoRoute(
      path: RouterPaths.forgotPassword,
      builder: (context, state) => const ForgotPasswordPage(),
    ),
    GoRoute(
      path: RouterPaths.setupAccount,
      builder: (context, state) => SetupAccountPage(
        arguments: state.extra as SetupAccountArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.verifyOtp,
      builder: (context, state) => VerifyOtpPage(
        arguments: state.extra as VerifyOtpArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.changePassword,
      builder: (context, state) => const ChangePasswordPage(),
    ),
    GoRoute(
      path: RouterPaths.userInfo,
      builder: (context, state) => const UserInfoPage(),
    ),
    GoRoute(
      path: RouterPaths.chatBot,
      builder: (context, state) => const ChatBotPage(),
    ),
    GoRoute(
      path: RouterPaths.root,
      builder: (context, state) => RootPage(
        arguments: state.extra as RootArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.permissionSetting,
      builder: (context, state) => const PermissionSettingPage(),
    ),
    GoRoute(
      path: RouterPaths.selectAddress,
      builder: (context, state) => SelectAddressPage(
        arguments: state.extra as SelectAddressArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.managementUnit,
      builder: (context, state) => SelectManagementUnitPage(
        arguments: state.extra as SelectManagementUnitArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.allProducts,
      builder: (context, state) => AllProductsPage(
        arguments: state.extra as AllProductsArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.allProductsAgency,
      builder: (context, state) => AllProductsAgencyPage(
        arguments: state.extra as AllProductsAgencyArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.productDetail,
      builder: (context, state) => ProductDetailPage(
        arguments: state.extra as ProductDetailArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.productInfo,
      builder: (context, state) => ProductInfoPage(
        arguments: state.extra as ProductInfoArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.searchStore,
      builder: (context, state) => SearchStorePage(
        arguments: state.extra as SearchStoreArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.createProductOrder,
      builder: (context, state) => CreateProductOrderPage(
        arguments: state.extra as CreateProductOrderArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.createServiceOrder,
      builder: (context, state) => CreateServiceOrderPage(
        arguments: state.extra as CreateServiceOrderArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.saveContactInfo,
      builder: (context, state) => SaveContactInfoPage(
        arguments: state.extra as SaveContactInfoArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.orderSuccess,
      builder: (context, state) => OrderSuccessPage(
        arguments: state.extra as OrderSuccessArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.paymentSuccess,
      builder: (context, state) => PaymentSuccessPage(
        arguments: state.extra as PaymentSuccessArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectVoucher,
      builder: (context, state) => SelectVoucherPage(
        arguments: state.extra as SelectVoucherArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailVoucher,
      builder: (context, state) => DetailVoucherPage(
        arguments: state.extra as DetailVoucherArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.listPackageService,
      builder: (context, state) => ListPackageServicesPage(
        arguments: state.extra as ListPackageServicesArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.listSingleService,
      builder: (context, state) => SingleServicesPage(
        arguments: state.extra as SingleServicesArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.listPartnerService,
      builder: (context, state) => PartnerServicesPage(
        arguments: state.extra as PartnerServicesArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.addSeller,
      builder: (context, state) => AddSellerPage(
        arguments: state.extra as AddSellerArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.listPackageService,
      builder: (context, state) => ListPackageServicesPage(
        arguments: state.extra as ListPackageServicesArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.addProduct,
      builder: (context, state) => AddProductPage(
        arguments: state.extra as AddProductArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.choosePaymentProvider,
      builder: (context, state) => PaymentProviderPage(
        arguments: state.extra as PaymentProviderArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.allOrder,
      builder: (context, state) => ListOrderPage(
        arguments: state.extra as ListOrderArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.notification,
      builder: (context, state) => const NotificationPage(),
    ),
    GoRoute(
      path: RouterPaths.collectionInfoWarehouse,
      builder: (context, state) => const CollectionInfoWarehousePage(),
    ),
    GoRoute(
      path: RouterPaths.createCustomerInfo,
      builder: (context, state) => CreateCustomerInfoPage(
        arguments: state.extra as CreateCustomerInfoArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.createCustomerInfoSuccess,
      builder: (context, state) => CreateCustomerInfoSuccessPage(
        arguments: state.extra as CreateCustomerInfoSuccessArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.customerSurveyForm,
      builder: (context, state) => CustomerSurveyFormPage(
        arguments: state.extra as CustomerSurveyFormArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailInfoCustomerCollection,
      builder: (context, state) => DetailInfoCustomerCollectionPage(
        arguments: state.extra as DetailInfoCustomerCollectionArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.impactHistory,
      builder: (context, state) => ImpactHistoryPage(
        arguments: state.extra as ImpactHistoryArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.chooseDistrict,
      builder: (context, state) => ChooseDistrictScreen(
        arguments: state.extra as ChooseDistrictArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.chooseGoods,
      builder: (context, state) => SelectGoodsScreen(
        arguments: state.extra as SelectGoodsArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectCampaign,
      builder: (context, state) => SelectCampaignPage(
        arguments: state.extra as SelectCampaignArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailNotification,
      builder: (context, state) => DetailNotificationPage(
        arguments: state.extra as DetailNotificationArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.allAssociation,
      builder: (context, state) => const ListAssociationPage(),
    ),
    GoRoute(
      path: RouterPaths.createAssociation,
      builder: (context, state) => AssociationCreatePage(
        arguments: state.extra as AssociationCreateArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.associationDetail,
      builder: (context, state) => AssociationDetailPage(
        arguments: state.extra as AssociationDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.cus360,
      builder: (context, state) => Cus360Page(
        arguments: state.extra as Cus360Arguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectTypeAssociation,
      builder: (context, state) => SelectAssociationChannelView(
        arguments: state.extra as SelectAssociationChannelArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectAssociationCustomer,
      builder: (context, state) => SelectAssociationCustomerView(
        arguments: state.extra as SelectAssociationCustomerArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.createAssociationSuccess,
      builder: (context, state) => CreateAssociationSuccessPage(
        arguments: state.extra as CreateSuccessAssociationArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectAssociationSupply,
      builder: (context, state) => SelectAssociationSupplyView(
        arguments: state.extra as SelectAssociationSupplyArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectAssociationService,
      builder: (context, state) => SelectAssociationServiceView(
        arguments: state.extra as SelectAssociationServiceArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailContract,
      builder: (context, state) => DetailContractPage(
        arguments: state.extra as DetailContractArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectPriceSolarEnergy,
      builder: (context, state) => SelectPriceSolarEnergyPage(
        arguments: state.extra as SelectPriceSolarEnergyArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.priceSolarEnergyDetail,
      builder: (context, state) => PriceSolarEnergyDetailPage(
        arguments: state.extra as PriceSolarEnergyDetailArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.allAssociationWO,
      builder: (context, state) => const AssociationWoListPage(),
    ),
    GoRoute(
      path: RouterPaths.associationWODetail,
      builder: (context, state) => AssociationWoDetailPage(
        arguments: state.extra as AssociationWoDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectDemandAssociation,
      builder: (context, state) => SelectAssociationDemandView(
        arguments: state.extra as SelectAssociationDemandArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.debtStatistics,
      builder: (context, state) => DebtStatisticsPage(
        arguments: state.extra as DebtStatisticsArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.createRequestBuyProduct,
      builder: (context, state) => CreateRequestBuyProductPage(
        arguments: state.extra as CreateRequestBuyProductArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.requestBuyProduct,
      builder: (context, state) => const RequestBuyProductPage(),
    ),
    GoRoute(
      path: RouterPaths.detailRequestBuyProduct,
      builder: (context, state) => DetailRequestBuyProductPage(
        arguments: state.extra as DetailRequestBuyProductArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.addSupplyRequest,
      builder: (context, state) => AddSupplyRequestPage(
        arguments: state.extra as AddSupplyRequestArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.confirmCommission,
      builder: (context, state) => ConfirmCommissionPage(
        arguments: state.extra as ConfirmCommissionArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.bill,
      builder: (context, state) => BillPage(
        arguments: state.extra as BillArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.billDetail,
      builder: (context, state) => BillDetailPage(
        arguments: state.extra as BillDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.billUpdate,
      builder: (context, state) => BillUpdatePage(
        arguments: state.extra as BillUpdateArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.calendar,
      builder: (context, state) => CalendarPage(
        arguments: state.extra as CalendarArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.explanation,
      builder: (context, state) => ExplanationPage(
        arguments: state.extra as ExplanationArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.stockExport,
      builder: (context, state) => StockExportPage(
        arguments: state.extra as StockExportArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.requestDistrictWarehouse,
      builder: (context, state) => RequestDistrictWarehousePage(
        arguments: state.extra as RequestDistrictWarehouseArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.exportDistrictWarehouse,
      builder: (context, state) => ExportDistrictWarehousePage(
        arguments: state.extra as ExportDistrictWarehouseArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.requestBranchWarehouse,
      builder: (context, state) => RequestBranchWarehousePage(
        arguments: state.extra as RequestBranchWarehouseArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.exportBranchWarehouse,
      builder: (context, state) => ExportBranchWarehousePage(
        arguments: state.extra as ExportBranchWarehouseArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.transferEmployee,
      builder: (context, state) => TransferEmployeePage(
        arguments: state.extra as TransferEmployeeArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.transferEmployeeImplement,
      builder: (context, state) => TransferEmployeeImplementPage(
        arguments: state.extra as TransferEmployeeImplementArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.addSupply,
      builder: (context, state) => AddSupplyPage(
        arguments: state.extra as AddSupplyArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.returnSupply,
      builder: (context, state) => ReturnSupplyPage(
        arguments: state.extra as ReturnSupplyArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectSerial,
      builder: (context, state) => SelectSerialPage(
        arguments: state.extra as SelectSerialArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.myStockExport,
      builder: (context, state) => MyStockExportPage(
        arguments: state.extra as MyStockExportArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.onePayView,
      builder: (context, state) => OnePayView(
        arguments: state.extra as OnePayArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.editAssociationSuccess,
      builder: (context, state) => EditAssociationSuccessPage(
        arguments: state.extra as EditAssociationArguments,
      ),
    ),
    // GoRoute(
    //   path: RouterPaths.detailOrder,
    //   builder: (context, state) => DetailOrderPage(
    //     arguments: state.extra as DetailOrderArguments,
    //   ),
    // ),
    GoRoute(
      path: RouterPaths.searchSupplies,
      builder: (context, state) => SearchSuppliesPage(
        arguments: state.extra as SearchSuppliesArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.addSupplies,
      builder: (context, state) => AddSuppliesPage(
        arguments: state.extra as AddSuppliesArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.addSuppliesSalePoint,
      builder: (context, state) => AddSuppliesSalePointPage(
        arguments: state.extra as AddSuppliesSalePointArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.addSuppliesSalePointCombo,
      builder: (context, state) => AddSuppliesSalePointComboPage(
        arguments: state.extra as AddSuppliesSalePointComboArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.warehousePersonal,
      builder: (context, state) => const WarehousePersonalPage(),
    ),
    GoRoute(
      path: RouterPaths.confirmationBill,
      builder: (context, state) => ConfirmationBillPage(
        arguments: state.extra as ConfirmationBillArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailStock,
      builder: (context, state) => DetailStockPage(
        arguments: state.extra as DetailStockArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.supplyInfo,
      builder: (context, state) => SupplyInfoPage(
        arguments: state.extra as SupplyInfoArgument,
      ),
    ),
    GoRoute(
      path: RouterPaths.orderShippingDetail,
      builder: (context, state) => OrderShippingDetailPage(
        arguments: state.extra as OrderShippingDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.orderSingleServiceDetail,
      builder: (context, state) => OrderSingleServicePage(
        arguments: state.extra as OrderSingleServiceArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.orderInstallationDetail,
      builder: (context, state) => OrderInstallationPage(
        arguments: state.extra as OrderInstallationArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectFieldOfWorkPage,
      builder: (context, state) => const SelectFieldOfWorkPage(),
    ),
    GoRoute(
      path: RouterPaths.requestCancelPage,
      builder: (context, state) => RequestCancelScreen(
        arguments: state.extra as RequestCancelArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.requestCancelReceivePage,
      builder: (context, state) => CancelReceivePage(
        arguments: state.extra as CancelReceiveArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.requestSchedulePage,
      builder: (context, state) => RequestSchedulePage(
        arguments: state.extra as RequestScheduleArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.signContractOrder,
      builder: (context, state) => SignContractOrderPage(
        arguments: state.extra as SignContractOrderArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.signContractRegister,
      builder: (context, state) => SignContractRegister(
        arguments: state.extra as SignContractRegisterArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.viewFileContract,
      builder: (context, state) => ViewContractFile(
        fileUrl: state.extra as String,
      ),
    ),
    GoRoute(
      path: RouterPaths.viewPdf,
      builder: (context, state) => PdfViewScreen(
        arguments: state.extra as PdfViewArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.billOrder,
      builder: (context, state) => BillOrderPage(
        arguments: state.extra as BillOrderArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectPackageService,
      builder: (context, state) => SelectPackageServicePage(
        arguments: state.extra as SelectPackageServiceArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectOriginalPricePage,
      builder: (context, state) => SelectOriginalPricePage(
        arguments: state.extra as SelectOriginalPriceArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectPriceAndSerialPage,
      builder: (context, state) => SelectPriceAndSerialPage(
        arguments: state.extra as SelectPriceAndSerialArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.checklistTaskPage,
      builder: (context, state) => ChecklistTaskPage(
        arguments: state.extra as ChecklistTaskArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailBranch,
      builder: (context, state) => DetailBranchPage(
        arguments: state.extra as DetailBranchArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.checklistService,
      builder: (context, state) => ChecklistServicePage(
        arguments: state.extra as ChecklistServiceArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectServicePage,
      builder: (context, state) => SelectServicePage(
        arguments: state.extra as SelectServiceArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.editOrderItemPage,
      builder: (context, state) => EditOrderItemPage(
        arguments: state.extra as EditOrderItemArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.warningOrder,
      builder: (context, state) => WarningOrderPage(
        arguments: state.extra as WarningOrderArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioContract,
      builder: (context, state) => AioContractPage(
        arguments: state.extra as AioContractArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioAddSeller,
      builder: (context, state) => AioAddSellerPage(
        arguments: state.extra as AioAddSellerArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioContractCreate,
      builder: (context, state) => AioContractCreatePage(
        arguments: state.extra as AioContractCreateArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioContractDetail,
      builder: (context, state) => AioContractDetailPage(
        arguments: state.extra as AioContractDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioAddPackage,
      builder: (context, state) => AddPackagePage(
        arguments: state.extra as AddPackageArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioAddService,
      builder: (context, state) => AddServicePage(
        arguments: state.extra as AddServiceArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioContractPackage,
      builder: (context, state) => AioContractPackagePage(
        arguments: state.extra as AioContractPackageArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioRequestCancelPage,
      builder: (context, state) => AioRequestCancelScreen(
        arguments: state.extra as AioRequestCancelArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioCheckInventory,
      builder: (context, state) => AioCheckInventoryPage(
        arguments: state.extra as AioCheckInventoryArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioBillDetail,
      builder: (context, state) => AioBillDetailPage(
        arguments: state.extra as AioBillDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.aioContractDetailPerformer,
      builder: (context, state) => AioContractDetailPerformerPage(
        arguments: state.extra as AioContractDetailPerformerArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.listStockGoodPage,
      builder: (context, state) => ListStockGoodPage(
        arguments: state.extra as ListStockGoodArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.updateContractRealPage,
      builder: (context, state) => UpdateContractRealPage(
        arguments: state.extra as UpdateContractRealArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.createReportError,
      builder: (context, state) => const CreateReportErrorPage(),
    ),
    GoRoute(
      path: RouterPaths.editReportError,
      builder: (context, state) => EditReportErrorPage(
        argument: state.extra as EditReportErrorArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.allReportError,
      builder: (context, state) => const ListReportErrorPage(),
    ),
    GoRoute(
      path: RouterPaths.detailReportError,
      builder: (context, state) => DetailReportErrorPage(
        argument: state.extra as DetailReportErrorArgument,
      ),
    ),
    GoRoute(
      path: RouterPaths.duplicateReportError,
      builder: (context, state) => DuplicateReportErrorPage(
        argument: state.extra as DuplicateReportErrorArgument,
      ),
    ),
    GoRoute(
      path: RouterPaths.listErrorInstructions,
      builder: (context, state) => const ListErrorInstructionsPage(),
    ),
    GoRoute(
      path: RouterPaths.detailErrorInstructions,
      builder: (context, state) => DetailErrorInstructionsPage(
        instructionArgument: state.extra as DetailErrorInstructionArgument,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectGroupError,
      builder: (context, state) => SelectGroupErrorPage(
        arguments: state.extra as SelectGroupErrorArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.viewDocumentCim,
      builder: (context, state) => ViewDocumentCim(
        fileUrl: state.extra as String,
      ),
    ),
    GoRoute(
      path: RouterPaths.feedbackError,
      builder: (context, state) => FeedbackErrorPage(
        errorId: state.extra as int,
      ),
    ),
    GoRoute(
      path: RouterPaths.explanationDetail,
      builder: (context, state) => ExplanationDetailPage(
        arguments: state.extra as ExplanationDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.explanationHistory,
      builder: (context, state) => ExplanationHistoryPage(
        arguments: state.extra as ExplanationHistoryArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.searchWarranty,
      builder: (context, state) => const SearchWarrantyPage(),
    ),
    GoRoute(
      path: RouterPaths.listWarranty,
      builder: (context, state) => ListWarrantyPage(
        arguments: state.extra as ListWarrantyArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailWarranty,
      builder: (context, state) => DetailWarrantyPage(
        arguments: state.extra as DetailWarrantyArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.listWarrantyCenter,
      builder: (context, state) => ListWarrantyCenterPage(
        arguments: state.extra as ListWarrantyCenterArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.scanQRCode,
      builder: (context, state) {
        return const ScanQRCodeScreen();
      },
    ),
    GoRoute(
      path: RouterPaths.complain,
      builder: (context, state) => const ComplainPage(),
    ),
    GoRoute(
      path: RouterPaths.createComplain,
      builder: (context, state) => CreateComplainPage(
        arguments: state.extra as CreateComplainArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailComplain,
      builder: (context, state) => ComplainDetailPage(
        arguments: state.extra as ComplainDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.historyComplain,
      builder: (context, state) => ComplainHistoryPage(
        arguments: state.extra as ComplainHistoryArguments?,
      ),
    ),

    GoRoute(
      path: RouterPaths.interactCustomerComplain,
      builder: (context, state) => InteractCustomerPage(
        arguments: state.extra as InteractCustomerArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.serialContractComplain,
      builder: (context, state) => SelectSerialComplainPage(
        arguments: state.extra as SelectSerialComplainArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.signContractComplain,
      builder: (context, state) => SignContractComplainPage(
        arguments: state.extra as SignContractComplainArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.complain,
      builder: (context, state) => const ComplainPage(),
    ),
    GoRoute(
      path: RouterPaths.createComplain,
      builder: (context, state) => CreateComplainPage(
        arguments: state.extra as CreateComplainArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailComplain,
      builder: (context, state) => ComplainDetailPage(
        arguments: state.extra as ComplainDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.historyComplain,
      builder: (context, state) => ComplainHistoryPage(
        arguments: state.extra as ComplainHistoryArguments?,
      ),
    ),

    GoRoute(
      path: RouterPaths.interactCustomerComplain,
      builder: (context, state) => InteractCustomerPage(
        arguments: state.extra as InteractCustomerArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.serialContractComplain,
      builder: (context, state) => SelectSerialComplainPage(
        arguments: state.extra as SelectSerialComplainArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.signContractComplain,
      builder: (context, state) => SignContractComplainPage(
        arguments: state.extra as SignContractComplainArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.requirementWarranty,
      builder: (context, state) => const RequirementWarrantyPage(),
    ),
    GoRoute(
      path: RouterPaths.appointmentSuggestion,
      builder: (context, state) => AppointmentSuggestionPage(
        arguments: state.extra as AppointmentSuggestionArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.createRequirementWarranty,
      builder: (context, state) => CreateComplainWarrantyPage(
        arguments: state.extra as CreateRequirementWarrantyArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailRequirementWarrantyClaim,
      builder: (context, state) => DetailRequirementWarrantyPage(
        arguments: state.extra as RequirementWarrantyDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.addSuppliesRequirementWarrantyClaim,
      builder: (context, state) => AddSuppliesRequirementWarrantyPage(
        arguments: state.extra as AddSuppliesRequirementWarrantyArgument,
      ),
    ),
    GoRoute(
      path: RouterPaths.requestWarehouseMaterials,
      builder: (context, state) => RequestWarehouseMaterialsPage(
        arguments: state.extra as RequestWarehouseMaterialsArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.addProductRequirementWarranty,
      builder: (context, state) => AddProductRequirementWarrantyPage(
        arguments: state.extra as AddProductOrderContractArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.historyRequirementWarranty,
      builder: (context, state) => HistoryRequirementWarrantyPage(
        arguments: state.extra as HistoryRequirementWarrantyArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.historyOrderAgency,
      builder: (context, state) => HistoryOrderAgencyPage(
        arguments: state.extra as HistoryOrderAgencyArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.manageCollaborator,
      builder: (context, state) => const ManagingCollaboratorPage(),
    ),
    GoRoute(
      path: RouterPaths.shoppingCart,
      builder: (context, state) => const ShoppingCartPage(),
    ),
    GoRoute(
      path: RouterPaths.productDetailAgency,
      builder: (context, state) => ProductDetailAgencyPage(
        arguments: state.extra as ProductDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.requestOrder,
      builder: (context, state) => const RequestOrderPage(),
    ),
    GoRoute(
      path: RouterPaths.addProductAgency,
      builder: (context, state) => AddProductAgencyPage(
        arguments: state.extra as AddProductAgencyArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.searchProductAgency,
      builder: (context, state) => const SearchStoreAgencyPage(),
    ),
    GoRoute(
      path: RouterPaths.createSuccessOrderInfo,
      builder: (context, state) => CreateOrderSuccessWidget(
        argument: state.extra as CreateOrderSuccessArgument,
      ),
    ),

    GoRoute(
      path: RouterPaths.workAgencyPage,
      builder: (context, state) => WorkAgencyPage(
        argument: state.extra as WorkAgencyArgument,
      ),
    ),
    GoRoute(
      path: RouterPaths.updateInvoice,
      builder: (context, state) => UpdateInvoicePage(
        arguments: state.extra as UpdateInvoiceArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.orderAgencyManagementDetail,
      builder: (context, state) => OrderAgencyManagementDetailPage(
        arguments: state.extra as OrderAgencyManagementDetailArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.signContractInvoice,
      builder: (context, state) => SignContractInvoicePage(
        arguments: state.extra as SignContractInvoiceArguments,
      ),
    ),

    // Customer Quote
    GoRoute(
      path: RouterPaths.customerQuoteHome,
      builder: (context, state) => CustomerQuotePage(
        arguments: state.extra as HomeCustomerQuoteArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.customerQuoteDetail,
      builder: (context, state) => DetailCustomerQuotePage(
        arguments: state.extra as DetailCustomerQuoteViewArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.customerQuoteHistory,
      builder: (context, state) => HistoryCustomerQuotePage(
        arguments: state.extra as HistoryCustomerQuoteArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.customerQuoteCreate,
      builder: (context, state) => CreateCustomerQuotePage(
        arguments: state.extra as CreateCustomerQuoteArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.selectPaymentQuote,
      builder: (context, state) => SelectPaymentQuotePage(
        arguments: state.extra as SelectPaymentQuoteArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.editProductQuote,
      builder: (context, state) => EditProductQuotePage(
        arguments: state.extra as EditProductQuoteArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.availableProductsQuote,
      builder: (context, state) => AvailableProductsQuotePage(
        arguments: state.extra as AvailableProductsQuoteArgument?,
      ),
    ),

    GoRoute(
      path: RouterPaths.addSaleMember,
      builder: (context, state) => AddSaleMemberPage(
        arguments: state.extra as AddSaleMemberArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.confirmSaleMember,
      builder: (context, state) => ConfirmSaleMemberPage(
        arguments: state.extra as ConfirmSaleMemberArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.saleTeamDetail,
      builder: (context, state) => const SalesTeamDetailPage(),
    ),
    GoRoute(
      path: RouterPaths.saleTeamHome,
      builder: (context, state) => const SaleTeamHomePage(),
    ),
    GoRoute(
      path: RouterPaths.takeImageIdentifier,
      builder: (context, state) => TakeImageIdentifierPage(
        arguments: state.extra as TakeImageIdentifierArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.sendRequestTeam,
      builder: (context, state) => SendRequestTeamPage(
        arguments: state.extra as SendRequestTeamArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.saleMemberInfo,
      builder: (context, state) => SaleMemberInfoPage(
        arguments: state.extra as SaleMemberInfoArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.customerFeedback,
      builder: (context, state) => const CustomerFeedbackPage(),
    ),
    GoRoute(
      path: RouterPaths.createFeedback,
      builder: (context, state) => CreateFeedbackPage(
        arguments: state.extra as CreateFeedbackArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.listFeedback,
      builder: (context, state) => ListFeedbackPage(
        arguments: state.extra as ListFeedbackArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.detailFeedback,
      builder: (context, state) => DetailFeedbackPage(
        arguments: state.extra as DetailFeedbackArguments,
      ),
    ),
    GoRoute(
      path: RouterPaths.contractDraftPage,
      builder: (context, state) => const ContractDraftPage(),
    ),
    GoRoute(
      path: RouterPaths.contractDraftDetailPage,
      builder: (context, state) => ContractDraftDetailPage(
        code: state.extra as String,
      ),
    ),
    GoRoute(
      path: RouterPaths.createContractDraftPage,
      builder: (context, state) => CreateContractDraftPage(
        arguments: state.extra as CreateContractDraftArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.addProductServicePage,
      builder: (context, state) => AddProductServicePage(
        arguments: state.extra as AddProductServiceArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.createBusinessPlanPage,
      builder: (context, state) => CreateBusinessPlanPage(
        arguments: state.extra as CreateBusinessPlanArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.changeApproveLeader,
      builder: (context, state) => ChangeApprovedLeaderPage(
        arguments: state.extra as ChangeApprovedLeaderArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.previewDraftContract,
      builder: (context, state) => PreviewDraftContractPage(
        arguments: state.extra as PreviewDraftContractArgument?,
      ),
    ),
    GoRoute(
      path: RouterPaths.previewContractAppendix,
      builder: (context, state) => PreviewContractAppendixPage(
        arguments: state.extra as PreviewContractAppendixArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.historyContractDraft,
      builder: (context, state) => HistoryContractDraftPage(
        arguments: state.extra as HistoryContractDraftArguments?,
      ),
    ),
    GoRoute(
      path: RouterPaths.timekeeping,
      builder: (context, state) => const TimekeepingPage(),
    ),
    GoRoute(
      path: RouterPaths.detailRequestTimekeeping,
      builder: (context, state) => DetailRequestTimekeepingPage(
        id: state.extra as String,
      ),
    ),
    GoRoute(
      path: RouterPaths.createOrUpdateRequestTimekeeping,
      builder: (context, state) => CreateOrUpdateRequestTimekeepingPage(
        arguments: state.extra as CreateOrUpdateRequestTimeKeepingArguments?,
      ),
    ),
  ],
  observers: [timeTrackingObserver],
);

String? _guard(BuildContext context, GoRouterState state) {
  // final bool signedIn = _auth.signedIn;
  // final bool signingIn = state.subloc == '/signin';

  // // Go to /signin if the user is not signed in
  // if (!signedIn && !signingIn) {
  //   return '/signin';
  // }

  // no redirect
  return null;
}

// Add animation transition page
class FadeTransitionPage extends CustomTransitionPage<void> {
  /// Creates a [FadeTransitionPage].
  FadeTransitionPage({
    required LocalKey super.key,
    required super.child,
  }) : super(
          transitionsBuilder: (BuildContext context,
              Animation<double> animation,
              Animation<double> secondaryAnimation,
              Widget child) {
            return FadeTransition(
              opacity: animation.drive(_curveTween),
              child: child,
            );
          },
        );

  static final CurveTween _curveTween = CurveTween(curve: Curves.easeIn);
}
