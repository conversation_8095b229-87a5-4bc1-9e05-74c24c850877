import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/extensions/file_to_base64.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/domain/params/register/register_image.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/image_base64_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_image/upload_image.dart';

class BuildImageInformationArguments {
  final String? title;
  final int maxImageSelect;
  final LoadStatus? uploadStatus;
  final List<RegisterImageEntity>? images;
  final Function(RegisterImageEntity image)? deleteImage;
  final Function(List<RegisterImageEntity> images)? addImage;

  BuildImageInformationArguments({
    this.title,
    this.images,
    this.addImage,
    this.deleteImage,
    this.uploadStatus,
    this.maxImageSelect = 12,
  });
}

class BuildImageInformation extends StatefulHookConsumerWidget {
  final BuildImageInformationArguments arguments;

  const BuildImageInformation({
    super.key,
    required this.arguments,
  });

  @override
  ConsumerState<BuildImageInformation> createState() =>
      _BuildImageEntityInformationState();
}

class _BuildImageEntityInformationState
    extends ConsumerState<BuildImageInformation> {
  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        top: BaseSpacing.spacing2,
        bottom: BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing3,
            ),
            child: RichText(
              text: TextSpan(
                style: UITextStyle.body2Medium,
                children: <TextSpan>[
                  TextSpan(
                    text: '${widget.arguments.title}',
                  ),
                  TextSpan(
                    text: ' *',
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ],
              ),
            ),
          ),
          buildImage(images: widget.arguments.images ?? []),
          Padding(
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacing1,
            ),
            child: Text(
              'Cho phép tải lên tối đa ${widget.arguments.maxImageSelect} ảnh',
              style: UITextStyle.body3Regular.copyWith(
                color: BaseColors.primary,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildImage({
    required List<RegisterImageEntity> images,
  }) {
    return SizedBox(
      height: BaseSpacing.spacing20,
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: images.length < widget.arguments.maxImageSelect
            ? images.length + 1
            : widget.arguments.maxImageSelect,
        scrollDirection: Axis.horizontal,
        separatorBuilder: (_, __) => const SizedBox(
          width: BaseSpacing.spacing2,
        ),
        itemBuilder: (context, index) {
          if (index == images.length) {
            return images.length < widget.arguments.maxImageSelect
                ? InkWellWidget(
                    onTap: () {
                      onTakePicture(index);
                    },
                    child: MyAssets.icons.iconAddImageDashline.svg(),
                  )
                : const SizedBox.shrink();
          }
          return Stack(
            children: [
              Container(
                width: 76,
                height: 76,
                padding: const EdgeInsets.only(
                  top: BaseSpacing.spacing1,
                  right: BaseSpacing.spacing1,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    BaseSpacing.spacing2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    BaseSpacing.spacing2,
                  ),
                  child: ImageBase64Widget(
                    base64Image: images[index].base64 ?? '',
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: Visibility(
                  visible: true,
                  child: InkWellWidget(
                    onTap: () {
                      widget.arguments.addImage != null
                          ? widget.arguments.deleteImage!(images[index])
                          : null;
                    },
                    child: MyAssets.icons.iconCloseRed.svg(),
                  ),
                ),
              ),
              if (widget.arguments.uploadStatus == LoadStatus.loading) ...[
                const Positioned.fill(
                  child: Center(
                    child: LoadingIndicatorWidget(),
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  void onTakePicture(
    int index,
  ) async {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Upload ảnh",
      isFlexible: true,
      child: UploadImageBottomSheet(
        maxImageSelect: widget.arguments.maxImageSelect,
        onTakeImage: (file) async {
          String name = file.path.split('/').last;
          String base64 = await fileToBase64(file);
          List<RegisterImageEntity> listImage = [
            RegisterImageEntity(
              name: name,
              base64: base64,
            ),
          ];
          if (validatorImage(images: listImage)) {
            widget.arguments.addImage != null
                ? widget.arguments.addImage!(listImage)
                : null;
          }
        },
        onPickMultiImage: (file) async {
          List<RegisterImageEntity> listImage = [];
          for (var item in file) {
            String name = item.path.split('/').last;
            String base64 = await fileToBase64(item);
            listImage.add(
              RegisterImageEntity(
                name: name,
                base64: base64,
              ),
            );
          }
          if (validatorImage(images: listImage)) {
            widget.arguments.addImage != null
                ? widget.arguments.addImage!(listImage)
                : null;
          }
        },
      ),
    );
  }

  bool validatorImage({
    required List<RegisterImageEntity> images,
  }) {
    bool isCheck = false;
    final countImgCurrent = widget.arguments.images?.length ?? 0;
    final countImg = images.length;
    final sum = countImgCurrent + countImg;

    if (sum > widget.arguments.maxImageSelect) {
      AppDialog.showDialogCenter(
        ErrorDialog.navigatorKey.currentContext!,
        message: "Không chọn quá 12 ảnh",
        status: DialogStatus.error,
      );
      isCheck = false;
    } else {
      isCheck = true;
    }
    return isCheck;
  }
}
