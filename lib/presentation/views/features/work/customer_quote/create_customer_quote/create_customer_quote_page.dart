import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pattern_formatter/pattern_formatter.dart';
import 'package:vcc/domain/body/customer_quote/new_customer_quote_body.dart';
import 'package:vcc/domain/body/customer_quote/update_detail_customer_quote_body.dart';
import 'package:vcc/domain/entities/address_info.dart';
import 'package:vcc/domain/entities/complain/detail_complain_entity.dart';
import 'package:vcc/domain/entities/customer_quote/customer_info_entity.dart';
import 'package:vcc/domain/entities/customer_quote/customer_quote_detail_entity.dart';
import 'package:vcc/domain/entities/customer_quote/quote_product_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/enums/customer_quote/payment_quote_enum.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/params/create_complain/complain_image.dart';
import 'package:vcc/extensions/file_type.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_image/upload_image.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/widget/document_report_file_widget.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/available_products_quote/available_products_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/create_customer_quote/create_customer_quote_view_model.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/edit_product_quote/edit_product_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/select_payment_quote/select_payment_quote_page.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/select_payment_quote/widgets/payment_quote_entity.dart';
import 'package:vcc/presentation/views/features/work/customer_quote/widgets/product_item_widget.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/cus360_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/dotted_border/dotted_border.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/required_label_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/input_formatter_utils.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class CreateCustomerQuoteArguments {
  final String? code;
  final CustomerQuoteDetailEntity? detailData;
  final CustomerInfoEntity? customerInfo;
  final bool isCus360;

  CreateCustomerQuoteArguments({
    this.code,
    this.detailData,
    this.customerInfo,
    this.isCus360 = false,
  });
}

class CreateCustomerQuotePage extends StatefulHookConsumerWidget {
  final CreateCustomerQuoteArguments? arguments;

  const CreateCustomerQuotePage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<CreateCustomerQuotePage> createState() =>
      _DetailWarrantyClaimPageState();
}

class _DetailWarrantyClaimPageState
    extends ConsumerState<CreateCustomerQuotePage> {
  final personalPhoneController = TextEditingController();
  final personalNameController = TextEditingController();
  final personalAddressController = TextEditingController();

  final companyTaxCodeController = TextEditingController();
  final companyNameController = TextEditingController();
  final companyPhoneController = TextEditingController();
  final companyAddressController = TextEditingController();
  final companyContactNameController = TextEditingController();
  final companyContactPhoneController = TextEditingController();

  final surchargeController = TextEditingController();
  final discountController = TextEditingController();
  final noteController = TextEditingController();

  final FocusNode personalPhoneFocusNode = FocusNode();
  final FocusNode personalNameFocusNode = FocusNode();
  final FocusNode personalAddressFocusNode = FocusNode();

  final FocusNode companyTaxCodeFocusNode = FocusNode();
  final FocusNode companyNameFocusNode = FocusNode();
  final FocusNode companyPhoneFocusNode = FocusNode();
  final FocusNode companyAddressFocusNode = FocusNode();
  final FocusNode companyContactNameFocusNode = FocusNode();
  final FocusNode companyContactPhoneFocusNode = FocusNode();

  final FocusNode surchargeFocusNode = FocusNode();
  final FocusNode discountFocusNode = FocusNode();
  final FocusNode noteFocusNode = FocusNode();

  final formUserKey = GlobalKey<FormState>();
  final formCompanyKey = GlobalKey<FormState>();
  final GlobalKey<FormFieldState> userPhoneNumberKey =
      GlobalKey<FormFieldState>();

  @override
  void initState() {
    Future(
      () {
        if (widget.arguments?.customerInfo != null) {
          ref.read(createCustomerQuoteProvider.notifier).changedProductInfo(
                info: widget.arguments?.detailData,
                customerInfo: widget.arguments?.customerInfo,
              );

          if (widget.arguments?.customerInfo?.isB2CCustomer == true) {
            personalPhoneController.text =
                widget.arguments?.customerInfo?.phoneNumber ?? '';
            personalNameController.text =
                widget.arguments?.customerInfo?.fullName ?? '';
            personalAddressController.text =
                widget.arguments?.customerInfo?.address ?? '';
          } else if (widget.arguments?.customerInfo?.isB2BCustomer == true) {
            companyTaxCodeController.text =
                widget.arguments?.customerInfo?.taxCode ??
                    widget.arguments?.customerInfo?.customerId ??
                    '';
            companyNameController.text =
                widget.arguments?.customerInfo?.fullName ?? '';
            companyPhoneController.text =
                widget.arguments?.customerInfo?.phoneNumber ?? '';
            companyAddressController.text =
                widget.arguments?.customerInfo?.address ?? '';
            companyContactNameController.text =
                widget.arguments?.customerInfo?.contactName ?? '';
            companyContactPhoneController.text =
                widget.arguments?.customerInfo?.contactNumber ?? '';
          }
          if (personalPhoneController.text.validatePhone()) {
            getCustomerInfo360(
              phoneNumber: personalPhoneController.text,
              isCustomerInfo: false,
            );
          }
          if (companyTaxCodeController.text.validatePhone()) {
            getCompanyInfo360(
              taxCode: companyTaxCodeController.text,
              isCustomerInfo: false,
            );
          }
        }
        surchargeController.text = moneyFormat(
            '${widget.arguments?.detailData?.surcharge?.toInt() ?? 0}');
        discountController.text = moneyFormat(
            '${widget.arguments?.detailData?.discount?.toInt() ?? 0}');

        noteController.text = widget.arguments?.detailData?.note ?? '';
      },
    );
    super.initState();
  }

  Future<void> onRefresh() async {}

  @override
  void dispose() {
    personalPhoneController.dispose();
    personalNameController.dispose();
    personalAddressController.dispose();
    companyTaxCodeController.dispose();
    companyNameController.dispose();
    companyPhoneController.dispose();
    companyAddressController.dispose();
    companyContactNameController.dispose();
    companyContactPhoneController.dispose();
    surchargeController.dispose();
    discountController.dispose();

    personalPhoneFocusNode.dispose();
    personalNameFocusNode.dispose();
    personalAddressFocusNode.dispose();
    companyTaxCodeFocusNode.dispose();
    companyNameFocusNode.dispose();
    companyPhoneFocusNode.dispose();
    companyAddressFocusNode.dispose();
    companyContactNameFocusNode.dispose();
    companyContactPhoneFocusNode.dispose();
    surchargeFocusNode.dispose();
    discountFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createCustomerQuoteProvider);
    return LayoutPage(
      appbar: AppBarCustom(
        title: widget.arguments?.code == null
            ? "Tạo thông tin bàn giao"
            : "Cập nhật thông tin bàn giao",
      ),
      body: Stack(
        children: [
          buildDetail(context),
          if (state.updateStatus == LoadStatus.loading ||
              state.loadCustomerStatus == LoadStatus.loading ||
              state.updateQuoteStatus == LoadStatus.loading)
            Positioned.fill(
              child: Container(
                color: BaseColors.textOnColor.withOpacity(0.35),
                child: const Center(
                  child: LoadingIndicatorWidget(),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget buildDetail(BuildContext context) {
    final state = ref.watch(createCustomerQuoteProvider);
    final provider = ref.read(createCustomerQuoteProvider.notifier);

    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }

    if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: "Không có dữ liệu",
        isFullHeight: false,
        heightSpacerTop: 100,
        onRefresh: onRefresh,
      );
    }
    return GestureDetector(
      onTap: () {
        return FocusScope.of(context).unfocus();
      },
      child: SingleChildScrollView(
        child: Stack(
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                buildCCustomerInfo(),
                DividerWidget(
                  height: 5,
                  color: BaseColors.backgroundGray,
                  margin: const EdgeInsets.symmetric(
                    vertical: BaseSpacing.spacing2,
                  ),
                ),
                buildProductListView(),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: BaseSpacing.spacing4,
                    horizontal: BaseSpacing.spacing4,
                  ),
                  child: TextFieldWidget(
                    controller: surchargeController,
                    focusNode: surchargeFocusNode,
                    labelText: "Phụ phí",
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                    inputFormatters: [
                      ThousandsFormatter(
                        allowFraction: true,
                      ),
                    ],
                    onFocusChange: (isFocus) {
                      if (!isFocus) {
                        surchargeFocusNode.unfocus();
                      }
                    },
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: BaseSpacing.spacing4,
                  ),
                  child: TextFieldWidget(
                    controller: discountController,
                    focusNode: discountFocusNode,
                    labelText: "Giảm giá",
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                    inputFormatters: [
                      ThousandsFormatter(
                        allowFraction: true,
                      ),
                    ],
                    onFocusChange: (isFocus) {
                      if (!isFocus) {
                        discountFocusNode.unfocus();
                      }
                    },
                  ),
                ),
                DividerWidget(
                  height: 5,
                  color: BaseColors.backgroundGray,
                  margin: const EdgeInsets.symmetric(
                    vertical: BaseSpacing.spacing2,
                  ),
                ),
                InkWell(
                  onTap: () async {
                    final result = await context.push(
                      RouterPaths.selectPaymentQuote,
                      extra: SelectPaymentQuoteArguments(
                        item: state.paymentType,
                      ),
                    );

                    if (result != null) {
                      provider.changedPayment(
                        type: result as PaymentQuoteEntity,
                      );
                    }
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(
                      BaseSpacing.spacing2,
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.start,
                      children: [
                        MyAssets.icons.iconPaymentMethod.svg(),
                        Expanded(
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              horizontal: BaseSpacing.spacing2,
                            ),
                            child: Text(
                              'Phương thức thanh toán',
                              style: UITextStyle.body2Bold,
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 90,
                          child: Text(
                            state.paymentType?.paymentType?.title ?? 'Chọn',
                            softWrap: true,
                            textAlign: TextAlign.end,
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                        ),
                        MyAssets.icons.iconArrowRightS20.svg(),
                      ],
                    ),
                  ),
                ),
                DividerWidget(
                  height: 5,
                  color: BaseColors.backgroundGray,
                  margin: const EdgeInsets.symmetric(
                    vertical: BaseSpacing.spacing2,
                  ),
                ),
                buildOtherInfoView(),
                widget.arguments?.code != null
                    ? IgnorePointer(
                        ignoring: state.updateQuoteStatus == LoadStatus.loading,
                        child: Padding(
                          padding: const EdgeInsets.all(
                            BaseSpacing.spacing4,
                          ),
                          child: BaseButton(
                            text: "Lưu",
                            backgroundColor: BaseColors.primary,
                            textColor: BaseColors.textOnColor,
                            onTap: () async {
                              FocusScope.of(context).unfocus();
                              final surcharge =
                                  moneyParse(surchargeController.text);
                              final discount =
                                  moneyParse(discountController.text);
                              final customer = state.userTypeSelected ==
                                      UserType.personal
                                  ? CustomerInfoEntity(
                                      customerId: widget.arguments?.customerInfo
                                              ?.customerId ??
                                          widget.arguments?.customerInfo
                                              ?.phoneNumber,
                                      gender: widget
                                          .arguments?.customerInfo?.gender,
                                      provinceCode: state.customerAddress
                                              ?.province?.code ??
                                          widget.arguments?.customerInfo
                                              ?.provinceCode ??
                                          state.infoCus360?.provinceCode,
                                      districtCode: state.customerAddress
                                              ?.district?.code ??
                                          widget.arguments?.customerInfo
                                              ?.districtCode ??
                                          state.infoCus360?.districtCode,
                                      wardCode:
                                          state.customerAddress?.ward?.code ??
                                              widget.arguments?.customerInfo
                                                  ?.wardCode ??
                                              state.infoCus360?.wardCode,
                                      phoneNumber: personalPhoneController.text,
                                      fullName: personalNameController.text,
                                      address: state.customerAddress
                                              ?.getFullAddress ??
                                          widget.arguments?.customerInfo
                                              ?.address ??
                                          state.infoCus360?.address,
                                      customerType:
                                          state.userTypeSelected.keyToServer,
                                    )
                                  : CustomerInfoEntity(
                                      customerId: widget.arguments
                                              ?.customerInfo!.taxCode ??
                                          widget.arguments?.customerInfo
                                              ?.customerId,
                                      gender: widget
                                          .arguments?.customerInfo?.gender,
                                      provinceCode: state
                                              .companyAddress?.province?.code ??
                                          widget.arguments?.customerInfo
                                              ?.provinceCode ??
                                          state.infoCus360?.provinceCode,
                                      districtCode: state
                                              .companyAddress?.district?.code ??
                                          widget.arguments?.customerInfo
                                              ?.districtCode ??
                                          state.infoCus360?.districtCode,
                                      wardCode:
                                          state.companyAddress?.ward?.code ??
                                              widget.arguments?.customerInfo
                                                  ?.wardCode ??
                                              state.infoCus360?.wardCode,
                                      taxCode: state.infoCus360?.taxCode ??
                                          companyTaxCodeController.text,
                                      address: state
                                              .companyAddress?.getFullAddress ??
                                          widget.arguments?.customerInfo
                                              ?.address ??
                                          state.infoCus360?.address,
                                      fullName: companyNameController.text,
                                      phoneNumber: companyPhoneController.text,
                                      contactNumber:
                                          companyContactPhoneController.text,
                                      contactName:
                                          companyContactNameController.text,
                                      customerType:
                                          state.userTypeSelected.keyToServer,
                                    );

                              List<String>? listImage = [];
                              List<String> files = [];

                              if ((formUserKey.currentState?.validate() ??
                                      false) ||
                                  (formCompanyKey.currentState?.validate() ??
                                      false)) {
                                for (ComplainFileEntity item
                                    in (state.listImageInfo ?? [])) {
                                  listImage.add(item.link ?? '');
                                }

                                for (ComplainFileEntity item
                                    in (state.listFileInfo ?? [])) {
                                  files.add(item.link ?? '');
                                }

                                if ((state.listProduct ?? []).isEmpty) {
                                  buildPopupError(
                                    note:
                                        "Danh sách sản phẩm không được để trống",
                                  );
                                  return;
                                }
                                for (QuoteProductEntity element
                                    in (state.listProduct ?? [])) {
                                  final isNotSerial =
                                      ((element.serials ?? []).isNotEmpty &&
                                          (element.serials ?? []).length !=
                                              (element.quantity ?? 0));
                                  if (isNotSerial) {
                                    buildPopupError(
                                      note:
                                          "Số lượng serial không khớp với số lượng sản phẩm ${element.itemName}",
                                    );
                                    return;
                                  }
                                  if (element.unitPrice == null) {
                                    buildPopupError(
                                      note: "Giá sản phẩm không được để trống",
                                    );
                                    return;
                                  }
                                }
                                if (state.paymentType?.paymentType
                                            ?.keyToServer ==
                                        null ||
                                    state.paymentType?.paymentType
                                            ?.keyToServer ==
                                        PaymentQuoteEnum.none.keyToServer) {
                                  buildPopupError(
                                    note: "Hãy chọn phương thức thanh toán",
                                  );
                                  return;
                                }

                                final result =
                                    await provider.updateDetailCustomerQuote(
                                        productCode: widget.arguments?.code,
                                        body: UpdateDetailCustomerQuoteBody(
                                          customerInfo: customer,
                                          type: widget.arguments?.customerInfo
                                              ?.customerType,
                                          items: state.listProduct,
                                          method: state.paymentType?.paymentType
                                              ?.keyToServer,
                                          images: listImage,
                                          urls: files,
                                          surcharge: surcharge.toDouble(),
                                          discount: discount.toDouble(),
                                          note: noteController.text,
                                        ));
                                if (!context.mounted) return;
                                if (result) {
                                  onShowPopup(
                                    isUpdate: true,
                                  );
                                  context.pop(state.detailInfo);
                                }
                              }
                            },
                          ),
                        ),
                      )
                    : IgnorePointer(
                        ignoring: state.updateStatus == LoadStatus.loading,
                        child: Padding(
                          padding: const EdgeInsets.all(
                            BaseSpacing.spacing4,
                          ),
                          child: BaseButton(
                            text: "Tạo",
                            backgroundColor: BaseColors.primary,
                            textColor: BaseColors.textOnColor,
                            onTap: () async {
                              FocusScope.of(context).unfocus();
                              final surcharge =
                                  moneyParse(surchargeController.text);
                              final discount =
                                  moneyParse(discountController.text);
                              final customer = state.userTypeSelected ==
                                      UserType.personal
                                  ? CustomerInfoEntity(
                                      customerId: widget.arguments?.customerInfo
                                              ?.phoneNumber ??
                                          state.infoCus360?.customerId ??
                                          personalPhoneController.text,
                                      gender: widget
                                          .arguments?.customerInfo?.gender,
                                      provinceCode: state.customerAddress
                                              ?.province?.code ??
                                          widget.arguments?.customerInfo
                                              ?.provinceCode ??
                                          state.infoCus360?.provinceCode,
                                      districtCode: state.customerAddress
                                              ?.district?.code ??
                                          widget.arguments?.customerInfo
                                              ?.districtCode ??
                                          state.infoCus360?.districtCode,
                                      wardCode:
                                          state.customerAddress?.ward?.code ??
                                              widget.arguments?.customerInfo
                                                  ?.wardCode ??
                                              state.infoCus360?.wardCode,
                                      phoneNumber: personalPhoneController.text,
                                      fullName: personalNameController.text,
                                      address: state.customerAddress
                                              ?.getFullAddress ??
                                          widget.arguments?.customerInfo
                                              ?.address ??
                                          state.infoCus360?.address,
                                      customerType:
                                          state.userTypeSelected.keyToServer,
                                    )
                                  : CustomerInfoEntity(
                                      customerId: widget.arguments?.customerInfo
                                              ?.taxCode ??
                                          widget.arguments?.customerInfo
                                              ?.customerId ??
                                          state.infoCus360?.taxCode ??
                                          state.infoCus360?.customerId ??
                                          companyTaxCodeController.text,
                                      gender: widget
                                          .arguments?.customerInfo?.gender,
                                      provinceCode: state
                                              .companyAddress?.province?.code ??
                                          widget.arguments?.customerInfo
                                              ?.provinceCode ??
                                          state.infoCus360?.provinceCode,
                                      districtCode: state
                                              .companyAddress?.district?.code ??
                                          widget.arguments?.customerInfo
                                              ?.districtCode ??
                                          state.infoCus360?.districtCode,
                                      wardCode:
                                          state.companyAddress?.ward?.code ??
                                              widget.arguments?.customerInfo
                                                  ?.wardCode ??
                                              state.infoCus360?.wardCode,
                                      taxCode: state.infoCus360?.taxCode ??
                                          companyTaxCodeController.text,
                                      address: state
                                              .companyAddress?.getFullAddress ??
                                          widget.arguments?.customerInfo
                                              ?.address ??
                                          state.infoCus360?.address,
                                      fullName: companyNameController.text,
                                      phoneNumber: companyPhoneController.text,
                                      contactNumber:
                                          companyContactPhoneController.text,
                                      contactName:
                                          companyContactNameController.text,
                                      customerType:
                                          state.userTypeSelected.keyToServer,
                                    );

                              List<String>? listImage = [];
                              List<String> files = [];

                              for (ComplainFileEntity item
                                  in (state.listImageInfo ?? [])) {
                                listImage.add(item.link ?? '');
                              }

                              for (ComplainFileEntity item
                                  in (state.listFileInfo ?? [])) {
                                files.add(item.link ?? '');
                              }

                              if ((formUserKey.currentState?.validate() ??
                                      false) ||
                                  (formCompanyKey.currentState?.validate() ??
                                      false)) {
                                if ((state.listProduct ?? []).isEmpty) {
                                  buildPopupError(
                                    note:
                                        "Danh sách sản phẩm không được để trống",
                                  );
                                  return;
                                }
                                for (QuoteProductEntity element
                                    in (state.listProduct ?? [])) {
                                  if (element.unitPrice == null) {
                                    buildPopupError(
                                      note: "Giá sản phẩm không được để trống",
                                    );
                                    return;
                                  }
                                  final isNotSerial = (element.itemCode !=
                                              null &&
                                          ((element.hasSerial ?? 0) >= 1 ||
                                              element.isSerial == true) &&
                                          (element.serials ?? []).length !=
                                              (element.quantity ?? 0)) ||
                                      (element.itemCode == null &&
                                          (element.serials ?? []).isNotEmpty &&
                                          (element.serials ?? []).length !=
                                              (element.quantity ?? 0));
                                  if (isNotSerial) {
                                    buildPopupError(
                                      note:
                                          "Số lượng serial không khớp với số lượng sản phẩm ${element.itemName}",
                                    );
                                    return;
                                  }
                                }
                                if (state.paymentType?.paymentType
                                            ?.keyToServer ==
                                        null ||
                                    state.paymentType?.paymentType
                                            ?.keyToServer ==
                                        PaymentQuoteEnum.none.keyToServer) {
                                  buildPopupError(
                                    note: "Hãy chọn phương thức thanh toán",
                                  );
                                  return;
                                }

                                final bool result =
                                    await provider.createCustomerQuote(
                                        productCode: widget.arguments?.code,
                                        body: NewCustomerQuoteBody(
                                          customerInfo: customer,
                                          items: state.listProduct,
                                          method: state.paymentType?.paymentType
                                              ?.keyToServer,
                                          images: listImage,
                                          urls: files,
                                          surcharge: surcharge.toDouble(),
                                          discount: discount.toDouble(),
                                          note: noteController.text,
                                        ));
                                if (!context.mounted) return;
                                if (result) {
                                  onShowPopup(
                                    isUpdate: false,
                                  );
                                  context.pop(result);
                                }
                              }
                            },
                          ),
                        ),
                      ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// thông tin khách hàng
  Widget buildCCustomerInfo() {
    final state = ref.watch(createCustomerQuoteProvider);
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: Text(
              "Thông tin khách hàng",
              style: UITextStyle.body1SemiBold,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing3,
            ),
            child: Text(
              "Loại khách hàng",
              style: UITextStyle.body2SemiBold,
            ),
          ),
          IgnorePointer(
            ignoring: widget.arguments?.customerInfo != null,
            child: Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing6,
              ),
              child: Row(
                children: [
                  Expanded(
                    child: RadioWidget<UserType?>(
                      value: UserType.personal,
                      groupValue: state.userTypeSelected,
                      onChanged: (value) {
                        ref
                            .read(createCustomerQuoteProvider.notifier)
                            .changedUserType(type: value);
                      },
                      displayWidget: (context, item) {
                        return Text(
                          item?.display ?? '',
                          style: UITextStyle.body1Regular.copyWith(
                            color: BaseColors.textBody,
                          ),
                        );
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioWidget<UserType?>(
                      value: UserType.company,
                      groupValue: state.userTypeSelected,
                      onChanged: (value) {
                        ref
                            .read(createCustomerQuoteProvider.notifier)
                            .changedUserType(type: value);
                      },
                      displayWidget: (context, item) {
                        return Text(
                          item?.display ?? '',
                          style: UITextStyle.body1Regular.copyWith(
                            color: BaseColors.textBody,
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
          ),
          state.userTypeSelected == UserType.personal
              ? _buildPersonalInfoView()
              : _buildCompanyInfoView(),
          Padding(
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: noteController,
              focusNode: noteFocusNode,
              labelText: "Ghi chú",
              textInputAction: TextInputAction.done,
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  noteFocusNode.unfocus();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  /// cá nhân
  Widget _buildPersonalInfoView() {
    final state = ref.watch(createCustomerQuoteProvider);
    return Form(
      key: formUserKey,
      autovalidateMode: AutovalidateMode.always,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            key: userPhoneNumberKey,
            controller: personalPhoneController,
            focusNode: personalPhoneFocusNode,
            enabled: widget.arguments?.customerInfo == null,
            isRequired: true,
            labelText: "Số điện thoại",
            maxLength: 11,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            onChanged: (value) {
              if (value.isNotEmpty) {
                if (personalPhoneController.text.validatePhone()) {
                  getCustomerInfo360(
                    phoneNumber: personalPhoneController.text,
                    isCustomerInfo: true,
                  );
                }
              } else {
                ref
                    .read(createCustomerQuoteProvider.notifier)
                    .enableEditUserForm();
              }
            },
            onClear: () {
              ref
                  .read(createCustomerQuoteProvider.notifier)
                  .enableEditUserForm();
            },
            suffix: !state.turnOnEditUserInfo
                ? Cus360Widget(
                    customerType: UserType.personal.keyToServer,
                    customerPhone: personalPhoneController.text,
                    customerId: int.tryParse(state.customerInfo?.customerId ??
                        state.infoCus360?.customerId ??
                        ''),
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidatePhone(
                value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                if (personalPhoneController.text.validatePhone()) {
                  getCustomerInfo360(
                    phoneNumber: personalPhoneController.text,
                  );
                }
                personalPhoneFocusNode.unfocus();
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: personalNameController,
              focusNode: personalNameFocusNode,
              isRequired: true,
              labelText: "Họ và tên",
              enabled: state.turnOnEditUserInfo,
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.words,
              onChanged: (value) {},
              onClear: () => personalNameController.clear,
              inputFormatters: [
                SpaceInputFormatter(),
                FilteringTextInputFormatter.allow(
                  RegExp(
                    r'[a-zA-Zà-ỹÀ-ỹ ]',
                  ),
                ),
              ],
              validator: (value) {
                return ValidateUtils.onValidateUserName(
                  value,
                );
              },
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  personalNameController.text = StringUtils.capitalizeEachWord(
                    personalNameController.text,
                  );
                }
              },
            ),
          ),
          Focus(
            focusNode: personalAddressFocusNode,
            onFocusChange: (isFocus) {
              if (!isFocus) {
                personalAddressFocusNode.unfocus();
              }
            },
            child: DropdownWidget(
              labelText: "Địa chỉ",
              isRequired: true,
              showErrorOnFocus: true,
              content: state.customerAddress?.address ??
                  widget.arguments?.customerInfo?.address ??
                  state.infoCus360?.address,
              suffix: MyAssets.icons.iconArrowRightS20.svg(),
              validator: (value) {
                return ValidateUtils.onValidateAddress(
                  value,
                );
              },
              onTap: () {
                onSelectAddress();
              },
            ),
          ),
        ],
      ),
    );
  }

  /// doanh nghiệp
  Widget _buildCompanyInfoView() {
    var state = ref.watch(createCustomerQuoteProvider);
    const key = ValueKey(
      "company",
    );

    return Form(
      key: formCompanyKey,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            controller: companyTaxCodeController,
            focusNode: companyTaxCodeFocusNode,
            isRequired: true,
            labelText: "Mã số thuế doanh nghiệp",
            maxLength: 14,
            textInputAction: TextInputAction.done,
            enabled: widget.arguments?.customerInfo == null,
            maxSizeSuffix: true,
            onChanged: (value) {
              if (value.isNotEmpty) {
                if (companyTaxCodeController.text.validatePhone()) {
                  getCompanyInfo360(
                    taxCode: companyTaxCodeController.text,
                    isCustomerInfo: true,
                  );
                }
              } else {
                ref
                    .read(createCustomerQuoteProvider.notifier)
                    .enableEditCompanyForm();
              }
            },
            onClear: () {
              ref
                  .read(createCustomerQuoteProvider.notifier)
                  .enableEditCompanyForm();
            },
            suffix: !state.turnOnEditCompanyInfo
                ? Cus360Widget(
                    customerType: UserType.company.keyToServer,
                    customerPhone: personalPhoneController.text,
                    taxCode: state.infoCus360?.taxCode ??
                        companyTaxCodeController.text,
                    customerId: int.tryParse(state.customerInfo?.customerId ??
                        state.infoCus360?.customerId ??
                        ''),
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidateTax(
                value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                getCompanyInfo360(
                  taxCode: companyTaxCodeController.text,
                );
                companyTaxCodeFocusNode.unfocus();
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: companyNameController,
              focusNode: companyNameFocusNode,
              labelText: "Tên doanh nghiệp",
              isRequired: true,
              enabled: state.turnOnEditCompanyInfo,
              textInputAction: TextInputAction.done,
              inputFormatters: [
                SpaceInputFormatter(),
              ],
              validator: (value) {
                return ValidateUtils.onValidateCompanyName(
                  value,
                );
              },
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  companyNameFocusNode.unfocus();
                }
              },
            ),
          ),
          TextFieldWidget(
            controller: companyPhoneController,
            focusNode: companyPhoneFocusNode,
            labelText: "Số điện thoại doanh nghiệp",
            isRequired: true,
            enabled: state.turnOnEditCompanyInfo,
            textInputAction: TextInputAction.done,
            keyboardType: TextInputType.phone,
            inputFormatters: [
              SpaceInputFormatter(),
            ],
            validator: (value) {
              return ValidateUtils.onValidateCompanyPhone(
                value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                companyPhoneFocusNode.unfocus();
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: Focus(
              focusNode: companyAddressFocusNode,
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  companyAddressFocusNode.unfocus();
                }
              },
              child: DropdownWidget(
                labelText: "Địa chỉ doanh nghiệp",
                isRequired: true,
                content: state.companyAddress?.address ??
                    widget.arguments?.customerInfo?.address ??
                    state.infoCus360?.address,
                validator: (value) {
                  return ValidateUtils.onValidateAddress(
                    value,
                  );
                },
                suffix: MyAssets.icons.iconArrowRightS20.svg(),
                onTap: () {
                  onSelectAddress(
                    isCompany: true,
                  );
                },
              ),
            ),
          ),
          TextFieldWidget(
            controller: companyContactNameController,
            focusNode: companyContactNameFocusNode,
            labelText: "Họ và tên người liên hệ",
            isRequired: true,
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(
                  r'[a-zA-Zà-ỹÀ-ỹ ]',
                ),
              ),
            ],
            validator: (value) {
              return ValidateUtils.onValidateContactUserName(
                value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                companyContactNameController.text =
                    StringUtils.capitalizeEachWord(
                  companyContactNameController.text,
                );
                companyContactNameFocusNode.unfocus();
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: companyContactPhoneController,
              focusNode: companyContactPhoneFocusNode,
              isRequired: true,
              labelText: "Số điện thoại người liên hệ",
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.phone,
              validator: (value) {
                return ValidateUtils.onValidatePhone(
                  value,
                );
              },
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  companyContactPhoneFocusNode.unfocus();
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  void onSelectAddress({
    bool isCompany = false,
  }) async {
    var state = ref.watch(createCustomerQuoteProvider);
    AddressEntity? address;
    AddressInfo? addressInfo =
        isCompany ? state.companyAddress : state.customerAddress;

    if (addressInfo != null) {
      address = AddressEntity(
        addressDetail: addressInfo.addressDetail?.split(',').first,
        ward: addressInfo.ward,
        district: addressInfo.district,
        province: addressInfo.province,
        isGetFromLocal: addressInfo.isGetFromLocal,
      );
    }

    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) {
          ref.read(createCustomerQuoteProvider.notifier).selectAddress(
                address.convertToAddressInfo,
              );
        },
      ),
    );
  }

  void getCompanyInfo360({
    String? taxCode,
    bool? isCustomerInfo,
  }) async {
    if ((taxCode ?? '').validateTax()) {
      final result = await ref
          .read(createCustomerQuoteProvider.notifier)
          .getCompanyInfo360(
            taxCode: taxCode,
            isCustomerInfo: isCustomerInfo,
          );

      if (result != null) {
        companyNameController.text =
            widget.arguments?.customerInfo?.fullName ?? result.fullName ?? '';
        companyPhoneController.text =
            widget.arguments?.customerInfo?.phoneNumber ??
                result.phoneNumber ??
                '';
      } else {
        companyNameController.text =
            widget.arguments?.customerInfo?.fullName ?? "";
      }
      companyTaxCodeFocusNode.unfocus();
    } else {
      ref.read(createCustomerQuoteProvider.notifier).enableEditCompanyForm();
      companyNameController.text = "";
    }
  }

  void getCustomerInfo360({
    String? phoneNumber,
    bool? isCustomerInfo,
  }) async {
    if ((phoneNumber ?? '').validatePhone()) {
      final result = await ref
          .read(createCustomerQuoteProvider.notifier)
          .getCustomerInfo360(
            phoneNumber: phoneNumber,
            isCustomerInfo: isCustomerInfo,
          );
      if (result != null) {
        personalNameController.text =
            widget.arguments?.customerInfo?.fullName ?? result.fullName ?? '';
      } else {
        personalNameController.text =
            widget.arguments?.customerInfo?.fullName ?? "";
      }
      personalPhoneFocusNode.unfocus();
    } else {
      ref.read(createCustomerQuoteProvider.notifier).enableEditUserForm();
      personalNameController.clear();
    }
  }

  /// danh sách sản phẩm
  Widget buildProductListView() {
    final state = ref.watch(createCustomerQuoteProvider);
    final provider = ref.read(createCustomerQuoteProvider.notifier);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
            horizontal: BaseSpacing.spacing4,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Danh sách sản phẩm",
                style: UITextStyle.body1SemiBold,
              ),
              InkWellWidget(
                onTap: () {
                  addNewProduct(
                    mContext: context,
                    onNewProduct: () async {
                      final result = await context.push<QuoteProductEntity>(
                        RouterPaths.editProductQuote,
                        extra: EditProductQuoteArguments(
                          isSerial: true,
                        ),
                      );
                      if (result != null) {
                        provider.addNewProduct(
                          product: result,
                        );
                      }
                    },
                    onAvailableProduct: () async {
                      // sang màn danh sách sản phẩm có sẵn
                      final result =
                          await context.push<List<QuoteProductEntity>>(
                        RouterPaths.availableProductsQuote,
                        extra: AvailableProductsQuoteArgument(
                          listProduct: state.listProduct,
                        ),
                      );
                      if (result != null) {
                        provider.addNewProduct(
                          goods: result,
                        );
                      }
                    },
                  );
                },
                child: Wrap(
                  crossAxisAlignment: WrapCrossAlignment.center,
                  children: [
                    Padding(
                      padding: const EdgeInsets.only(
                        right: BaseSpacing.spacing1,
                      ),
                      child: MyAssets.icons.iconAddCircle.svg(),
                    ),
                    Text(
                      "Thêm",
                      style: UITextStyle.body1SemiBold.copyWith(
                        color: BaseColors.primary,
                      ),
                    ),
                  ],
                ),
              )
            ],
          ),
        ),
        const DividerWidget(
          margin: EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
            horizontal: BaseSpacing.spacing4,
          ),
        ),
        ListView.separated(
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            final product = (state.listProduct ?? [])[index];
            return ProductItemWidget(
              isCreateQuote: true,
              product: product,
              index: index,
              disableChangeQuantity: false,
              updateQuantityInput: (quantity) {
                if ((quantity ?? 0) <= 0) {
                  showDialogConfirmRemoveProduct(
                    index: index,
                    product: product,
                  );
                } else {
                  provider.changeQuantity(
                    indexItem: index,
                    quantity: quantity ?? 0,
                  );
                }
              },
              onPlus: (value) {
                provider.changeQuantity(
                  indexItem: index,
                  quantity: value,
                );
              },
              onMinus: (value) {
                final result = provider.changeQuantity(
                  indexItem: index,
                  quantity: value,
                );

                if (!(result ?? true)) {
                  showDialogConfirmRemoveProduct(
                    index: index,
                    product: product,
                  );
                }
              },
              onRemoveProduct: (context) {
                showDialogConfirmRemoveProduct(
                  index: index,
                  product: product,
                );
              },
              onSerialsChangedAction: () {
                getEditProductInfo(
                  product: product,
                );
              },
              onChangedStr: (value) {
                getEditProductInfo(
                  product: product,
                );
              },
            );
          },
          separatorBuilder: (context, index) => DividerWidget(
            height: 1,
            color: BaseColors.backgroundGray,
            margin: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
              horizontal: BaseSpacing.spacing4,
            ),
          ),
          itemCount: (state.listProduct ?? []).length,
        ),
      ],
    );
  }

  /// thông tin khác
  Widget buildOtherInfoView() {
    final state = ref.watch(createCustomerQuoteProvider);
    List<ComplainFileEntity>? listImage = [];
    List<ComplainFileEntity> files = [];

    state.listImageInfo?.forEach(
      (item) {
        listImage.add(item);
      },
    );

    for (var item in (state.listFileInfo ?? [])) {
      files.add(item);
    }

    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Thông tin khác",
            style: UITextStyle.body1SemiBold,
          ),
          const DividerWidget(
            margin: EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
          ),
          RequiredLabelWidget(
            labelText: 'Ảnh:',
            required: false,
            subText: "Cho phép tải lên tối đa 5 ảnh",
            subTextColor: BaseColors.primary,
          ),
          buildListImageView(
            listImage: listImage,
          ),
          const Padding(
            padding: EdgeInsets.only(
              top: BaseSpacing.spacing4,
            ),
            child: RequiredLabelWidget(
              labelText: 'File đính kèm:',
              required: false,
            ),
          ),
          buildFileView(
            files: files,
          ),
        ],
      ),
    );
  }

  Widget buildListImageView({
    required List<ComplainFileEntity> listImage,
  }) {
    final state = ref.watch(createCustomerQuoteProvider);
    final provider = ref.read(createCustomerQuoteProvider.notifier);
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
      ),
      child: SizedBox(
        height: 80,
        child: ListView.separated(
          shrinkWrap: true,
          itemCount: listImage.length + 1,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.only(
            right: BaseSpacing.spacing4,
          ),
          separatorBuilder: (_, __) => const SizedBox(
            width: BaseSpacing.spacing2,
          ),
          reverse: true,
          itemBuilder: (context, index) {
            if (index == listImage.length) {
              return listImage.length < 5
                  ? Stack(
                      children: [
                        InkWellWidget(
                          onTap: () {
                            onTakePicture(
                              index,
                            );
                          },
                          child: MyAssets.icons.iconAddImageDashline.svg(),
                        ),
                        if (state.uploadImageStatus == LoadStatus.loading)
                          const Positioned.fill(
                            child: Center(
                              child: LoadingIndicatorWidget(),
                            ),
                          ),
                      ],
                    )
                  : const SizedBox.shrink();
            }
            return Stack(
              children: [
                Container(
                  height: 80,
                  width: 80,
                  padding: const EdgeInsets.only(
                    top: BaseSpacing.spacing1,
                    right: BaseSpacing.spacing1,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(8),
                    child: ImageWidget(
                      enableShowPreview: true,
                      listImage[index].link ?? '',
                    ),
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  child: Visibility(
                    visible: true,
                    child: InkWellWidget(
                      onTap: () {
                        provider.deleteImage(
                          listImage[index],
                        );
                      },
                      child: MyAssets.icons.iconCloseRed.svg(),
                    ),
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget buildFileView({
    required List<ComplainFileEntity> files,
  }) {
    final provider = ref.read(createCustomerQuoteProvider.notifier);
    final index = files.length;

    List<ComplainFileEntity> images = [];
    List<ComplainFileEntity> pdf = [];
    for (var element in files) {
      bool isPdf = FileTypeOption.isPdfLink(
        filePath: element.link ?? '',
      );
      if (isPdf) {
        pdf.add(element);
      } else {
        images.add(element);
      }
    }

    return Padding(
      padding: const EdgeInsets.only(
        top: BaseSpacing.spacing2,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildOnTakeFileVIew(
            onTap: () {
              onTakeFile(
                index,
              );
            },
          ),
          // list image
          if (images.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing2,
              ),
              child: SizedBox(
                height: 80,
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: images.length,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(
                    right: BaseSpacing.spacing4,
                  ),
                  separatorBuilder: (_, __) => const SizedBox(
                    width: BaseSpacing.spacing2,
                  ),
                  itemBuilder: (context, index) {
                    return Stack(
                      children: [
                        Container(
                          height: 80,
                          width: 80,
                          padding: const EdgeInsets.only(
                            top: BaseSpacing.spacing1,
                            right: BaseSpacing.spacing1,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                              borderRadius: BorderRadius.circular(8),
                              child: ImageWidget(
                                enableShowPreview: true,
                                images[index].link ?? '',
                              )),
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Visibility(
                            visible: true,
                            child: InkWellWidget(
                              onTap: () {
                                provider.deleteFile(
                                  images[index],
                                );
                              },
                              child: MyAssets.icons.iconCloseRed.svg(),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
          //list pdf
          if (pdf.isNotEmpty) ...[
            SizedBox(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: pdf.length,
                scrollDirection: Axis.vertical,
                padding: const EdgeInsets.only(
                  right: BaseSpacing.spacing4,
                ),
                separatorBuilder: (_, __) => const SizedBox(
                  width: BaseSpacing.spacing2,
                ),
                physics: const NeverScrollableScrollPhysics(),
                reverse: true,
                itemBuilder: (context, index) {
                  return Stack(
                    children: [
                      Container(
                        height: 85,
                        width: double.infinity,
                        padding: const EdgeInsets.only(
                          top: BaseSpacing.spacing1,
                          right: BaseSpacing.spacing1,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: DocumentReportFileWidget(
                              label: "File đính kèm",
                              file: ListFileRecord(
                                fileName:
                                    pdf[index].link?.split('/').last ?? '',
                                link: pdf[index].link ?? '',
                              ),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Visibility(
                          visible: true,
                          child: InkWellWidget(
                            onTap: () {
                              provider.deleteFile(
                                pdf[index],
                              );
                            },
                            child: MyAssets.icons.iconCloseRed.svg(),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  void showDialogConfirmRemoveProduct({
    required int index,
    required QuoteProductEntity product,
  }) {
    final provider = ref.read(createCustomerQuoteProvider.notifier);
    AppDialog.showDialogConfirm(
      context,
      barrierDismissible: false,
      title: "Xác nhận",
      message: 'Bạn có chắc chắn muốn xoá sản phẩm "${product.itemName}"?',
      buttonNameConfirm: "Xoá",
      onConfirmAction: () async {
        provider.removeProduct(
          index: index,
          product: product,
        );
      },
      buttonNameCancel: "Hủy bỏ",
      onCancelAction: () {
        provider.changeQuantity(
          indexItem: index,
          quantity: product.quantity ?? 1,
        );
      },
    );
  }

  void onTakePicture(int index) {
    final state = ref.read(createCustomerQuoteProvider);
    final provider = ref.read(createCustomerQuoteProvider.notifier);
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Upload ảnh",
      isFlexible: true,
      child: UploadImageBottomSheet(
        onPickMultiImage: (file) async {
          List<ComplainFileEntity> listFile = [];

          for (var item in file) {
            final result = await provider.uploadFileV2(
              file: item,
            );
            listFile.addAll(result);
          }
          final validator =
              validatorImage((state.listImageInfo ?? []) + listFile);
          if (validator) {
            provider.addImage(
              files: listFile,
            );
          }
        },
        onTakeImage: (file) async {
          final listFile = await provider.uploadFileV2(
            file: file,
          );
          final validator =
              validatorImage((state.listImageInfo ?? []) + listFile);
          if (validator) {
            provider.addImage(
              files: listFile,
            );
          }
        },
      ),
    );
  }

  void onTakeFile(int index) async {
    final provider = ref.read(createCustomerQuoteProvider.notifier);
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Tải lên tập tin",
      isFlexible: true,
      child: UploadImageBottomSheet(
        extensions: const [
          'png',
          'jpg',
          'pdf',
          'PNG',
          'JPG',
          'PDF',
        ],
        onPickFile: (file) async {
          final listFile = await provider.uploadFileV2(
            isFile: true,
            file: file,
          );
          provider.addFile(
            files: listFile,
          );
        },
      ),
    );
  }

  bool validatorImage(List<ComplainFileEntity> files) {
    bool isCheck = true;
    if (files.length > 5) {
      AppDialog.showDialogCenter(
        ErrorDialog.navigatorKey.currentContext!,
        message: "Không chọn ảnh quá 5 ảnh",
        status: DialogStatus.error,
      );
      return isCheck = false;
    }
    return isCheck;
  }

  Widget _buildOnTakeFileVIew({
    void Function()? onTap,
  }) {
    final state = ref.watch(createCustomerQuoteProvider);

    return Stack(
      children: [
        InkWell(
          onTap: onTap,
          child: DottedBorder(
            color: BaseColors.borderDefault,
            radius: const Radius.circular(
              BaseSpacing.spacing2,
            ),
            borderType: BorderType.rRect,
            padding: const EdgeInsets.all(
              BaseSpacing.spacing2,
            ),
            dashPattern: const [6, 8],
            child: Container(
              padding: const EdgeInsets.all(
                BaseSpacing.spacing2,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      right: BaseSpacing.spacing2,
                    ),
                    child: MyAssets.icons.iconDocUpload.svg(),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tải lên tập tin',
                        style: UITextStyle.body1Regular.copyWith(
                          color: BaseColors.textBody,
                        ),
                      ),
                      Text(
                        'Định dạng PDF, JPG, PNG',
                        style: UITextStyle.body1Regular.copyWith(
                          color: BaseColors.textSubtitle,
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        if (state.loadStatus == LoadStatus.loading)
          const Positioned.fill(
            child: Center(
              child: LoadingIndicatorWidget(),
            ),
          ),
      ],
    );
  }

  void addNewProduct({
    Function()? onNewProduct,
    Function()? onAvailableProduct,
    required BuildContext mContext,
  }) {
    AppDialog.showDialogSelected(
      mContext,
      title: "Thêm sản phẩm",
      height: 165,
      widgetContent: (onClose) {
        return SizedBox(
          width: double.infinity,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              InkWellWidget(
                onTap: () {
                  onNewProduct!.call();
                  onClose();
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    vertical: BaseSpacing.spacing2,
                  ),
                  child: Text(
                    'Sản phẩm mới',
                    style: UITextStyle.body1Regular,
                  ),
                ),
              ),
              const DividerWidget(
                height: 2,
              ),
              InkWellWidget(
                onTap: () {
                  onAvailableProduct!.call();
                  onClose();
                },
                child: Container(
                  width: double.infinity,
                  padding: const EdgeInsets.symmetric(
                    vertical: BaseSpacing.spacing2,
                  ),
                  child: Text(
                    'Sản phẩm có sẵn',
                    style: UITextStyle.body1Regular,
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  void getEditProductInfo({
    required QuoteProductEntity product,
  }) async {
    final provider = ref.read(createCustomerQuoteProvider.notifier);
    // chuyển màn sửa thông tin sản phẩm
    final result = await context.push<QuoteProductEntity>(
      RouterPaths.editProductQuote,
      extra: EditProductQuoteArguments(
        isEdit: true,
        item: product,
        isSerial: product.isSerial ?? (product.serials ?? []).isNotEmpty,
      ),
    );
    if (result != null) {
      if ((result.itemCode ?? '').isNotEmpty) {
        if (product.itemCode == result.itemCode) {
          provider.changeInfoProduct(
            product: result,
          );
        }
      } else if (product.itemName == result.itemName) {
        provider.changeInfoProduct(
          product: result,
        );
      }
    }
  }

  void onShowPopup({
    required bool isUpdate,
  }) {
    AppDialog.showDialogCenter(
      context,
      message: isUpdate ? "Cập nhật thành công" : "Tạo thành công",
      status: DialogStatus.success,
    );
  }

  void buildPopupError({
    required String note,
  }) {
    AppDialog.showDialogCenter(
      context,
      message: note,
      status: DialogStatus.error,
    );
  }

  int moneyParse(String value) {
    return int.tryParse(
          value.replaceAll(RegExp(r'[^0-9]'), ''),
        ) ??
        0;
  }

  String moneyFormat(String value) {
    return StringUtils.displayMoney(
      int.tryParse(
            value.replaceAll(RegExp(r'[^0-9]'), ''),
          ) ??
          0,
    );
  }
}
