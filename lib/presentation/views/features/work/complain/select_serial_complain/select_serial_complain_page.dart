import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/widgets.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/entities/complain/check_serial_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/features/work/complain/select_serial_complain/select_serial_complain_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/deBouncer.dart';

class SelectSerialComplainPage extends StatefulHookConsumerWidget {
  final SelectSerialComplainArguments? arguments;

  const SelectSerialComplainPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<SelectSerialComplainPage> createState() =>
      _SelectContractComplainPageState();
}

class _SelectContractComplainPageState
    extends ConsumerState<SelectSerialComplainPage> {
  late ScrollController controller;
  late Debounce<String> deBouncer;
  late TextEditingController searchController;

  @override
  void initState() {
    searchController = TextEditingController();
    controller = ScrollController();
    controller.addListener(_scrollListener);
    Future(
      () {
        ref.read(selectSerialComplainProvider.notifier).initialData(
              customerPhone: widget.arguments?.customerPhone,
              goodsName: widget.arguments?.goodsName,
              serialSelected: widget.arguments?.serialSelected ?? [],
            );
      },
    );
    deBouncer = Debounce<String>(
      const Duration(
        milliseconds: 500,
      ),
      (value) {
        ref.read(selectSerialComplainProvider.notifier).getListContractComplain(
              keyword: (value ?? '').trim(),
            );
      },
    );
    super.initState();
  }

  @override
  dispose() {
    searchController.dispose();
    controller.dispose();
    super.dispose();
  }

  Future<void> refreshData() async {
    ref.read(selectSerialComplainProvider.notifier).getListContractComplain();
  }

  void _scrollListener() {
    final maxScroll = controller.position.maxScrollExtent;
    final currentScroll = controller.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(selectSerialComplainProvider.notifier).fetchNextCampaign();
    }
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(selectSerialComplainProvider);
    var rf = ref.read(selectSerialComplainProvider.notifier);
    PreferredSizeWidget appbarWidget;

    appbarWidget = const AppBarCustom(
      title: "Chọn serial",
    );

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: LayoutPage(
        appbar: appbarWidget,
        bottomAction: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: BaseSpacing.spacing4,
            vertical: BaseSpacing.spacing2,
          ),
          child: SafeArea(
            child: BaseButton(
              text: "Xác nhận",
              onTap: () async {
                context.pop(
                  state.listSerialSelected ?? [],
                );
              },
            ),
          ),
        ),
        body: RefreshIndicatorWidget(
          onRefresh: refreshData,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              if ((state.listSerialSelected ?? []).isNotEmpty)
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: BaseSpacing.spacing6,
                    vertical: BaseSpacing.spacing4,
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Serial đã chọn",
                        style: UITextStyle.body2Medium,
                      ),
                      InkWellWidget(
                        onTap: () {
                          rf.resetSelectedSerial();
                        },
                        child: Text(
                          "Thiết lập lại",
                          style: UITextStyle.caption1Medium.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              SizedBox(
                width: MediaQuery.of(context).size.width,
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: BaseSpacing.spacing6,
                  ),
                  child: Wrap(
                    children: (state.listSerialSelected ?? [])
                        .map(
                          (item) => Visibility(
                            visible: item.isCheck ?? false,
                            child: Padding(
                              padding: const EdgeInsets.only(
                                top: BaseSpacing.spacing1,
                                bottom: BaseSpacing.spacing1,
                                right: BaseSpacing.spacing1,
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: BaseColors.backgroundGray1,
                                  borderRadius: BorderRadius.circular(
                                    BaseSpacing.spacing2,
                                  ),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.all(
                                    BaseSpacing.spacing2,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Text(item.serial ?? ''),
                                      InkWellWidget(
                                        onTap: () {
                                          rf.removeSerial(
                                            value: Serial(
                                              serial: item.serial,
                                              isCheck: false,
                                            ),
                                          );
                                        },
                                        child: MyAssets.icons.closeCircle.svg(),
                                      )
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          ),
                        )
                        .toList(),
                  ),
                ),
              ),
              const Padding(
                padding: EdgeInsets.only(
                  top: BaseSpacing.spacing3,
                  bottom: BaseSpacing.spacing1,
                ),
                child: DividerWidget(
                  height: BaseSpacing.spacing2,
                ),
              ),
              SearchTextFieldWidget(
                controller: searchController,
                hintText: "Mã serial",
                onChanged: (value) {
                  deBouncer.value = value;
                },
                onSubmitted: (value) {
                  ref
                      .read(selectSerialComplainProvider.notifier)
                      .getListContractComplain(
                        keyword: searchController.text,
                      );
                },
              ).paddingSymmetric(
                vertical: BaseSpacing.spacing2,
                horizontal: BaseSpacing.spacing6,
              ),
              Expanded(
                child: _buildListEvent(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildListEvent() {
    final state = ref.watch(selectSerialComplainProvider);
    final rf = ref.read(selectSerialComplainProvider.notifier);

    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Hệ thống đang bảo trì, vui lòng thử lại sau.',
        onRefresh: refreshData,
      );
    } else {
      if ((state.listSerial ?? []).isEmpty) {
        return EmptyListWidget(
          title: "Không tìm thấy",
          onRefresh: refreshData,
        );
      } else {
        return ListView.builder(
          shrinkWrap: true,
          controller: controller,
          padding: EdgeInsets.zero,
          itemCount: (state.listSerial ?? []).length,
          itemBuilder: (context, index) {
            final item = state.listSerial![index];

            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                InkWellWidget(
                  onTap: () {
                    Navigator.pop(
                      context,
                      item,
                    );
                  },
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: <Widget>[
                      BaseCheckBox(
                        value: item.isCheck ?? false,
                        onCheckBoxChange: (value) {
                          rf.updateSerial(
                            value: Serial(
                              serial: item.serial,
                              isCheck: value,
                            ),
                          );
                        },
                      ),
                      Text(
                        item.serial ?? '',
                        style: UITextStyle.body1Regular,
                      ),
                    ],
                  ).paddingSymmetric(
                    horizontal: 10,
                  ),
                ),
                const DividerWidget().paddingSymmetric(
                  horizontal: BaseSpacing.spacing6,
                  vertical: BaseSpacing.spacing2,
                ),
              ],
            );
          },
        );
      }
    }
  }
}
