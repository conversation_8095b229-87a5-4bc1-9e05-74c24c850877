import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/address_info.dart';
import 'package:vcc/domain/entities/complain/contract_complain_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/enums/complain/create_complain/enum_category_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/params/create_complain/complain_image.dart';
import 'package:vcc/extensions/file_to_base64.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_image/upload_image.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/drop_down_complain_widget.dart';
import 'package:vcc/presentation/views/widgets/image_base64_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/cus360_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/presentation/views/widgets/video_widget.dart';
import 'package:vcc/utils/input_formatter_utils.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';
import 'create_complain_info_view_model.dart';

class CreateComplainArguments {
  final UserType? userType;
  final String? customerPhone;
  final String? companyTaxCode;

  CreateComplainArguments({
    this.userType,
    this.customerPhone,
    this.companyTaxCode,
  });
}

class CreateComplainPage extends StatefulHookConsumerWidget {
  final CreateComplainArguments? arguments;

  const CreateComplainPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<CreateComplainPage> createState() =>
      _CreateComplaintPageState();
}

class _CreateComplaintPageState extends ConsumerState<CreateComplainPage> {
  late TextEditingController userPhoneNumberController;
  late TextEditingController userContactPhoneNumberController;
  late TextEditingController userNameController;
  late TextEditingController userContactNameController;
  late TextEditingController taxCompanyController;
  late TextEditingController companyNameController;
  late TextEditingController companyPhoneNumberController;
  late TextEditingController companyUserPhoneNumberController;
  late TextEditingController companyUserContactNameController;
  late TextEditingController companyPhoneContactNameController;
  late TextEditingController contentController;
  late TextEditingController serialController;
  late TextEditingController contractController;

  final formUserKey = GlobalKey<FormState>();
  final formCompanyKey = GlobalKey<FormState>();
  final formOrderKey = GlobalKey<FormState>();
  final formComplainKey = GlobalKey<FormState>();
  final GlobalKey<FormFieldState> userPhoneNumberKey =
      GlobalKey<FormFieldState>();

  final FocusNode phoneNumberFocus = FocusNode();
  final FocusNode serialFocus = FocusNode();
  final FocusNode contractFocus = FocusNode();
  final FocusNode taxFocusNode = FocusNode();
  final FocusNode contentFocusNode = FocusNode();
  bool isValidSerial = false;
  bool isValidContract = false;

  @override
  void initState() {
    userPhoneNumberController = TextEditingController();
    userNameController = TextEditingController();
    userContactPhoneNumberController = TextEditingController();
    userContactNameController = TextEditingController();
    taxCompanyController = TextEditingController();
    companyNameController = TextEditingController();
    companyPhoneNumberController = TextEditingController();
    companyUserPhoneNumberController = TextEditingController();
    contentController = TextEditingController();
    companyUserContactNameController = TextEditingController();
    companyPhoneContactNameController = TextEditingController();
    serialController = TextEditingController();
    contractController = TextEditingController();

    if (widget.arguments != null) {
      Future(
        () {
          ref.read(createComplainInfoProvider.notifier).changeUserType(
                widget.arguments?.userType ?? UserType.personal,
              );
          userPhoneNumberController.text =
              widget.arguments?.customerPhone ?? '';
          checkStaff(
            context: context,
            phoneNumber: widget.arguments?.customerPhone,
          );
        },
      );
    }
    super.initState();
    Future(
      () {
        ref.read(createComplainInfoProvider.notifier).getData();
      },
    );
  }

  @override
  void dispose() {
    userPhoneNumberController.dispose();
    userNameController.dispose();
    userContactNameController.dispose();
    userContactPhoneNumberController.dispose();
    taxCompanyController.dispose();
    companyNameController.dispose();
    companyPhoneNumberController.dispose();
    companyUserPhoneNumberController.dispose();
    companyUserContactNameController.dispose();
    companyPhoneContactNameController.dispose();
    serialController.dispose();
    contractController.dispose();
    taxFocusNode.dispose();
    phoneNumberFocus.dispose();
    contentController.dispose();
    contentFocusNode.dispose();
    serialFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createComplainInfoProvider);
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: LayoutPage(
        appbar: const AppBarCustom(
          title: "Tạo khiếu nại - phản ánh",
        ),
        bottomAction: IgnorePointer(
          ignoring: state.loadStatus == LoadStatus.loading ||
              state.uploadImageStatus == LoadStatus.loading,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: BaseSpacing.spacing4,
              vertical: BaseSpacing.spacing2,
            ),
            child: SafeArea(
              child: BaseButton(
                text: "Lưu",
                onTap: () async {
                  onSaveInfo();
                },
              ),
            ),
          ),
        ),
        body: state.createComplainStatus == LoadStatus.loading
            ? const Center(
                child: LoadingIndicatorWidget(),
              )
            : Stack(
                children: [
                  SingleChildScrollView(
                    child: Form(
                      key: formComplainKey,
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          _buildCustomerInfo(),
                          const DividerWidget(
                            height: BaseSpacing.spacing2,
                          ),
                          _buildOrderInfo(),
                          const DividerWidget(
                            height: BaseSpacing.spacing2,
                          ),
                          _buildComplainInfo()
                        ],
                      ),
                    ),
                  ),
                  if (state.loadStatus == LoadStatus.loading ||
                      state.uploadImageStatus ==
                          LoadStatus.loading) ...<Widget>[
                    const Center(
                      child: LoadingIndicatorWidget(),
                    ),
                  ],
                ],
              ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    var state = ref.watch(createComplainInfoProvider);
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: Text(
              "Thông tin khách hàng",
              style: UITextStyle.body1SemiBold.copyWith(
                color: BaseColors.textTitle,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing4,
            ),
            child: Text(
              'Loại khách hàng ',
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing5,
            ),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: RadioWidget<UserType>(
                    value: UserType.personal,
                    groupValue: state.userType,
                    onChanged: (value) {
                      ref
                          .read(createComplainInfoProvider.notifier)
                          .changeUserType(
                            value,
                          );
                    },
                    displayWidget: (context, item) {
                      return _buildDisplayUserText(
                        item,
                      );
                    },
                  ),
                ),
                Expanded(
                  child: RadioWidget<UserType>(
                    value: UserType.company,
                    groupValue: state.userType,
                    onChanged: (value) {
                      ref
                          .read(createComplainInfoProvider.notifier)
                          .changeUserType(
                            value,
                          );
                    },
                    displayWidget: (context, item) {
                      return _buildDisplayUserText(item);
                    },
                  ),
                ),
              ],
            ),
          ),
          state.userType == UserType.personal
              ? _buildUserInfo()
              : _buildCompanyInfo(),
        ],
      ),
    );
  }

  Widget _buildOrderInfo() {
    var state = ref.watch(createComplainInfoProvider);
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Form(
        key: formOrderKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              'Thông tin đơn hàng',
              style: UITextStyle.body1SemiBold.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
            Padding(
              padding: const EdgeInsets.only(
                top: BaseSpacing.spacing4,
              ),
              child: DropDownComplainWidget(
                data: state.listCategory ?? [],
                title: "Ngành hàng",
                isRequired: true,
                onChangeItem: (value) {
                  ref.read(createComplainInfoProvider.notifier).changeCategory(
                        value,
                      );
                },
                validator: (value) {
                  return ValidateUtils.onValidateNotNull(
                    title: "Ngành hàng",
                    value: value,
                  );
                },
                selected: state.categoryDropDown,
                displayValue: (item) => item.name ?? '',
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing4,
              ),
              child: DropDownComplainWidget(
                data: state.listService?.toList() ?? [],
                title: "Sản phẩm/Dịch vụ",
                isRequired: true,
                validator: (value) {
                  return ValidateUtils.onValidateNotNull(
                    title: "Sản phẩm/Dịch vụ",
                    value: value,
                  );
                },
                onChangeItem: (value) {
                  ref.read(createComplainInfoProvider.notifier).changeService(
                        value,
                      );
                },
                selected: state.serviceDropDown,
                displayValue: (item) => item.name ?? '',
              ),
            ),
            if (state.categoryDropDown?.businessBranchId ==
                EnumCategoryType.solution.businessBranchId) ...<Widget>[
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: BaseSpacing.spacing4,
                    ),
                    child: Text(
                      'Serial sản phẩm khiếu nại',
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                      bottom: BaseSpacing.spacing4,
                    ),
                    child: InkWellWidget(
                      onTap: () {
                        ref
                            .read(createComplainInfoProvider.notifier)
                            .changeStatusSkipSerial();
                        if (state.isSkipSerial) {
                          serialController.clear();
                        }
                      },
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          (state.isSkipSerial)
                              ? MyAssets.icons.iconChecked.svg()
                              : MyAssets.icons.iconCheckbox.svg(),
                          const SizedBox(
                            width: BaseSpacing.spacing4,
                          ),
                          Text(
                            'Không có serial',
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textBody,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  if (!state.isSkipSerial) ...<Widget>[
                    Padding(
                      padding: const EdgeInsets.only(
                        bottom: BaseSpacing.spacing4,
                      ),
                      child: TextFieldWidget(
                        controller: serialController,
                        isRequired: true,
                        labelText: "Số serial",
                        focusNode: serialFocus,
                        textInputAction: TextInputAction.done,
                        validator: (value) {
                          return ValidateUtils.onValidateNotNull(
                            title: "Số serial",
                            value: value,
                          );
                        },
                        onFocusChange: (isFocus) async {
                          if (!isFocus) {
                            if (serialController.text.isNotEmpty) {
                              isValidSerial = await ref
                                  .read(createComplainInfoProvider.notifier)
                                  .fillContractCodeBySerial(
                                    serial: serialController.text.trim(),
                                  );
                            }
                          }
                        },
                      ),
                    ),
                  ]
                ],
              ),
            ],
            Text(
              'Mã đơn hàng/hợp đồng/YCTX',
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing4,
              ),
              child: InkWellWidget(
                onTap: () {
                  ref
                      .read(createComplainInfoProvider.notifier)
                      .changeStatusContractCode();
                },
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    (state.isSkipContractCode)
                        ? MyAssets.icons.iconChecked.svg()
                        : MyAssets.icons.iconCheckbox.svg(),
                    const SizedBox(
                      width: BaseSpacing.spacing4,
                    ),
                    Text(
                      'Không có mã',
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textBody,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (!state.isSkipContractCode) ...<Widget>[
              Column(
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: BaseSpacing.spacing4,
                    ),
                    child: TextFieldWidget(
                      controller: contractController,
                      isRequired: true,
                      labelText: "Mã đơn hàng/hợp đồng/YCTX",
                      focusNode: contractFocus,
                      textInputAction: TextInputAction.done,
                      validator: (value) {
                        return ValidateUtils.onValidateNotNull(
                          title: "Mã đơn hàng/hợp đồng/YCTX",
                          value: value,
                        );
                      },
                      onFocusChange: (isFocus) async {
                        if (!isFocus) {
                          isValidContract = await ref
                              .read(createComplainInfoProvider.notifier)
                              .getListContractComplain(
                                keyword: contractController.text.trim(),
                              );
                        }
                      },
                    ),
                  ),
                  _buildRowPartnerInfo(
                    label: "Thợ thực hiện",
                    value: state.infoContract?.performerName ?? '',
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                      top: BaseSpacing.spacing4,
                    ),
                    child: _buildRowPartnerInfo(
                      label: "Số điện thoại",
                      value: state.infoContract?.performerPhone ?? '',
                    ),
                  ),
                ],
              ),
            ]
          ],
        ),
      ),
    );
  }

  Widget _buildComplainInfo() {
    var state = ref.watch(createComplainInfoProvider);
    List<ComplainFileEntity>? listImage = [];
    List<ComplainFileEntity>? listVideo = [];

    state.listFile?.forEach(
      (item) {
        if (item.type.isVideo) {
          listVideo.add(item);
        } else if (item.type.isImage) {
          listImage.add(item);
        }
      },
    );
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            'Thông tin phản ánh',
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: DropDownComplainWidget(
              data: state.listComplain ?? [],
              title: "Nhóm sự cố khiếu nại",
              isRequired: true,
              validator: (value) {
                return ValidateUtils.onValidateNotNull(
                  title: "Nhóm sự cố khiếu nại",
                  value: value,
                );
              },
              onChangeItem: (value) {
                ref.read(createComplainInfoProvider.notifier).changeComplain(
                      value,
                    );
              },
              selected: state.complainDropDown,
              displayValue: (item) => item.name ?? '',
            ),
          ),
          DropDownComplainWidget(
            data: state.listComplainType ?? [],
            title: "Loại khiếu nại",
            isRequired: true,
            onChangeItem: (value) {
              ref
                  .read(createComplainInfoProvider.notifier)
                  .changeComplainTypeDropDown(value);
            },
            validator: (value) {
              return ValidateUtils.onValidateNotNull(
                title: "Loại khiếu nại",
                value: value,
              );
            },
            selected: state.complainTypeDropDown,
            displayValue: (item) => item.name ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: contentController,
              textInputAction: TextInputAction.done,
              height: 110,
              alignment: Alignment.topLeft,
              labelText: "Nội dung phản ánh",
              isRequired: true,
              focusNode: contentFocusNode,
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  RegExp(Patterns.note),
                ),
              ],
              validator: (value) {
                return ValidateUtils.onValidateNotNull(
                  title: "Nội dung phản ánh",
                  value: value,
                );
              },
            ),
          ),
          Text(
            'Ảnh đính kèm',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          _buildListViewImage(
            listImage,
          ),
          if (listVideo.isNotEmpty) ...<Widget>[
            _buildListVideoView(
              listVideo,
              state,
            ),
          ]
        ],
      ),
    );
  }

  Widget _buildListViewImage(
    List<ComplainFileEntity> listImage,
  ) {
    final state = ref.watch(
      createComplainInfoProvider,
    );
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
      ),
      child: SizedBox(
        height: BaseSpacing.spacing20,
        child: ListView.separated(
          shrinkWrap: true,
          itemCount: listImage.length + 1,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.only(
            right: BaseSpacing.spacing4,
          ),
          separatorBuilder: (_, __) => const SizedBox(
            width: BaseSpacing.spacing2,
          ),
          itemBuilder: (context, index) {
            if (index == listImage.length) {
              return (state.listFile?.length ?? 0) < 6
                  ? InkWellWidget(
                      onTap: () {
                        onTakePicture(
                          index: index,
                        );
                      },
                      child: MyAssets.icons.iconAddImageDashline.svg(),
                    )
                  : const SizedBox.shrink();
            }
            return Stack(
              children: [
                Container(
                  height: 76,
                  width: 76,
                  padding: const EdgeInsets.only(
                    top: BaseSpacing.spacing1,
                    right: BaseSpacing.spacing1,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                    child: listImage[index].base64String != null
                        ? ImageBase64Widget(
                            base64Image: listImage[index].base64String ?? "",
                          )
                        : ImageWidget(
                            listImage[index].link ?? '',
                          ),
                  ),
                ),
                Positioned(
                  top: BaseSpacing.spacing0,
                  right: BaseSpacing.spacing0,
                  child: Visibility(
                    visible: true,
                    child: InkWellWidget(
                      onTap: () {
                        ref
                            .read(createComplainInfoProvider.notifier)
                            .deleteFile(
                              listImage[index],
                            );
                      },
                      child: MyAssets.icons.iconCloseRed.svg(),
                    ),
                  ),
                ),
                if (state.uploadImageStatus == LoadStatus.loading) ...[
                  const Positioned.fill(
                    child: Center(
                      child: LoadingIndicatorWidget(),
                    ),
                  ),
                ]
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildListVideoView(
    List<ComplainFileEntity> listVideo,
    CreateComplainInfoState state,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
      ),
      child: SizedBox(
        height: BaseSpacing.spacing20,
        child: ListView.separated(
          shrinkWrap: true,
          itemCount: listVideo.length,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.only(
            right: BaseSpacing.spacing4,
          ),
          separatorBuilder: (_, __) => const SizedBox(
            width: BaseSpacing.spacing2,
          ),
          itemBuilder: (context, index) {
            return Stack(
              children: [
                Container(
                  height: 76,
                  width: 76,
                  padding: const EdgeInsets.only(
                    top: BaseSpacing.spacing1,
                    right: BaseSpacing.spacing1,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                    child: VideoWidget(
                      listVideo[index].link ?? '',
                    ),
                  ),
                ),
                Positioned(
                  top: BaseSpacing.spacing0,
                  right: BaseSpacing.spacing0,
                  child: Visibility(
                    visible: true,
                    child: InkWellWidget(
                      onTap: () {
                        ref
                            .read(createComplainInfoProvider.notifier)
                            .deleteFile(
                              listVideo[index],
                            );
                      },
                      child: MyAssets.icons.iconCloseRed.svg(),
                    ),
                  ),
                ),
                if (state.uploadImageStatus == LoadStatus.loading) ...<Widget>[
                  const Positioned.fill(
                    child: Center(
                      child: LoadingIndicatorWidget(),
                    ),
                  ),
                ]
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    var state = ref.watch(createComplainInfoProvider);
    return Form(
      key: formUserKey,
      autovalidateMode: AutovalidateMode.always,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            key: userPhoneNumberKey,
            controller: userPhoneNumberController,
            focusNode: phoneNumberFocus,
            isRequired: true,
            labelText: "Số điện thoại",
            maxLength: 11,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            suffix: !state.turnOnEditUserInfo
                ? Cus360Widget(
                    customerType: UserType.personal.keyToServer,
                    customerPhone: userPhoneNumberController.text,
                    customerId: state.customerInfo?.customerId,
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidatePhone(
                value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                if (serialController.text.isNotEmpty) {
                  state = state.copyWith(
                    contractDataEntitySelected: ContractDataEntity(),
                  );
                }
                if (userPhoneNumberController.text.validatePhone()) {
                  checkStaff(
                    context: context,
                    phoneNumber: userPhoneNumberController.text,
                  );
                }
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: userNameController,
              isRequired: true,
              labelText: "Họ và tên",
              enabled: state.turnOnEditUserInfo,
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.words,
              inputFormatters: [
                SpaceInputFormatter(),
                FilteringTextInputFormatter.allow(
                  RegExp(
                    r'[a-zA-Zà-ỹÀ-ỹ ]',
                  ),
                ),
              ],
              validator: (value) {
                return ValidateUtils.onValidateUserName(
                  value,
                );
              },
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  userNameController.text = StringUtils.capitalizeEachWord(
                    userNameController.text,
                  );
                }
              },
            ),
          ),
          TextFieldWidget(
            controller: userContactNameController,
            labelText: "Họ và tên người liên hệ",
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(
                  r'[a-zA-Zà-ỹÀ-ỹ ]',
                ),
              ),
            ],
            validator: (value) {
              return ValidateUtils.onValidateContactUserNameNullAble(
                value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                userContactNameController.text = StringUtils.capitalizeEachWord(
                  userContactNameController.text,
                );
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: userContactPhoneNumberController,
              labelText: "Số điện thoại người liên hệ",
              maxLength: 11,
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.done,
              maxSizeSuffix: true,
              validator: (value) {
                return ValidateUtils.onValidatePhoneNullAble(
                  value,
                );
              },
              onFocusChange: (isFocus) {
                if (!isFocus) {}
              },
            ),
          ),
          DropdownWidget(
            labelText: "Địa chỉ",
            isRequired: true,
            content: state.customerAddress?.getFullAddress,
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            validator: (value) {
              return ValidateUtils.onValidateAddress(
                value,
              );
            },
            onTap: () {
              onSelectAddress();
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyInfo() {
    var state = ref.watch(createComplainInfoProvider);
    const key = ValueKey(
      "company",
    );

    return Form(
      key: formCompanyKey,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            controller: taxCompanyController,
            focusNode: taxFocusNode,
            isRequired: true,
            labelText: "Mã số thuế doanh nghiệp",
            maxLength: 14,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            suffix: !state.turnOnEditCompanyInfo
                ? Cus360Widget(
                    customerType: UserType.company.keyToServer,
                    taxCode: taxCompanyController.text,
                    customerId: state.companyInfo?.customerId,
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidateTax(
                value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                getCompanyInfo360(
                  taxCode: taxCompanyController.text,
                );
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: companyNameController,
              labelText: "Tên doanh nghiệp",
              isRequired: true,
              enabled: state.turnOnEditCompanyInfo,
              textInputAction: TextInputAction.done,
              inputFormatters: [
                SpaceInputFormatter(),
              ],
              validator: (value) {
                return ValidateUtils.onValidateCompanyName(
                  value,
                );
              },
            ),
          ),
          TextFieldWidget(
            controller: companyPhoneNumberController,
            labelText: "Số điện thoại doanh nghiệp",
            isRequired: true,
            textInputAction: TextInputAction.done,
            inputFormatters: [
              SpaceInputFormatter(),
            ],
            validator: (value) {
              return ValidateUtils.onValidateCompanyPhone(
                value,
              );
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing4,
            ),
            child: DropdownWidget(
              labelText: "Địa chỉ doanh nghiệp",
              isRequired: true,
              // enabled: state.turnOnEditCompanyInfo,
              content: state.companyAddress?.getFullAddress,
              validator: (value) {
                return ValidateUtils.onValidateAddress(
                  value,
                );
              },
              suffix: MyAssets.icons.iconArrowRightS20.svg(),
              onTap: () {
                onSelectAddress(
                  isCompany: true,
                );
              },
            ),
          ),
          TextFieldWidget(
            controller: companyUserContactNameController,
            labelText: "Họ tên người liên hệ",
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(
                  r'[a-zA-Zà-ỹÀ-ỹ ]',
                ),
              ),
            ],
            validator: (value) {
              return ValidateUtils.onValidateContactUserNameNullAble(
                value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                companyUserContactNameController.text =
                    StringUtils.capitalizeEachWord(
                  companyUserContactNameController.text,
                );
              }
            },
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacing4,
            ),
            child: TextFieldWidget(
              controller: companyPhoneContactNameController,
              labelText: "Số điện thoại người liên hệ",
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.phone,
              validator: (value) {
                return ValidateUtils.onValidatePhoneNullAble(
                  value,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDisplayUserText(UserType user) {
    return Text(
      user.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _buildRowPartnerInfo({
    String? label,
    String? value,
  }) {
    return Row(
      children: <Widget>[
        Text(
          '$label: ',
          style: UITextStyle.body2Regular.copyWith(
            color: BaseColors.textSubtitle,
          ),
        ),
        Text(
          value ?? '',
          style: UITextStyle.body2Regular.copyWith(
            color: BaseColors.textSubtitle,
          ),
        ),
      ],
    );
  }

  void onSelectAddress({
    bool isCompany = false,
  }) async {
    var state = ref.watch(createComplainInfoProvider);
    AddressEntity? address;
    AddressInfo? addressInfo =
        isCompany ? state.companyAddress : state.customerAddress;

    if (addressInfo != null) {
      address = AddressEntity(
        addressDetail: addressInfo.addressDetail,
        ward: addressInfo.ward,
        district: addressInfo.district,
        province: addressInfo.province,
        isGetFromLocal: addressInfo.isGetFromLocal,
      );
    }

    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) {
          ref.read(createComplainInfoProvider.notifier).selectAddress(
                address.convertToAddressInfo,
              );
        },
      ),
    );
  }

  void getCompanyInfo360({
    String? taxCode,
  }) async {
    if ((taxCode ?? '').validateTax()) {
      final customerInfo =
          await ref.read(createComplainInfoProvider.notifier).getCompanyInfo360(
                taxCode: taxCode,
              );

      if (customerInfo != null) {
        companyNameController.text = customerInfo.fullName ?? '';
        // companyPhoneNumberController.text = customerInfo.phone ?? '';
      } else {
        companyNameController.text = "";
      }
    } else {
      ref.read(createComplainInfoProvider.notifier).enableEditCompanyForm();
      companyNameController.text = "";
    }
  }

  void checkStaff({
    required BuildContext context,
    String? phoneNumber,
  }) async {
    final checkStaff =
        await ref.read(createComplainInfoProvider.notifier).checkInternalStaff(
              phoneNumber: phoneNumber,
            );

    if (checkStaff ?? false) {
      if (!context.mounted) return;

      AppDialog.showDialogConfirm(
        context,
        title: "Xác nhận",
        message:
            "Số điện thoại của nhân viên nội bộ, bạn có chắc chắn tạo khiếu nại không?",
        buttonNameConfirm: "Tạo khiếu nại",
        onConfirmAction: () {
          phoneNumberFocus.unfocus();
          getCustomerInfo360(
            phoneNumber: phoneNumber,
            isInternalStaff: true,
          );
        },
        onCancelAction: () {
          userPhoneNumberController.text = "";
          phoneNumberFocus.unfocus();
        },
      );
    } else {
      getCustomerInfo360(
        phoneNumber: phoneNumber,
      );
    }
  }

  void getCustomerInfo360({
    String? phoneNumber,
    bool? isInternalStaff,
  }) async {
    if ((phoneNumber ?? '').validatePhone()) {
      final customerInfo = await ref
          .read(createComplainInfoProvider.notifier)
          .getCustomerInfo360(
            phoneNumber: phoneNumber,
            isInternalStaff: isInternalStaff,
          );

      if (customerInfo != null) {
        userNameController.text = customerInfo.fullName ?? '';
      }
      taxFocusNode.unfocus();
    } else {
      ref.read(createComplainInfoProvider.notifier).enableEditUserForm();
    }
  }

  void onSaveInfo() async {
    final formOrder = formOrderKey.currentState;
    final formComplain = formComplainKey.currentState;
    FocusScope.of(context).unfocus();
    bool isPersonalType =
        ref.watch(createComplainInfoProvider).userType == UserType.personal;

    if (isPersonalType) {
      final form = formUserKey.currentState;

      if (form!.validate() &&
          formOrder!.validate() &&
          formComplain!.validate()) {
        form.save();
        onSendInfo(
          context: context,
        );
      } else {
        ref.read(createComplainInfoProvider.notifier).onValidateUserForm();
      }
    } else {
      final form = formCompanyKey.currentState;

      if (form!.validate() &&
          formOrder!.validate() &&
          formComplain!.validate()) {
        form.save();
        onSendInfo(
          context: context,
        );
      } else {
        ref.read(createComplainInfoProvider.notifier).onValidateCompanyForm();
      }
    }
  }

  void onSendInfo({
    required BuildContext context,
  }) async {
    final state = ref.watch(createComplainInfoProvider);
    if (state.isSkipSerial == false &&
        isValidSerial == false &&
        state.categoryDropDown!.businessBranchId ==
            EnumCategoryType.solution.businessBranchId) {
      AppDialog.showDialogCenter(
        context,
        message: "Vui lòng nhập đúng serial",
        status: DialogStatus.error,
      );
      return;
    }
    if (state.isSkipContractCode == false && isValidContract == false) {
      AppDialog.showDialogCenter(
        context,
        message: "Vui lòng nhập đúng mã hợp đồng",
        status: DialogStatus.error,
      );
      return;
    }
    ref.read(createComplainInfoProvider.notifier).createComplain(
          customerName: userNameController.text,
          customerPhone: userPhoneNumberController.text,
          companyCustomerPhone: companyPhoneNumberController.text,
          companyTaxCode: taxCompanyController.text,
          companyPhoneNumber: companyPhoneContactNameController.text,
          contactName: userContactNameController.text.trim(),
          contactPhoneNumber: userContactPhoneNumberController.text.trim(),
          companyName: companyNameController.text,
          noteContent: contentController.text,
          contractCode:
              state.isSkipContractCode ? null : contractController.text.trim(),
          serial: state.categoryDropDown!.businessBranchId ==
                      EnumCategoryType.solution.businessBranchId &&
                  state.isSkipSerial == false
              ? serialController.text.trim()
              : null,
          goodsCode: state.isSkipSerial == false
              ? state.contractDataEntitySelected?.goodsCode
              : null,
          goodsName: state.isSkipSerial == false
              ? state.contractDataEntitySelected?.goodsName
              : null,
          companyContactName: companyUserContactNameController.text.trim(),
          companyContactPhoneNumber:
              companyPhoneContactNameController.text.trim(),
          onSuccess: () {
            AppDialog.showDialogCenter(
              context,
              message: "Tạo khiếu nại - phản ánh thành công",
              status: DialogStatus.success,
              barrierDismissible: false,
              onConfirm: () => context.pop(
                true,
              ),
            );
          },
        );
  }

  void onTakePicture({
    required int index,
  }) async {
    final state = ref.watch(
      createComplainInfoProvider,
    );
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Upload File",
      isFlexible: true,
      child: UploadImageBottomSheet(
        onPickMultiImage: (file) async {
          List<ComplainFileEntity> listFile = [];

          for (var item in file) {
            String base64Image = await fileToBase64(item);
            final result = await ref
                .read(createComplainInfoProvider.notifier)
                .uploadFileV2(
                  complainImage: ComplainFileEntity(
                    base64String: base64Image,
                  ),
                  file: item,
                );
            listFile.addAll(result);
          }
          final validator = validatorImage(state, listFile);
          if (validator == true) {
            ref.read(createComplainInfoProvider.notifier).addFile(
                  files: listFile,
                );
          }
        },
        onTakeImage: (file) async {
          String base64Image = await fileToBase64(file);
          final listFile =
              await ref.read(createComplainInfoProvider.notifier).uploadFileV2(
                    complainImage: ComplainFileEntity(
                      base64String: base64Image,
                    ),
                    file: file,
                  );
          final validator = validatorImage(state, listFile);
          if (validator == true) {
            ref.read(createComplainInfoProvider.notifier).addFile(
                  files: listFile,
                );
          }
        },
        onPickVideo: (file) async {
          String base64Image = await fileToBase64(file);
          final listFile =
              await ref.read(createComplainInfoProvider.notifier).uploadFileV2(
                    complainImage: ComplainFileEntity(
                      base64String: base64Image,
                    ),
                    file: file,
                  );
          final validator = validatorImage(state, listFile);
          if (validator == true) {
            ref.read(createComplainInfoProvider.notifier).addFile(
                  files: listFile,
                );
          }
        },
      ),
    );
  }

  bool validatorImage(
    CreateComplainInfoState state,
    List<ComplainFileEntity> files,
  ) {
    List<ComplainFileEntity> listFile = state.listFile ?? [];
    final currentImages = listFile
        .where(
          (file) => file.type.isImage,
        )
        .length;
    final currentVideo = listFile
        .where(
          (file) => file.type.isVideo,
        )
        .length;

    final imgDataSum = files
        .where(
          (file) => file.type.isImage,
        )
        .length;
    final sumImages = currentImages + imgDataSum;

    if (sumImages > 5) {
      AppDialog.showDialogCenter(
        ErrorDialog.navigatorKey.currentContext!,
        message: "Không chọn quá 5 ảnh",
        status: DialogStatus.error,
      );
      return false;
    }
    if (currentVideo >= 1 &&
        (files.where((file) => file.type.isVideo)).isNotEmpty) {
      AppDialog.showDialogCenter(
        ErrorDialog.navigatorKey.currentContext!,
        message: "Không chọn quá 1 video",
        status: DialogStatus.error,
      );
      return false;
    }
    return true;
  }
}
