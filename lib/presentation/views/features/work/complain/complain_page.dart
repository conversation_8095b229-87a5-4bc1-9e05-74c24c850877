import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/body/complain_body.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_complain_view/filter_complain_view.dart';
import 'package:vcc/presentation/views/features/work/complain/perform_complain/perform_complain_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/tab_bar_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/deBouncer.dart';
import 'complain_view_model.dart';
import 'manage_complain/manage_complain.dart';

class ComplainPage extends StatefulHookConsumerWidget {
  const ComplainPage({
    super.key,
  });

  @override
  ConsumerState<ComplainPage> createState() => _ReportPageState();
}

class _ReportPageState extends ConsumerState<ComplainPage>
    with TickerProviderStateMixin {
  List<String> listPage = [
    "Thực hiện",
    "Quản lý",
  ];

  late TabController tabController;
  late TextEditingController searchController;
  late Debounce<String> deBouncer;

  @override
  void initState() {
    searchController = TextEditingController();
    tabController = TabController(
      length: listPage.length,
      initialIndex: 0,
      vsync: this,
    );
    deBouncer = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref.read(complainProvider.notifier).changeKeySearch(
              value ?? '',
            );
      },
    );
    Future(() {
      ref.read(complainProvider.notifier).getAllComplain();
    });
    super.initState();
  }

  Future<void> onRefresh() async {
    if (ref.watch(complainProvider).tabIndex == 0) {
      ref.read(complainProvider.notifier).getAllComplain();
    } else {
      ref.read(complainProvider.notifier).getAllComplain(
            isManage: true,
          );
    }
  }

  @override
  void dispose() {
    tabController.dispose();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(complainProvider);

    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: LayoutPage(
        backgroundColor: BaseColors.backgroundWhite,
        appbar: const AppBarCustom(
          title: "Khiếu nại - phản ánh",
        ),
        floatingActionButton: SafeArea(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: InkWellWidget(
              onTap: () async {
                final result = await context.push(
                  RouterPaths.createComplain,
                );
                if (result == true) {
                  onRefresh();
                }
                // context.push(
                //   RouterPaths.detailRequirementWarrantyClaim,
                // );
              },
              child: Container(
                decoration: BoxDecoration(
                  color: BaseColors.primary,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(
                        BaseSpacing.spacing05,
                        BaseSpacing.spacing2,
                      ),
                      blurRadius: BaseSpacing.spacing25,
                      spreadRadius: BaseSpacing.spacing0,
                      color: BaseColors.backgroundBlack.withOpacity(
                        0.2,
                      ),
                    ),
                  ],
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: state.screenOffset > 200
                      ? BaseSpacing.spacing3
                      : BaseSpacing.spacing4,
                  vertical: BaseSpacing.spacing3,
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    MyAssets.icons.iconPlusAdd.svg(
                      height: BaseSpacing.spacing6,
                      width: BaseSpacing.spacing6,
                    ),
                    const SizedBox(
                      width: BaseSpacing.spacing2,
                    ),
                    Text(
                      "Tạo khiếu nại",
                      style: UITextStyle.body1Medium.copyWith(
                        color: BaseColors.backgroundWhite,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            TabBarWidget(
              tabItems: listPage,
              tabController: tabController,
              onTap: (index) {
                if (state.tabIndex != index) {
                  searchController.clear();
                  deBouncer.value = '';
                  ref.read(complainProvider.notifier).changeTabIndex(
                        index,
                      );
                }
              },
            ),
            Expanded(
              child: Column(
                children: <Widget>[
                  _buildSearchZone(),
                  state.tabIndex == 0
                      ? const PerformComplainPage()
                      : const ManageComplainPage(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchZone() {
    final state = ref.watch(complainProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: BaseSpacing.spacing4,
            ),
            child: Row(
              children: [
                Expanded(
                  child: SearchTextFieldWidget(
                    controller: searchController,
                    hintText: "Mã phản ánh, tên khách hàng",
                    onChanged: (value) {
                      deBouncer.value = value;
                    },
                  ),
                ),
                const SizedBox(
                  width: BaseSpacing.spacing4,
                ),
                InkWellWidget(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    var complainState = ref.watch(complainProvider);
                    AppBottomSheet.showNormalBottomSheet(
                      context,
                      title: "Bộ lọc",
                      height: MediaQuery.of(context).size.height * 0.8,
                      child: FilterComplainViewSheet(
                        startDateStr: complainState.startTimeStr,
                        endDateStr: complainState.endTimeStr,
                        completedType: complainState.completedTypes,
                        startDate: complainState.startDate,
                        endDate: complainState.endDate,
                        complainType: complainState.complainTypes,
                        onApply: (FilterArgumentCallback value) {
                          ref.read(complainProvider.notifier).changeDate(
                                value.startTime,
                                value.endTime,
                              );

                          ref.read(complainProvider.notifier).getAllComplain(
                                body: ComplainBody(
                                  listStatus:
                                      (value.complainType ?? []).isNotEmpty
                                          ? value.complainType!
                                              .map((e) => e.keyToServer)
                                              .toList()
                                          : [],
                                  listCompleteStatus:
                                      (value.completedType ?? []).isNotEmpty
                                          ? value.completedType!
                                              .map((e) => e.keyToServer)
                                              .toList()
                                          : [],
                                  keySearch: searchController.text,
                                  startDate: value.startTime,
                                  endDate: value.endTime,
                                ),
                                isManage: state.tabIndex == 0 ? false : true,
                              );
                        },
                      ),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: BaseSpacing.spacing2,
                    ),
                    child: Row(
                      children: [
                        MyAssets.icons.filter.svg(),
                        Text(
                          'Lọc',
                          style: UITextStyle.caption1Medium.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(
            height: BaseSpacing.spacing2,
          ),
          const DividerWidget(
            height: BaseSpacing.spacing2,
          ),
        ],
      ),
    );
  }
}
