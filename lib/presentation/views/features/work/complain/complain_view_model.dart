import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/complain_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/complain_body.dart';
import 'package:vcc/domain/entities/complain/complain_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';

part 'complain_state.dart';

final complainProvider =
    StateNotifierProvider.autoDispose<ComplainViewModel, ComplainState>(
  (ref) => ComplainViewModel(
    ref: ref,
  ),
);

class ComplainViewModel extends StateNotifier<ComplainState> {
  final Ref ref;

  ComplainViewModel({
    required this.ref,
  }) : super(ComplainState());

  void changeTabIndex(int page) {
    state = state.copyWith(
      tabIndex: page,
    );
  }

  void changeOffset(double offset) {
    state = state.copyWith(
      screenOffset: offset,
    );
  }

  void changeKeySearch(String keySearch) {
    state = state.copyWith(
      keySearch: keySearch.trim(),
    );

    if (state.tabIndex == 0) {
      getAllComplain();
    } else {
      getAllComplain(
        isManage: true,
      );
    }
  }

  void changeDate(
    DateTime? startTime,
    DateTime? endTime,
  ) {
    if (startTime == null) {
      state.startDate = null;
      state = state.copyWith(
        startDate: null,
      );
    }

    if (endTime == null) {
      state.endDate = null;
      state = state.copyWith(
        endDate: null,
      );
    }

    state = state.copyWith(
      startDate: startTime,
      endDate: endTime,
    );
  }

  void getAllComplain({
    ComplainBody? body,
    bool? isManage,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      startDate: body?.startDate,
      endDate: body?.endDate,
      complainTypes: body?.listStatus ?? state.complainTypes,
      completedTypes: body?.listCompleteStatus ?? state.completedTypes,
      listAllComplain: [],
    );

    try {
      final result = await appLocator<ComplainRepository>().getListComplain(
        complainBody: ComplainBody(
          keySearch: state.keySearch,
          listStatus: state.complainTypes ?? [],
          listCompleteStatus: state.completedTypes ?? [],
          startDate: state.startDate,
          endDate: state.endDate,
          sysUserId: GlobalData.instance.userInfo?.sysUserId,
          isManager: isManage,
          pageIndex: BaseConstant.page,
          pageSize: BaseConstant.size,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listAllComplain: data.data,
            allTotalResult: (data.data ?? []).isNotEmpty
                ? data.data!.first.totalPage
                : BaseConstant.defaultPage,
            allCurrentPage: BaseConstant.page,
            allCurrentPageSize: (data.data ?? []).length,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void fetchAllComplain({
    bool? isManage,
  }) async {
    if (state.listAllComplain!.length >= state.allTotalResult) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final result = await appLocator<ComplainRepository>().getListComplain(
        complainBody: ComplainBody(
          keySearch: state.keySearch,
          listStatus: state.complainTypes ?? [],
          listCompleteStatus: state.completedTypes ?? [],
          startDate: state.startDate,
          endDate: state.endDate,
          isManager: isManage,
          sysUserId: GlobalData.instance.userInfo?.sysUserId,
          pageIndex: BaseConstant.defaultPage + 1,
          pageSize: state.allCurrentPageSize,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listAllComplain: state.listAllComplain! + data.data!,
            allTotalResult: data.data?.first.totalPage,
            allCurrentPage: state.allCurrentPage,
            allCurrentPageSize: state.allCurrentPageSize + BaseConstant.size,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }
}
