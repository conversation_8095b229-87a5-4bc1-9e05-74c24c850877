import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:intl/intl.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/body/insert_log_mobile_call_body.dart';
import 'package:vcc/domain/body/sign_acceptance_contract_body.dart';
import 'package:vcc/domain/body/work_list_body.dart';
import 'package:vcc/domain/entities/complain/check_serial_entity.dart';
import 'package:vcc/domain/entities/complain/detail_complain_entity.dart';
import 'package:vcc/domain/enums/call_type.dart';
import 'package:vcc/domain/enums/complain/detail_complain/enum_interact_customer.dart';
import 'package:vcc/domain/enums/complain/detail_complain/enum_symbol_process.dart';
import 'package:vcc/domain/enums/complain/enum_complain_status.dart';
import 'package:vcc/domain/enums/complain/sign_contract_complain/enum_sign_acceptance_complain.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/mobile_call_type.dart';
import 'package:vcc/domain/enums/send_otp_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/params/create_complain/complain_image.dart';
import 'package:vcc/extensions/file_to_base64.dart';
import 'package:vcc/extensions/file_type.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/complain_extension/complain_extension_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/complain_refuse/complain_refuse_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_image/upload_image.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/create_requirement_warranty/create_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/interact_customer_page/interact_customer_page.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/sign_contract_complain/sign_contract_complain_page.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/widget/document_report_file_widget.dart';
import 'package:vcc/presentation/views/features/work/complain/history_complain/complain_history_page.dart';
import 'package:vcc/presentation/views/features/work/complain/select_serial_complain/select_serial_complain_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_complain_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/image_base64_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/presentation/views/widgets/video_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/mobile_call_utils.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/complain/enum_expired_status.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/widget/build_complain_entrust.dart';

import 'package:vcc/presentation/views/features/work/complain/detail_complain/widget/build_row_detail_complain_view.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/widget/build_customer_widget.dart';
import 'complain_detail_view_model.dart';

class ComplainDetailArguments {
  final int? code;
  final bool? isManage;

  ComplainDetailArguments({
    this.code,
    this.isManage = false,
  });
}

class ComplainDetailPage extends StatefulHookConsumerWidget {
  final ComplainDetailArguments arguments;

  const ComplainDetailPage({
    super.key,
    required this.arguments,
  });

  @override
  ConsumerState<ComplainDetailPage> createState() => _ComplainDetailPage();
}

class _ComplainDetailPage extends ConsumerState<ComplainDetailPage> {
  final formUserKey = GlobalKey<FormState>();
  final formCompanyKey = GlobalKey<FormState>();
  late TextEditingController searchReceiverController;
  late TextEditingController reasonController;
  late TextEditingController reasonDetailController;
  late TextEditingController reasonContractController;
  late TextEditingController resultController;
  late TextEditingController deviceQuantity;
  late TextEditingController noteController;
  late TextEditingController contractController;
  late ScrollController scrollCancelReasonController;
  late ScrollController scrollReAssociateController;
  late ScrollController scrollSiteSurveyFailController;
  bool? isAssignComplain;
  bool? isHasProcess;

  bool isEnableAction = false;
  final formProcessKey = GlobalKey<FormState>();
  FocusNode reasonDetailFocus = FocusNode();
  FocusNode resultFocus = FocusNode();
  bool isValidContract = false;

  @override
  void initState() {
    searchReceiverController = TextEditingController();
    reasonController = TextEditingController();
    resultController = TextEditingController();
    noteController = TextEditingController();
    contractController = TextEditingController();
    deviceQuantity = TextEditingController();
    reasonDetailController = TextEditingController();
    reasonContractController = TextEditingController();
    resultController = TextEditingController();
    scrollCancelReasonController = ScrollController();
    scrollReAssociateController = ScrollController();
    scrollSiteSurveyFailController = ScrollController();
    Future(
      () async {
        ref.read(complainDetailProvider.notifier).addNewWork();
        final detailComplainEntity =
            await ref.read(complainDetailProvider.notifier).getDetail(
                  widget.arguments.code!,
                );
        if (detailComplainEntity != null) {
          setDataComplainAdditional();
        }
      },
    );

    super.initState();
  }

  void setDataComplainAdditional() {
    final state = ref.watch(complainDetailProvider);
    if (state.detailComplain!.isAdditionInfo) {
      reasonDetailController.text = state.reasonDetail ?? '';
      resultController.text = state.contentProcess ?? '';
    }
  }

  @override
  void dispose() {
    searchReceiverController.dispose();
    reasonController.dispose();
    reasonDetailController.dispose();
    scrollCancelReasonController.dispose();
    scrollReAssociateController.dispose();
    scrollSiteSurveyFailController.dispose();
    resultController.dispose();
    noteController.dispose();
    contractController.dispose();
    reasonContractController.dispose();
    deviceQuantity.dispose();
    super.dispose();
  }

  Future<void> refreshData() async {
    ref.read(complainDetailProvider.notifier).getDetail(
          widget.arguments.code!,
        );
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(complainDetailProvider);
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: LayoutPage(
        appbar: AppBarCustom(
          title: "Chi tiết khiếu nại",
          actionWidget: [
            InkWellWidget(
              onTap: () {
                context.push(
                  RouterPaths.historyComplain,
                  extra: ComplainHistoryArguments(
                    id: state.detailComplain?.ticketId,
                    detailComplain: state.detailComplain,
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(
                  BaseSpacing.spacing4,
                ),
                child: MyAssets.icons.iconOclockWhite.svg(),
              ),
            ),
          ],
        ),
        body: _buildBody(),
      ),
    );
  }

  Widget _buildBody() {
    final state = ref.watch(complainDetailProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Không có dữ liệu',
        onRefresh: refreshData,
      );
    } else {
      isEnableAction =
          !state.detailComplain!.isWaiting && !widget.arguments.isManage!;
      // trang thai cho thuc hien
      //  tab thực hiện thì check là boft3 và là thợ thực hiện thì đươcj điều phối
      //  tab quản lý thì được điều phối
      isAssignComplain =
          (state.detailComplain!.isBoft3 && state.detailComplain!.isMe) ||
              widget.arguments.isManage!;

      isHasProcess =
          state.detailComplain!.isMe && state.detailComplain!.isProcess;

      List<ListFileRecord>? listWorkReport = [];
      List<ListFileRecord>? listAcceptanceReport = [];

      state.detailComplain?.listBb?.forEach(
        (item) {
          if (item.objectType == SendOtpType.workComplain.keyToServer) {
            listWorkReport.add(item);
          }
          if (item.objectType == SendOtpType.acceptanceComplain.keyToServer) {
            listAcceptanceReport.add(item);
          }
        },
      );

      return RefreshIndicatorWidget(
        onRefresh: refreshData,
        child: Stack(
          children: [
            SingleChildScrollView(
              physics: const AlwaysScrollableScrollPhysics(),
              child: Form(
                key: formProcessKey,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    buildStatusCode(),
                    DividerWidget(
                      height: BaseSpacing.spacing15s,
                      color: BaseColors.secondaryBackground,
                    ),
                    buildInfoContact(),
                    DividerWidget(
                      height: BaseSpacing.spacing15s,
                      color: BaseColors.secondaryBackground,
                    ),
                    if ((state.detailComplain?.packageName ?? '')
                        .isNotEmpty) ...<Widget>[
                      orderInfoView(),
                    ],
                    if ((state.detailComplain?.packageName ?? '').isEmpty &&
                        state.detailComplain!.isProcessing) ...<Widget>[
                      orderInfo(),
                    ],
                    DividerWidget(
                      height: BaseSpacing.spacing15s,
                      color: BaseColors.secondaryBackground,
                    ),
                    complainInfo(),
                    DividerWidget(
                      height: BaseSpacing.spacing15s,
                      color: BaseColors.secondaryBackground,
                    ),
                    if ((state.detailComplain!.isAdditionInfo &&
                            !state.isAdditional) ||
                        state.detailComplain!.isCompleted) ...<Widget>[
                      Padding(
                        padding: const EdgeInsets.only(
                          bottom: BaseSpacing.spacing4,
                        ),
                        child: complainCompletedInfo(),
                      ),
                    ],
                    if ((state.detailComplain?.listBb ?? []).isNotEmpty &&
                        !state.detailComplain!.isMe) ...<Widget>[
                      if (listAcceptanceReport.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: BaseSpacing.spacing4,
                          ),
                          child: _listHandleView(listAcceptanceReport),
                        ),
                      ],
                      if (listWorkReport.isNotEmpty) ...[
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: BaseSpacing.spacing4,
                          ),
                          child: _listWorkView(
                            listWorkReport,
                          ),
                        ),
                      ],
                    ],
                    if (state.detailComplain!.isMe &&
                        isEnableAction) ...<Widget>[
                      Column(
                        children: [
                          // trang thai dang thuc hien
                          if (state.detailComplain?.isProcessing ??
                              false) ...<Widget>[
                            complainProcessInfo(),
                          ],
                          if (state.isAdditional) ...<Widget>[
                            _additionalInfoView(),
                          ],
                          if ((state.detailComplain?.listBb ?? [])
                              .isNotEmpty) ...<Widget>[
                            if (listAcceptanceReport.isNotEmpty) ...<Widget>[
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: BaseSpacing.spacing4,
                                ),
                                margin: const EdgeInsets.only(
                                  top: BaseSpacing.spacing4,
                                ),
                                child: _listHandleView(listAcceptanceReport),
                              ),
                            ],
                            if (listWorkReport.isNotEmpty) ...<Widget>[
                              Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: BaseSpacing.spacing4,
                                ),
                                child: _listWorkView(listWorkReport),
                              ),
                            ]
                          ],
                          if (state.detailComplain?.isProcessing ??
                              false) ...<Widget>[
                            completedButton(),
                          ],
                          if (!state.detailComplain!.isWaiting) ...<Widget>[
                            interactionButton(
                              context: context,
                            ),
                          ],
                          if (state.detailComplain!.isSolution &&
                              state.detailComplain!.isProcessing &&
                              (((state.detailComplain?.packageName ?? '')
                                      .isNotEmpty) ||
                                  (isValidContract &&
                                      contractController.text
                                          .trim()
                                          .isNotEmpty))) ...<Widget>[
                            createWarranty(),
                          ],
                          if (!state.detailComplain!.isExpired &&
                              state.detailComplain!.isProcessing) ...<Widget>[
                            extensionButton(),
                          ],
                          if (state.detailComplain?.isAdditionInfo ??
                              false) ...<Widget>[
                            additionalInfoButton(),
                          ],
                          if (state.detailComplain?.isProcess ??
                              false) ...<Widget>[
                            rejectButton(),
                          ],
                          _buildBottom(),
                        ],
                      ),
                    ],
                    if (state.detailComplain!.isProcess &&
                        widget.arguments.isManage == true) ...<Widget>[
                      _buildBottom(),
                    ]
                  ],
                ),
              ),
            ),
            if (state.uploadImageStatus == LoadStatus.loading ||
                state.updateStatus == LoadStatus.loading) ...[
              Positioned.fill(
                child: Container(
                  color: BaseColors.backgroundGray.withOpacity(0.5),
                  child: const Center(
                    child: LoadingIndicatorWidget(),
                  ),
                ),
              ),
            ],
          ],
        ),
      );
    }
  }

  Column _listWorkView(List<ListFileRecord> files) {
    final widthSize = MediaQuery.of(context).size.width;
    List<ListFileRecord> images = [];
    List<ListFileRecord> pdf = [];
    for (var element in files) {
      bool isPdf = FileTypeOption.isPdfLink(
        filePath: element.link ?? '',
      );
      if (isPdf) {
        pdf.add(element);
      } else {
        images.add(element);
      }
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            bottom: BaseSpacing.spacing2,
          ),
          child: Text(
            "Biên bản làm việc",
            style: UITextStyle.body2Medium,
          ),
        ),
        if (images.isNotEmpty) ...<Widget>[
          Container(
            height: (widthSize - BaseSpacing.spacing10) / BaseSpacing.spacing1,
            width: MediaQuery.of(context).size.width,
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing2,
            ),
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: List.generate(
                  images.length,
                  (index) {
                    return Container(
                      height: (widthSize - BaseSpacing.spacing10) /
                          BaseSpacing.spacing1,
                      width: (widthSize - BaseSpacing.spacing10) /
                          BaseSpacing.spacing1,
                      padding: const EdgeInsets.only(
                        top: BaseSpacing.spacing1,
                        right: BaseSpacing.spacing2,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(
                          BaseSpacing.spacing2,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(
                          BaseSpacing.spacing2,
                        ),
                        child: ImageWidget(
                          images[index].link ?? "",
                          enableShowPreview: true,
                          size: const Size(
                            76,
                            76,
                          ),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          ),
        ],
        if (pdf.isNotEmpty) ...<Widget>[
          ListView.separated(
            itemCount: pdf.length,
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacing2,
            ),
            separatorBuilder: (_, __) => const SizedBox(
              width: BaseSpacing.spacing2,
            ),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final item = pdf[index];
              ListFileRecord itemBB = ListFileRecord(
                link: item.link,
                path: item.path,
                fileName: item.fileName,
              );
              return Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: BaseSpacing.spacing2,
                ),
                child: DocumentReportFileWidget(
                  label: "Biên bản làm việc",
                  file: itemBB,
                ),
              );
            },
          )
        ],
      ],
    );
  }

  Column _listHandleView(List<ListFileRecord> listBbNt) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "Biên bản nghiệm thu:",
          style: UITextStyle.body2Medium,
        ),
        Padding(
          padding: const EdgeInsets.only(
            top: BaseSpacing.spacing2,
            bottom: BaseSpacing.spacing35,
          ),
          child: ListView.separated(
            itemCount: listBbNt.length,
            padding: EdgeInsets.zero,
            separatorBuilder: (_, __) => const SizedBox(
              width: BaseSpacing.spacing2,
            ),
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final item = listBbNt[index];
              return Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: BaseSpacing.spacing2,
                ),
                child: DocumentReportFileWidget(
                  file: item,
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget buildStatusCode() {
    final state = ref.watch(complainDetailProvider);
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      right: BaseSpacing.spacing1,
                    ),
                    child: Text(
                      state.detailComplain?.code ?? "",
                      style: UITextStyle.body2SemiBold.copyWith(
                        color: BaseColors.textTitle,
                      ),
                    ),
                  ),
                  InkWellWidget(
                    onTap: () {
                      AppUtils.copyToClipboard(
                        state.detailComplain?.code ?? '',
                      );
                    },
                    child: MyAssets.icons.copyClipBoard.svg(),
                  ),
                ],
              ),
              if (state.detailComplain?.isWarning ?? false) ...<Widget>[
                Row(
                  children: [
                    ExpiredStatusEnumExtension.fromStatus(
                            state.detailComplain?.completeStatus)
                        .iconExpired,
                    Padding(
                      padding: const EdgeInsets.only(
                        left: BaseSpacing.spacing1,
                      ),
                      child: Text(
                        ExpiredStatusEnumExtension.fromStatus(
                                state.detailComplain?.completeStatus)
                            .title,
                        style: UITextStyle.body2Medium.copyWith(
                          color: ExpiredStatusEnumExtension.fromStatus(
                                  state.detailComplain?.completeStatus)
                              .textDetailColor,
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ],
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing3,
            ),
            child: DividerWidget(
              height: BaseSpacing.spacingPx,
              color: BaseColors.secondaryBackground,
            ),
          ),
          if ((state.detailComplain?.reasonRejectAssign ?? '')
              .isNotEmpty) ...<Widget>[
            rejectInfoByEmployee(),
          ],
          if (!state.detailComplain!.isWaiting) ...<Widget>[
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing1,
              ),
              child: BuildRowDetailComplainView(
                title: "Thời gian thực hiện",
                value: "${state.detailComplain?.startDateStr ?? ""}"
                    " đến "
                    "${state.detailComplain?.endDateStr ?? ""}",
              ),
            ),
          ],
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing1,
            ),
            child: BuildRowDetailComplainView(
              title: "Trạng thái",
              view: Text(
                ComplainStatusEnumExtension.fromStatus(
                        state.detailComplain?.status)
                    .title,
                style: UITextStyle.body2Regular.copyWith(
                  color: ComplainStatusEnumExtension.fromStatus(
                    state.detailComplain?.status,
                  ).textColor,
                ),
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing1,
            ),
            child: BuildRowDetailComplainView(
              title: "Mức độ ưu tiên",
              value: state.detailComplain?.priorityStr ?? '',
            ),
          ),
        ],
      ),
    );
  }

  Widget rejectInfoByEmployee() {
    final state = ref.watch(complainDetailProvider);
    final item = state.detailComplain;
    return Container(
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(
            BaseSpacing.spacing2,
          ),
          border: Border.all(
            color: BaseColors.warningOrangeBorder,
          ),
          color: BaseColors.warningSurface),
      child: Padding(
        padding: const EdgeInsets.all(
          BaseSpacing.spacing2,
        ),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing2,
              ),
              child: MyAssets.icons.iconWarningOrangeS16.svg(),
            ),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Điều phối bị từ chối',
                    style: UITextStyle.body2SemiBold,
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                      top: BaseSpacing.spacing2,
                    ),
                    child: Text(
                      'Lý do: ${item?.reasonRejectAssign ?? ''}; Thợ từ chối: ${item?.rejectAssignByString ?? ''}',
                      style: UITextStyle.body2Regular,
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }

  Widget buildInfoContact() {
    final state = ref.watch(complainDetailProvider);
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          SizedBox(
            height: BaseSpacing.spacing12,
            child: Text(
              "Thông tin liên hệ",
              style: UITextStyle.body1SemiBold,
            ),
          ),
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  top: BaseSpacing.spacing1,
                  right: BaseSpacing.spacing25,
                ),
                child: MyAssets.icons.iconUserS16.svg(),
              ),
              actionContact(),
              if (!state.detailComplain!.isAdditionInfo) ...<Widget>[
                AppTextButton(
                  padding: EdgeInsets.zero,
                  title: "",
                  iconLeft: MyAssets.icons.iconCallCustomer.svg(),
                  onTap: () {
                    onCallCustomer(ref);
                  },
                ),
              ]
            ],
          ),
        ],
      ),
    );
  }

  Widget actionContact() {
    final state = ref.watch(complainDetailProvider);
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            state.detailComplain?.customerName ?? '',
            style: UITextStyle.body1Regular.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing05,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  StringUtils.maskPhoneNumber(
                    state.detailComplain?.customerPhone ?? '',
                  ),
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ],
            ),
          ),
          Text(
            state.detailComplain?.contactAddress ?? '',
            style: UITextStyle.body2Regular.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing3,
            ),
            child: Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                  BaseSpacing.spacing15s,
                ),
                border: Border.all(
                  color: BaseColors.borderDefault,
                  width: BaseSpacing.spacingPx,
                ),
              ),
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: BaseSpacing.spacing3,
                  vertical: BaseSpacing.spacing1,
                ),
                child: Text(
                  "Khách hàng ${UserTypeExtension.fromString(
                    state.detailComplain?.customerTypeStr,
                  ).display}",
                  style: UITextStyle.caption1Regular.copyWith(
                    color: BaseColors.textBody,
                  ),
                ),
              ),
            ),
          ),
          InkWellWidget(
            onTap: () async {
              await AppBottomSheet.showNormalBottomSheet(
                context,
                title: "Thông tin khách hàng",
                height: MediaQuery.of(context).size.height * 0.9,
                child: BuildCustomerWidget(
                  argument: BuildCustomerArgument(
                    addressDetail: state.detailComplain?.contactAddress ?? '',
                    contactName: state.detailComplain?.contactName ?? '',
                    performerPhone: state.detailComplain?.performerPhone ?? '',
                    userType: UserTypeExtension.fromString(
                      state.detailComplain?.customerTypeStr,
                    ),
                    phoneOrTaxCode: state.detailComplain?.taxCode ?? '',
                    customerId: state.detailComplain!.isB2CCustomer
                        ? state.customerInfo?.customerId
                        : state.companyInfo?.customerId,
                    customerName: state.detailComplain?.customerName,
                    customerPhone: state.detailComplain?.customerPhone,
                    contactPhone: state.detailComplain?.contactPhoneNumber,
                    customerTypeStr: state.detailComplain?.customerTypeStr,
                    objectId: state.detailComplain?.ticketId.toString(),
                    isShowCallAction: !state.detailComplain!.isAdditionInfo,
                  ),
                ),
              );
            },
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  "Xem thông tin khách hàng",
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.primary,
                  ),
                ),
                MyAssets.icons.iconArrowRightS18.svg(
                  colorFilter: ColorFilter.mode(
                    BaseColors.primary,
                    BlendMode.srcIn,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget orderInfo() {
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing3,
            ),
            child: Text(
              "Thông tin đơn hàng",
              style: UITextStyle.body1SemiBold,
            ),
          ),
          DividerWidget(
            height: BaseSpacing.spacingPx,
            color: BaseColors.secondaryBackground,
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacing3,
            ),
            child: contractOrder(),
          ),
        ],
      ),
    );
  }

  Widget orderInfoView() {
    final state = ref.watch(complainDetailProvider);
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Thông tin đơn hàng",
            style: UITextStyle.body1SemiBold,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing3,
            ),
            child: DividerWidget(
              height: BaseSpacing.spacingPx,
              color: BaseColors.secondaryBackground,
            ),
          ),
          BuildRowDetailComplainView(
            title: "Mã đơn hàng",
            value: state.detailComplain?.packageName ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Thợ thực hiện",
              value: state.detailComplain?.performerContract ?? '',
            ),
          ),
          if (state.detailComplain!.isSolution &&
              (state.detailComplain?.serial ?? '').isNotEmpty) ...<Widget>[
            BuildRowDetailComplainView(
              title: "Mã sản phẩm bảo hành",
              value: state.detailComplain?.goodsCode ?? '',
            ),
            Padding(
              padding: const EdgeInsets.only(
                top: BaseSpacing.spacing2,
              ),
              child: BuildRowDetailComplainView(
                title: "Tên sản phẩm bảo hành",
                value: state.detailComplain?.goodsName ?? '',
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing2,
              ),
              child: BuildRowDetailComplainView(
                title: "Serial sản phẩm bảo hành",
                value: state.detailComplain?.serial ?? '',
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget complainInfo() {
    final state = ref.watch(complainDetailProvider);
    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing3,
            ),
            child: Text(
              "Thông tin phản ánh",
              style: UITextStyle.body1SemiBold,
            ),
          ),
          DividerWidget(
            height: BaseSpacing.spacingPx,
            color: BaseColors.secondaryBackground,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Ngành hàng",
              value: state.detailComplain?.productCategoryName ?? '',
            ),
          ),
          BuildRowDetailComplainView(
            title: "Nhóm khiếu nại",
            value: state.detailComplain?.complaintGroupName ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Loại khiếu nại",
              value: state.detailComplain?.complaintTypeName ?? '',
            ),
          ),
          BuildRowDetailComplainView(
            title: "Sản phẩm/Dịch vụ",
            value: state.detailComplain?.productName ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Nội dung phản ánh",
              value: state.detailComplain?.contentComplaint ?? '',
            ),
          ),
          BuildRowDetailComplainView(
            title: "Người tạo ticket",
            value: state.detailComplain?.createdByName ?? '',
          ),
        ],
      ),
    );
  }

  Widget complainProcessInfo() {
    final state = ref.watch(complainDetailProvider);
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            "Thông tin xử lý",
            style: UITextStyle.body1SemiBold,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: DividerWidget(
              height: BaseSpacing.spacingPx,
              color: BaseColors.secondaryBackground,
            ),
          ),
          BuildRowDetailComplainView(
            title: "Thời gian tiếp nhận",
            value: state.detailComplain?.actualStartDateStr ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Nhân viên xử lý",
              value: state.detailComplain?.performerName ?? '',
            ),
          ),
          createReasonGroup(),
        ],
      ),
    );
  }

  Widget complainCompletedInfo() {
    final state = ref.watch(complainDetailProvider);
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            "Thông tin xử lý",
            style: UITextStyle.body1SemiBold,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: DividerWidget(
              height: BaseSpacing.spacingPx,
              color: BaseColors.secondaryBackground,
            ),
          ),
          BuildRowDetailComplainView(
            title: "Thời gian tiếp nhận",
            value: state.detailComplain?.actualStartDateStr ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Thời gian hoàn thành",
              value: state.detailComplain?.actualEndDateStr ?? '',
            ),
          ),
          BuildRowDetailComplainView(
            title: "Nhân viên xử lý",
            value: state.detailComplain?.performerName ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Nhóm nguyên nhân",
              value: state.detailComplain?.overallReasonString ?? '',
            ),
          ),
          BuildRowDetailComplainView(
            title: "Nguyên nhân phản ánh",
            value: state.detailComplain?.mainReasonString ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Nguyên nhân chi tiết",
              value: state.detailComplain?.detailedReason ?? '',
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Kết quả xử lý tổng quan",
              value: state.detailComplain?.contentProcess ?? '',
            ),
          ),
          if ((state.detailComplain?.workList ?? []).isNotEmpty) ...<Widget>[
            Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing2,
              ),
              child: Text(
                "Nội dung xử lý chi tiết:",
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textSubtitle,
                ),
              ),
            ),
          ],
          if ((state.detailComplain?.workList ?? []).isNotEmpty) ...<Widget>[
            Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(
                  BaseSpacing.spacing2,
                ),
                border: Border.all(
                  color: BaseColors.borderDivider,
                ),
              ),
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing2,
              ),
              child: Column(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(
                          BaseSpacing.spacing1,
                        ),
                      ),
                      color: BaseColors.borderDefault,
                    ),
                    child: SizedBox(
                      height: BaseSpacing.spacing10,
                      child: Row(
                        children: [
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.only(
                                left: BaseSpacing.spacing2,
                              ),
                              child: const Text(
                                "Công việc",
                              ),
                            ),
                          ),
                          Container(
                            width: BaseSpacing.spacingPx,
                            color: BaseColors.borderDivider,
                          ),
                          Expanded(
                            child: Container(
                              padding: const EdgeInsets.only(
                                left: BaseSpacing.spacing2,
                              ),
                              child: const Text(
                                "Kết quả",
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                  ListView.builder(
                    shrinkWrap: true,
                    physics: const NeverScrollableScrollPhysics(),
                    itemCount: state.detailComplain?.workList?.length,
                    itemBuilder: (context, index) {
                      final item =
                          (state.detailComplain?.workList ?? [])[index];
                      return Column(
                        children: [
                          SizedBox(
                            height: BaseSpacing.spacing10,
                            child: Row(
                              children: [
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                      left: BaseSpacing.spacing2,
                                    ),
                                    child: Text(
                                      item.workName ?? '',
                                      style: UITextStyle.body2Regular.copyWith(
                                        color: BaseColors.textTitle,
                                      ),
                                    ),
                                  ),
                                ),
                                Container(
                                  width: BaseSpacing.spacingPx,
                                  color: BaseColors.textOnColor,
                                ),
                                Expanded(
                                  child: Padding(
                                    padding: const EdgeInsets.only(
                                      left: BaseSpacing.spacing2,
                                    ),
                                    child: Text(
                                      item.workContent ?? '',
                                      style: UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textTitle),
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (index !=
                              (state.detailComplain?.workList ?? []).length -
                                  1) ...<Widget>[
                            DividerWidget(
                              height: BaseSpacing.spacingPx,
                              color: BaseColors.secondaryBackground,
                            ),
                          ],
                        ],
                      );
                    },
                  ),
                ],
              ),
            ),
          ],
          Padding(
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacing2,
            ),
            child: Text(
              "File xử lý:",
              style: UITextStyle.body2Medium,
            ),
          ),
          if ((state.detailComplain?.listFileTicket ?? [])
              .isNotEmpty) ...<Widget>[
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing2,
              ),
              child: listFileView(
                detailComplain: state.detailComplain,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget listFileView({
    DetailComplainEntity? detailComplain,
  }) {
    final widthSize = MediaQuery.of(context).size.width;
    ComplainFileEntity? videoFile;
    List<ComplainFileEntity>? listImg = [];
    detailComplain?.listFileTicket?.forEach(
      (file) {
        if (file.type.isVideo) {
          videoFile = file;
        } else {
          listImg.add(file);
        }
      },
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: (widthSize - BaseSpacing.spacing10) / BaseSpacing.spacing1,
          padding: const EdgeInsets.only(
            bottom: BaseSpacing.spacing2,
          ),
          child: SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: List.generate(
                listImg.length,
                (index) {
                  return Stack(
                    children: [
                      Container(
                        height: (widthSize - BaseSpacing.spacing10) /
                            BaseSpacing.spacing1,
                        width: (widthSize - BaseSpacing.spacing10) /
                            BaseSpacing.spacing1,
                        padding: const EdgeInsets.only(
                          top: BaseSpacing.spacing1,
                          right: BaseSpacing.spacing1,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(
                            BaseSpacing.spacing2,
                          ),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(
                            BaseSpacing.spacing2,
                          ),
                          child: ImageWidget(
                            listImg[index].link ?? "",
                            enableShowPreview: true,
                            size: const Size(
                              76,
                              76,
                            ),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ),
        if (videoFile != null)
          Container(
            height: (widthSize - BaseSpacing.spacing10) / BaseSpacing.spacing1,
            width: (widthSize - BaseSpacing.spacing10) / BaseSpacing.spacing1,
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacing1,
              right: BaseSpacing.spacing1,
            ),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(
                BaseSpacing.spacing2,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(
                BaseSpacing.spacing2,
              ),
              child: VideoWidget(
                videoFile?.link ?? "",
                enableShowPreview: true,
                size: const Size(
                  76,
                  76,
                ),
              ),
            ),
          ),
      ],
    );
  }

  Widget createReasonGroup() {
    final state = ref.watch(complainDetailProvider);
    final rf = ref.read(complainDetailProvider.notifier);
    List<ComplainFileEntity>? listImage = [];
    List<ComplainFileEntity>? listVideo = [];

    state.listFile?.forEach(
      (item) {
        if (item.type.isVideo) {
          listVideo.add(item);
        } else if (item.type.isImage) {
          listImage.add(item);
        }
      },
    );
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            bottom: BaseSpacing.spacing2,
          ),
          child: DropDownComplainWidget(
            data: state.listService?.toList() ?? [],
            title: "Sản phẩm/Dịch vụ",
            isRequired: true,
            onChangeItem: (value) {
              rf.changeService(value);
            },
            validator: (value) {
              return ValidateUtils.onValidateNotNull(value: value);
            },
            selected: state.serviceSelected,
            displayValue: (item) => item.name ?? '',
          ),
        ),
        DropDownComplainWidget(
          data: state.listSymbol ?? [],
          title: "Hình thức xử lý",
          isRequired: true,
          onChangeItem: (value) {
            rf.changeSymbol(value);
          },
          selected: state.symbolSelected,
          displayValue: (item) => item.name ?? '',
          validator: (value) {
            return ValidateUtils.onValidateNotNull(
              value: value,
            );
          },
        ),
        // không có serial sản phẩm va la GPTH va hinh thuc la tai hien truong
        state.loadProcessSymbolStatus == LoadStatus.loading
            ? const LoadingIndicatorWidget()
            : Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if ((state.detailComplain?.isSolution ?? false) &&
                      state.symbolSelected!.value ==
                          EnumSymbolProcess.inTrust.keyToServer &&
                      state.serviceSelected?.haveSerial == false) ...<Widget>[
                    Padding(
                      padding: const EdgeInsets.only(
                        top: BaseSpacing.spacing2,
                      ),
                      child: TextFieldWidget(
                        controller: deviceQuantity,
                        textInputAction: TextInputAction.done,
                        alignment: Alignment.topLeft,
                        keyboardType: TextInputType.number,
                        labelText: "Số lượng thiết bị",
                        isRequired: true,
                        inputFormatters: [
                          FilteringTextInputFormatter.allow(
                            RegExp(Patterns.bankNumber),
                          ),
                        ],
                        validator: (value) {
                          if (value.isEmpty) {
                            return ValidateUtils.onValidateNotNull(
                              title: "Số lượng thiết bị",
                              value: value,
                            );
                          } else if (int.parse(value) <= 0) {
                            return "Số lượng tối thiểu là 1";
                          }
                          return null;
                        },
                      ),
                    ),
                  ],
                  // phải có serial && dich vu la GPTH và hình thuwsc xu ly la tai hien truong
                  // co serial sản phẩm va la GPTH va hinh thuc la tai hien truong
                  // truyền của sp dịch vu
                  if ((state.detailComplain?.isSolution ?? false) &&
                      state.symbolSelected!.value ==
                          EnumSymbolProcess.inTrust.keyToServer &&
                      state.serviceSelected?.haveSerial == true) ...<Widget>[
                    Padding(
                      padding: const EdgeInsets.only(
                        bottom: BaseSpacing.spacing4,
                      ),
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: BaseSpacing.spacing2,
                            ),
                            child: titleRequired(
                              title: 'Thiết bị đã xử lý',
                            ),
                          ),
                          serialSelectView(),
                        ],
                      ),
                    ),
                  ],
                  if (state.symbolSelected!.value ==
                      EnumSymbolProcess.inTrust.keyToServer) ...<Widget>[
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: BaseSpacing.spacing2,
                      ),
                      child: Column(
                        children: [
                          titleRequired(
                            title: 'Nội dung xử lý chi tiết',
                          ),
                          state.processingContentStatus == LoadStatus.loading
                              ? const LoadingIndicatorWidget()
                              : ListView.builder(
                                  itemCount: (state.workList ?? []).length,
                                  shrinkWrap: true,
                                  physics: const NeverScrollableScrollPhysics(),
                                  itemBuilder: (context, index) {
                                    return Stack(
                                      alignment: Alignment.topRight,
                                      children: [
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                            vertical: BaseSpacing.spacing2,
                                          ),
                                          child: Container(
                                            decoration: BoxDecoration(
                                              borderRadius:
                                                  BorderRadius.circular(
                                                BaseSpacing.spacing2,
                                              ),
                                              border: Border.all(
                                                color: BaseColors.borderDefault,
                                              ),
                                            ),
                                            child: Column(
                                              crossAxisAlignment:
                                                  CrossAxisAlignment.start,
                                              children: [
                                                TextFieldWidget(
                                                  maxLines: null,
                                                  labelText:
                                                      "Nhập công việc chi tiết",
                                                  border: Border.all(
                                                    color: Colors.transparent,
                                                  ),
                                                  onChanged: (value) {
                                                    state.workList![index]
                                                        ["workName"] = value;
                                                    setState(() {});
                                                  },
                                                  controller:
                                                      TextEditingController(
                                                    text: state.workList![index]
                                                        ["workName"],
                                                  ),
                                                ),
                                                DividerWidget(
                                                  height: BaseSpacing.spacingPx,
                                                  color: BaseColors
                                                      .secondaryBackground,
                                                ),
                                                TextFieldWidget(
                                                  maxLines: null,
                                                  border: Border.all(
                                                    color: Colors.transparent,
                                                  ),
                                                  labelText:
                                                      "Nhập kết quả công việc",
                                                  onChanged: (value) {
                                                    state.workList![index]
                                                        ["workContent"] = value;
                                                    setState(() {});
                                                  },
                                                  controller:
                                                      TextEditingController(
                                                    text: state.workList![index]
                                                        ["workContent"],
                                                  ),
                                                ),
                                              ],
                                            ),
                                          ),
                                        ),
                                        if ((state.workList ?? []).length >
                                            1) ...<Widget>[
                                          InkWellWidget(
                                            onTap: () {
                                              ref
                                                  .read(complainDetailProvider
                                                      .notifier)
                                                  .removeAtWorkList(index);
                                              setState(() {});
                                            },
                                            child: MyAssets.icons.closeCircleRed
                                                .svg(),
                                          ),
                                        ],
                                      ],
                                    );
                                  },
                                ),
                          BaseButton(
                            text: "Thêm công việc",
                            icon: MyAssets.icons.icAdd.svg(),
                            backgroundColor: BaseColors.primarySurface,
                            textColor: BaseColors.primary,
                            onTap: () {
                              state.workList?.add(
                                {
                                  "workName": "",
                                  "workContent": "",
                                },
                              );
                              setState(() {});
                            },
                          ),
                        ],
                      ),
                    ),
                  ],
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: BaseSpacing.spacing2,
                    ),
                    child: TextFieldWidget(
                      controller: resultController,
                      textInputAction: TextInputAction.done,
                      height: 110,
                      alignment: Alignment.topLeft,
                      labelText: "Kết quả xử lý tổng quan",
                      isRequired: true,
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(
                            Patterns.note,
                          ),
                        ),
                      ],
                      validator: (value) {
                        return ValidateUtils.onValidateNotNull(
                          title: "Kết quả xử lý",
                          value: value,
                        );
                      },
                    ),
                  ),
                ],
              ),
        DropDownComplainWidget(
          data: state.listReasonGroup ?? [],
          title: "Nhóm nguyên nhân",
          isRequired: true,
          onChangeItem: (value) {
            ref.read(complainDetailProvider.notifier).changeReasonGroup(
                  value,
                );
          },
          selected: state.reasonGroupSelected,
          validator: (val) {
            return ValidateUtils.onValidateNotNull(
              value: val,
              title: "Nhóm nguyên nhân",
            );
          },
          displayValue: (item) => item.name ?? '',
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
          ),
          child: DropDownComplainWidget(
            data: state.listReasonComplain ?? [],
            title: "Nguyên nhân phản ánh",
            isRequired: true,
            onChangeItem: (value) {
              ref.read(complainDetailProvider.notifier).changeComplain(
                    value,
                  );
            },
            validator: (val) {
              return ValidateUtils.onValidateNotNull(
                title: "Nguyên nhân phản ánh",
                value: val,
              );
            },
            selected: state.reasonSelected,
            displayValue: (item) => item.name ?? '',
          ),
        ),
        TextFieldWidget(
          controller: reasonDetailController,
          textInputAction: TextInputAction.done,
          height: 110,
          alignment: Alignment.topLeft,
          labelText: "Nguyên nhân chi tiết",
          isRequired: true,
          inputFormatters: [
            FilteringTextInputFormatter.allow(
              RegExp(Patterns.note),
            ),
          ],
          validator: (value) {
            return ValidateUtils.onValidateNotNull(
              title: "Nguyên nhân chi tiết",
              value: value,
            );
          },
        ),

        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
          ),
          child: TextFieldWidget(
            controller: noteController,
            textInputAction: TextInputAction.done,
            height: 110,
            alignment: Alignment.topLeft,
            labelText: "Ghi chú",
            autofocus: false,
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                RegExp(Patterns.note),
              ),
            ],
          ),
        ),

        Text(
          'File xử lý',
          style: UITextStyle.body2Medium.copyWith(
            color: BaseColors.textLabel,
          ),
        ),
        _listImageView(
          listImage,
        ),
        if (listVideo.isNotEmpty) ...<Widget>[
          _listVideoView(
            listVideo,
          ),
        ],
      ],
    );
  }

  Widget serialSelectView() {
    final state = ref.watch(complainDetailProvider);
    return Container(
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          BaseSpacing.spacing2,
        ),
        border: Border.all(
          color: BaseColors.borderDivider,
        ),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            padding: const EdgeInsets.all(
              BaseSpacing.spacing4,
            ),
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(
                  BaseSpacing.spacing2,
                ),
              ),
              color: BaseColors.backgroundGray1,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  "Serial",
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textBody,
                  ),
                ),
                InkWellWidget(
                  onTap: () async {
                    final result = await context.push(
                      RouterPaths.serialContractComplain,
                      extra: SelectSerialComplainArguments(
                        customerPhone: state.detailComplain?.customerPhone,
                        goodsName: state.serviceSelected?.name,
                        serialSelected: state.listSerial,
                      ),
                    );
                    if (result is List<Serial>) {
                      List<String> currentSerial =
                          result.map((e) => e.serial ?? '').toList();
                      ref.read(complainDetailProvider.notifier).addListSerial(
                            currentSerial,
                          );
                    }
                  },
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          right: BaseSpacing.spacing2,
                        ),
                        child: MyAssets.icons.icAdd.svg(),
                      ),
                      Text(
                        "Chọn serial",
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Flexible(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: BaseSpacing.spacing4,
              ),
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: (state.listSerial ?? []).length,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  final item = state.listSerial![index];
                  return itemSerialView(
                    code: item,
                  );
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget itemSerialView({
    String? code,
    bool isShow = true,
  }) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing4,
          ),
          child: Row(
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  right: BaseSpacing.spacing4,
                ),
                child: InkWellWidget(
                  onTap: () {
                    ref.read(complainDetailProvider.notifier).removeSerial(
                          code ?? '',
                        );
                  },
                  child: MyAssets.icons.icSub.svg(),
                ),
              ),
              Text(
                code ?? '',
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
            ],
          ),
        ),
        if (isShow) ...<Widget>[
          DividerWidget(
            height: BaseSpacing.spacingPx,
            color: BaseColors.secondaryBackground,
          ),
        ],
      ],
    );
  }

  void onTakePicture(
    int index,
  ) async {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Upload ảnh",
      isFlexible: true,
      child: UploadImageBottomSheet(
        onPickMultiImage: (file) async {
          List<ComplainFileEntity> listFile = [];

          for (var item in file) {
            String base64Image = await fileToBase64(item);
            final result =
                await ref.read(complainDetailProvider.notifier).uploadFileV2(
                      complainImage: ComplainFileEntity(
                        base64String: base64Image,
                      ),
                      file: item,
                    );
            listFile.addAll(result);
          }
          final validator = validatorImage(
            listFile,
          );
          if (validator) {
            ref.read(complainDetailProvider.notifier).addFile(
                  files: listFile,
                );
          }
        },
        onTakeImage: (file) async {
          String base64Image = await fileToBase64(file);
          final listFile =
              await ref.read(complainDetailProvider.notifier).uploadFileV2(
                    complainImage: ComplainFileEntity(
                      base64String: base64Image,
                    ),
                    file: file,
                  );
          final validator = validatorImage(listFile);
          if (validator) {
            ref.read(complainDetailProvider.notifier).addFile(
                  files: listFile,
                );
          }
        },
        onPickVideo: (file) async {
          String base64Image = await fileToBase64(file);
          final listFile =
              await ref.read(complainDetailProvider.notifier).uploadFileV2(
                    complainImage: ComplainFileEntity(
                      base64String: base64Image,
                    ),
                    file: file,
                  );
          final validator = validatorImage(listFile);
          if (validator == true) {
            ref.read(complainDetailProvider.notifier).addFile(
                  files: listFile,
                );
          }
        },
      ),
    );
  }

  Widget _buildBottom() {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing4,
      ),
      child: BuildComplainEnTrust(
        isAssignComplain: isAssignComplain,
        refreshData: refreshData,
        isHasProcess: isHasProcess,
      ),
    );
  }

  Widget rejectButton() {
    final state = ref.watch(complainDetailProvider);
    return Padding(
      padding: const EdgeInsets.only(
        left: BaseSpacing.spacing4,
        right: BaseSpacing.spacing4,
        top: BaseSpacing.spacing4,
      ),
      child: BaseButton(
        text: "Từ chối",
        onTap: () {
          AppBottomSheet.showNormalBottomSheet(
            context,
            title: "Lý do từ chối",
            height: MediaQuery.of(context).size.height * 0.8,
            child: ComplainRefuseView(
              ticketId: state.detailComplain?.ticketId,
              refreshData: refreshData,
            ),
          );
        },
        backgroundColor: Colors.white,
        textColor: BaseColors.textLabel,
        borderColor: BaseColors.secondaryBorder,
      ),
    );
  }

  Widget completedButton() {
    return Padding(
      padding: const EdgeInsets.only(
        right: BaseSpacing.spacing4,
        left: BaseSpacing.spacing4,
        top: BaseSpacing.spacing4,
      ),
      child: BaseButton(
        text: "Hoàn thành ",
        onTap: () {
          onDoneComplain(
            context,
          );
        },
        backgroundColor: BaseColors.primary,
        textColor: Colors.white,
        borderColor: BaseColors.secondaryBorder,
      ),
    );
  }

  Widget interactionButton({
    required BuildContext context,
  }) {
    final state = ref.watch(
      complainDetailProvider,
    );
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: BaseSpacing.spacing4,
          ),
          margin: const EdgeInsets.only(
            top: BaseSpacing.spacing4,
          ),
          child: BaseButton(
            text: "Tương tác khách hàng",
            onTap: () async {
              final result = await context.push(
                RouterPaths.interactCustomerComplain,
                extra: InteractCustomerArguments(
                  ticketId: state.detailComplain?.ticketId,
                  customerPhone: state.detailComplain?.customerPhone,
                  userType: state.detailComplain!.isB2CCustomer
                      ? UserType.personal
                      : UserType.company,
                ),
              );
              if (result == true && context.mounted) {
                AppDialog.showDialogCenter(
                  context,
                  message: "Tương khách hàng thành công",
                  status: DialogStatus.success,
                  onConfirm: () {
                    refreshData();
                  },
                );
              }
            },
            backgroundColor: Colors.white,
            textColor: BaseColors.primary,
            borderColor: BaseColors.primary,
          ),
        ),
      ],
    );
  }

  Widget extensionButton() {
    final state = ref.watch(
      complainDetailProvider,
    );
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: BaseSpacing.spacing4,
          ),
          margin: const EdgeInsets.only(
            top: BaseSpacing.spacing4,
          ),
          child: BaseButton(
            text: "Gia hạn",
            onTap: () async {
              AppBottomSheet.showNormalBottomSheet(
                context,
                title: "Gia hạn",
                height: MediaQuery.of(context).size.height * 0.8,
                child: ComplainExtensionView(
                  ticketId: state.detailComplain?.ticketId,
                  refreshData: refreshData,
                ),
              );
            },
            backgroundColor: Colors.white,
            textColor: BaseColors.primary,
            borderColor: BaseColors.primary,
          ),
        ),
      ],
    );
  }

  Widget additionalInfoButton() {
    final state = ref.watch(
      complainDetailProvider,
    );
    return Column(
      children: [
        Container(
          padding: const EdgeInsets.symmetric(
            horizontal: BaseSpacing.spacing4,
          ),
          margin: const EdgeInsets.only(
            top: BaseSpacing.spacing4,
          ),
          child: BaseButton(
            text: !state.isAdditional ? "Bổ sung thông tin" : "Cập Nhật",
            onTap: () async {
              if (state.isAdditional) {
                if ((state.reasonDetail ?? '').isEmpty) {
                  AppDialog.showDialogCenter(
                    context,
                    message: "Vui lòng nhập nguyên nhân chi tiết",
                    status: DialogStatus.error,
                  );
                  return;
                }
                if ((state.contentProcess ?? '').isEmpty) {
                  AppDialog.showDialogCenter(
                    context,
                    message: "Vui lòng nhập kết quả xử lý tổng quan",
                    status: DialogStatus.error,
                  );
                  return;
                }
                if (state.reasonGroupSelected?.value == null) {
                  AppDialog.showDialogCenter(
                    context,
                    message: "Vui lòng chọn nhóm nguyên nhân",
                    status: DialogStatus.error,
                  );
                  return;
                }
                if (state.reasonSelected?.value == null) {
                  AppDialog.showDialogCenter(
                    context,
                    message: "Vui lòng chọn nguyên nhân phản ánh",
                    status: DialogStatus.error,
                  );
                  return;
                }
                bool isUpdate = await ref
                    .read(complainDetailProvider.notifier)
                    .updateTicketInfo(
                      ticketId: state.detailComplain?.ticketId,
                      detailedReason: state.reasonDetail?.trim(),
                      contentProcess: state.contentProcess?.trim(),
                    );
                ref.read(complainDetailProvider.notifier).changedUpdateInfo(
                      state.isAdditional,
                    );

                if (isUpdate == true) {
                  refreshData();
                }
              } else {
                ref
                    .read(complainDetailProvider.notifier)
                    .changedUpdateInfo(state.isAdditional);
              }
            },
            backgroundColor: BaseColors.primary,
            textColor: Colors.white,
          ),
        ),
      ],
    );
  }

  Future<void> onDoneComplain(BuildContext context) async {
    final state = ref.watch(complainDetailProvider);
    final rf = ref.read(complainDetailProvider.notifier);
    List<WorkList> currentWorkList = rf.convertToWorkList(state.workList);
    FocusScope.of(context).unfocus();
    if (formProcessKey.currentState!.validate()) {
      String serial = (state.listSerial ?? []).join(',');
      bool isCheckWorkList = rf.isValidWorkList(state.workList);
      if (!isCheckWorkList &&
          state.symbolSelected?.value ==
              EnumSymbolProcess.inTrust.keyToServer) {
        return ErrorDialog.showErrorDialog(
          'Nội dung công việc không được để trống',
        );
      }
      if (state.contract == EnumContractCustomer.hasContract &&
          !isValidContract) {
        return ErrorDialog.showErrorDialog(
          'Mã hợp đồng không tồn tại',
        );
      }
      if (state.symbolSelected?.value ==
              EnumSymbolProcess.inTrust.keyToServer &&
          !state.detailComplain!.isWarranty) {
        context.push(
          RouterPaths.signContractComplain,
          extra: SignContractComplainArguments(
            phoneNumber: state.detailComplain?.contactPhoneNumber ?? state.detailComplain?.customerPhone ?? '',
            isCompletedComplain: true,
            employeeSignedArg: (state.detailComplain?.listBb ?? []).isNotEmpty
                ? state.detailComplain?.listBb?.first.signStatus ==
                    EnumSignAcceptanceComplain.waitingCustomerSign.keyToServer
                : false,
            signAcceptanceContractBody: SignAcceptanceContractBody(
              ticketId: state.detailComplain?.ticketId,
              action: EnumSignAcceptanceComplain.complete.keyToServer,
              sysUserId: GlobalData.instance.userInfo?.sysUserId,
              overallReason: state.reasonGroupSelected?.value,
              mainReason: state.reasonSelected?.value,
              detailedReason: reasonDetailController.text.trim(),
              contentProcess: resultController.text.trim(),
              processForm: state.symbolSelected?.value,
              workList: currentWorkList,
              serial: serial,
              contractCode:
                  state.contract == EnumContractCustomer.hasContract &&
                          isValidContract
                      ? contractController.text.trim()
                      : null,
              listFile: state.listFile,
              systemContractDetail: state.reasonContractSelected?.name,
              reasonNotContract: state.contract.keyToServer,
              systemContractReason: reasonContractController.text.trim(),
              description: noteController.text.trim(),
              quantityAdvice: (state.detailComplain?.isSolution ?? false) &&
                      state.symbolSelected!.value ==
                          EnumSymbolProcess.inTrust.keyToServer &&
                      state.serviceSelected?.haveSerial == false
                  ? int.parse(
                      deviceQuantity.text.trim(),
                    )
                  : null,
            ),
            onCompleted: ({
              signInteractArg,
            }) {
              if (!context.mounted) return;
              AppDialog.showDialogCenter(
                context,
                message: "Ký nghiệm thu thành công!",
                status: DialogStatus.success,
                onConfirm: () {
                  refreshData();
                },
              );
              AppDialog.showDialogFeedback(
                context,
                source: "Màn hình hoàn thành sự cố khiếu nại",
              );
            },
          ),
        );
      } else {
        bool result = await ref.read(complainDetailProvider.notifier).completed(
              body: SignAcceptanceContractBody(
                ticketId: state.detailComplain?.ticketId,
                action: EnumSignAcceptanceComplain.complete.keyToServer,
                sysUserId: GlobalData.instance.userInfo?.sysUserId,
                overallReason: state.reasonGroupSelected?.value,
                mainReason: state.reasonSelected?.value,
                detailedReason: reasonDetailController.text.trim(),
                contentProcess: resultController.text.trim(),
                processForm: state.symbolSelected?.value,
                workList: currentWorkList,
                serial: serial,
                contractCode:
                    state.contract == EnumContractCustomer.hasContract &&
                            isValidContract
                        ? contractController.text.trim()
                        : null,
                listFile: state.listFile,
                systemContractDetail: state.reasonContractSelected?.name,
                reasonNotContract: state.contract.keyToServer,
                systemContractReason: reasonContractController.text.trim(),
                description: noteController.text.trim(),
                quantityAdvice: (state.detailComplain?.isSolution ?? false) &&
                        state.symbolSelected!.value ==
                            EnumSymbolProcess.inTrust.keyToServer &&
                        state.serviceSelected?.haveSerial == false
                    ? int.parse(
                        deviceQuantity.text.trim(),
                      )
                    : null,
              ),
            );

        if (!context.mounted) return;
        if (result) {
          AppDialog.showDialogCenter(
            context,
            message: "Hoàn thành khiếu nại!",
            status: DialogStatus.success,
            onConfirm: () {
              refreshData();
            },
          );
          AppDialog.showDialogFeedback(
            context,
            source: "Màn hình hoàn thành sự cố khiếu nại",
          );
        }
      }
    }
  }

  void onCallCustomer(WidgetRef ref) async {
    var state = ref.watch(complainDetailProvider);

    if (GlobalData.instance.userInfo?.isMobileCall ?? false) {
      final result = await MobileCallUtils.startCall(
        callerPhoneNumber: state.detailComplain?.customerPhone ?? "",
        callerName: state.detailComplain?.customerName ?? "",
        customerNumber: state.detailComplain?.customerTypeStr ==
                UserType.company.keyToServer
            ? state.detailComplain?.customerPhone ?? ''
            : (state.detailComplain?.contactPhoneNumber ?? '').isNotEmpty
                ? state.detailComplain?.contactPhoneNumber
                : state.detailComplain?.customerPhone ?? '',
        customerName: state.detailComplain?.customerName ?? '',
        code: state.detailComplain?.code ?? "",
        content: state.detailComplain?.contactAddress ?? '',
        workName: 'Phản ánh',
      );

      if (result != null) {
        ref.read(complainDetailProvider.notifier).saveLogMobileCall(
              body: result.convertMobileCallBody(
                mobileCallType: MobileCallType.associationRequest,
                referenceId: state.detailComplain?.code,
                customerNumber: state.detailComplain?.customerTypeStr ==
                        UserType.company.keyToServer
                    ? state.detailComplain?.customerPhone ?? ''
                    : (state.detailComplain?.contactPhoneNumber ?? '')
                            .isNotEmpty
                        ? state.detailComplain?.contactPhoneNumber
                        : state.detailComplain?.customerPhone ?? '',
              ),
            );
        ref.read(complainDetailProvider.notifier).insertLogMobileCall(
              body: InsertMobileCallBody(
                calleeNumber: state.detailComplain?.customerPhone ?? "",
                callerNumber: result.callerNumber,
                callId: result.callID,
                objectId: state.detailComplain?.ticketId.toString(),
                startTime:
                    DateFormat(DateTimeFormater.fullDateTimeFormat).format(
                  DateTime.fromMillisecondsSinceEpoch(
                    result.startTime!.toInt(),
                  ),
                ),
                endTime: DateFormat(DateTimeFormater.fullDateTimeFormat).format(
                  DateTime.fromMillisecondsSinceEpoch(
                    result.endTime!.toInt(),
                  ),
                ),
                ringingTime:
                    DateFormat(DateTimeFormater.fullDateTimeFormat).format(
                  DateTime.fromMillisecondsSinceEpoch(
                    result.ringingTime!.toInt(),
                  ),
                ),
                duration: result.duration,
                callStatus: result.errorType,
                // để tạm chưa check hết status
                callType: CallType.staffCall.type,
              ),
            );
      }
      refreshData();
    } else {
      AppUtils.callPhoneNumber(
        phoneNumber: (state.detailComplain!.contactPhoneNumber ?? '').isNotEmpty
            ? state.detailComplain!.contactPhoneNumber ?? ''
            : state.detailComplain!.customerPhone ?? '',
      );
    }
  }

  Widget titleRequired({
    String? title,
  }) {
    return Row(
      children: [
        Text(
          '$title',
          style: UITextStyle.body2Medium.copyWith(),
        ),
        Text(
          ' *',
          style: UITextStyle.body2Medium.copyWith(
            color: BaseColors.primary,
          ),
        ),
      ],
    );
  }

  Widget contractOrder() {
    final state = ref.watch(complainDetailProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.only(
            bottom: BaseSpacing.spacing3,
          ),
          child: titleRequired(
            title: "Thông tin đơn hàng/ hợp đồng",
          ),
        ),
        Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            Flexible(
              child: Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing3,
                ),
                child: RadioWidget<EnumContractCustomer>(
                  value: EnumContractCustomer.hasContract,
                  groupValue: state.contract,
                  onChanged: (value) {
                    ref.read(complainDetailProvider.notifier).changeContract(
                          value,
                        );
                  },
                  displayWidget: (context, item) {
                    return _buildDisplayUserText(
                      item,
                    );
                  },
                ),
              ),
            ),
            Flexible(
              child: Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing2,
                ),
                child: RadioWidget<EnumContractCustomer>(
                  value: EnumContractCustomer.noContract,
                  groupValue: state.contract,
                  onChanged: (value) {
                    ref.read(complainDetailProvider.notifier).changeContract(
                          value,
                        );
                  },
                  displayWidget: (context, item) {
                    return _buildDisplayUserText(
                      item,
                    );
                  },
                ),
              ),
            ),
            if (state.contract == EnumContractCustomer.hasContract) ...<Widget>[
              searchContractByPhoneOrSerial(),
            ],
            if (state.contract == EnumContractCustomer.noContract) ...<Widget>[
              reasonContractView(),
            ]
          ],
        ),
      ],
    );
  }

  Widget reasonContractView() {
    final state = ref.watch(
      complainDetailProvider,
    );
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.only(
            top: BaseSpacing.spacing2,
          ),
          child: DropDownComplainWidget(
            data: state.reasonContract ?? [],
            validator: (value) {
              return ValidateUtils.onValidateNotNull(
                title: "Lý do",
                value: value,
              );
            },
            title: "Lý do",
            isRequired: true,
            onChangeItem: (value) {
              ref.read(complainDetailProvider.notifier).changeReasonContract(
                    value,
                  );
            },
            selected: state.reasonContractSelected,
            displayValue: (item) => item.name ?? '',
          ),
        ),
        Padding(
          padding: const EdgeInsets.only(
            top: BaseSpacing.spacing4,
          ),
          child: TextFieldWidget(
            controller: reasonContractController,
            textInputAction: TextInputAction.done,
            height: 110,
            alignment: Alignment.topLeft,
            labelText: "Lý do chi tiết",
            isRequired: true,
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                RegExp(
                  Patterns.note,
                ),
              ),
            ],
            validator: (value) {
              return ValidateUtils.onValidateNotNull(
                value: value,
                title: "Lý do chi tiết",
              );
            },
          ),
        ),
      ],
    );
  }

  Widget searchContractByPhoneOrSerial() {
    return Padding(
      padding: const EdgeInsets.only(
        top: BaseSpacing.spacing2,
        bottom: BaseSpacing.spacing4,
      ),
      child: Padding(
        padding: const EdgeInsets.only(
          bottom: BaseSpacing.spacing4,
        ),
        child: TextFieldWidget(
          controller: contractController,
          isRequired: true,
          labelText: "Mã đơn hàng/hợp đồng/YCTX",
          textInputAction: TextInputAction.done,
          validator: (value) {
            return ValidateUtils.onValidateNotNull(
              title: "Mã đơn hàng/hợp đồng/YCTX",
              value: value,
            );
          },
          onFocusChange: (isFocus) async {
            if (!isFocus) {
              isValidContract = false;
              isValidContract = await ref
                  .read(complainDetailProvider.notifier)
                  .getListContractComplain(
                    keyword: contractController.text.trim(),
                  );
            }
          },
        ),
      ),
    );
  }

  Widget _buildDisplayUserText(EnumContractCustomer user) {
    return Text(
      user.title ?? '',
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _additionalInfoView() {
    final state = ref.watch(complainDetailProvider);
    List<ComplainFileEntity>? listImage = [];
    List<ComplainFileEntity>? listVideo = [];

    state.listFile?.forEach(
      (item) {
        if (item.type.isVideo) {
          listVideo.add(item);
        } else if (item.type.isImage) {
          listImage.add(item);
        }
      },
    );

    return Padding(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            "Thông tin xử lý",
            style: UITextStyle.body1SemiBold,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing3,
            ),
            child: DividerWidget(
              height: BaseSpacing.spacingPx,
              color: BaseColors.secondaryBackground,
            ),
          ),
          BuildRowDetailComplainView(
            title: "Thời gian tiếp nhận",
            value: state.detailComplain?.productCategoryName ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: BuildRowDetailComplainView(
              title: "Nhân viên xử lý",
              value: state.detailComplain?.performerName ?? '',
            ),
          ),
          DropDownComplainWidget(
            data: state.listReasonGroup ?? [],
            title: "Nhóm nguyên nhân",
            isRequired: true,
            onChangeItem: (value) {
              ref.read(complainDetailProvider.notifier).changeReasonGroup(
                    value,
                  );
            },
            selected: state.reasonGroupSelected,
            validator: (val) {
              return ValidateUtils.onValidateNotNull(
                value: val,
              );
            },
            displayValue: (item) => item.name ?? '',
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: DropDownComplainWidget(
              data: state.listReasonComplain ?? [],
              title: "Nguyên nhân phản ánh",
              isRequired: true,
              onChangeItem: (value) {
                ref.read(complainDetailProvider.notifier).changeComplain(
                      value,
                    );
              },
              validator: (val) {
                return ValidateUtils.onValidateNotNull(
                  title: "Nguyên nhân phản ánh",
                  value: val,
                );
              },
              selected: state.reasonSelected,
              displayValue: (item) => item.name ?? '',
            ),
          ),
          TextFieldWidget(
            controller: reasonDetailController,
            textInputAction: TextInputAction.done,
            height: 110,
            focusNode: reasonDetailFocus,
            alignment: Alignment.topLeft,
            labelText: "Nguyên nhân chi tiết",
            isRequired: true,
            onChanged: (value) {
              ref.read(complainDetailProvider.notifier).changeReasonDetail(
                    value,
                  );
            },
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                RegExp(Patterns.note),
              ),
            ],
            validator: (value) {
              return ValidateUtils.onValidateNotNull(
                title: "Nguyên nhân chi tiết",
                value: value,
              );
            },
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing2,
            ),
            child: TextFieldWidget(
              autovalidateMode: AutovalidateMode.onUserInteraction,
              controller: resultController,
              textInputAction: TextInputAction.done,
              height: 110,
              alignment: Alignment.topLeft,
              labelText: "Kết quả xử lý tổng quan",
              isRequired: true,
              onChanged: (value) {
                ref.read(complainDetailProvider.notifier).changeContentProcess(
                      value,
                    );
              },
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  RegExp(Patterns.note),
                ),
              ],
              validator: (value) {
                return ValidateUtils.onValidateNotNull(
                  title: "Kết quả xử lý",
                  value: value,
                );
              },
            ),
          ),
          Text(
            'File xử lý',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          _listImageView(
            listImage,
          ),
          if (listVideo.isNotEmpty) ...<Widget>[
            _listVideoView(
              listVideo,
            ),
          ],
        ],
      ),
    );
  }

  Widget _listImageView(
    List<ComplainFileEntity> listImage,
  ) {
    final state = ref.watch(complainDetailProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
      ),
      child: SizedBox(
        height: BaseSpacing.spacing20,
        child: ListView.separated(
          shrinkWrap: true,
          itemCount: listImage.length + 1,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.only(
            right: BaseSpacing.spacing4,
          ),
          separatorBuilder: (_, __) => const SizedBox(
            width: BaseSpacing.spacing2,
          ),
          itemBuilder: (context, index) {
            if (index == listImage.length) {
              return (state.listFile?.length ?? 0) < 6
                  ? InkWellWidget(
                      onTap: () {
                        onTakePicture(index);
                      },
                      child: MyAssets.icons.iconAddImageDashline.svg(),
                    )
                  : const SizedBox.shrink();
            }
            return Stack(
              children: [
                Container(
                  height: 76,
                  width: 76,
                  padding: const EdgeInsets.only(
                    top: BaseSpacing.spacing1,
                    right: BaseSpacing.spacing1,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                    child: listImage[index].base64String != null
                        ? ImageBase64Widget(
                            base64Image: listImage[index].base64String ?? "",
                          )
                        : ImageWidget(
                            listImage[index].link ?? '',
                          ),
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  child: Visibility(
                    visible: true,
                    child: InkWellWidget(
                      onTap: () {
                        ref.read(complainDetailProvider.notifier).deleteFile(
                              listImage[index],
                            );
                      },
                      child: MyAssets.icons.iconCloseRed.svg(),
                    ),
                  ),
                ),
                if (state.uploadImageStatus == LoadStatus.loading) ...[
                  const Positioned.fill(
                    child: Center(
                      child: LoadingIndicatorWidget(),
                    ),
                  ),
                ],
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _listVideoView(
    List<ComplainFileEntity> listVideo,
  ) {
    final state = ref.watch(
      complainDetailProvider,
    );
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
      ),
      child: SizedBox(
        height: BaseSpacing.spacing20,
        child: ListView.separated(
          shrinkWrap: true,
          itemCount: listVideo.length,
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.only(
            right: BaseSpacing.spacing4,
          ),
          separatorBuilder: (_, __) => const SizedBox(
            width: BaseSpacing.spacing2,
          ),
          itemBuilder: (context, index) {
            return Stack(
              children: [
                Container(
                  height: 76,
                  width: 76,
                  padding: const EdgeInsets.only(
                    top: BaseSpacing.spacing1,
                    right: BaseSpacing.spacing1,
                  ),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                    child: VideoWidget(
                      listVideo[index].link ?? '',
                    ),
                  ),
                ),
                Positioned(
                  top: 0,
                  right: 0,
                  child: Visibility(
                    visible: true,
                    child: InkWellWidget(
                      onTap: () {
                        ref.read(complainDetailProvider.notifier).deleteFile(
                              listVideo[index],
                            );
                      },
                      child: MyAssets.icons.iconCloseRed.svg(),
                    ),
                  ),
                ),
                if (state.uploadImageStatus == LoadStatus.loading) ...<Widget>[
                  const Positioned.fill(
                    child: Center(
                      child: LoadingIndicatorWidget(),
                    ),
                  ),
                ],
              ],
            );
          },
        ),
      ),
    );
  }

  bool validatorImage(
    List<ComplainFileEntity> files,
  ) {
    final state = ref.watch(complainDetailProvider);
    List<ComplainFileEntity> listFile = state.listFile ?? [];
    bool isCheck = false;
    final imgCount = listFile.where((file) => file.type.isImage).length;
    final videoCount = listFile.where((file) => file.type.isVideo).length;

    final imgDataSum = files.where((file) => file.type.isImage).length;
    final sumImages = imgCount + imgDataSum; // tổng ảnh

    if (sumImages > 5) {
      AppDialog.showDialogCenter(
        ErrorDialog.navigatorKey.currentContext!,
        message: "Không chọn quá 5 ảnh",
        status: DialogStatus.error,
      );
      isCheck = false;
    } else if (videoCount >= 1 &&
        (files.where((file) => file.type.isVideo)).isNotEmpty) {
      AppDialog.showDialogCenter(
        ErrorDialog.navigatorKey.currentContext!,
        message: "Không chọn quá 1 video",
        status: DialogStatus.error,
      );
      isCheck = false;
    } else {
      isCheck = true;
    }

    return isCheck;
  }

  Widget createWarranty() {
    final state = ref.watch(complainDetailProvider);
    return Column(
      children: [
        const SizedBox(
          height: BaseSpacing.spacing4,
        ),
        BaseButton(
          text: "Tạo bảo hành",
          onTap: () {
            DetailComplainEntity detailComplain = state.detailComplain!;
            if ((detailComplain.packageName ?? '').isEmpty && isValidContract) {
              detailComplain.packageName = contractController.text.trim();
            }
            context.push(
              RouterPaths.createRequirementWarranty,
              extra: CreateRequirementWarrantyArguments(
                detailComplainInfo: detailComplain,
              ),
            );
          },
          backgroundColor: Colors.white,
          textColor: BaseColors.primary,
          borderColor: BaseColors.primary,
        ).paddingSymmetric(
          horizontal: BaseSpacing.spacing4,
        ),
      ],
    );
  }
}
