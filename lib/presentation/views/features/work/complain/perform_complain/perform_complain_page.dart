import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/body/complain_body.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/presentation/views/features/work/complain/complain_view_model.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/complain_detail_page.dart';
import 'package:vcc/presentation/views/features/work/complain/widget/build_item_ticket.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';

class PerformComplainPage extends StatefulHookConsumerWidget {
  const PerformComplainPage({
    super.key,
  });

  @override
  ConsumerState<PerformComplainPage> createState() => _PerformTicketPageState();
}

class _PerformTicketPageState extends ConsumerState<PerformComplainPage> {
  late ScrollController scrollController;

  @override
  void initState() {
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(complainProvider.notifier).fetchAllComplain();
    }
  }

  Future<void> onRefresh() async {
    final state = ref.watch(complainProvider);
    ref.read(complainProvider.notifier).getAllComplain(
          body: ComplainBody(
            listStatus: state.complainTypes,
            listCompleteStatus: state.completedTypes,
            keySearch: state.keySearch,
            startDate: state.startDate,
            endDate: state.endDate,
            sysUserId: GlobalData.instance.userInfo?.sysUserId,
            isManager: false,
            pageIndex: state.allCurrentPage,
            pageSize: BaseConstant.size,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(complainProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }

    if ((state.listAllComplain ?? []).isEmpty) {
      return Expanded(
        child: EmptyListWidget(
          title: "Không có dữ liệu",
          onRefresh: onRefresh,
        ),
      );
    }

    return Expanded(
      child: RefreshIndicator(
        onRefresh: onRefresh,
        child: Container(
          color: BaseColors.backgroundGray,
          child: ListView.separated(
            controller: scrollController,
            itemCount: state.listAllComplain?.length ?? 0,
            physics: const AlwaysScrollableScrollPhysics(),
            shrinkWrap: true,
            separatorBuilder: (_, __) => DividerWidget(
              height: BaseSpacing.spacing2,
              color: BaseColors.backgroundGray,
            ),
            itemBuilder: (context, index) {
              final item = state.listAllComplain![index];

              return BuildItemTicket(
                item: item,
                onTap: () async {
                  await context.push(
                    RouterPaths.detailComplain,
                    extra: ComplainDetailArguments(
                      code: item.ticketId,
                    ),
                  );
                  onRefresh();
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
