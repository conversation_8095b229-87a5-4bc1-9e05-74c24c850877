import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/question_campaign_entity.dart';
import 'package:vcc/domain/enums/answer_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/survey_type.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/customer_survey_form/customer_survey_form_view_model.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/customer_survey_form/widgets/check_box_type_widget.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/customer_survey_form/widgets/datetime_type_widget.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/customer_survey_form/widgets/free_text_type_widget.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/customer_survey_form/widgets/media_type_widget.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/customer_survey_form/widgets/number_type_widget.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/customer_survey_form/widgets/radio_button_type_widget.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';

import 'models/flat_question_data.dart';
import 'services/survey_business_logic.dart';
import 'survey_utils.dart';

class CustomerSurveyFormArguments {
  final String? campaignId;
  final String? surveyCode;
  final DateTime? executionDate;

  CustomerSurveyFormArguments({
    this.surveyCode,
    this.campaignId,
    this.executionDate,
  });
}

class CustomerSurveyFormPage extends StatefulHookConsumerWidget {
  final CustomerSurveyFormArguments? arguments;

  const CustomerSurveyFormPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<CustomerSurveyFormPage> createState() =>
      _CustomerSurveyFormPageState();
}

class _CustomerSurveyFormPageState
    extends ConsumerState<CustomerSurveyFormPage> {
  @override
  void initState() {
    Future(() {
      ref.read(customerSurveyFormProvider.notifier).getListSurvey(
            campaignId: widget.arguments?.campaignId ?? '',
          );
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(customerSurveyFormProvider);

    return Stack(
      children: [
        LayoutPage(
          appbar: const AppBarCustom(
            title: "Bảng thu thập thông tin",
          ),
          bottomAction: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            child: SafeArea(
              child: BaseButton(
                text: "Hoàn thành khảo sát",
                isLoading: state.sendStatus == LoadStatus.loading,
                onTap: state.sendStatus == LoadStatus.loading
                    ? null
                    : () {
                        onDoneSurvey(context);
                      },
              ),
            ),
          ),
          body: InkWell(
            splashColor: Colors.transparent,
            highlightColor: Colors.transparent,
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: Container(
              color: BaseColors.backgroundWhite,
              padding: const EdgeInsets.only(top: 16),
              child: _buildBody(state),
            ),
          ),
        ),
        // Loading overlay khi upload image
        if (state.uploadImageStatus == LoadStatus.loading)
          const Center(
            child: LoadingIndicatorWidget(),
          )
      ],
    );
  }

  /// Xây dựng body với loading state
  Widget _buildBody(CustomerSurveyFormState state) {
    switch (state.loadStatus) {
      case LoadStatus.loading:
        return const Center(
          child: LoadingIndicatorWidget(),
        );
      case LoadStatus.failure:
        return Container(
          color: BaseColors.backgroundGray,
          child: EmptyListWidget(
            title: "Không có dữ liệu",
            onRefresh: () async {},
          ),
        );
      case LoadStatus.success:
        return _buildFlatQuestionList(state);
      default:
        return _buildFlatQuestionList(state);
    }
  }

  /// Xây dựng danh sách câu hỏi phẳng (không có hierarchy)
  Widget _buildFlatQuestionList(CustomerSurveyFormState state) {
    final flatQuestions =
        SurveyBusinessLogic.getFlatQuestionList(state.listQuestion ?? []);

    return ListView.builder(
      itemCount: flatQuestions.length,
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 24),
      itemBuilder: (context, index) {
        final questionData = flatQuestions[index];
        final question = questionData.question;

        if (question.hideQuestion ?? false) {
          return const SizedBox();
        }

        // Kiểm tra xem câu hỏi hiển thị không
        bool shouldShow = true;
        if (questionData.isSubQuestion) {
          final parentToCheck =
              questionData.directParent ?? questionData.parentQuestion!;
          shouldShow = SurveyUtils.shouldShowQuestion(
            parentQuestion: parentToCheck,
            childQuestion: question,
          );
        }

        if (!shouldShow) {
          return const SizedBox();
        }

        // Cache shouldShow status
        questionData.cacheShouldShow(shouldShow);

        // Đếm số thứ tự dựa trên các câu hỏi đang hiển thị
        final questionNumber = _getVisibleQuestionNumber(flatQuestions, index);
        questionData.cacheVisibleNumber(questionNumber);

        // Kiểm tra xem có phải câu hỏi cuối cùng đang hiển thị không
        final isLastVisible = _isLastVisibleQuestion(flatQuestions, index);
        questionData.cacheIsLastVisible(isLastVisible);

        return Padding(
          padding: EdgeInsets.only(bottom: isLastVisible ? 0 : 16),
          child: _buildQuestionWidget(
            item: question,
            index: questionData.originalIndex,
            subIndex: questionData.subIndex,
            numberQuestion: questionNumber,
            questionData: questionData,
          ),
        );
      },
    );
  }

  /// Tính số thứ tự dựa trên các câu hỏi đang hiển thị (với caching)
  int _getVisibleQuestionNumber(
      List<FlatQuestionData> flatQuestions, int currentIndex) {
    final currentData = flatQuestions[currentIndex];
    if (currentData.cachedVisibleNumber != null) {
      return currentData.cachedVisibleNumber!;
    }

    int visibleCount = 0;

    for (int i = 0; i <= currentIndex; i++) {
      final questionData = flatQuestions[i];
      final question = questionData.question;

      if (question.hideQuestion ?? false) {
        continue;
      }

      bool shouldShow = questionData.cachedShouldShow ?? true;
      if (questionData.cachedShouldShow == null && questionData.isSubQuestion) {
        final parentToCheck =
            questionData.directParent ?? questionData.parentQuestion!;
        shouldShow = SurveyUtils.shouldShowQuestion(
          parentQuestion: parentToCheck,
          childQuestion: question,
        );
      }

      if (shouldShow) {
        visibleCount++;
      }
    }

    return visibleCount;
  }

  /// Kiểm tra xem có phải câu hỏi cuối cùng đang hiển thị không (với caching)
  bool _isLastVisibleQuestion(
      List<FlatQuestionData> flatQuestions, int currentIndex) {
    final currentData = flatQuestions[currentIndex];
    if (currentData.cachedIsLastVisible != null) {
      return currentData.cachedIsLastVisible!;
    }

    // Kiểm tra từ vị trí hiện tại đến cuối danh sách
    for (int i = currentIndex + 1; i < flatQuestions.length; i++) {
      final questionData = flatQuestions[i];
      final question = questionData.question;

      // Bỏ qua nếu câu hỏi bị ẩn
      if (question.hideQuestion ?? false) {
        continue;
      }

      // Sử dụng cached shouldShow nếu có
      bool shouldShow = questionData.cachedShouldShow ?? true;
      if (questionData.cachedShouldShow == null && questionData.isSubQuestion) {
        final parentToCheck =
            questionData.directParent ?? questionData.parentQuestion!;
        shouldShow = SurveyUtils.shouldShowQuestion(
          parentQuestion: parentToCheck,
          childQuestion: question,
        );
      }

      if (shouldShow) {
        return false;
      }
    }

    return true;
  }

  /// Xây dựng widget câu hỏi với số thứ tự liên tục
  Widget _buildQuestionWidget({
    required QuestionCampaignEntity item,
    required int index,
    int? subIndex,
    required int numberQuestion,
    required FlatQuestionData questionData,
  }) {
    switch (item.type) {
      case SurveyType.radioButton:
        return RadioButtonTypeWidget(
          index: numberQuestion,
          question: item,
          onTap: (value) {
            ref.read(customerSurveyFormProvider.notifier).onChooseAnswer(
                  parIndex: index,
                  subIndex: subIndex,
                  answer: value,
                );
          },
        );
      case SurveyType.checkBox:
        return CheckBoxTypeWidget(
          index: numberQuestion,
          question: item,
          onTap: (value) {
            ref.read(customerSurveyFormProvider.notifier).onChooseMultiAnswer(
                  parIndex: index,
                  subIndex: subIndex,
                  answer: value,
                );
          },
        );
      case SurveyType.number:
        return NumberTypeWidget(
          index: numberQuestion,
          question: item,
          onChange: (value) {
            ref.read(customerSurveyFormProvider.notifier).saveContent(
                  parIndex: index,
                  subIndex: subIndex,
                  content: value,
                );
          },
        );
      case SurveyType.dateTime:
        return DatetimeTypeWidget(
          question: item,
          index: numberQuestion,
          onChooseDateTime: (value) {
            ref.read(customerSurveyFormProvider.notifier).saveContent(
                  parIndex: index,
                  subIndex: subIndex,
                  content: value.toIso8601String(),
                );
          },
        );
      case SurveyType.freeText:
        return FreeTextTypeWidget(
          question: item,
          index: numberQuestion,
          onChange: (value) {
            ref.read(customerSurveyFormProvider.notifier).saveContent(
                  parIndex: index,
                  subIndex: subIndex,
                  content: value,
                );
          },
        );
      case SurveyType.media:
        return Consumer(
          builder: (context, ref, child) {
            final state = ref.watch(customerSurveyFormProvider);

            final currentImages = questionData.getCurrentImages(state.listQuestion ?? []);

            return MediaTypeWidget(
              question: item,
              index: numberQuestion,
              images: currentImages,
              onChooseImage: (image) async {
                final result = await ref
                    .read(customerSurveyFormProvider.notifier)
                    .uploadImage(image);
                ref
                    .read(customerSurveyFormProvider.notifier)
                    .saveMediaByQuestionId(
                      questionId: item.id!,
                      images: result,
                    );
              },
              onDelete: (image) {
                ref
                    .read(customerSurveyFormProvider.notifier)
                    .deleteImageByQuestionId(
                      questionId: item.id!,
                      image: image,
                    );
              },
            );
          },
        );
      default:
        return const SizedBox();
    }
  }

  void onDoneSurvey(BuildContext context) async {
    final state = ref.read(customerSurveyFormProvider);
    final listSurvey = state.listQuestion ?? [];

    final flatQuestions = SurveyBusinessLogic.getFlatQuestionList(listSurvey);
    bool isContinue = true;
    bool isCheck = true;

    for (var i = 0; i < flatQuestions.length; i++) {
      if (!isContinue || !isCheck) {
        break;
      }

      final questionData = flatQuestions[i];
      final item = questionData.question;

      // Bỏ qua câu hỏi bị ẩn
      if (item.hideQuestion ?? false) {
        continue;
      }

      // Kiểm tra xem câu hỏi có nên hiển thị không
      bool shouldShow = true;
      if (questionData.isSubQuestion) {
        final parentToCheck =
            questionData.directParent ?? questionData.parentQuestion!;
        shouldShow = SurveyUtils.shouldShowQuestion(
          parentQuestion: parentToCheck,
          childQuestion: item,
        );
      }

      // Chỉ validate câu hỏi đang hiển thị
      if (!shouldShow) {
        continue;
      }

      // Tính số thứ tự hiển thị
      final questionNumber = _getVisibleQuestionNumber(flatQuestions, i);

      // Validate câu hỏi
      isCheck = _validateQuestion(
        item: item,
        questionNumber: questionNumber.toString(),
        questionData: questionData.toMap(),
        onCancelCheck: () {
          isContinue = false;
        },
      );
    }

    if (isCheck == false) {
      return;
    }

    final result =
        await ref.read(customerSurveyFormProvider.notifier).completeCollection(
              surveyCode: widget.arguments?.surveyCode,
            );

    if (!context.mounted) return;
    if (result ?? false) {
      context.pop(true);
    }
  }

  bool checkQuestion({
    required QuestionCampaignEntity item,
    String? questionNumber,
    Function? onCancelCheck,
  }) {
    if (item.isRequired ?? false) {
      if (item.type == SurveyType.radioButton) {
        if (item.answerUser == null) {
          AppDialog.showDialogCenter(
            context,
            message: SurveyUtils.getQuestionErrorMessage(questionNumber ?? ''),
            status: DialogStatus.error,
          );
          onCancelCheck?.call();
          return false;
        }

        if (item.answerUser?.answerType == AnswerType.stopSurvey) {
          onCancelCheck?.call();
          return true;
        }
        return true;
      } else if (item.type == SurveyType.checkBox) {
        if (item.answersUser == null || item.answersUser!.isEmpty) {
          AppDialog.showDialogCenter(
            context,
            message: SurveyUtils.getQuestionErrorMessage(questionNumber ?? ''),
            status: DialogStatus.error,
          );
          onCancelCheck?.call();
          return false;
        }

        if (item.answersUser!
            .any((element) => element.answerType == AnswerType.stopSurvey)) {
          onCancelCheck?.call();
          return true;
        }
        return true;
      } else if (item.type == SurveyType.freeText ||
          item.type == SurveyType.number ||
          item.type == SurveyType.dateTime) {
        if (item.answerContentUser == null ||
            item.answerContentUser!.trim().isEmpty) {
          AppDialog.showDialogCenter(
            context,
            message: SurveyUtils.getQuestionErrorMessage(questionNumber ?? ''),
            status: DialogStatus.error,
          );
          onCancelCheck?.call();
          return false;
        }
        return true;
      } else if (item.type == SurveyType.media) {
        if (item.files == null || item.files!.isEmpty) {
          AppDialog.showDialogCenter(
            context,
            message: SurveyUtils.getQuestionErrorMessage(questionNumber ?? ''),
            status: DialogStatus.error,
          );
          onCancelCheck?.call();
          return false;
        }
        return true;
      }
    }

    return true;
  }

  /// Validate câu hỏi với logic xử lý parent-child relationship
  bool _validateQuestion({
    required QuestionCampaignEntity item,
    required String questionNumber,
    required Map<String, dynamic> questionData,
    required VoidCallback onCancelCheck,
  }) {
    // Nếu câu hỏi không bắt buộc, không cần validate
    if (!(item.isRequired ?? false)) {
      return true;
    }

    // Kiểm tra xem có phải câu hỏi con không
    final isSubQuestion = questionData['isSubQuestion'] == true;

    if (isSubQuestion) {
      // Đây là câu hỏi con bắt buộc
      final directParent =
          questionData['directParent'] as QuestionCampaignEntity?;
      final rootParent =
          questionData['parentQuestion'] as QuestionCampaignEntity;
      final parentToCheck = directParent ?? rootParent;

      // Kiểm tra parent có được trả lời không
      bool parentHasAnswer = SurveyUtils.hasAnswer(parentToCheck);

      // Nếu parent không được trả lời, không validate câu hỏi con
      // (vì câu hỏi con chỉ hiển thị khi parent được trả lời)
      if (!parentHasAnswer) {
        return true; // Bỏ qua validation
      }
    }

    // Lấy data từ state thay vì từ item để đảm bảo data mới nhất
    final state = ref.read(customerSurveyFormProvider);
    final originalIndex = questionData['originalIndex'] as int;
    final subIndex = questionData['subIndex'] as int?;

    QuestionCampaignEntity currentQuestion;
    if (subIndex != null) {
      currentQuestion =
          state.listQuestion![originalIndex].listSubQuestion![subIndex];
    } else {
      currentQuestion = state.listQuestion![originalIndex];
    }

    return checkQuestion(
      item: currentQuestion,
      questionNumber: questionNumber,
      onCancelCheck: onCancelCheck,
    );
  }
}
