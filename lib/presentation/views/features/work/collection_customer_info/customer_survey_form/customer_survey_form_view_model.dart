import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/requirement_repository.dart';
import 'package:vcc/data/repositories/resource_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/answer_question_body.dart';
import 'package:vcc/domain/entities/answer_campaign_entity.dart';
import 'package:vcc/domain/entities/question_campaign_entity.dart';
import 'package:vcc/domain/enums/answer_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/survey_type.dart';
import 'services/survey_business_logic.dart';

part 'customer_survey_form_state.dart';

final customerSurveyFormProvider = StateNotifierProvider.autoDispose<
    CustomerSurveyFormViewModel, CustomerSurveyFormState>(
  (ref) => CustomerSurveyFormViewModel(ref: ref),
);

class CustomerSurveyFormViewModel
    extends StateNotifier<CustomerSurveyFormState> {
  final Ref ref;

  CustomerSurveyFormViewModel({
    required this.ref,
  }) : super(const CustomerSurveyFormState());

  void getListSurvey({
    required String campaignId,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result =
          await appLocator<RequirementRepository>().getListQuestionOfCampaign(
        campaignId: campaignId,
      );

      await result?.when(
        success: (data) async {
          List<QuestionCampaignEntity> listData = data.data ?? [];

          // Xây dựng cấu trúc cây từ dữ liệu phẳng
          listData = SurveyBusinessLogic.buildQuestionTree(listData);

          for (int i = 0; i < listData.length; i++) {
            final question = listData[i];
            if (question.listSubQuestion != null) {
              for (int j = 0; j < question.listSubQuestion!.length; j++) {
                final sub = question.listSubQuestion![j];
                if (sub.listSubQuestion != null) {
                  for (int k = 0; k < sub.listSubQuestion!.length; k++) {
                    final subSub = sub.listSubQuestion![k];
                  }
                }
              }
            }
          }

          List<QuestionCampaignEntity> listDeleteQuestion = [];

          for (var i = 0; i < listData.length; i++) {
            if ((listData[i].answers ?? []).isNotEmpty) {
              for (var j = 0; j < listData[i].answers!.length; j++) {
                if ((listData[i].answers![j].triggerQuestionIds ?? [])
                        .isNotEmpty &&
                    listData[i].answers![j].answerType ==
                        AnswerType.subQuestion) {
                  for (var k = 0;
                      k < listData[i].answers![j].triggerQuestionIds!.length;
                      k++) {
                    for (var parent = 0; parent < listData.length; parent++) {
                      if (listData[i].answers![j].triggerQuestionIds![k] ==
                          listData[parent].id) {
                        List<QuestionCampaignEntity> listSubQuestion =
                            listData[i].listSubQuestion ?? [];

                        listSubQuestion.add(listData[parent]);

                        listData[i].listSubQuestion = listSubQuestion;

                        listDeleteQuestion.add(listData[parent]);
                      }
                    }
                  }
                }
              }
            }
          }

          for (var i = 0; i < listDeleteQuestion.length; i++) {
            listData.remove(listDeleteQuestion[i]);
          }

          state = state.copyWith(
            listQuestion: listData,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void onChooseAnswer({
    required int parIndex,
    int? subIndex,
    required AnswerCampaignEntity answer,
  }) {
    final listQuestion = state.listQuestion!;

    if (subIndex != null) {
      // Câu hỏi con thay đổi answer
      final parentQuestion = listQuestion[parIndex];
      final subQuestion = parentQuestion.listSubQuestion![subIndex];

      // Clear answers của các câu hỏi con của câu hỏi này trước khi set answer mới
      SurveyBusinessLogic.clearSubQuestionAnswers(subQuestion);

      subQuestion.answerUser = answer;
    } else {
      // Câu hỏi cha thay đổi answer
      final parentQuestion = listQuestion[parIndex];

      // Clear answers của tất cả câu hỏi con trước khi set answer mới
      SurveyBusinessLogic.clearSubQuestionAnswers(parentQuestion);

      parentQuestion.answerUser = answer;
    }

    // Xử lý logic hiển thị câu hỏi dựa trên triggerQuestionIds và answerType
    _handleQuestionVisibility(answer, listQuestion, parIndex);

    state = state.copyWith(
      listQuestion: listQuestion,
    );
  }

  void onChooseMultiAnswer({
    required int parIndex,
    int? subIndex,
    required AnswerCampaignEntity answer,
  }) {
    final listQuestion = state.listQuestion!;

    if (subIndex != null) {
      // Câu hỏi con checkbox
      final parentQuestion = listQuestion[parIndex];
      final subQuestion = parentQuestion.listSubQuestion![subIndex];

      if ((subQuestion.answersUser ?? []).isNotEmpty) {
        final listAnswer = subQuestion.answersUser!;
        final itemExist = listAnswer.where((e) => e.id == answer.id).toList();

        if (itemExist.isNotEmpty) {
          // Remove answer
          subQuestion.answersUser!.remove(answer);
          // Clear sub-questions của answer này
          SurveyBusinessLogic.clearSubQuestionsForAnswer(subQuestion, answer.id!);
        } else {
          // Add answer
          subQuestion.answersUser!.add(answer);
        }
      } else {
        subQuestion.answersUser = [answer];
      }
    } else {
      // Câu hỏi cha checkbox
      final parentQuestion = listQuestion[parIndex];

      if ((parentQuestion.answersUser ?? []).isNotEmpty) {
        final listAnswer = parentQuestion.answersUser!;
        final itemExist = listAnswer.where((e) => e.id == answer.id).toList();

        if (itemExist.isNotEmpty) {
          // Remove answer
          parentQuestion.answersUser!.remove(answer);
          // Clear sub-questions của answer này
          SurveyBusinessLogic.clearSubQuestionsForAnswer(parentQuestion, answer.id!);
        } else {
          // Add answer
          parentQuestion.answersUser!.add(answer);
        }
      } else {
        parentQuestion.answersUser = [answer];
      }
    }

    state = state.copyWith(
      listQuestion: listQuestion,
    );
  }

  void saveContent({
    required int parIndex,
    int? subIndex,
    required String content,
  }) {
    final listQuestion = state.listQuestion!;

    if (subIndex != null) {
      // Câu hỏi con thay đổi content
      final parentQuestion = listQuestion[parIndex];
      final subQuestion = parentQuestion.listSubQuestion![subIndex];

      // Nếu content thay đổi, clear sub-questions
      if (subQuestion.answerContentUser != content) {
        SurveyBusinessLogic.clearSubQuestionAnswers(subQuestion);
      }

      subQuestion.answerContentUser = content;
    } else {
      // Câu hỏi cha thay đổi content
      final parentQuestion = listQuestion[parIndex];

      // Nếu content thay đổi, clear sub-questions
      if (parentQuestion.answerContentUser != content) {
        SurveyBusinessLogic.clearSubQuestionAnswers(parentQuestion);
      }

      parentQuestion.answerContentUser = content;
    }

    state = state.copyWith(
      listQuestion: listQuestion,
    );
  }

  List<String> convertAnswer(
    QuestionCampaignEntity item,
  ) {
    List<String> answerContents = [];
    switch (item.type) {
      case SurveyType.radioButton:
        if (item.answerUser != null) {
          answerContents = [
            item.answerUser?.id ?? '',
          ];
        }
        break;

      case SurveyType.checkBox:
        if ((item.answersUser ?? []).isNotEmpty) {
          for (var e in item.answersUser!) {
            answerContents.add(e.id ?? '');
          }
        }
        break;

      case SurveyType.dateTime:
      case SurveyType.freeText:
      case SurveyType.number:
        answerContents = [item.answerContentUser ?? ''];
        break;

      case SurveyType.media:
        answerContents = item.files ?? [];
        break;

      default:
        break;
    }

    return answerContents;
  }

  Future<bool?> completeCollection({
    String? surveyCode,
  }) async {
    state = state.copyWith(
      sendStatus: LoadStatus.loading,
    );

    try {
      List<AnswerQuestionInfo> listAnswer = [];

      // Sử dụng flat list để xử lý tất cả câu hỏi (bao gồm nested)
      final flatQuestions = SurveyBusinessLogic.getFlatQuestionList(state.listQuestion ?? []);

      for (var questionData in flatQuestions) {
        final question = questionData.question;

        // Bỏ qua câu hỏi bị ẩn
        if (question.hideQuestion == true) {
          continue;
        }

        List<String> answerContents = convertAnswer(question);

        listAnswer.add(
          AnswerQuestionInfo(
            questionId: question.id,
            answerContents: answerContents,
          ),
        );
      }

      final body = AnswerQuestionBody(
        surveyCode: surveyCode,
        answerQuestions: listAnswer,
      );

      bool checkResult = false;

      final result =
          await appLocator<RequirementRepository>().completeCollection(
        body: body,
      );

      await result?.when(
        success: (data) async {
          checkResult = true;
          state = state.copyWith(
            sendStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            sendStatus: LoadStatus.failure,
          );
        },
      );
      return checkResult;
    } catch (error) {
      state = state.copyWith(
        sendStatus: LoadStatus.failure,
      );
      return null;
    }
  }

  void saveMediaByQuestionId({
    required String questionId,
    required List<String> images,
  }) {
    final listQuestion = List<QuestionCampaignEntity>.from(state.listQuestion ?? []);
    bool found = false;

    // Tìm question theo ID trong tất cả levels
    for (int i = 0; i < listQuestion.length && !found; i++) {
      final parentQuestion = listQuestion[i];

      // Kiểm tra parent question
      if (parentQuestion.id == questionId) {
        final currentFiles = List<String>.from(parentQuestion.files ?? []);
        if (images.isNotEmpty) {
          currentFiles.addAll(images);
          parentQuestion.files = currentFiles;
          found = true;
        }
      }

      // Kiểm tra sub-questions
      if (!found && parentQuestion.listSubQuestion != null) {
        for (int j = 0; j < parentQuestion.listSubQuestion!.length && !found; j++) {
          final subQuestion = parentQuestion.listSubQuestion![j];

          if (subQuestion.id == questionId) {
            final currentFiles = List<String>.from(subQuestion.files ?? []);
            if (images.isNotEmpty) {
              currentFiles.addAll(images);
              subQuestion.files = currentFiles;
              found = true;
            }
          }

          // Kiểm tra sub-sub-questions
          if (!found && subQuestion.listSubQuestion != null) {
            for (int k = 0; k < subQuestion.listSubQuestion!.length && !found; k++) {
              final subSubQuestion = subQuestion.listSubQuestion![k];

              if (subSubQuestion.id == questionId) {
                final currentFiles = List<String>.from(subSubQuestion.files ?? []);
                if (images.isNotEmpty) {
                  currentFiles.addAll(images);
                  subSubQuestion.files = currentFiles;
                  found = true;
                }
              }
            }
          }
        }
      }
    }
    state = state.copyWith(
      listQuestion: listQuestion,
    );
  }

  Future<List<String>> uploadImage(File file) async {
    state = state.copyWith(
      uploadImageStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<ResourceRepository>().uploadImage(
        file: file,
      );

      List<String> images = [];

      await result?.when(
        success: (data) async {
          images = data;

          state = state.copyWith(
            uploadImageStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            uploadImageStatus: LoadStatus.failure,
          );
        },
      );

      return images;
    } catch (error) {
      state = state.copyWith(
        uploadImageStatus: LoadStatus.failure,
      );
      return [];
    }
  }

  void deleteImageByQuestionId({
    required String questionId,
    required String image,
  }) {
    final listQuestion = List<QuestionCampaignEntity>.from(state.listQuestion ?? []);
    bool found = false;

    // Tìm question theo ID trong tất cả levels
    for (int i = 0; i < listQuestion.length && !found; i++) {
      final parentQuestion = listQuestion[i];

      // Kiểm tra parent question
      if (parentQuestion.id == questionId) {
        final currentFiles = List<String>.from(parentQuestion.files ?? []);
        currentFiles.remove(image);
        parentQuestion.files = currentFiles;
        found = true;
      }

      // Kiểm tra sub-questions
      if (!found && parentQuestion.listSubQuestion != null) {
        for (int j = 0; j < parentQuestion.listSubQuestion!.length && !found; j++) {
          final subQuestion = parentQuestion.listSubQuestion![j];

          if (subQuestion.id == questionId) {
            final currentFiles = List<String>.from(subQuestion.files ?? []);
            currentFiles.remove(image);
            subQuestion.files = currentFiles;
            found = true;
          }

          // Kiểm tra sub-sub-questions
          if (!found && subQuestion.listSubQuestion != null) {
            for (int k = 0; k < subQuestion.listSubQuestion!.length && !found; k++) {
              final subSubQuestion = subQuestion.listSubQuestion![k];

              if (subSubQuestion.id == questionId) {
                final currentFiles = List<String>.from(subSubQuestion.files ?? []);
                currentFiles.remove(image);
                subSubQuestion.files = currentFiles;
                found = true;
              }
            }
          }
        }
      }
    }

    state = state.copyWith(
      listQuestion: listQuestion,
    );
  }

  /// Xử lý logic hiển thị câu hỏi dựa trên triggerQuestionIds và answerType
  void _handleQuestionVisibility(
    AnswerCampaignEntity answer,
    List<QuestionCampaignEntity> listQuestion,
    int parIndex,
  ) {
    // Nếu là STOP_SURVEY, ẩn tất cả câu hỏi sau
    if (answer.answerType == AnswerType.stopSurvey) {
      for (var i = parIndex + 1; i < listQuestion.length; i++) {
        listQuestion[i].hideQuestion = true;
        _hideAllSubQuestions(listQuestion[i]);
      }
      return;
    }

    // Hiển thị lại tất cả câu hỏi sau (trừ khi có logic khác)
    for (var i = parIndex + 1; i < listQuestion.length; i++) {
      listQuestion[i].hideQuestion = false;
    }

    // Xử lý triggerQuestionIds - hiển thị câu hỏi con được trigger
    if (answer.triggerQuestionIds != null &&
        answer.triggerQuestionIds!.isNotEmpty) {
      _showTriggeredQuestions(answer.triggerQuestionIds!, listQuestion);
    }
  }

  /// Ẩn tất cả câu hỏi con của một câu hỏi
  void _hideAllSubQuestions(QuestionCampaignEntity question) {
    if (question.listSubQuestion != null) {
      for (var subQuestion in question.listSubQuestion!) {
        subQuestion.hideQuestion = true;
        _hideAllSubQuestions(subQuestion);
      }
    }
  }

  /// Hiển thị các câu hỏi được trigger
  void _showTriggeredQuestions(
    List<String> triggerQuestionIds,
    List<QuestionCampaignEntity> listQuestion,
  ) {
    Map<String, QuestionCampaignEntity> questionMap = {};
    _buildQuestionMap(listQuestion, questionMap);

    // Hiển thị các câu hỏi được trigger
    for (String questionId in triggerQuestionIds) {
      if (questionMap.containsKey(questionId)) {
        questionMap[questionId]!.hideQuestion = false;
      }
    }
  }

  /// Xây dựng map câu hỏi (bao gồm cả câu hỏi con) để tra cứu nhanh
  void _buildQuestionMap(
    List<QuestionCampaignEntity> questions,
    Map<String, QuestionCampaignEntity> questionMap,
  ) {
    for (var question in questions) {
      questionMap[question.id!] = question;
      if (question.listSubQuestion != null) {
        _buildQuestionMap(question.listSubQuestion!, questionMap);
      }
    }
  }
}
