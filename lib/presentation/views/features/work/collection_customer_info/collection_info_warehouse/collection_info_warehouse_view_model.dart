import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/requirement_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/entities/count_collection_entity.dart';
import 'package:vcc/domain/entities/filter_collection_entity.dart';
import 'package:vcc/domain/enums/collection_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/utils/log_utils.dart';

part 'collection_info_warehouse_state.dart';

final collectionInfoWarehouseProvider = StateNotifierProvider.autoDispose<
    CollectionInfoWarehouseViewModel, CollectionInfoWarehouseState>(
  (ref) => CollectionInfoWarehouseViewModel(ref: ref),
);

class CollectionInfoWarehouseViewModel
    extends StateNotifier<CollectionInfoWarehouseState> {
  final Ref ref;

  CollectionInfoWarehouseViewModel({
    required this.ref,
  }) : super(const CollectionInfoWarehouseState());

  void changeTabIndex(int page) {
    state = state.copyWith(
      tabIndex: page,
    );
  }

  void changeCollectionType(CollectionType type) {
    state = state.copyWith(
      collectionType: type,
    );

    getMyCollection();
  }

  void changeOffset(double offset) {
    state = state.copyWith(
      screenOffset: offset,
    );
  }

  void changeKeySearch(String keySearch) {
    final filter = state.filterInfo ?? FilterCollectionEntity();
    filter.keySearch = keySearch;

    state = state.copyWith(
      keySearch: keySearch,
      filterInfo: filter,
    );

    if (state.tabIndex == 0) {
      getMyCollection();
    } else {
      getAllCollection();
    }
    countCollectionStatus();
  }

  void changeFilterInfo({
    required FilterCollectionEntity filterInfo,
  }) {
    state = state.copyWith(
      filterInfo: filterInfo,
    );

    if (state.tabIndex == 0) {
      getMyCollection();
    } else {
      getAllCollection();
    }
  }

  void chooseCollection(CollectionInfoEntity collection) {
    var listCollection = state.listCollectionSelected ?? [];

    if (listCollection.isEmpty) {
      listCollection.add(collection);
    } else {
      final data = listCollection
          .where((element) => element.code == collection.code)
          .toList();

      if (data.isNotEmpty) {
        listCollection.remove(data.first);
      } else {
        listCollection.add(collection);
      }
    }

    state = state.copyWith(
      listCollectionSelected: listCollection,
    );
  }

  Future<bool> acceptCollection() async {
    try {
      final result =
          await appLocator<RequirementRepository>().receiveCollection(
        body: (state.listCollectionSelected ?? [])
            .map((e) => e.code ?? '')
            .toList(),
      );
      bool isCheck = false;

      await result?.when(
        success: (data) async {
          isCheck = true;
          state = state.copyWith(
            listCollectionSelected: [],
          );
          getAllCollection();
        },
        error: (err) async {
          LogUtils.e("$err");
          state = state.copyWith(
            message: err.message,
          );
        },
      );

      return isCheck;
    } catch (error) {
      LogUtils.e("$error");
      state = state.copyWith(
        message: "Đã có lỗi xảy ra, vui lòng thử lại sau",
      );
      return false;
    }
  }

  void countCollectionStatus() async {
    try {
      final result =
          await appLocator<RequirementRepository>().countCollectionStatus(
        keySearch: state.filterInfo?.keySearch,
        campaignId: state.filterInfo?.campaign?.id,
        status:
            state.filterInfo?.collectionStatus?.map((e) => e.keyword).toList(),
        customerType: state.filterInfo?.customerType?.keyToServer,
        districtCodes:
            state.filterInfo?.districtCodes?.map((e) => e.code ?? '').toList(),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            countCollection: data,
          );
        },
        error: (err) {
          LogUtils.e("$err");
        },
      );
    } catch (error) {
      LogUtils.e("$error");
    }
  }

  void getMyCollection() async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result =
          await appLocator<RequirementRepository>().getListCollection(
        keySearch: state.filterInfo?.keySearch,
        campaignId: state.filterInfo?.campaign?.id,
        status:
            state.filterInfo?.collectionStatus?.map((e) => e.keyword).toList(),
        customerType: state.filterInfo?.customerType?.keyToServer,
        districtCodes:
            state.filterInfo?.districtCodes?.map((e) => e.code ?? '').toList(),
        assignment: state.collectionType.keyToServer,
        isGetAll: false,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listMyCollection: data.data,
            mineTotalResult: data.total,
            loadStatus: LoadStatus.success,
          );
          countCollectionStatus();
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void fetchMyCollection() async {
    if (state.listMyCollection!.length >= state.mineTotalResult) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final result =
          await appLocator<RequirementRepository>().getListCollection(
        keySearch: state.filterInfo?.keySearch,
        campaignId: state.filterInfo?.campaign?.id,
        status:
            state.filterInfo?.collectionStatus?.map((e) => e.keyword).toList(),
        customerType: state.filterInfo?.customerType?.keyToServer,
        districtCodes:
            state.filterInfo?.districtCodes?.map((e) => e.code ?? '').toList(),
        page: state.mineCurrentPage + 1,
        isGetAll: false,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listMyCollection: state.listMyCollection! + (data.data ?? []),
            mineTotalResult: data.total,
            mineCurrentPage: state.mineCurrentPage + 1,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void getAllCollection() async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result =
          await appLocator<RequirementRepository>().getListCollection(
        keySearch: state.filterInfo?.keySearch,
        campaignId: state.filterInfo?.campaign?.id,
        status:
            state.filterInfo?.collectionStatus?.map((e) => e.keyword).toList(),
        customerType: state.filterInfo?.customerType?.keyToServer,
        districtCodes:
            state.filterInfo?.districtCodes?.map((e) => e.code ?? '').toList(),
        isGetAll: true,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listAllCollection: data.data,
            allTotalResult: data.total,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void fetchAllCollection() async {
    if (state.listAllCollection!.length >= state.allTotalResult) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final result =
          await appLocator<RequirementRepository>().getListCollection(
        keySearch: state.filterInfo?.keySearch,
        campaignId: state.filterInfo?.campaign?.id,
        status:
            state.filterInfo?.collectionStatus?.map((e) => e.keyword).toList(),
        customerType: state.filterInfo?.customerType?.keyToServer,
        districtCodes:
            state.filterInfo?.districtCodes?.map((e) => e.code ?? '').toList(),
        page: state.allCurrentPage + 1,
        isGetAll: true,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listAllCollection: state.listAllCollection! + (data.data ?? []),
            allTotalResult: data.total,
            allCurrentPage: state.allCurrentPage + 1,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }
}
