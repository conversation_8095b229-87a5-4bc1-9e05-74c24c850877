import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/domain/entities/filter_collection_entity.dart';
import 'package:vcc/domain/enums/collection_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/filer_collection/filter_collection_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/collection_info_warehouse/all_collection/all_collection_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/collection_info_warehouse/collection_info_warehouse_view_model.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/collection_info_warehouse/my_collection/my_collection_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/tab_bar_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/deBouncer.dart';

class CollectionInfoWarehousePage extends StatefulHookConsumerWidget {
  const CollectionInfoWarehousePage({
    super.key,
  });

  @override
  ConsumerState<CollectionInfoWarehousePage> createState() =>
      _CollectionInfoWarehousePageState();
}

class _CollectionInfoWarehousePageState
    extends ConsumerState<CollectionInfoWarehousePage>
    with TickerProviderStateMixin {
  List<String> listPage = [
    "Thu thập của tôi",
    "Kho thu thập",
  ];

  late TabController tabController;
  late TextEditingController searchController;
  late Debounce<String> deBouncer;

  @override
  void initState() {
    searchController = TextEditingController();
    tabController = TabController(
      length: listPage.length,
      initialIndex: 0,
      vsync: this,
    );
    deBouncer = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref
            .read(collectionInfoWarehouseProvider.notifier)
            .changeKeySearch(value ?? '');
      },
    );
    Future(() {
      ref
          .read(collectionInfoWarehouseProvider.notifier)
          .countCollectionStatus();
      ref.read(collectionInfoWarehouseProvider.notifier).getMyCollection();
    });
    _changeTabController();
    super.initState();
  }

  _changeTabController() {
    tabController.addListener(() {
      if (tabController.indexIsChanging) {
        ref
            .watch(collectionInfoWarehouseProvider.notifier)
            .changeFilterInfo(filterInfo: FilterCollectionEntity());
        if (tabController.index == 0) {
          ref.read(collectionInfoWarehouseProvider.notifier).getMyCollection();
        }
        if (tabController.index == 1) {
          ref.read(collectionInfoWarehouseProvider.notifier).getAllCollection();
        }
      }
    });
  }

  Future<void> onRefresh() async {
    if (ref.watch(collectionInfoWarehouseProvider).tabIndex == 0) {
      ref.read(collectionInfoWarehouseProvider.notifier).getMyCollection();
      ref
          .read(collectionInfoWarehouseProvider.notifier)
          .countCollectionStatus();
    } else {
      ref.read(collectionInfoWarehouseProvider.notifier).getAllCollection();
    }
  }

  @override
  void dispose() {
    tabController.dispose();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(collectionInfoWarehouseProvider);

    return LayoutPage(
      backgroundColor: BaseColors.backgroundWhite,
      appbar: const AppBarCustom(
        title: "Thu thập TTKH",
      ),
      floatingActionButton: Visibility(
        visible: state.tabIndex == 0,
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: InkWellWidget(
              onTap: () async {
                await context.push(RouterPaths.createCustomerInfo);
                ref
                    .read(collectionInfoWarehouseProvider.notifier)
                    .getMyCollection();
              },
              child: Container(
                decoration: BoxDecoration(
                  color: BaseColors.primary,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      offset: const Offset(2, 4),
                      blurRadius: 10,
                      spreadRadius: 0,
                      color: BaseColors.backgroundBlack.withOpacity(0.2),
                    ),
                  ],
                ),
                padding: EdgeInsets.symmetric(
                  horizontal: state.screenOffset > 200 ? 12 : 16,
                  vertical: 12,
                ),
                child: state.screenOffset > 200
                    ? MyAssets.icons.iconPlusAdd.svg()
                    : Row(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: <Widget>[
                          MyAssets.icons.iconPlusAdd.svg(
                            height: 24,
                            width: 24,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            "Tạo thu thập",
                            style: UITextStyle.body1Medium.copyWith(
                              color: BaseColors.backgroundWhite,
                            ),
                          ),
                        ],
                      ),
              ),
            ),
          ),
        ),
      ),
      bottomAction: Visibility(
        visible: state.tabIndex == 1 &&
            (state.listCollectionSelected ?? []).isNotEmpty,
        child: Container(
          decoration: BoxDecoration(
            color: BaseColors.backgroundWhite,
            boxShadow: AppBoxShadows.shadowNormal,
          ),
          child: SafeArea(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              child: BaseButton(
                text: "Tiếp nhận",
                onTap: () async {
                  final result = await ref
                      .read(collectionInfoWarehouseProvider.notifier)
                      .acceptCollection();

                  if (!context.mounted) return;
                  if (result) {
                    AppDialog.showDialogCenter(
                      context,
                      message: "Tiếp nhận thu thập thành công",
                      status: DialogStatus.success,
                    );
                  }
                },
              ),
            ),
          ),
        ),
      ),
      body: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TabBarWidget(
            tabItems: listPage,
            tabController: tabController,
            onTap: (index) {
              if (state.tabIndex != index) {
                ref
                    .read(collectionInfoWarehouseProvider.notifier)
                    .changeTabIndex(index);
              }
            },
          ),
          Expanded(
            child: RefreshIndicatorWidget(
              onRefresh: onRefresh,
              child: Column(
                children: <Widget>[
                  Container(
                    color: BaseColors.backgroundWhite,
                    padding: const EdgeInsets.fromLTRB(16, 8, 12, 8),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: SearchTextFieldWidget(
                            controller: searchController,
                            hintText: "Mã thu thập, tên khách hàng",
                            onChanged: (value) {
                              deBouncer.value = value;
                            },
                          ),
                        ),
                        const SizedBox(width: 8),
                        InkWellWidget(
                          onTap: () async {
                            final result =
                                await AppBottomSheet.showNormalBottomSheet(
                              context,
                              title: "Bộ lọc",
                              height: MediaQuery.of(context).size.height * 0.95,
                              child: FilterCollectionView(
                                filterInfo: state.filterInfo,
                                isMyCollection: state.tabIndex == 0,
                                refreshData: onRefresh,
                              ),
                            );

                            if (result is FilterCollectionEntity) {
                              ref
                                  .read(
                                      collectionInfoWarehouseProvider.notifier)
                                  .changeFilterInfo(filterInfo: result);
                            }
                          },
                          child: Padding(
                            padding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 4,
                            ),
                            child: (state.filterInfo?.isFilter ?? false)
                                ? Row(
                                    children: [
                                      MyAssets.icons.iconUsedFilter.svg(),
                                      Text(
                                        'Lọc',
                                        style:
                                            UITextStyle.caption1Medium.copyWith(
                                          color: BaseColors.primary,
                                        ),
                                      ),
                                    ],
                                  )
                                : Row(
                                    children: [
                                      MyAssets.icons.filter.svg(),
                                      Text(
                                        'Lọc',
                                        style:
                                            UITextStyle.caption1Medium.copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                    ],
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (state.tabIndex == 0) ...[
                    Container(
                      color: BaseColors.backgroundWhite,
                      padding: const EdgeInsets.symmetric(
                        vertical: 8,
                      ),
                      child: SizedBox(
                        height: 32,
                        child: ListView.separated(
                          itemCount: CollectionType.values.length,
                          scrollDirection: Axis.horizontal,
                          padding: const EdgeInsets.symmetric(horizontal: 16),
                          physics: const ClampingScrollPhysics(),
                          separatorBuilder: (_, __) => const SizedBox(width: 8),
                          itemBuilder: (context, index) {
                            final item = CollectionType.values[index];

                            return InkWellWidget(
                              borderRadius: BorderRadius.circular(100),
                              onTap: () {
                                ref
                                    .read(collectionInfoWarehouseProvider
                                        .notifier)
                                    .changeCollectionType(item);
                              },
                              child: Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 12,
                                  vertical: 6,
                                ),
                                decoration: BoxDecoration(
                                  color: state.collectionType == item
                                      ? BaseColors.primarySurface
                                      : BaseColors.backgroundGray1,
                                  borderRadius: BorderRadius.circular(100),
                                ),
                                child: Row(
                                  children: <Widget>[
                                    Text(
                                      item.display,
                                      style: UITextStyle.body2Regular.copyWith(
                                        color: state.collectionType == item
                                            ? BaseColors.primary
                                            : BaseColors.textBody,
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    Container(
                                      height: 16,
                                      width: 16,
                                      decoration: BoxDecoration(
                                        borderRadius: BorderRadius.circular(8),
                                        color: state.collectionType == item
                                            ? BaseColors.primary
                                            : BaseColors.secondary,
                                      ),
                                      child: Center(
                                        child: Text(
                                          "${item.countStatus(state.countCollection)}",
                                          style: UITextStyle.caption1Medium
                                              .copyWith(
                                            color: BaseColors.backgroundWhite,
                                          ),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                    ),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                  ],
                  Expanded(
                    child: state.tabIndex == 0
                        ? const MyCollectionPage()
                        : const AllCollectionPage(),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
