import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/address_info.dart';
import 'package:vcc/domain/entities/campaign_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/enums/gender_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/create_customer_info/create_customer_info_view_model.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/create_customer_info_success/create_customer_info_success_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/select_campaign/select_campaign_page.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/cus360_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/input_formatter_utils.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class CreateCustomerInfoArguments {
  final UserType? userType;
  final String? customerPhone;
  final String? companyTaxCode;

  CreateCustomerInfoArguments({
    this.userType,
    this.customerPhone,
    this.companyTaxCode,
  });
}

class CreateCustomerInfoPage extends StatefulHookConsumerWidget {
  final CreateCustomerInfoArguments? arguments;

  const CreateCustomerInfoPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<CreateCustomerInfoPage> createState() =>
      _CreateCustomerInfoState();
}

class _CreateCustomerInfoState extends ConsumerState<CreateCustomerInfoPage> {
  late TextEditingController userPhoneNumberController;
  late TextEditingController userNameController;
  late TextEditingController taxCompanyController;
  late TextEditingController companyNameController;
  late TextEditingController companyUserPhoneNumberController;
  late TextEditingController noteController;

  final formUserKey = GlobalKey<FormState>();
  final formCompanyKey = GlobalKey<FormState>();

  final FocusNode phoneNumberFocus = FocusNode();
  final FocusNode taxFocusNode = FocusNode();

  @override
  void initState() {
    userPhoneNumberController = TextEditingController();
    userNameController = TextEditingController();

    taxCompanyController = TextEditingController();
    companyNameController = TextEditingController();
    companyUserPhoneNumberController = TextEditingController();

    noteController = TextEditingController();

    if (widget.arguments != null) {
      Future(() {
        ref.read(createCustomerInfoProvider.notifier).changeUserType(
              widget.arguments?.userType ?? UserType.personal,
            );
        userPhoneNumberController.text = widget.arguments?.customerPhone ?? '';
        checkStaff(
          context: context,
          phoneNumber: widget.arguments?.customerPhone,
        );
      });
    }
    super.initState();
  }

  @override
  void dispose() {
    userPhoneNumberController.dispose();
    userNameController.dispose();
    taxCompanyController.dispose();
    companyNameController.dispose();
    companyUserPhoneNumberController.dispose();
    noteController.dispose();
    taxFocusNode.dispose();
    phoneNumberFocus.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Tạo thu thập thông tin",
      ),
      bottomAction: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: BaseButton(
            text: "Lưu",
            onTap: () async {
              onSaveInfo();
            },
          ),
        ),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: InkWell(
              highlightColor: Colors.transparent,
              splashColor: Colors.transparent,
              onTap: () {
                FocusScope.of(context).requestFocus(FocusNode());
              },
              child: Container(
                color: BaseColors.backgroundWhite,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildCustomerInfo(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    _buildDemandForm(),
                  ],
                ),
              ),
            ),
          ),
          if (ref.watch(createCustomerInfoProvider).createCollectionStatus ==
              LoadStatus.loading) ...[
            const Center(
              child: LoadingIndicatorWidget(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCustomerInfo() {
    var state = ref.watch(createCustomerInfoProvider);
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            "Thông tin khách hàng",
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loại khách hàng ',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: <Widget>[
              Expanded(
                child: RadioWidget<UserType>(
                  value: UserType.personal,
                  groupValue: state.userType,
                  onChanged: (value) {
                    ref
                        .read(createCustomerInfoProvider.notifier)
                        .changeUserType(value);
                  },
                  displayWidget: (context, item) {
                    return _displayUserText(item);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<UserType>(
                  value: UserType.company,
                  groupValue: state.userType,
                  onChanged: (value) {
                    ref
                        .read(createCustomerInfoProvider.notifier)
                        .changeUserType(value);
                  },
                  displayWidget: (context, item) {
                    return _displayUserText(item);
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          state.userType == UserType.personal
              ? _buildUserInfo()
              : _buildCompanyInfo(),
        ],
      ),
    );
  }

  Widget _buildDemandForm() {
    var state = ref.watch(createCustomerInfoProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            "Nhu cầu thu thập thông tin",
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
          const SizedBox(height: 16),
          DropdownWidget(
            labelText: "Chiến dịch",
            isRequired: true,
            content: state.campaignSelected?.name ?? '',
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            onTap: () async {
              final result = await context.push(
                RouterPaths.selectCampaign,
                extra: SelectCampaignArguments(
                  campaign: state.campaignSelected,
                  campaignType: state.campaignType,
                ),
              );

              if (result is CampaignEntity) {
                ref
                    .read(createCustomerInfoProvider.notifier)
                    .selectCampaign(result);
              }
            },
          ),
          const SizedBox(height: 16),
          DropdownWidget(
            labelText: "Ngày thực hiện",
            isRequired: true,
            content: state.userType == UserType.personal
                ? state.userTimeSetup?.display()
                : state.companyTimeSetup?.display(),
            suffix: MyAssets.icons.iconCalendarS24.svg(),
            onTap: () async {
              openDatetimePicker();
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget.area(
            controller: noteController,
            maxLines: 4,
            maxLength: 2000,
            hintText: "Ghi chú",
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                RegExp(
                  Patterns.note,
                  multiLine: true,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    var state = ref.watch(createCustomerInfoProvider);

    return Form(
      key: formUserKey,
      autovalidateMode: state.autoValidateUserForm,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            controller: userPhoneNumberController,
            focusNode: phoneNumberFocus,
            isRequired: true,
            labelText: "Số điện thoại",
            maxLength: 11,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            suffix: !state.turnOnEditUserInfo
                ? Cus360Widget(
                    customerType: UserType.personal.keyToServer,
                    customerPhone: userPhoneNumberController.text,
                    customerId: state.customerInfo?.customerId,
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                if (userPhoneNumberController.text.validatePhone()) {
                  checkStaff(
                    context: context,
                    phoneNumber: userPhoneNumberController.text,
                  );
                }
              }
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: userNameController,
            isRequired: true,
            labelText: "Họ và tên",
            enabled: state.turnOnEditUserInfo,
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
              ),
            ],
            validator: (value) {
              return ValidateUtils.onValidateUserName(value);
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                userNameController.text = StringUtils.capitalizeEachWord(
                  userNameController.text,
                );
              }
            },
          ),
          const SizedBox(height: 16),
          DropdownWidget(
            labelText: "Địa chỉ",
            isRequired: true,
            // enabled: state.turnOnEditUserInfo,
            content: state.customerAddress?.address,
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            validator: (value) {
              return ValidateUtils.onValidateAddress(value);
            },
            onTap: () {
              onSelectAddress();
            },
          ),
          const SizedBox(height: 16),
          Text(
            'Giới tính',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: <Widget>[
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.male,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(createCustomerInfoProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.female,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(createCustomerInfoProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.different,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(createCustomerInfoProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyInfo() {
    var state = ref.watch(createCustomerInfoProvider);
    const key = ValueKey("company");

    return Form(
      key: formCompanyKey,
      autovalidateMode: state.autoValidateCompanyForm,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            controller: taxCompanyController,
            focusNode: taxFocusNode,
            isRequired: true,
            labelText: "Mã số thuế doanh nghiệp",
            maxLength: 14,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            suffix: !state.turnOnEditCompanyInfo
                ? Cus360Widget(
                    customerType: UserType.company.keyToServer,
                    taxCode: taxCompanyController.text,
                    customerId: state.companyInfo?.customerId,
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidateTax(value);
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                getCompanyInfo360(
                  taxCode: taxCompanyController.text,
                );
              }
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyNameController,
            labelText: "Tên doanh nghiệp",
            isRequired: true,
            enabled: state.turnOnEditCompanyInfo,
            textInputAction: TextInputAction.done,
            inputFormatters: [
              SpaceInputFormatter(),
            ],
            validator: (value) {
              return ValidateUtils.onValidateCompanyName(value);
            },
          ),
          const SizedBox(height: 16),
          DropdownWidget(
            labelText: "Địa chỉ doanh nghiệp",
            isRequired: true,
            // enabled: state.turnOnEditCompanyInfo,
            content: state.companyAddress?.getFullAddress,
            validator: (value) {
              return ValidateUtils.onValidateAddress(value);
            },
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            onTap: () {
              onSelectAddress(isCompany: true);
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyUserPhoneNumberController,
            isRequired: true,
            labelText: "Số điện thoại người liên hệ",
            textInputAction: TextInputAction.done,
            keyboardType: TextInputType.phone,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
          ),
        ],
      ),
    );
  }

  Widget _displayGenderText(GenderType gender) {
    return Text(
      gender.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _displayUserText(UserType user) {
    return Text(
      user.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  void onSelectAddress({
    bool isCompany = false,
  }) async {
    var state = ref.watch(createCustomerInfoProvider);
    AddressEntity? address;
    AddressInfo? addressInfo =
        isCompany ? state.companyAddress : state.customerAddress;

    if (addressInfo != null) {
      address = AddressEntity(
        addressDetail: addressInfo.addressDetail,
        ward: addressInfo.ward,
        district: addressInfo.district,
        province: addressInfo.province,
        isGetFromLocal: addressInfo.isGetFromLocal,
      );
    }

    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) {
          ref.read(createCustomerInfoProvider.notifier).selectAddress(
                address.convertToAddressInfo,
              );
        },
      ),
    );
  }

  void openDatetimePicker() {
    var dob = DateTime.now();
    CustomBottomPicker.date(
      height: MediaQuery.of(context).size.height * 0.40,
      title: "Chọn thời gian",
      buttonText: "Xác nhận",
      initialDateTime: dob,
      minDateTime: dob.subtract(const Duration(hours: 1)),
      titleStyle: UITextStyle.body1SemiBold,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      dateOrder: DatePickerDateOrder.dmy,
      onSubmit: (date) {
        ref.read(createCustomerInfoProvider.notifier).setUpTime(date);
      },
    ).show(context);
  }

  void getCompanyInfo360({
    String? taxCode,
  }) async {
    if ((taxCode ?? '').validateTax()) {
      final customerInfo =
          await ref.read(createCustomerInfoProvider.notifier).getCompanyInfo360(
                taxCode: taxCode,
              );

      if (customerInfo != null) {
        companyNameController.text = customerInfo.fullName ?? '';
      } else {
        companyNameController.text = "";
        companyUserPhoneNumberController.text = "";
      }
    } else {
      ref.read(createCustomerInfoProvider.notifier).enableEditCompanyForm();
      companyNameController.text = "";
      companyUserPhoneNumberController.text = "";
    }
  }

  void checkStaff({
    required BuildContext context,
    String? phoneNumber,
  }) async {
    final checkStaff =
        await ref.read(createCustomerInfoProvider.notifier).checkInternalStaff(
              phoneNumber: phoneNumber,
            );

    if (checkStaff ?? false) {
      if (!context.mounted) return;

      AppDialog.showDialogConfirm(
        context,
        title: "Xác nhận",
        message:
            "Số điện thoại của nhân viên nội bộ, bạn có chắc chắn tạo thu thập TTKH không?",
        buttonNameConfirm: "Tạo thu thập",
        onConfirmAction: () {
          phoneNumberFocus.unfocus();
          getCustomerInfo360(
            phoneNumber: phoneNumber,
            isInternalStaff: true,
          );
        },
        onCancelAction: () {
          userPhoneNumberController.text = "";
          phoneNumberFocus.unfocus();
        },
      );
    } else {
      getCustomerInfo360(
        phoneNumber: phoneNumber,
      );
    }
  }

  void getCustomerInfo360({
    String? phoneNumber,
    bool? isInternalStaff,
  }) async {
    if ((phoneNumber ?? '').validatePhone()) {
      final customerInfo = await ref
          .read(createCustomerInfoProvider.notifier)
          .getCustomerInfo360(
            phoneNumber: phoneNumber,
            isInternalStaff: isInternalStaff,
          );

      if (customerInfo != null) {
        userNameController.text = customerInfo.fullName ?? '';
      } else {
        userNameController.text = "";
      }
    } else {
      userNameController.text = "";
      ref.read(createCustomerInfoProvider.notifier).enableEditUserForm();
    }
  }

  void onSaveInfo() async {
    bool isPersonalType =
        ref.watch(createCustomerInfoProvider).userType == UserType.personal;

    if (isPersonalType) {
      final form = formUserKey.currentState;

      if (form!.validate()) {
        form.save();
        onSendInfo(context);
      } else {
        ref.read(createCustomerInfoProvider.notifier).onValidateUserForm();
      }
    } else {
      final form = formCompanyKey.currentState;

      if (form!.validate()) {
        form.save();
        onSendInfo(context);
      } else {
        ref.read(createCustomerInfoProvider.notifier).onValidateCompanyForm();
      }
    }
  }

  void onSendInfo(BuildContext context) async {
    final result = await ref
        .read(createCustomerInfoProvider.notifier)
        .createCustomerInfoCollection(
          customerName: userNameController.text,
          customerPhone: userPhoneNumberController.text,
          companyCustomerPhone: companyUserPhoneNumberController.text,
          companyTaxCode: taxCompanyController.text,
          companyName: companyNameController.text,
          noteContent: noteController.text,
        );

    if (result != null) {
      if (!context.mounted) return;
      context.push(
        RouterPaths.createCustomerInfoSuccess,
        extra: CreateCustomerInfoSuccessArguments(
          collectionInfo: result,
        ),
      );
    }
  }
}
