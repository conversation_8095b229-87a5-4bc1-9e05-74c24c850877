import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/work_schedule_enum.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_work_schedule_view/filter_work_schedule_view.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_page.dart';
import 'package:vcc/presentation/views/features/order/list_order/list_order_page.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/widget/aio_contract_widget.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/widget/complain_widget.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/widget/deployment_order_widget.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/widget/my_collection_widget.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/widget/warranty_widget.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/work_schedule_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/card_custom_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/utils/date_time.dart';

class WorkSchedulePage extends StatefulHookConsumerWidget {
  final ListOrderArguments? arguments;

  const WorkSchedulePage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<WorkSchedulePage> createState() => _WorkSchedulePageState();
}

class _WorkSchedulePageState extends ConsumerState<WorkSchedulePage> {
  CalendarFormat _calendarFormat = CalendarFormat.month;
  DateTime now = DateTime.now();

  @override
  void initState() {
    Future(() async {
      ref.read(workScheduleProvider.notifier).setFilterStatus();
      ref.read(workScheduleProvider.notifier).getEventDay();
      ref.read(workScheduleProvider.notifier).getWorkCount(
            startTime: now.startDay,
            endTime: now.endDay,
          );
      ref.read(workScheduleProvider.notifier).getOrders();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(workScheduleProvider);
    final rf = ref.read(workScheduleProvider.notifier);
    return LayoutPage(
      backgroundColor: BaseColors.backgroundGray,
      appbar: AppBarCustom(
        centerTitle: true,
        title: displayText,
        actionWidget: [
          InkWellWidget(
            onTap: () => _bottomSheetFilterByDate(),
            child: Padding(
              padding: const EdgeInsets.only(
                right: 16.0,
              ),
              child: state.isFilter
                  ? MyAssets.icons.iconFilterWhiteS24.svg()
                  : MyAssets.icons.iconFilterBorderWhiteS24.svg(),
            ),
          ),
        ],
      ),
      body: Stack(
        children: [
          Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              CardCustomWidget(
                padding: const EdgeInsets.symmetric(
                  vertical: 20,
                ),
                borderRadius: const BorderRadius.vertical(
                  bottom: Radius.circular(12),
                ),
                titleWidget: TableCalendar(
                  calendarStyle: CalendarStyle(
                    rangeHighlightColor: BaseColors.primary.withOpacity(0.1),
                    cellMargin: const EdgeInsets.symmetric(
                      vertical: 10,
                    ),
                  ),
                  startingDayOfWeek: StartingDayOfWeek.monday,
                  daysOfWeekStyle: DaysOfWeekStyle(
                    dowTextFormatter: (date, locale) => date.dowFormatter(date),
                    weekdayStyle: BaseStyle.captionLarge.copyWith(
                      color: BaseColors.textSubtitle,
                    ),
                    weekendStyle: BaseStyle.captionLarge.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                  onRangeSelected: (start, end, focusedDay) {
                    rf.changeIsRangeTime(
                      isRangeTime: focusedDay.isSameMonth(start!) ||
                          focusedDay.isSameMonth(end!),
                    );
                    rf.changeDate(start, end);
                    rf.updateFocusDay(focusedDay);
                  },
                  rangeSelectionMode: RangeSelectionMode.toggledOn,
                  rangeStartDay: state.startTime,
                  rangeEndDay: state.endTime,
                  firstDay: now.subtract(
                    const Duration(
                      days: 365,
                    ),
                  ),
                  lastDay: now.add(
                    const Duration(
                      days: 365,
                    ),
                  ),
                  focusedDay: state.focusedDay ?? now,
                  headerVisible: false,
                  selectedDayPredicate: (day) => isSameDay(
                    day,
                    state.selectedDay,
                  ),
                  calendarFormat: _calendarFormat,
                  availableCalendarFormats: const {
                    CalendarFormat.month: '',
                    CalendarFormat.week: '',
                  },
                  onFormatChanged: (format) {
                    setState(() {
                      _calendarFormat = format;
                    });
                    onChangeIsRangeTime();
                  },
                  onPageChanged: (newFocusedDay) {
                    rf.updateFocusDay(newFocusedDay);
                    rf.getEventDay(
                      eventDate: newFocusedDay,
                    );
                    onChangeIsRangeTime();
                  },
                  calendarBuilders: CalendarBuilders(
                    defaultBuilder: (context, day, _) {
                      final isOnlyOneDaySelected = state.startTime != null &&
                          state.endTime == null &&
                          isSameDay(state.startTime!, day);

                      if (isOnlyOneDaySelected) {
                        return _buildRoundedDay(
                          day,
                          backgroundColor: BaseColors.primary,
                          textColor: BaseColors.backgroundWhite,
                        );
                      }

                      return null;
                    },
                    rangeStartBuilder: (context, day, _) {
                      final isRange = state.endTime != null &&
                          !isSameDay(
                            state.startTime,
                            state.endTime,
                          );
                      if (!isRange) {
                        return _buildRoundedDay(
                          day,
                          backgroundColor: BaseColors.primary,
                          textColor: BaseColors.backgroundWhite,
                        );
                      }
                      return rangeStartBuilder(day);
                    },
                    rangeEndBuilder: (context, day, _) {
                      final isRange = state.endTime != null &&
                          !isSameDay(
                            state.startTime,
                            state.endTime,
                          );
                      if (!isRange) return null;
                      return rangeEndBuilder(day);
                    },
                    todayBuilder: (context, day, focusedDay) => todayBuilder(
                      day: day,
                    ),
                    markerBuilder: (context, date, events) => markBuilder(
                      date: date,
                    ),
                  ),
                  eventLoader: (day) {
                    return state.eventDays?[DateTime.utc(
                          day.year,
                          day.month,
                          day.day,
                        )] ??
                        [];
                  },
                  headerStyle: const HeaderStyle(
                    formatButtonVisible: false,
                    titleCentered: true,
                  ),
                ),
              ),
              _filterOrderByType(),
              Expanded(
                child: _buildOrderByType(),
              ),
            ],
          ),
          if (state.loadStatus == LoadStatus.loading) ...[
            const Center(
              child: LoadingIndicatorWidget(),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildRoundedDay(
    DateTime day, {
    required Color backgroundColor,
    required Color textColor,
  }) {
    return Container(
      width: 32,
      height: 32,
      margin: const EdgeInsets.only(
        bottom: 10,
      ),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(8),
      ),
      alignment: Alignment.center,
      child: Text(
        '${day.day}',
        style: TextStyle(
          color: textColor,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  void _bottomSheetFilterByDate() {
    final state = ref.watch(workScheduleProvider);
    final rf = ref.read(workScheduleProvider.notifier);
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Bộ lọc",
      height: MediaQuery.of(context).size.height * 0.8,
      child: FilterWorkScheduleViewSheet(
        startDate: state.startTime,
        endDate: state.endTime,
        onApply: (FilterWorkScheduleArgumentCallBack value) {
          rf.changeDate(
            value.startTime,
            value.endTime,
          );
          if (value.startTime == null) {
            rf.updateFilterStatus(false);
            return;
          }
          rf.updateFilterStatus(true);
        },
      ),
    );
  }

  Widget rangeStartBuilder(
    DateTime day,
  ) {
    return Container(
      width: 32,
      height: 32,
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: BaseColors.primary,
        borderRadius: const BorderRadius.horizontal(
          left: Radius.circular(8),
        ),
      ),
      alignment: Alignment.center,
      child: Text(
        '${day.day}',
        style: TextStyle(
          color: BaseColors.backgroundWhite,
        ),
      ),
    );
  }

  Widget rangeEndBuilder(
    DateTime day,
  ) {
    return Container(
      width: 32,
      height: 32,
      margin: const EdgeInsets.only(bottom: 10),
      decoration: BoxDecoration(
        color: BaseColors.primary,
        borderRadius: const BorderRadius.horizontal(
          right: Radius.circular(8),
        ),
      ),
      alignment: Alignment.center,
      child: Text(
        '${day.day}',
        style: TextStyle(
          color: BaseColors.backgroundWhite,
        ),
      ),
    );
  }

  Widget _buildOrderByType() {
    final state = ref.watch(workScheduleProvider);
    final rf = ref.read(workScheduleProvider.notifier);

    final workScheduleSelected = state.workScheduleSelected;

    switch (workScheduleSelected) {
      case WorkScheduleEnum.deploymentOrder:
        return DeploymentOrderWidget(
          onRefresh: () {
            rf.updateWorkSchedule();
          },
        );
      case WorkScheduleEnum.warrantyRequest:
        return WarrantyWidget(
          onRefresh: () {
            rf.updateWorkSchedule();
          },
        );
      case WorkScheduleEnum.customerInformationGather:
        return MyCollectionWidget(
          onRefresh: () {
            rf.updateWorkSchedule();
          },
        );
      case WorkScheduleEnum.bundleOrder:
        return AioContractWidget(
          arguments: AioContractArguments(),
          onRefresh: () {
            rf.updateWorkSchedule();
          },
        );
      default:
        return PerformComplainWidget(
          onRefresh: () {
            rf.updateWorkSchedule();
          },
        );
    }
  }

  Widget _filterOrderByType() {
    final state = ref.watch(workScheduleProvider);
    return Container(
      margin: const EdgeInsets.symmetric(
        vertical: 16,
      ),
      alignment: Alignment.center,
      height: 32,
      child: ListView.separated(
        itemCount: (state.dailyWork ?? []).length,
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(horizontal: 16),
        physics: const ClampingScrollPhysics(),
        shrinkWrap: true,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          final item = (state.dailyWork ?? [])[index];
          return InkWellWidget(
            borderRadius: BorderRadius.circular(100),
            onTap: () {
              ref.read(workScheduleProvider.notifier).changeWorkSchedule(
                    WorkScheduleEnumExtension.fromKey(
                      item.workTypeKey,
                    ),
                  );
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: state.workScheduleSelected ==
                        WorkScheduleEnumExtension.fromKey(
                          item.workTypeKey,
                        )
                    ? BaseColors.primarySurface
                    : BaseColors.backgroundGray1,
                borderRadius: BorderRadius.circular(100),
              ),
              child: Row(
                children: <Widget>[
                  Text(
                    item.title ?? '',
                    style: UITextStyle.body2Regular.copyWith(
                      color: state.workScheduleSelected ==
                              WorkScheduleEnumExtension.fromKey(
                                item.workTypeKey,
                              )
                          ? BaseColors.primary
                          : BaseColors.textBody,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Container(
                    height: 16,
                    padding: const EdgeInsets.symmetric(
                      horizontal: BaseSpacing.spacing1,
                    ),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: state.workScheduleSelected ==
                              WorkScheduleEnumExtension.fromKey(
                                item.workTypeKey,
                              )
                          ? BaseColors.primary
                          : BaseColors.secondary,
                    ),
                    child: Center(
                      child: Text(
                        "${item.orderCount ?? '0'}",
                        style: UITextStyle.caption1Medium.copyWith(
                          color: BaseColors.backgroundWhite,
                        ),
                      ),
                    ),
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget todayBuilder({
    required DateTime day,
  }) {
    return Container(
      width: 32,
      height: 32,
      margin: const EdgeInsets.only(
        bottom: 10,
      ),
      decoration: BoxDecoration(
        border: Border.all(
          color: BaseColors.primary,
          width: 1,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      alignment: Alignment.center,
      child: Text(
        '${day.day}',
        style: const TextStyle(
          color: Colors.black,
          fontWeight: FontWeight.w500,
        ),
      ),
    );
  }

  Widget markBuilder({required DateTime date}) {
    final state = ref.watch(workScheduleProvider);
    final hasEvent =
        state.eventDays?[DateTime.utc(date.year, date.month, date.day)] != null;
    if (hasEvent) {
      return Container(
        width: 6,
        height: 6,
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          color: BaseColors.primary,
        ),
      );
    }
    return const SizedBox();
  }

  void onChangeIsRangeTime() {
    final state = ref.watch(workScheduleProvider);
    final rf = ref.read(workScheduleProvider.notifier);
    if (_calendarFormat == CalendarFormat.week) {
      final weekStart = state.focusedDay!.subtract(
        Duration(days: state.focusedDay!.weekday - DateTime.monday),
      );
      final weekEnd = state.focusedDay!.add(
        Duration(days: DateTime.sunday - state.focusedDay!.weekday),
      );
      rf.changeIsRangeTime(
          isRangeTime: !(weekEnd.isBefore(state.startTime!) ||
              state.endTime!.isBefore(weekStart)));
    } else {
      final isCheckSameMonth = state.startTime != null &&
          state.endTime != null &&
          (state.focusedDay!.isSameMonth(state.startTime!) ||
              state.focusedDay!.isSameMonth(state.endTime!) ||
              state.focusedDay!
                  .isMonthBetween(state.startTime!, state.endTime!));
      rf.changeIsRangeTime(isRangeTime: isCheckSameMonth);
    }
  }

  String get displayText {
    final state = ref.watch(workScheduleProvider);
    DateTime focusedDay = state.focusedDay ?? now;
    return state.isRangeTime
        ? '${state.startTime?.display(
            format: DateTimeFormater.dateFormatVi,
          )} - ${state.endTime?.display(
            format: DateTimeFormater.dateFormatVi,
          )}'
        : _calendarFormat == CalendarFormat.week
            ? 'Tuần ${focusedDay.getWeekOfMonth} tháng ${focusedDay.month}'
            : 'Tháng ${focusedDay.month} năm ${focusedDay.year}';
  }
}
