import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/data/repositories/complain_repository.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/data/repositories/requirement_repository.dart';
import 'package:vcc/data/repositories/requirement_warranty_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/complain_body.dart';
import 'package:vcc/domain/body/list_order_body.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/entities/complain/complain_entity.dart';
import 'package:vcc/domain/entities/order/daily_work_entity.dart';
import 'package:vcc/domain/entities/order/order_info_short_entity.dart';
import 'package:vcc/domain/entities/warranty/warranty_claim_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_order_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_status_type.dart';
import 'package:vcc/domain/enums/collection_performed_by_me_enum.dart';
import 'package:vcc/domain/enums/collection_status.dart';
import 'package:vcc/domain/enums/complain/enum_complain_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/order_type_filter.dart';
import 'package:vcc/domain/enums/requirement_warranty/requirement_warranty_status_enum.dart';
import 'package:vcc/domain/enums/requirement_warranty/warranty_roles_enum.dart';
import 'package:vcc/domain/enums/work_schedule_enum.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/utils/date_time.dart';

part 'work_schedule_state.dart';

final workScheduleProvider =
    StateNotifierProvider.autoDispose<WorkScheduleViewModel, WorkScheduleState>(
  (ref) => WorkScheduleViewModel(ref: ref),
);

class WorkScheduleViewModel extends StateNotifier<WorkScheduleState> {
  final Ref ref;

  WorkScheduleViewModel({
    required this.ref,
  }) : super(WorkScheduleState());

  List<int>? complainStatus;
  List<String>? warrantyStatus;
  List<String>? deploymentOrderStatus;
  List<int>? packageOrderStatus;
  List<int>? packageOrderType;
  List<String>? collectCustomerInfo;
  List<String>? participantTypes;
  String? assignment;

  void changeWorkSchedule(WorkScheduleEnum item) {
    if (item == state.workScheduleSelected) {
      return;
    }
    state = state.copyWith(
      workScheduleSelected: item,
    );
    getOrderByType(item);
  }

  void setFilterStatus() {
    List<OrderTypeFilter> orderTypes = [];
    List<OrderType>? orderType = [];
    participantTypes = WarrantyRolesEnum.values
        .where((e) =>
            e == WarrantyRolesEnum.worker || e == WarrantyRolesEnum.manager)
        .map((e) => e.keyToServer)
        .toList();

    complainStatus = ComplainStatusEnum.values
        .where((e) =>
            e != ComplainStatusEnum.completed && e != ComplainStatusEnum.close)
        .map((e) => e.keyToServer)
        .toList();

    warrantyStatus = EnumRequirementWarrantyStatus.values
        .where((e) => e != EnumRequirementWarrantyStatus.completeWarranty)
        .map((e) => e.keyToServer)
        .toList();

    collectCustomerInfo = CollectionStatus.values
        .where(
            (e) => e != CollectionStatus.close && e != CollectionStatus.cancel)
        .map((e) => e.keyword)
        .toList();

    List<AioContractStatusType> aioContractStatus = [
      AioContractStatusType.notDone,
      AioContractStatusType.doing,
      AioContractStatusType.suggestSuspension,
      AioContractStatusType.waitingDistrict,
      AioContractStatusType.delivered,
    ];

    List<AioContractOrderType> aioContractOrderType = [
      AioContractOrderType.deploy,
      AioContractOrderType.manage,
    ];

    packageOrderStatus = aioContractStatus
        .map(
          (e) => e.keyToServer ?? 0,
        )
        .toList();

    packageOrderType = aioContractOrderType
        .map(
          (e) => e.keyToServer,
        )
        .toList();

    deploymentOrderStatus = OrderStatus.values
        .where((e) =>
            e != OrderStatus.complete &&
            e != OrderStatus.success &&
            e != OrderStatus.cancel &&
            e != OrderStatus.all)
        .map((e) => e.keyToServer)
        .toList();

    orderType = [
      OrderType.service,
      OrderType.supply,
      OrderType.package,
      OrderType.combo,
      OrderType.maintenance,
      OrderType.salePoint,
      OrderType.salePointSingle,
      OrderType.salePointCombo,
      OrderType.partnerWarrantyService,
      OrderType.partnerNotWarrantyService,
      OrderType.partnerOperate,
      OrderType.partnerMaintenance,
      OrderType.partnerResolveProblem,
    ];

    for (var element in orderType) {
      OrderTypeFilter? orderTypeFilter;

      if (element == OrderType.partnerResolveProblem ||
          element == OrderType.partnerOperate ||
          element == OrderType.partnerIsInstallingOrder ||
          element == OrderType.partnerNotWarrantyService ||
          element == OrderType.partnerWarrantyService ||
          element == OrderType.partnerMaintenance) {
        orderTypeFilter = OrderTypeFilter.partners;
      } else if (element == OrderType.salePointSingle ||
          element == OrderType.salePointCombo ||
          element == OrderType.salePoint) {
        orderTypeFilter = OrderTypeFilter.salePoints;
      } else {
        orderTypeFilter = OrderTypeFilter.values
            .where((e) => e.keyToServer == element.keyToServer)
            .firstOrNull;
      }

      if (orderTypeFilter != null) {
        if (orderTypes.isNotEmpty) {
          if (!orderTypes.contains(orderTypeFilter)) {
            orderTypes.add(
              orderTypeFilter,
            );
          }
        } else {
          orderTypes.add(
            orderTypeFilter,
          );
        }
      }
    }
    assignment = CollectionPerformedByMeEnum.performed.keyToServer;
    state = state.copyWith(
      orderTypesDefault: orderTypes,
    );
  }

  void getOrderByType(WorkScheduleEnum item) {
    if (item == WorkScheduleEnum.deploymentOrder) {
      // don trien khai
      getOrders();
    } else if (item == WorkScheduleEnum.bundleOrder) {
      // don goi gia
      getAioContract();
    } else if (item == WorkScheduleEnum.customerInformationGather) {
      // thu thap TT
      getMyCollection();
    } else if (item == WorkScheduleEnum.warrantyRequest) {
      // yc bao hanh
      getAllWarranty();
    } else {
      // khieu nai
      getAllComplain();
    }
  }

  // lay danh sach don trien khai
  Future<void> getOrders() async {
    state = state.copyWith(
      currentPage: BaseConstant.page,
      loadOrderStatus: LoadStatus.loading,
      isFilter: state.startTime != null,
    );

    try {
      final result = await appLocator<OrderRepository>().getShortOrders(
        body: ListOrderBody(
          page: BaseConstant.page,
          pageSize: BaseConstant.size,
          startTime: state.startTime?.toLocal().toIso8601String(),
          endTime: state.endTime?.toLocal().toIso8601String(),
          status: deploymentOrderStatus,
          participantTypes: participantTypes,
          ordersType: (state.ordersType ?? []).isEmpty
              ? (state.orderTypesDefault ?? [])
                  .map((e) => e.keyToServer)
                  .toList()
              : state.ordersType,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            shortOrders: data.data,
            totalItems: data.total ?? 0,
            loadOrderStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> fetchNextDeploymentOrderData() async {
    if (state.shortOrders!.length >= (state.totalItems ?? 0)) {
      return;
    }

    if (state.loadOrderStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadOrderStatus: LoadStatus.loadMore,
    );

    try {
      final result = await appLocator<OrderRepository>().getShortOrders(
        body: ListOrderBody(
          page: state.currentPage + 1,
          pageSize: BaseConstant.size,
          startTime: state.startTime?.toIso8601String(),
          endTime: state.endTime?.toIso8601String(),
          participantTypes: participantTypes,
          ordersType: (state.ordersType ?? []).isEmpty
              ? (state.orderTypesDefault ?? [])
                  .map((e) => e.keyToServer)
                  .toList()
              : state.ordersType,
          status: deploymentOrderStatus,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            shortOrders: (state.shortOrders ?? []) + (data.data ?? []),
            currentPage: state.currentPage + 1,
            loadOrderStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  // lay ds don goi gia (trien khai)
  Future<void> getAioContract() async {
    state = state.copyWith(
      loadOrderStatus: LoadStatus.loading,
    );
    try {
      final resultParam =
          await appLocator<AioOrderRepository>().findListTotalContract(
        params: AioContractEntity(
          detailFinished: 0,
          sysGroupId: GlobalData.instance.userInfo?.sysGroupId,
          sysUserId: GlobalData.instance.userInfo?.sysUserId ??
              GlobalData.instance.userInfo?.userId,
          filterB2c: packageOrderType,
          listStatus: packageOrderStatus,
          startDate: state.startTime?.display(
            format: DateTimeFormater.dateFormatVi,
          ),
          endDate: state.endTime?.display(
            format: DateTimeFormater.dateFormatVi,
          ),
          page: BaseConstant.page + 1,
          pageSize: BaseConstant.size,
        ),
      );

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.success,
            listAioContract: data.data,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> fetchNextAioContract() async {
    if (state.listAioContract!.length >= state.allTotalContractCurrentResult) {
      return;
    }

    if (state.loadOrderStatus != LoadStatus.success) {
      return;
    }

    // lay ds don goi gia (trien khai)
    state = state.copyWith(
      loadOrderStatus: LoadStatus.loading,
    );
    try {
      final resultParam =
          await appLocator<AioOrderRepository>().findListTotalContract(
        params: AioContractEntity(
          detailFinished: 0,
          sysGroupId: GlobalData.instance.userInfo?.sysGroupId,
          sysUserId: GlobalData.instance.userInfo?.sysUserId ??
              GlobalData.instance.userInfo?.userId,
          filterB2c: packageOrderType,
          listStatus: packageOrderStatus,
          startDate: state.startTime?.display(
            format: DateTimeFormater.dateFormatVi,
          ),
          endDate: state.endTime?.display(
            format: DateTimeFormater.dateFormatVi,
          ),
          page: state.aioContractCurrentPage,
          pageSize: BaseConstant.size,
        ),
      );

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.success,
            listAioContract: (state.listAioContract ?? []) + (data.data ?? []),
            aioContractCurrentPage: state.aioContractCurrentPage + 1,
            allTotalContractCurrentResult: data.data?.first.totalRecord ?? 0,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  // lay ds khieu nai
  Future<void> getAllComplain() async {
    state = state.copyWith(
      loadOrderStatus: LoadStatus.loading,
      listAllComplain: [],
    );
    try {
      final result = await appLocator<ComplainRepository>().getListComplain(
        complainBody: ComplainBody(
          endDateFrom: state.startTime?.toLocal(),
          endDateTo: state.endTime?.toLocal(),
          sysUserId: GlobalData.instance.userInfo?.sysUserId,
          isManager: false,
          listStatus: complainStatus,
          pageIndex: BaseConstant.page,
          pageSize: BaseConstant.size,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listAllComplain: data.data,
            allTotalComplainResult: (data.data ?? []).isNotEmpty
                ? data.data!.first.totalPage
                : BaseConstant.defaultPage,
            allComplainCurrentPage: BaseConstant.page,
            allComplainCurrentSize: (data.data ?? []).length,
            loadOrderStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  void fetchNextComplain() async {
    if (state.listAllComplain!.length >= state.allTotalComplainResult) {
      return;
    }

    if (state.loadOrderStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadOrderStatus: LoadStatus.loadMore,
    );

    try {
      final result = await appLocator<ComplainRepository>().getListComplain(
        complainBody: ComplainBody(
          listStatus: complainStatus,
          endDateFrom: state.startTime?.toLocal(),
          endDateTo: state.endTime?.toLocal(),
          isManager: false,
          sysUserId: GlobalData.instance.userInfo?.sysUserId,
          pageIndex: BaseConstant.defaultPage,
          pageSize: state.allComplainCurrentSize + BaseConstant.size,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listAllComplain: data.data!,
            allTotalComplainResult: data.data?.first.totalPage,
            allComplainCurrentPage: state.allComplainCurrentPage,
            allComplainCurrentSize:
                state.allComplainCurrentSize + BaseConstant.size,
            loadOrderStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  // lay ds yc bao hanh
  Future<void> getAllWarranty() async {
    state = state.copyWith(
      loadOrderStatus: LoadStatus.loading,
      currentWarrantyPage: 0,
    );

    try {
      final result =
          await appLocator<RequirementWarrantyRepository>().getListWarranty(
        permissionType: WarrantyRolesEnum.worker.keyToServer,
        startTime: state.startTime?.toLocal(),
        status: warrantyStatus,
        endTime: state.endTime?.toLocal(),
        page: BaseConstant.page,
        pageSize: BaseConstant.size,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listAllWarranty: data.data,
            allTotalWarrantyResult: data.total,
            loadOrderStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> fetchAllWarranty() async {
    if (state.listAllWarranty!.length >= state.allTotalWarrantyResult) {
      return;
    }

    if (state.loadOrderStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadOrderStatus: LoadStatus.loadMore,
    );

    try {
      final result =
          await appLocator<RequirementWarrantyRepository>().getListWarranty(
        permissionType: WarrantyRolesEnum.worker.keyToServer,
        status: warrantyStatus,
        page: state.warrantyCurrentPage + 1,
        pageSize: BaseConstant.size,
        startTime: state.startTime?.toLocal(),
        endTime: state.endTime?.toLocal(),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listAllWarranty: state.listAllWarranty! + data.data!,
            allTotalWarrantyResult: data.total,
            warrantyCurrentPage: state.warrantyCurrentPage + 1,
            loadOrderStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  // Thu thap TTKH
  Future<void> getMyCollection() async {
    state = state.copyWith(
      loadOrderStatus: LoadStatus.loading,
    );

    try {
      final result =
          await appLocator<RequirementRepository>().getListCollection(
        isGetAll: false,
        performType: assignment,
        status: collectCustomerInfo,
        startTime: state.startTime?.display(
          format: DateTimeFormater.dateTimeFormat,
        ),
        endTime: state.endTime?.display(
          format: DateTimeFormater.dateTimeFormat,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listMyCollection: data.data,
            mineTotalResult: data.total,
            loadOrderStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
        message: error.toString(),
      );
    }
  }

  Future<void> fetchMyCollection() async {
    if (state.listMyCollection!.length >= state.mineTotalResult) {
      return;
    }

    if (state.loadOrderStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadOrderStatus: LoadStatus.loadMore,
    );

    try {
      final result =
          await appLocator<RequirementRepository>().getListCollection(
        isGetAll: false,
        page: state.mineCurrentPage + 1,
        performType: assignment,
        status: collectCustomerInfo,
        startTime: state.startTime?.display(
          format: DateTimeFormater.dateTimeFormat,
        ),
        endTime: state.endTime?.display(
          format: DateTimeFormater.dateTimeFormat,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            listMyCollection: state.listMyCollection! + (data.data ?? []),
            mineTotalResult: data.total,
            mineCurrentPage: state.mineCurrentPage + 1,
            loadOrderStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadOrderStatus: LoadStatus.failure,
      );
    }
  }

  void changeDate(DateTime? startTime, DateTime? endTime) {
    DateTime now = DateTime.now();
    state.startTime = startTime;

    state.endTime = endTime;

    state = state.copyWith(
      startTime: (startTime ?? now).startDay,
      endTime: endTime?.endDay ??
          (startTime != null ? startTime.endDay : now.endDay),
    );
    getWorkCount();
    getOrderByType(
      state.workScheduleSelected ?? WorkScheduleEnum.deploymentOrder,
    );
  }

  Future<void> getEventDay({DateTime? eventDate}) async {
    state = state.copyWith(
      eventDate: eventDate,
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().getEventDay(
        date: (state.eventDate ?? DateTime.now()).display(
          format: DateTimeFormater.dateTimeFormat,
        ),
      );

      await result?.when(
        success: (data) async {
          Map<DateTime, List<String>>? eventDays = {};
          data.schedules?.forEach((element) {
            DateTime utc = DateTime.utc(
              element.workingDay?.year ?? 0,
              element.workingDay?.month ?? 0,
              element.workingDay?.day ?? 0,
            );
            eventDays.putIfAbsent(
              utc,
              () => [],
            );
          });
          state = state.copyWith(
            eventDays: eventDays,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  // lay so dem don
  Future<void> getWorkCount({
    DateTime? startTime,
    DateTime? endTime,
  }) async {
    List<DailyWorkEntity> dailyWork = [];
    try {
      if (startTime != null) {
        state = state.copyWith(
          startTime: startTime,
          endTime: endTime,
        );
      }
      final result = await appLocator<OrderRepository>().getWorkCount(
        startTime: state.startTime?.display(
          format: DateTimeFormater.dateTimeFormat,
        ),
        endTime: state.endTime?.display(
          format: DateTimeFormater.dateTimeFormat,
        ),
      );
      await result?.when(
        success: (data) async {
          dailyWork = [
            DailyWorkEntity(
              title: 'Đơn triển khai',
              orderCount: data.deploymentOrderCount,
              workTypeKey: WorkScheduleEnum.deploymentOrder.keyToServer,
            ),
            DailyWorkEntity(
              title: 'Đơn gói giá ',
              orderCount: data.bundleOrderCount,
              workTypeKey: WorkScheduleEnum.bundleOrder.keyToServer,
            ),
            DailyWorkEntity(
              title: 'Thu thập thông tin khách hàng',
              orderCount: data.customerInformationGatherCount,
              workTypeKey:
                  WorkScheduleEnum.customerInformationGather.keyToServer,
            ),
            DailyWorkEntity(
              title: 'Yêu cầu bảo hành',
              orderCount: data.warrantyRequestCount,
              workTypeKey: WorkScheduleEnum.warrantyRequest.keyToServer,
            ),
            DailyWorkEntity(
              title: 'Khiếu nại phản ánh',
              orderCount: data.customerClaimCount,
              workTypeKey: WorkScheduleEnum.customerClaim.keyToServer,
            ),
          ];
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            dailyWork: dailyWork,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void updateWorkSchedule() {
    getEventDay();
    getWorkCount();
  }

  void updateFocusDay(DateTime focusedDay) {
    state = state.copyWith(
      focusedDay: focusedDay,
    );
  }

  void updateFilterStatus(bool isFilter) {
    state = state.copyWith(
      isFilter: isFilter,
    );
  }

  void changeIsRangeTime({
    bool? isRangeTime,
  }) {
    state = state.copyWith(
      isRangeTime: isRangeTime,
    );
  }
}
