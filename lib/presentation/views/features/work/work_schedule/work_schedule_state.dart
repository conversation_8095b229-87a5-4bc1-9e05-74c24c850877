part of 'work_schedule_view_model.dart';

//ignore: must_be_immutable
class WorkScheduleState extends Equatable {
  DateTime? endTime;
  DateTime? startTime;
  final bool isFilter;
  final String? message;
  final DateTime? eventDate;
  final DateTime? focusedDay;
  final int mineTotalResult;
  final int mineCurrentPage;
  final LoadStatus loadStatus;
  final DateTime? selectedDay;
  final List<String>? ordersType;
  final int? currentWarrantyPage;
  final LoadStatus loadOrderStatus;
  final int allTotalComplainResult;
  final int allComplainCurrentPage;
  final int allComplainCurrentSize;
  final int allTotalWarrantyResult;
  final int warrantyCurrentPage;
  final int aioContractCurrentPage;
  final int allTotalContractCurrentResult;
  final List<DailyWorkEntity>? dailyWork;
  final List<ComplainEntity>? listAllComplain;
  final WorkScheduleEnum? workScheduleSelected;
  final Map<DateTime, List<String>>? eventDays;
  final List<OrderTypeFilter>? orderTypesDefault;
  final List<OrderInfoShortEntity>? shortOrders;
  final List<AioContractEntity>? listAioContract;
  final List<WarrantyClaimEntity>? listAllWarranty;
  final List<CollectionInfoEntity>? listMyCollection;
  final int? totalItems;
  final int currentPage;
  final bool isRangeTime;

  WorkScheduleState({
    this.ordersType,
    this.eventDate,
    this.startTime,
    this.totalItems,
    this.endTime,
    this.orderTypesDefault,
    this.shortOrders,
    this.listAioContract,
    this.message,
    this.listAllWarranty,
    this.listMyCollection,
    this.currentWarrantyPage,
    this.listAllComplain,
    this.allTotalWarrantyResult = 0,
    this.allTotalComplainResult = 0,
    this.allComplainCurrentPage = 0,
    this.allComplainCurrentSize = 0,
    this.aioContractCurrentPage = 0,
    this.allTotalContractCurrentResult = 0,
    this.mineCurrentPage = 0,
    this.mineTotalResult = 0,
    this.warrantyCurrentPage = 0,
    this.currentPage = 0,
    this.isFilter = false,
    this.eventDays,
    this.dailyWork,
    this.focusedDay,
    this.selectedDay,
    this.isRangeTime = true,
    this.loadOrderStatus = LoadStatus.initial,
    this.loadStatus = LoadStatus.initial,
    this.workScheduleSelected = WorkScheduleEnum.deploymentOrder,
  });

  get allMyColelectionPage => null;

  WorkScheduleState copyWith({
    LoadStatus? loadOrderStatus,
    LoadStatus? loadStatus,
    DateTime? selectedDay,
    WorkScheduleEnum? workScheduleSelected,
    CodeEntity? province,
    List<CodeEntity>? districts,
    List<String>? ordersType,
    DateTime? eventDate,
    DateTime? startTime,
    DateTime? endTime,
    List<OrderTypeFilter>? orderTypesDefault,
    List<OrderInfoShortEntity>? shortOrders,
    List<AioContractEntity>? listAioContract,
    String? message,
    List<WarrantyClaimEntity>? listAllWarranty,
    List<ComplainEntity>? listAllComplain,
    int? allTotalWarrantyResult,
    int? currentWarrantyPage,
    int? allTotalComplainResult,
    int? allComplainCurrentPage,
    int? allComplainCurrentSize,
    int? allComplainTotalData,
    int? warrantyCurrentPage,
    int? aioContractCurrentPage,
    int? allTotalContractCurrentResult,
    List<CollectionInfoEntity>? listMyCollection,
    int? mineTotalResult,
    int? mineCurrentPage,
    bool? isFilter,
    Map<DateTime, List<String>>? eventDays,
    List<DailyWorkEntity>? dailyWork,
    DateTime? focusedDay,
    int? totalItems,
    int? currentPage,
    bool? isRangeTime,
  }) {
    return WorkScheduleState(
      loadOrderStatus: loadOrderStatus ?? this.loadOrderStatus,
      loadStatus: loadStatus ?? this.loadStatus,
      selectedDay: selectedDay ?? this.selectedDay,
      workScheduleSelected: workScheduleSelected ?? this.workScheduleSelected,
      ordersType: ordersType ?? this.ordersType,
      eventDate: eventDate ?? this.eventDate,
      startTime: startTime ?? this.startTime,
      endTime: endTime ?? this.endTime,
      orderTypesDefault: orderTypesDefault ?? this.orderTypesDefault,
      shortOrders: shortOrders ?? this.shortOrders,
      listAioContract: listAioContract ?? this.listAioContract,
      message: message ?? this.message,
      listAllWarranty: listAllWarranty ?? this.listAllWarranty,
      currentWarrantyPage: currentWarrantyPage ?? this.currentWarrantyPage,
      listAllComplain: listAllComplain ?? this.listAllComplain,
      allComplainCurrentSize:
          allComplainCurrentSize ?? this.allComplainCurrentSize,
      allTotalWarrantyResult:
          allTotalWarrantyResult ?? this.allTotalWarrantyResult,
      allTotalComplainResult:
          allTotalComplainResult ?? this.allTotalComplainResult,
      allComplainCurrentPage:
          allComplainCurrentPage ?? this.allComplainCurrentPage,
      listMyCollection: listMyCollection ?? this.listMyCollection,
      mineCurrentPage: mineCurrentPage ?? this.mineCurrentPage,
      isFilter: isFilter ?? this.isFilter,
      eventDays: eventDays ?? this.eventDays,
      dailyWork: dailyWork ?? this.dailyWork,
      mineTotalResult: mineTotalResult ?? this.mineTotalResult,
      focusedDay: focusedDay ?? this.focusedDay,
      currentPage: currentPage ?? this.currentPage,
      totalItems: totalItems ?? this.totalItems,
      warrantyCurrentPage: warrantyCurrentPage ?? this.warrantyCurrentPage,
      isRangeTime: isRangeTime ?? this.isRangeTime,
      aioContractCurrentPage:
          aioContractCurrentPage ?? this.aioContractCurrentPage,
      allTotalContractCurrentResult:
          allTotalContractCurrentResult ?? this.allTotalContractCurrentResult,
    );
  }

  @override
  List<Object?> get props => [
        endTime,
        message,
        isFilter,
        eventDays,
        dailyWork,
        focusedDay,
        loadStatus,
        selectedDay,
        ordersType,
        eventDate,
        startTime,
        totalItems,
        currentPage,
        mineCurrentPage,
        mineTotalResult,
        loadOrderStatus,
        listAioContract,
        listAllWarranty,
        listAllComplain,
        listMyCollection,
        orderTypesDefault,
        isRangeTime,
        currentWarrantyPage,
        workScheduleSelected,
        allTotalWarrantyResult,
        allTotalComplainResult,
        allComplainCurrentPage,
        warrantyCurrentPage,
        allComplainCurrentSize,
        aioContractCurrentPage,
        allTotalContractCurrentResult,
      ];
}
