import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/enums/collection_status.dart';
import 'package:vcc/domain/enums/customer_manage_tab_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/work_schedule_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/detail_info_customer_collection/detail_info_customer_collection_page.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/date_time.dart';

class MyCollectionWidget extends StatefulHookConsumerWidget {
  const MyCollectionWidget({
    super.key,
    required this.onRefresh,
  });

  final Function onRefresh;

  @override
  ConsumerState<MyCollectionWidget> createState() => _MyCollectionWidgetState();
}

class _MyCollectionWidgetState extends ConsumerState<MyCollectionWidget> {
  late ScrollController scrollController;

  @override
  void initState() {
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    super.initState();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(workScheduleProvider.notifier).fetchMyCollection();
    }
  }

  Future<void> onRefresh() async {
    ref.read(workScheduleProvider.notifier).getMyCollection();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(workScheduleProvider);
    if (state.loadOrderStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }

    if ((state.listMyCollection ?? []).isEmpty) {
      return EmptyListWidget(
        title: "Không có dữ liệu",
        onRefresh: onRefresh,
      );
    }

    return RefreshIndicatorWidget(
      onRefresh: onRefresh,
      child: ListView.separated(
        controller: scrollController,
        itemCount: state.listMyCollection?.length ?? 0,
        shrinkWrap: true,
        physics: const AlwaysScrollableScrollPhysics(),
        separatorBuilder: (_, __) => DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        itemBuilder: (context, index) {
          final item = state.listMyCollection![index];
          String createDate = (item.createdAt ?? '').isNotEmpty
              ? DateFormat(DateTimeFormater.dateFormatVi)
                  .format(DateTime.parse(item.createdAt!))
              : "";

          DateTime? executionDate = (item.executionDate ?? '').isNotEmpty
              ? DateTime.parse(item.executionDate!)
              : null;

          //get day of executionDate
          String day = executionDate != null ? executionDate.dayOfMonth() : '';
          //get month of executionDate
          String month = executionDate != null ? executionDate.getMonth() : '';
          //get monday of executionDate
          String weekDay = executionDate != null ? executionDate.getDay() : '';

          String status =
              CollectionStatusExtension.fromString(item.status).display;

          return Container(
            color: BaseColors.backgroundWhite,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Row(
                            children: [
                              Flexible(
                                child: Text(
                                  item.code ?? '',
                                  style: UITextStyle.body2SemiBold,
                                ),
                              ),
                              const SizedBox(width: 8),
                              InkWellWidget(
                                onTap: () {
                                  AppUtils.copyToClipboard(item.code ?? '');
                                },
                                child: MyAssets.icons.iconCopySmall.svg(),
                              ),
                              const SizedBox(width: 4),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            "Ngày tạo: $createDate",
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(100),
                        color: CollectionStatusExtension.fromString(item.status)
                            .backgroundColor,
                      ),
                      child: Text(
                        status,
                        style: UITextStyle.body2Medium.copyWith(
                          color:
                              CollectionStatusExtension.fromString(item.status)
                                  .textColor,
                        ),
                      ),
                    )
                  ],
                ),
                const SizedBox(height: 12),
                InkWellWidget(
                  onTap: () async {
                    await context
                        .push(
                          RouterPaths.detailInfoCustomerCollection,
                          extra: DetailInfoCustomerCollectionArguments(
                            collectionCode: item.code ?? '',
                            tabType: CustomerManageTabType.myCollection.value,
                          ),
                        )
                        .whenComplete(
                          () => widget.onRefresh(),
                        );
                    onRefresh();
                  },
                  child: Row(
                    children: <Widget>[
                      Container(
                        width: 92,
                        height: 116,
                        padding: const EdgeInsets.symmetric(
                          horizontal: 8,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(16),
                          color: BaseColors.backgroundGray1,
                        ),
                        child: Center(
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Text(
                                weekDay,
                                style: UITextStyle.body1SemiBold,
                              ),
                              Container(
                                margin: const EdgeInsets.symmetric(
                                  vertical: 4,
                                ),
                                height: 1,
                                color: BaseColors.borderDefault,
                              ),
                              Text(
                                day,
                                style: UITextStyle.heading2SemiBold,
                              ),
                              Text(
                                month,
                                style: UITextStyle.body2SemiBold,
                              ),
                            ],
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          children: [
                            _buildItemRow(
                              icon: MyAssets.icons.iconUserS16.svg(),
                              value:
                                  item.customerName ?? item.companyName ?? '',
                            ),
                            _buildItemRow(
                              icon: MyAssets.icons.iconLocationS16.svg(),
                              value: item.addressFull ?? '',
                              maxLineValue: 2,
                            ),
                            _buildItemRow(
                              icon: MyAssets.icons.iconConnectS16.svg(),
                              value: item.campaignName ?? '',
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildItemRow({
    required Widget icon,
    required String value,
    int maxLineValue = 1,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 6,
      ),
      child: Row(
        children: <Widget>[
          icon,
          const SizedBox(width: 8),
          Flexible(
            child: Text(
              value,
              style: UITextStyle.bodyText2,
              maxLines: maxLineValue,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }
}
