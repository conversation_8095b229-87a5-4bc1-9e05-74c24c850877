import 'package:base_ui/base_ui.dart';
import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/theme/theme.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/entities/order_status_ui_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/order/list_order/list_order_page.dart';
import 'package:vcc/presentation/views/features/order/list_order/list_order_view_model.dart';
import 'package:vcc/presentation/views/features/order/list_order/widgets/order_item.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/work_schedule_view_model.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';

class DeploymentOrderWidget extends StatefulHookConsumerWidget {
  const DeploymentOrderWidget({
    super.key,
    this.arguments,
    required this.onRefresh,
  });

  final ListOrderArguments? arguments;

  final Function onRefresh;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ListOrderPage();
}

class _ListOrderPage extends ConsumerState<DeploymentOrderWidget> {
  late ScrollController scrollController;
  List<OrderStatusEntity> listOrderStatus = [];

  @override
  void initState() {
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(workScheduleProvider.notifier).fetchNextDeploymentOrderData();
    }
  }

  Future<void> refreshData() async {
    ref.read(workScheduleProvider.notifier).getOrders();
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<WorkScheduleState>(
      workScheduleProvider,
      (previous, current) {
        mapDataToOrderStatus();
      },
    );
    return _buildOrder();
  }

  Widget _buildOrder() {
    var orderState = ref.watch(workScheduleProvider);
    if (orderState.loadOrderStatus == LoadStatus.loading) {
      return const LoadingIndicatorWidget();
    }
    if (orderState.loadOrderStatus == LoadStatus.failure) {
      return Container(
        color: BaseColors.backgroundGray,
        child: EmptyListWidget(
          title: "Không có dữ liệu",
          onRefresh: refreshData,
        ),
      );
    }
    if ((orderState.shortOrders ?? []).isEmpty) {
      return Container(
        color: BaseColors.backgroundGray,
        child: EmptyListWidget(
          title: "Không có dữ liệu",
          onRefresh: refreshData,
        ),
      );
    } else {
      return Container(
        color: BaseColors.backgroundGray,
        child: RefreshIndicatorWidget(
          onRefresh: refreshData,
          child: ListView.builder(
            controller: scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: orderState.shortOrders?.length ?? 0,
            itemBuilder: (context, index) {
              return OrderItem(
                item: orderState.shortOrders![index],
                callBackOnBack: () {
                  ref.read(workScheduleProvider.notifier).getOrders();
                  widget.onRefresh();
                },
              );
            },
          ),
        ),
      );
    }
  }

  void mapDataToOrderStatus() {
    var countInfo = ref.watch(allOrderProvider).countOrderEntity;

    listOrderStatus = [
      OrderStatusEntity(
        icon: MyAssets.icons.totalOrderActive.svg(),
        iconDeActive: MyAssets.icons.totalOrderDeactive.svg(),
        statusName: 'Tất cả',
        orderStatus: OrderStatus.all,
        countOrder: countInfo?.totalOrder ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.draftOrderActive.svg(),
        iconDeActive: MyAssets.icons.draftOrderActive.svg(),
        statusName: "Đã đăng ký",
        orderStatus: OrderStatus.registered,
        countOrder: countInfo?.registeredCount ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.waitOrderActive.svg(),
        iconDeActive: MyAssets.icons.waitOrderDeactive.svg(),
        statusName: 'Xác nhận đơn hàng',
        orderStatus: OrderStatus.confirmWaiting,
        countOrder: countInfo?.confirmWaitingCount ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.confirmOrderActive.svg(),
        iconDeActive: MyAssets.icons.confirmOrderDeactive.svg(),
        statusName: "Đảm bảo hàng hóa",
        orderStatus: OrderStatus.goodWarrant,
        countOrder: countInfo?.goodWarrantCount ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.findingPartnerActive.svg(),
        iconDeActive: MyAssets.icons.findingPartnerDeactive.svg(),
        statusName: "Yêu cầu điều phối",
        orderStatus: OrderStatus.receptionWaiting,
        countOrder: countInfo?.receptionWaitingCount ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.waitingProcessActive.svg(),
        iconDeActive: MyAssets.icons.waitingProcessDeactive.svg(),
        statusName: 'Chờ thực hiện',
        orderStatus: OrderStatus.processWaiting,
        countOrder: countInfo?.processWaitingCount ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.deliveriesActive.svg(),
        iconDeActive: MyAssets.icons.deliveriesDeactive.svg(),
        statusName: 'Đang thực hiện ',
        orderStatus: OrderStatus.processing,
        countOrder: countInfo?.processingCount ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.completeDeliveriesActive.svg(),
        iconDeActive: MyAssets.icons.completeDeliveriesDeactive.svg(),
        statusName: 'Đã thực hiện',
        orderStatus: OrderStatus.processed,
        countOrder: countInfo?.processedCount ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.completeActive.svg(),
        iconDeActive: MyAssets.icons.completeDeactive.svg(),
        statusName: 'Hoàn thành',
        orderStatus: OrderStatus.complete,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.cancelActive.svg(),
        iconDeActive: MyAssets.icons.cancelDeactive.svg(),
        statusName: 'Hủy',
        orderStatus: OrderStatus.cancel,
      ),
    ];
  }
}
