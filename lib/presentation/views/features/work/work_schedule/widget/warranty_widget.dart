import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/detail_requirement_warranty/detail_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/requirement_warranty_home/widget/requirement_warranty_item_widget.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/work_schedule_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';

class WarrantyWidget extends StatefulHookConsumerWidget {
  const WarrantyWidget({
    super.key,
    required this.onRefresh,
  });

  final Function onRefresh;

  @override
  ConsumerState<WarrantyWidget> createState() => _WarrantyWidgetPageState();
}

class _WarrantyWidgetPageState extends ConsumerState<WarrantyWidget> {
  late ScrollController scrollController;

  @override
  void initState() {
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    super.initState();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(workScheduleProvider.notifier).fetchAllWarranty();
    }
  }

  Future<void> onRefresh() async {
    ref.read(workScheduleProvider.notifier).getAllWarranty();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(workScheduleProvider);
    if (state.loadOrderStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }

    if ((state.listAllWarranty ?? []).isEmpty) {
      return EmptyListWidget(
        title: "Không có dữ liệu",
        onRefresh: onRefresh,
      );
    }

    return RefreshIndicatorWidget(
      onRefresh: onRefresh,
      child: ListView.separated(
        controller: scrollController,
        itemCount: state.listAllWarranty?.length ?? 0,
        physics: const AlwaysScrollableScrollPhysics(),
        shrinkWrap: true,
        separatorBuilder: (_, __) => DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        itemBuilder: (context, index) {
          final item = state.listAllWarranty![index];
          return RequirementWarrantyItemWidget(
            item: item,
            onTap: () async {
              context
                  .push(
                RouterPaths.detailRequirementWarrantyClaim,
                extra: RequirementWarrantyDetailArguments(
                  code: item.warrantyCode,
                  isManage: false,
                ),
              )
                  .whenComplete(
                () {
                  widget.onRefresh();
                },
              );
            },
          );
        },
      ),
    );
  }
}
