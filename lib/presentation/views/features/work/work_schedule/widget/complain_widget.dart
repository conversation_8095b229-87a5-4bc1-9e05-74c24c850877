import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/complain_detail_page.dart';
import 'package:vcc/presentation/views/features/work/complain/widget/build_item_ticket.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/work_schedule_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';

class PerformComplainWidget extends StatefulHookConsumerWidget {
  const PerformComplainWidget({
    super.key,
    required this.onRefresh,
  });

  final Function onRefresh;

  @override
  ConsumerState<PerformComplainWidget> createState() =>
      _PerformComplainWidgetState();
}

class _PerformComplainWidgetState extends ConsumerState<PerformComplainWidget> {
  late ScrollController scrollController;

  @override
  void initState() {
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(workScheduleProvider.notifier).fetchNextComplain();
    }
  }

  Future<void> onRefresh() async {
    ref.read(workScheduleProvider.notifier).getAllComplain();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(workScheduleProvider);
    if (state.loadOrderStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }

    if ((state.listAllComplain ?? []).isEmpty) {
      return EmptyListWidget(
        title: "Không có dữ liệu",
        onRefresh: onRefresh,
      );
    }

    return RefreshIndicator(
      onRefresh: onRefresh,
      child: Container(
        color: BaseColors.backgroundGray,
        child: ListView.separated(
          controller: scrollController,
          itemCount: (state.listAllComplain ?? []).length,
          physics: const AlwaysScrollableScrollPhysics(),
          shrinkWrap: true,
          separatorBuilder: (_, __) => DividerWidget(
            height: BaseSpacing.spacing2,
            color: BaseColors.backgroundGray,
          ),
          itemBuilder: (context, index) {
            final item = (state.listAllComplain ?? [])[index];

            return BuildItemTicket(
              item: item,
              onTap: () async {
                await context.push(
                  RouterPaths.detailComplain,
                  extra: ComplainDetailArguments(
                    code: item.ticketId,
                  ),
                );
                widget.onRefresh();
                onRefresh();
              },
            );
          },
        ),
      ),
    );
  }
}
