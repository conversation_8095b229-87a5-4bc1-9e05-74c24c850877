import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_status_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_page.dart';
import 'package:vcc/presentation/views/features/work/work_schedule/work_schedule_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_package/aio_contract_package_page.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/string_utils.dart';

class AioContractWidget extends StatefulHookConsumerWidget {
  final AioContractArguments? arguments;
  final Function onRefresh;

  const AioContractWidget({
    super.key,
    this.arguments,
    required this.onRefresh,
  });

  @override
  ConsumerState<AioContractWidget> createState() => _AioContractWidgetState();
}

class _AioContractWidgetState extends ConsumerState<AioContractWidget> {
  late ScrollController scrollController;

  @override
  void initState() {
    super.initState();
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(workScheduleProvider);
    return _buildPage(state);
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  Widget _buildPage(WorkScheduleState state) {
    if (state.loadOrderStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadOrderStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      return Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DividerWidget(
            height: 8,
            color: BaseColors.backgroundGray,
          ),
          if ((state.listAioContract ?? []).isEmpty) ...[
            Expanded(
              child: EmptyListWidget(
                title: "Không có dữ liệu",
                onRefresh: refreshData,
              ),
            ),
          ],
          if ((state.listAioContract ?? []).isNotEmpty) ...[
            Expanded(
              child: RefreshIndicatorWidget(
                onRefresh: refreshData,
                child: ListView.builder(
                  shrinkWrap: true,
                  physics: const AlwaysScrollableScrollPhysics(),
                  itemCount: state.listAioContract?.length,
                  itemBuilder: (context, index) {
                    var item = (state.listAioContract ?? [])[index];
                    return InkWell(
                      onTap: () {
                        if (item.isPaying == 1) {
                          AppDialog.showDialogConfirm(
                            context,
                            title: "Chú ý",
                            message:
                                "Hiện tại hợp đồng (${item.contractCode}) đang được thực hiện trên một giao dịch khác. Bạn có muốn tiếp tục không ?",
                            buttonNameConfirm: "Xác nhận",
                            onConfirmAction: () async {
                              context.push(
                                RouterPaths.aioContractPackage,
                                extra: AioContractPackageArguments(
                                  aioContractEntity: item,
                                  backFunction: refreshData,
                                ),
                              );
                            },
                          );
                          return;
                        }
                        context
                            .push(
                          RouterPaths.aioContractPackage,
                          extra: AioContractPackageArguments(
                            aioContractEntity: item,
                            backFunction: refreshData,
                          ),
                        )
                            .whenComplete(
                          () {
                            refreshData();
                            widget.onRefresh();
                          },
                        );
                      },
                      child: Column(
                        children: [
                          Container(
                            color: BaseColors.backgroundWhite,
                            padding: const EdgeInsets.all(16),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Row(
                                  children: [
                                    Text(
                                      item.contractCode ?? "",
                                      style: UITextStyle.body2SemiBold.copyWith(
                                        color: BaseColors.textLabel,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 4,
                                    ),
                                    InkWell(
                                      onTap: () {
                                        AppUtils.copyToClipboard(
                                          item.contractCode ?? "",
                                        );
                                      },
                                      child: MyAssets.icons.copyClipBoard.svg(),
                                    ),
                                  ],
                                ),
                                Container(
                                  margin: const EdgeInsets.symmetric(
                                    vertical: 8,
                                  ),
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 12,
                                    vertical: 3,
                                  ),
                                  decoration: BoxDecoration(
                                    color: AioContractStatusTypeExtension
                                            .checkStatus(item.status)
                                        .bgColor,
                                    borderRadius: BorderRadius.circular(360),
                                  ),
                                  child: Text(
                                    AioContractStatusTypeExtension.checkStatus(
                                            item.status)
                                        .display,
                                    style: UITextStyle.body2Medium.copyWith(
                                      color: AioContractStatusTypeExtension
                                              .checkStatus(item.status)
                                          .color,
                                    ),
                                  ),
                                ),
                                if (item.sellerName != null) ...[
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Text(
                                    'Tạo bởi: ${item.sellerName ?? ''}',
                                    style: UITextStyle.caption1Regular.copyWith(
                                      color: BaseColors.textSubtitle,
                                    ),
                                  ),
                                ],
                                const SizedBox(
                                  height: 8,
                                ),
                                Text(
                                  'Ngày tạo: ${item.createdDate ?? ''}',
                                  style: UITextStyle.caption1Regular.copyWith(
                                    color: BaseColors.textSubtitle,
                                  ),
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                DividerWidget(
                                  height: 1,
                                  color: BaseColors.backgroundGray,
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      'Tên khách hàng: ',
                                      style: UITextStyle.body2Regular.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 8,
                                    ),
                                    Expanded(
                                      child: Text(
                                        item.customerName ?? "",
                                        textAlign: TextAlign.right,
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textLabel,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Số điện thoại: ',
                                      style: UITextStyle.body2Regular.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 8,
                                    ),
                                    Expanded(
                                      child: Text(
                                        textAlign: TextAlign.right,
                                        item.customerPhone ?? "",
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textLabel,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      'Địa chỉ: ',
                                      style: UITextStyle.body2Regular.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                    const SizedBox(
                                      width: 8,
                                    ),
                                    Expanded(
                                      child: Text(
                                        textAlign: TextAlign.right,
                                        item.customerAddress ?? "",
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textLabel,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      'Nhân viên bán hàng: ',
                                      style: UITextStyle.body2Regular.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                    Expanded(
                                      child: Text(
                                        item.sellerName ?? "",
                                        textAlign: TextAlign.right,
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textLabel,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                Row(
                                  children: [
                                    Text(
                                      'Giá trị hợp đồng (VNĐ): ',
                                      style: UITextStyle.body2Regular.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                    Expanded(
                                      child: Text(
                                        StringUtils.formatMoney(
                                            item.contractAmount ?? 0),
                                        textAlign: TextAlign.right,
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textLabel,
                                        ),
                                      ),
                                    )
                                  ],
                                ),
                              ],
                            ),
                          ),
                          if (index < (state.listAioContract ?? []).length - 1)
                            DividerWidget(
                              height: 8,
                              color: BaseColors.backgroundGray,
                            ),
                        ],
                      ),
                    );
                  },
                ),
              ),
            ),
          ],
        ],
      );
    }
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(workScheduleProvider.notifier).fetchNextAioContract();
    }
  }

  Future<void> refreshData() async {
    ref.read(workScheduleProvider.notifier).getAioContract();
  }
}
