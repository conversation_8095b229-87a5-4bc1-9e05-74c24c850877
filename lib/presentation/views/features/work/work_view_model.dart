import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/support_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/sales_team/sale_team_info_entity.dart';
import 'package:vcc/domain/entities/work/work_menu_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/user_company_type.dart';
import 'package:vcc/domain/enums/work_menu_type.dart';

part 'work_state.dart';

final workProvider =
    StateNotifierProvider.autoDispose<WorkViewModel, WorkState>(
  (ref) => WorkViewModel(ref: ref),
);

class WorkViewModel extends StateNotifier<WorkState> {
  final Ref ref;

  WorkViewModel({
    required this.ref,
  }) : super(const WorkState());

  Future<void> getWorks() async {
    state = state.copyWith(loadStatus: LoadStatus.loading);

    try {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getMenu() async {
    final user = GlobalData.instance.userInfo;

    state = state.copyWith(
      menu: [
        WorkMenuEntity(
          title: "Triển khai",
          items: [
            // WorkMenuEntity(
            //   title: "Lịch",
            //   code: WorkMenuType.calendar.value,
            //   icon: 'ic_work_schedule',
            // ),
            WorkMenuEntity(
              title: "Đơn triển khai",
              code: WorkMenuType.orderImplementation.value,
              icon: 'ic_work_order_1',
            ),
            WorkMenuEntity(
              title: "Đơn gói giá",
              code: WorkMenuType.contract.value,
              icon: 'ic_work_order',
            ),
            WorkMenuEntity(
              title: "Đơn giao vận",
              code: WorkMenuType.deliverOrder.value,
              icon: 'ic_work_order_2',
              orderType: OrderType.smart,
            ),
            WorkMenuEntity(
              title: "Phiếu xuất kho",
              code: WorkMenuType.stockExport.value,
              icon: 'ic_work_wms',
            ),
            WorkMenuEntity(
              title: "Thống kê công nợ",
              code: WorkMenuType.debtStatistics.value,
              icon: 'ic_work_debs_statistic',
            ),
            WorkMenuEntity(
              title: "Khiếu nại phản ánh",
              code: WorkMenuType.report.value,
              icon: 'ic_work_report',
            ),
            WorkMenuEntity(
              title: "Yêu cầu mua hàng",
              code: WorkMenuType.requestBuyProduct.value,
              icon: 'ic_work_request_order',
            ),
            WorkMenuEntity(
              title: "Yêu cầu bảo hành",
              code: WorkMenuType.warranty.value,
              icon: "ic_warranty",
            ),
          ],
        ),
        WorkMenuEntity(
          title: "Bán hàng",
          items: [
            WorkMenuEntity(
              title: "Đơn bán hàng",
              code: WorkMenuType.allOrder.value,
              icon: "ic_work_order",
            ),
            WorkMenuEntity(
              title: "Đơn gói giá",
              code: WorkMenuType.contractSale.value,
              icon: "ic_work_order",
            ),
            WorkMenuEntity(
              title: "Xác nhận hoa hồng",
              code: WorkMenuType.confirmCommission.value,
              icon: 'ic_work_commission',
            ),
            WorkMenuEntity(
              title: "Hóa đơn",
              code: WorkMenuType.bill.value,
              icon: 'ic_work_bill',
            ),
            WorkMenuEntity(
              title: "Nhóm bán hàng",
              code: WorkMenuType.salesTeam.value,
              icon: 'ic_sales_team',
              showMenu: (user?.canViewGroup ?? false) ||
                  (user?.saleGroupType ?? '').isNotEmpty,
            ),
            if (GlobalData.instance.userInfo?.userType ==
                UserCompanyType.internal)
              WorkMenuEntity(
                title: "Đơn hàng đại lý",
                code: WorkMenuType.orderAgency.value,
                icon: 'ic_order_agency',
              ),
            WorkMenuEntity(
              title: "Tài liệu bán hàng",
              code: WorkMenuType.documentSale.value,
              icon: 'ic_explanation',
            ),
            WorkMenuEntity(
              title: "Thông tin bàn giao",
              code: WorkMenuType.quote.value,
              icon: 'ic_work_bill',
            ),
            WorkMenuEntity(
              title: "Soạn thảo hợp đồng",
              code: WorkMenuType.contractDrafting.value,
              icon: 'ic_work_bill',
            ),
          ],
        ),
        WorkMenuEntity(
          title: "Tiếp xúc - Khảo sát",
          items: [
            WorkMenuEntity(
              title: "Quản lý YCTX",
              code: WorkMenuType.mngAssociation.value,
              icon: 'ic_work_association',
            ),
            WorkMenuEntity(
              title: "Thu thập TTKH",
              code: WorkMenuType.collectionInfoW.value,
              icon: 'ic_work_collection_info',
            ),
            WorkMenuEntity(
              title: "Quản lý WO khảo sát",
              code: WorkMenuType.allAssociationWO.value,
              icon: 'ic_work_bill',
            ),
          ],
        ),
        WorkMenuEntity(
          title: "Khác",
          items: [
            WorkMenuEntity(
              title: "Giải trình",
              code: WorkMenuType.explanation.value,
              icon: 'ic_explanation',
            ),
            WorkMenuEntity(
              title: "Báo lỗi hệ thống",
              code: WorkMenuType.reportError.value,
              icon: 'ic_report_error',
            ),
            WorkMenuEntity(
              title: "Đóng góp ý kiến",
              code: WorkMenuType.feedback.value,
              icon: 'icon_feedback',
            ),
            WorkMenuEntity(
              title: "Thông tin CTV",
              code: WorkMenuType.manageCollaborator.value,
              icon: 'ic_manage_collaborator',
            ),
          ],
        ),
      ],
    );
  }

  Future<SaleTeamInfoEntity?> getMyTeam() async {
    try {
      final result = await appLocator<SupportRepository>().getTeamInfo();

      SaleTeamInfoEntity? team;

      await result?.when(
        success: (data) async {
          if (data != null) {
            team = data;
          }
        },
        error: (err) async {
          team = null;
        },
      );

      return team;
    } catch (error) {
      return null;
    }
  }
}
