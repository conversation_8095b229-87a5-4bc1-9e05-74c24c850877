import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pattern_formatter/numeric_formatter.dart';
import 'package:vcc/domain/body/solar_energy_body.dart';
import 'package:vcc/domain/enums/installation_system_type.dart';
import 'package:vcc/domain/enums/power_station_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/select_address_view.dart';
import 'package:vcc/presentation/views/features/work/association/price_solar_energy_detail/price_solar_energy_detail_page.dart';
import 'package:vcc/presentation/views/features/work/association/select_price_solar_energy/select_price_solar_energy_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';

class SelectPriceSolarEnergyArguments {
  final String? referenceCode;

  SelectPriceSolarEnergyArguments({
    this.referenceCode,
  });
}

class SelectPriceSolarEnergyPage extends StatefulHookConsumerWidget {
  final SelectPriceSolarEnergyArguments? arguments;

  const SelectPriceSolarEnergyPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _SelectPriceSolarEnergyView();
}

class _SelectPriceSolarEnergyView
    extends ConsumerState<SelectPriceSolarEnergyPage> {
  late TextEditingController priceController;
  late TextEditingController energyBillController;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    priceController = TextEditingController();
    energyBillController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    energyBillController.dispose();
    priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(priceSolarEnergyProvider);
    return LayoutPage(
      appbar: AppBarCustom(
        title: "Báo giá sơ bộ NLMT",
        leadingWidget: MyAssets.icons.iconCloseS24.svg(),
      ),
      bottomAction: Container(
        color: BaseColors.backgroundWhite,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: BaseButton(
            text: "Xem báo giá",
            isEnable: true,
            onTap: () async {
              _formKey.currentState?.save();
              _formKey.currentState?.validate();

              if ((_formKey.currentState?.validate() ?? false)) {
                var body = SolarEnergyBody(
                  provinceCode: state.address?.province?.code ?? '',
                  monthlyElectricityBill: double.tryParse(
                    priceController.text.replaceAll('.', ''),
                  ),
                  monthlyElectricityKwh:
                      state.powerStationType != PowerStationType.family
                          ? double.tryParse(
                              energyBillController.text,
                            )
                          : null,
                  rateByDay: state.percentMorning,
                  solarSystemType: state.installationSystemType.keyToServer,
                  stationPowerType: state.powerStationType.keyToServer,
                  referenceCode: widget.arguments?.referenceCode,
                  functional: "ASSOCIATION",
                );

                await ref
                    .read(priceSolarEnergyProvider.notifier)
                    .getInfoSolarEnergy(
                      body: body,
                    );

                if (!context.mounted) return;
                context.push(
                  RouterPaths.priceSolarEnergyDetail,
                  extra: PriceSolarEnergyDetailArguments(
                    listSolarEnergy:
                        ref.watch(priceSolarEnergyProvider).listSolarEnergy,
                  ),
                );
              } else {
                ref.read(priceSolarEnergyProvider.notifier).reloadUI();
              }
            },
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: Form(
          key: _formKey,
          child: ListView(
            padding: const EdgeInsets.all(16),
            children: [
              DropdownWidget(
                labelText: "Tỉnh/Thành",
                isRequired: true,
                enabled: true,
                suffix: MyAssets.icons.arrowRight.svg(),
                content: state.address?.province?.name ?? '',
                validator: (value) {
                  if ((value ?? '').isEmpty) {
                    return "Trường bắt buộc nhập";
                  }
                  return null;
                },
                onTap: () {
                  AppBottomSheet.showNormalBottomSheet(
                    context,
                    title: "Chọn địa chỉ",
                    height: MediaQuery.of(context).size.height * 0.7,
                    child: SelectAddressView(
                      isSelectOnlyProvince: true,
                      onSelectAddress: (address) {
                        ref
                            .read(priceSolarEnergyProvider.notifier)
                            .selectAddress(address);
                      },
                    ),
                  );
                },
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Text(
                    "Hệ lắp đặt",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  Text(
                    " *",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.all(4),
                    child: MyAssets.icons.iconInfoCircleS16.svg(),
                  )
                ],
              ),
              const SizedBox(height: 12),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  RadioWidget<InstallationSystemType?>(
                    value: InstallationSystemType.pickupTrunk,
                    groupValue: state.installationSystemType,
                    onChanged: (value) {
                      ref
                          .read(priceSolarEnergyProvider.notifier)
                          .changeInstallationSystemType(value!);
                    },
                    displayWidget: (context, item) {
                      return _displayInstallationText(item!);
                    },
                  ),
                  const SizedBox(height: 16),
                  RadioWidget<InstallationSystemType?>(
                    value: InstallationSystemType.storageSystem,
                    groupValue: state.installationSystemType,
                    onChanged: (value) {
                      ref
                          .read(priceSolarEnergyProvider.notifier)
                          .changeInstallationSystemType(value!);
                    },
                    displayWidget: (context, item) {
                      return _displayInstallationText(item!);
                    },
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Text(
                    "Loại trạm điện",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  const SizedBox(
                    width: 4,
                  ),
                  Text(
                    " *",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 12),
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  RadioWidget<PowerStationType?>(
                    value: PowerStationType.family,
                    groupValue: state.powerStationType,
                    onChanged: (value) {
                      ref
                          .read(priceSolarEnergyProvider.notifier)
                          .changePowerStationType(value!);
                    },
                    displayWidget: (context, item) {
                      return _displayPowerStationText(item!);
                    },
                  ),
                  const SizedBox(height: 16),
                  RadioWidget<PowerStationType?>(
                    value: PowerStationType.business,
                    groupValue: state.powerStationType,
                    onChanged: (value) {
                      ref
                          .read(priceSolarEnergyProvider.notifier)
                          .changePowerStationType(value!);
                    },
                    displayWidget: (context, item) {
                      return _displayPowerStationText(item!);
                    },
                  ),
                  const SizedBox(height: 16),
                  RadioWidget<PowerStationType?>(
                    value: PowerStationType.manufacture,
                    groupValue: state.powerStationType,
                    onChanged: (value) {
                      ref
                          .read(priceSolarEnergyProvider.notifier)
                          .changePowerStationType(value!);
                    },
                    displayWidget: (context, item) {
                      return _displayPowerStationText(item!);
                    },
                  ),
                ],
              ),
              const SizedBox(height: 24),
              Row(
                children: [
                  Text(
                    "Tiền điện tiêu thụ hàng tháng",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    " *",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 8),
              TextFieldWidget(
                inputFormatters: [
                  ThousandsFormatter(
                    allowFraction: true,
                  ),
                ],
                validator: (value) {
                  if (value.isEmpty) {
                    return "Trường bắt buộc nhập";
                  }
                  return null;
                },
                hintText: '0đ',
                labelText: 'Số tiền (VNĐ)',
                controller: priceController,
                textInputAction: TextInputAction.done,
              ),
              const SizedBox(height: 24),
              Visibility(
                visible: state.powerStationType != PowerStationType.family,
                child: Column(
                  children: <Widget>[
                    Row(
                      children: [
                        Text(
                          "Số Kwh tiêu thụ hàng tháng",
                          style: UITextStyle.body2Medium.copyWith(
                            color: BaseColors.textLabel,
                          ),
                        ),
                        const SizedBox(width: 4),
                        Text(
                          " *",
                          style: UITextStyle.body2Medium.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    TextFieldWidget(
                      controller: energyBillController,
                      textInputAction: TextInputAction.done,
                      hintText: "Nhập số Kwh sử dụng hàng tháng",
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      inputFormatters: [
                        FilteringTextInputFormatter.digitsOnly,
                      ],
                      validator: (value) {
                        if (value.isEmpty) {
                          return "Trường bắt buộc nhập";
                        }
                        return null;
                      },
                    ),
                    const SizedBox(height: 24),
                  ],
                ),
              ),
              Row(
                children: [
                  Text(
                    "Tỷ lệ sử dụng theo ngày",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    " *",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                  const SizedBox(width: 4),
                  InkWellWidget(
                    child: Padding(
                      padding: const EdgeInsets.all(4),
                      child: MyAssets.icons.iconInfoCircleS16.svg(),
                    ),
                  )
                ],
              ),
              const SizedBox(height: 12),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    '${state.percentMorning.round().toString()}%',
                    style: UITextStyle.body1Regular.copyWith(
                      color: BaseColors.textBody,
                    ),
                  ),
                  Text(
                    '${state.percentNight.round().toString()}%',
                    style: UITextStyle.body1Regular.copyWith(
                      color: BaseColors.textBody,
                    ),
                  )
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  MyAssets.icons.iconSunDay.svg(),
                  Expanded(
                    child: Slider(
                      value: state.currentSliderValue,
                      max: 100,
                      label: state.currentSliderValue.round().toString(),
                      activeColor: BaseColors.primary,
                      inactiveColor: BaseColors.backgroundGray3,
                      onChanged: (double value) {
                        ref
                            .read(priceSolarEnergyProvider.notifier)
                            .selectPercent(value);
                      },
                    ),
                  ),
                  MyAssets.icons.iconMoonNight.svg(),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Ngày',
                    style: UITextStyle.body1Regular.copyWith(
                      color: BaseColors.textBody,
                    ),
                  ),
                  Text(
                    'Đêm',
                    style: UITextStyle.body1Regular.copyWith(
                      color: BaseColors.textBody,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _displayInstallationText(InstallationSystemType type) {
    return Text(
      type.getDisplayTitle,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _displayPowerStationText(PowerStationType type) {
    return Text(
      type.getDisplayTitle,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }
}
