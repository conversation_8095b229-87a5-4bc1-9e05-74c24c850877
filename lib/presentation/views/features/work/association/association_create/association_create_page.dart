import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/enums/gender_type.dart';
import 'package:vcc/domain/enums/list_error_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_association_channel/select_association_channel_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_association_customer/select_association_customer_view.dart';
import 'package:vcc/presentation/views/features/work/association/select_association_service/select_association_service_view.dart';
import 'package:vcc/presentation/views/features/work/association/select_association_supply/select_association_supply_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/work/association/association_create/association_create_view_model.dart';
import 'package:vcc/presentation/views/features/work/association/create_association_success/create_association_success_page.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/cus360_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/log_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class AssociationCreateArguments {
  final CollectionInfoEntity? collectionInfo;

  AssociationCreateArguments({
    this.collectionInfo,
  });
}

class AssociationCreatePage extends StatefulHookConsumerWidget {
  final AssociationCreateArguments? arguments;

  const AssociationCreatePage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _AssociationCreatePage();
}

class _AssociationCreatePage extends ConsumerState<AssociationCreatePage> {
  late TextEditingController userPhoneNumberController;
  late TextEditingController userNameController;

  late TextEditingController taxCompanyController;
  late TextEditingController companyPhoneNumberController;
  late TextEditingController companyNameController;
  late TextEditingController companyUserNameController;
  late TextEditingController companyUserPhoneNumberController;

  late TextEditingController noteController;

  final formUserKey = GlobalKey<FormState>();
  final formCompanyKey = GlobalKey<FormState>();
  final formInfoAssociation = GlobalKey<FormState>();

  @override
  void initState() {
    userPhoneNumberController = TextEditingController();
    taxCompanyController = TextEditingController();
    userNameController = TextEditingController();
    companyNameController = TextEditingController();
    companyPhoneNumberController = TextEditingController();
    companyUserNameController = TextEditingController();
    companyUserPhoneNumberController = TextEditingController();
    noteController = TextEditingController();

    Future(() {
      ref.read(createAssociationProvider.notifier).getConfigOverdue();
      ref.read(createAssociationProvider.notifier).initData(
            collectionInfo: widget.arguments?.collectionInfo,
          );

      if (widget.arguments?.collectionInfo != null) {
        final collectionInfo = widget.arguments!.collectionInfo!;
        final userType =
            collectionInfo.customerType == UserType.personal.keyToServer
                ? UserType.personal
                : UserType.company;

        if (userType == UserType.personal) {
          userPhoneNumberController.text = collectionInfo.customerPhone ?? '';
          userNameController.text = collectionInfo.customerName ?? '';
        } else {
          taxCompanyController.text = collectionInfo.companyTaxCode ?? '';
          companyPhoneNumberController.text =
              collectionInfo.companyPhoneNumber ?? '';
          companyNameController.text = collectionInfo.companyName ?? '';
          companyUserNameController.text = collectionInfo.customerName ?? '';
          companyUserPhoneNumberController.text =
              collectionInfo.customerPhone ?? '';
        }
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    userPhoneNumberController.dispose();
    taxCompanyController.dispose();
    userNameController.dispose();
    companyNameController.dispose();
    companyPhoneNumberController.dispose();
    companyUserPhoneNumberController.dispose();
    companyUserNameController.dispose();
    noteController.dispose();

    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Tạo yêu cầu tiếp xúc",
      ),
      bottomAction: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: BaseButton(
            text: "Lưu",
            onTap: () async {
              String noteData = noteController.text;
              final validate = await validateFormAndSubmit();
              if (validate == false) {
                return;
              }

              final result = await ref
                  .read(createAssociationProvider.notifier)
                  .convertFormAndSubmit(
                    note: noteData,
                    customerName: userNameController.text,
                    customerPhoneNumber: userPhoneNumberController.text,
                    companyName: companyNameController.text,
                    companyPhoneNumber: companyPhoneNumberController.text,
                    companyUserPhoneNumber:
                        companyUserPhoneNumberController.text,
                    companyUserName: companyUserNameController.text,
                    taxCompany: taxCompanyController.text,
                  );

              if (!context.mounted) return;
              if (result) {
                context.go(
                  RouterPaths.createAssociationSuccess,
                  extra: CreateSuccessAssociationArguments(
                    code: ref.watch(createAssociationProvider).code ?? '',
                  ),
                );
              }
            },
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).requestFocus(FocusNode());
        },
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildSelectCustomer(),
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              _buildCustomerInfo(),
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              _buildAssociationInfo(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCustomerInfo() {
    var state = ref.watch(createAssociationProvider);
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            "Thông tin khách hàng",
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loại khách hàng ',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: <Widget>[
              Expanded(
                child: RadioWidget<UserType>(
                  value: UserType.personal,
                  groupValue: state.userType,
                  onChanged: (value) {
                    ref
                        .read(createAssociationProvider.notifier)
                        .changeUserType(value);
                  },
                  displayWidget: (context, item) {
                    return _displayUserText(item);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<UserType>(
                  value: UserType.company,
                  groupValue: state.userType,
                  onChanged: (value) {
                    ref
                        .read(createAssociationProvider.notifier)
                        .changeUserType(value);
                  },
                  displayWidget: (context, item) {
                    return _displayUserText(item);
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          state.userType == UserType.personal
              ? _buildUserInfo()
              : _buildCompanyInfo(),
        ],
      ),
    );
  }

  Widget _buildAssociationInfo() {
    var state = ref.watch(createAssociationProvider);
    return Form(
      key: formInfoAssociation,
      autovalidateMode: state.autoValidateInfoAssociationForm,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Text(
              "Thông tin tiếp xúc",
              style: UITextStyle.body1SemiBold.copyWith(
                color: BaseColors.textTitle,
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            RichText(
              text: TextSpan(
                text: 'Nhu cầu khách hàng',
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textTitle,
                ),
                children: <TextSpan>[
                  TextSpan(
                    text: ' *',
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 12,
            ),
            Container(
              decoration: BoxDecoration(
                border: Border.all(
                  color: BaseColors.borderDivider,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 6,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color: BaseColors.backgroundGray,
                      borderRadius: BorderRadius.vertical(
                        top: const Radius.circular(8),
                        bottom: (state.associationSupplies ?? []).isNotEmpty
                            ? Radius.zero
                            : const Radius.circular(8),
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            "Sản phẩm",
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.textBody,
                            ),
                          ),
                        ),
                        AppTextButton(
                          iconLeft: MyAssets.icons.iconAddCircle.svg(),
                          title: "Thêm nhu cầu",
                          onTap: onSelectAssociationSupply,
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: (state.associationSupplies ?? []).map(
                      (associationSupply) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 16,
                          ),
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  ref
                                      .read(createAssociationProvider.notifier)
                                      .removeAssociationSupply(
                                        associationSupply,
                                      );
                                },
                                child: MyAssets.icons.iconMinusRounedRed.svg(),
                              ),
                              const SizedBox(
                                width: 8,
                              ),
                              Text(
                                associationSupply.name ?? '',
                                style: UITextStyle.body2Regular.copyWith(
                                  color: BaseColors.textBody,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ).toList(),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 16,
            ),
            Container(
              width: MediaQuery.of(context).size.width - 32,
              decoration: BoxDecoration(
                border: Border.all(
                  color: BaseColors.borderDivider,
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 6,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color: BaseColors.backgroundGray,
                      borderRadius: BorderRadius.vertical(
                        top: const Radius.circular(8),
                        bottom: (state.associationServices ?? []).isNotEmpty
                            ? Radius.zero
                            : const Radius.circular(8),
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            "Dịch vụ",
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.textBody,
                            ),
                          ),
                        ),
                        AppTextButton(
                          iconLeft: MyAssets.icons.iconAddCircle.svg(),
                          title: "Thêm nhu cầu",
                          onTap: onSelectAssociationService,
                        ),
                      ],
                    ),
                  ),
                  Column(
                    children: (state.associationServices ?? []).map(
                      (associationService) {
                        return Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 16,
                          ),
                          child: Row(
                            children: [
                              InkWell(
                                onTap: () {
                                  ref
                                      .read(createAssociationProvider.notifier)
                                      .removeAssociationService(
                                          associationService);
                                },
                                child: MyAssets.icons.iconMinusRounedRed.svg(),
                              ),
                              const SizedBox(
                                width: 8,
                              ),
                              Text(
                                associationService.name ?? '',
                                style: UITextStyle.body2Regular.copyWith(
                                  color: BaseColors.textBody,
                                ),
                              ),
                            ],
                          ),
                        );
                      },
                    ).toList(),
                  ),
                ],
              ),
            ),
            if (state.hasErrorProductService) ...[
              const SizedBox(
                height: 16,
              ),
              Padding(
                padding: const EdgeInsets.only(
                  left: 16,
                  top: 4,
                ),
                child: Text(
                  ListErrorType.productOrServiceAssociationNotNull.displayError,
                  style: UITextStyle.body3Regular.copyWith(
                    color: BaseColors.primary,
                  ),
                ),
              ),
            ],
            if (!state.enablePotentialCustomer) ...[
              const SizedBox(
                height: 24,
              ),
              DropdownWidget(
                labelText: "Kênh tiếp xúc",
                isRequired: true,
                content: state.associationChannel?.name,
                suffix: MyAssets.icons.iconArrowRightS20.svg(),
                onTap: onSelectAssociationType,
                validator: (value) {
                  return ValidateUtils.onValidateChannel(
                      state.associationChannel);
                },
              ),
              const SizedBox(
                height: 16,
              ),
              DropdownWidget(
                labelText: "Nguồn khách hàng biết đến",
                isRequired: true,
                content: state.associationCustomer?.name,
                suffix: MyAssets.icons.iconArrowRightS20.svg(),
                onTap: onSelectAssociationCustomer,
                validator: (value) {
                  return ValidateUtils.onValidateCustomerSource(
                    state.associationCustomer,
                  );
                },
              ),
              const SizedBox(
                height: 16,
              ),
              DropdownWidget(
                labelText: "Hạn hoàn thành",
                isRequired: true,
                content: state.schedulerDate?.display(
                  format: DateTimeFormater.dateTimeFormatView,
                ),
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                validator: (value) {
                  return ValidateUtils.onValidateSchedulerDate(
                    state.schedulerDate,
                  );
                },
                onTap: () async {
                  openDatetimePicker(state.overdueConfig?.value ?? "0");
                },
              ),
            ],
            const SizedBox(
              height: 16,
            ),
            TextFieldWidget(
              controller: noteController,
              textInputAction: TextInputAction.done,
              height: 110,
              maxLines: 4,
              maxLength: 2000,
              alignment: Alignment.topLeft,
              labelText: "Ghi chú",
              autofocus: false,
              inputFormatters: [
                FilteringTextInputFormatter.allow(
                  RegExp(
                    Patterns.note,
                    multiLine: true,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSelectCustomer() {
    var state = ref.watch(createAssociationProvider);
    return InkWellWidget(
      onTap: () {
        ref.read(createAssociationProvider.notifier).changePotentialCustomer();
      },
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            (state.enablePotentialCustomer)
                ? MyAssets.icons.iconChecked.svg()
                : MyAssets.icons.iconCheckbox.svg(),
            const SizedBox(width: 16),
            Text(
              'Khách hàng tiềm năng',
              style: UITextStyle.body2Regular.copyWith(
                color: BaseColors.textBody,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserInfo() {
    var state = ref.watch(createAssociationProvider);

    return Form(
      key: formUserKey,
      autovalidateMode: state.autoValidateUserForm,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            controller: userPhoneNumberController,
            isRequired: true,
            labelText: "Số điện thoại",
            maxLength: 11,
            keyboardType: TextInputType.phone,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            suffix: !state.enableUserName
                ? Cus360Widget(
                    customerType: UserType.personal.keyToServer,
                    customerPhone: state.customerInfo?.phone,
                    customerId: state.customerInfo?.customerId,
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                getCustomerInfo360(
                  phoneNumber: userPhoneNumberController.text,
                );
              }
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: userNameController,
            isRequired: true,
            labelText: "Họ và tên",
            enabled: state.enableUserName,
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            validator: (value) {
              return ValidateUtils.onValidateUserName(value);
            },
          ),
          const SizedBox(height: 16),
          DropdownWidget(
            labelText: "Địa chỉ",
            isRequired: true,
            enabled: state.enableUserAddress,
            content: state.customerAddress?.getFullAddress,
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            onTap: onSelectAddress,
            validator: (value) {
              return ValidateUtils.onValidateAddressCustomer(
                state.customerAddress,
              );
            },
          ),
          const SizedBox(height: 16),
          Text(
            'Giới tính',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: <Widget>[
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.male,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(createAssociationProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.female,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(createAssociationProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.different,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(createAssociationProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyInfo() {
    var state = ref.watch(createAssociationProvider);
    const key = ValueKey("company");

    return Form(
      key: formCompanyKey,
      autovalidateMode: state.autoValidateCompanyForm,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            controller: taxCompanyController,
            isRequired: true,
            labelText: "Mã số thuế doanh nghiệp",
            maxLength: 14,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            suffix: !state.enableCompanyPhoneNumber
                ? Cus360Widget(
                    customerType: UserType.company.keyToServer,
                    taxCode: taxCompanyController.text,
                    customerId: state.companyInfo?.customerId,
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidateTax(value);
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                getCompanyInfo360(
                  taxCode: taxCompanyController.text,
                );
              }
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyNameController,
            labelText: "Tên doanh nghiệp",
            enabled: state.enableCompanyName,
            textInputAction: TextInputAction.done,
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyPhoneNumberController,
            isRequired: true,
            maxLength: 11,
            labelText: "Số điện thoại doanh nghiệp",
            keyboardType: TextInputType.phone,
            enabled: state.enableCompanyPhoneNumber,
            textInputAction: TextInputAction.done,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
          ),
          const SizedBox(height: 16),
          DropdownWidget(
            labelText: "Địa chỉ doanh nghiệp",
            isRequired: true,
            enabled: state.enableCompanyAddress,
            content: state.companyAddress?.getFullAddress,
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            validator: (value) {
              return ValidateUtils.onValidateAddressCompany(
                state.companyAddress,
              );
            },
            onTap: onSelectAddress,
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyUserNameController,
            isRequired: true,
            labelText: "Họ và tên người liên hệ",
            textInputAction: TextInputAction.done,
            validator: (value) {
              return ValidateUtils.onValidateUserName(value);
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyUserPhoneNumberController,
            isRequired: true,
            labelText: "Số điện thoại người liên hệ",
            textInputAction: TextInputAction.done,
            keyboardType: TextInputType.phone,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
          ),
        ],
      ),
    );
  }

  void getCustomerInfo360({
    String? phoneNumber,
  }) async {
    if ((phoneNumber ?? '').validatePhone()) {
      final customerInfo =
          await ref.read(createAssociationProvider.notifier).getCustomerInfo360(
                phoneNumber: phoneNumber,
              );

      if (customerInfo != null) {
        userNameController.text = customerInfo.fullName ?? '';
      }
    } else {
      ref.read(createAssociationProvider.notifier).enableEditUserForm();
    }
  }

  void getCompanyInfo360({
    String? taxCode,
  }) async {
    if ((taxCode ?? '').validateTax()) {
      final customerInfo =
          await ref.read(createAssociationProvider.notifier).getCompanyInfo360(
                taxCode: taxCode,
              );

      if (customerInfo != null) {
        companyNameController.text = customerInfo.fullName ?? '';
        companyPhoneNumberController.text = customerInfo.phone ?? '';
      } else {
        companyNameController.text = "";
      }
    } else {
      ref.read(createAssociationProvider.notifier).enableEditCompanyForm();
      companyNameController.text = "";
    }
  }

  Widget _displayGenderText(GenderType gender) {
    return Text(
      gender.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _displayUserText(UserType user) {
    return Text(
      user.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  void onSelectAddress({
    bool? isSetup,
  }) async {
    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        onSelectAddress: (address) {
          ref
              .read(
                createAssociationProvider.notifier,
              )
              .selectAddress(
                address.convertToAddressInfo,
              );
        },
      ),
    );
  }

  void onSelectAssociationType({
    bool? isSetup,
  }) async {
    await context.push(
      RouterPaths.selectTypeAssociation,
      extra: SelectAssociationChannelArguments(
          onSelectAssociationChannel: (association) {
        ref
            .read(createAssociationProvider.notifier)
            .selectAssociationChannelSource(association);
      }),
    );
  }

  void onSelectAssociationCustomer({
    bool? isSetup,
  }) async {
    await context.push(
      RouterPaths.selectAssociationCustomer,
      extra: SelectAssociationCustomerArguments(
          onSelectAssociationCustomer: (association) {
        ref
            .read(createAssociationProvider.notifier)
            .selectAssociationCustomer(association);
      }),
    );
  }

  void onSelectAssociationSupply({
    bool? isSetup,
  }) async {
    final result = await context.push(
      RouterPaths.selectAssociationSupply,
      extra: SelectAssociationSupplyArguments(
        onSelectAssociationSupply: (association) {
          ref
              .read(createAssociationProvider.notifier)
              .selectAssociationSupply(association);
        },
        filterAssociationSupplies:
            ref.read(createAssociationProvider).associationSupplies,
      ),
    );
    if (result == true) {
      ref.read(createAssociationProvider.notifier).validateProductService();
    }
  }

  void onSelectAssociationService({
    bool? isSetup,
  }) async {
    final result = await context.push(
      RouterPaths.selectAssociationService,
      extra: SelectAssociationServiceArguments(
        onSelectAssociationService: (association) {
          ref
              .read(createAssociationProvider.notifier)
              .selectAssociationService(association);
        },
        filterAssociationServices:
            ref.read(createAssociationProvider).associationServices,
      ),
    );
    if (result == true) {
      ref.read(createAssociationProvider.notifier).validateProductService();
    }
  }

  void openDatetimePicker(String overdueConfig) {
    var dob = DateTime.now().add(
      Duration(
        hours: int.parse(overdueConfig),
      ),
    );

    CustomBottomPicker.dateTime(
      height: MediaQuery.of(context).size.height * 0.40,
      title: "Chọn thời gian",
      buttonText: "Xác nhận",
      initialDateTime: dob,
      minDateTime: DateTime.now(),
      use24hFormat: true,
      titleStyle: UITextStyle.body1SemiBold,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      dateOrder: DatePickerDateOrder.dmy,
      onSubmit: (date) {
        if (kDebugMode) {
          LogUtils.d('confirm $date');
        }
        ref.read(createAssociationProvider.notifier).setUpTime(date);
      },
    ).show(context);
  }

  Future<bool?> validateFormAndSubmit() async {
    var state = ref.watch(createAssociationProvider);
    ref
        .read(
          createAssociationProvider.notifier,
        )
        .validateProductService();

    if (state.userType == UserType.personal) {
      if ((formUserKey.currentState?.validate() ?? false) &&
          (formInfoAssociation.currentState?.validate() ?? false) &&
          !state.hasErrorProductService) {
        return true;
      }
      ref
          .read(
            createAssociationProvider.notifier,
          )
          .enableValidate(
            autoValidateUserForm: AutovalidateMode.onUserInteraction,
            autoValidateInfoAssociationForm: AutovalidateMode.onUserInteraction,
          );
    }
    if (state.userType == UserType.company) {
      if ((formCompanyKey.currentState?.validate() ?? false) &&
          (formInfoAssociation.currentState?.validate() ?? false) &&
          !state.hasErrorProductService) {
        return true;
      }
      ref
          .watch(
            createAssociationProvider.notifier,
          )
          .enableValidate(
            autoValidateCompanyForm: AutovalidateMode.onUserInteraction,
            autoValidateInfoAssociationForm: AutovalidateMode.onUserInteraction,
          );
    }
    return false;
  }
}
