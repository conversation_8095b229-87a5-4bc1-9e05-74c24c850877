import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_item_entity.dart';
import 'package:vcc/domain/entities/order/bill_entity.dart';
import 'package:vcc/domain/enums/action_type_enum.dart';
import 'package:vcc/domain/enums/bill_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/source_order.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_detail/aio_contract_detail_view_model.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_update/bill_update_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_detail/bill_detail_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_search.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/tab_bar_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/debouncer.dart';

import 'bill_view_model.dart';

class BillArguments {
  final String? data;

  BillArguments({
    this.data,
  });
}

class BillPage extends StatefulHookConsumerWidget {
  final BillArguments? arguments;

  const BillPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<BillPage> createState() => _BillPageState();
}

class _BillPageState extends ConsumerState<BillPage>
    with TickerProviderStateMixin {
  late Debounce<String> deBouncer;
  List<String> listPage = [
    "Tất cả",
    "Từ chối phát hành",
    "Chưa phát hành",
    "Đã phát hành",
    "Đã hủy",
  ];
  late TabController tabController;
  late ScrollController scrollController;

  @override
  void initState() {
    Future(() {
      ref.read(billProvider.notifier).getData();
    });
    tabController = TabController(
      length: listPage.length,
      initialIndex: 0,
      vsync: this,
    );
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    tabController.addListener(_tabListener);
    deBouncer = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref.read(billProvider.notifier).getData(
              keyword: value,
            );
      },
    );
    super.initState();
  }

  @override
  void dispose() {
    tabController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(billProvider.notifier).fetchNextData();
    }
  }

  void _tabListener() async {
    if (tabController.indexIsChanging) {
      await ref.read(billProvider.notifier).changeTab(
            BillTypeExtension.fromIndex(tabController.index),
          );
      ref.read(billProvider.notifier).getData();
    }
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(billProvider);

    PreferredSizeWidget appbarWidget;

    if (state.isSearching) {
      appbarWidget = AppBarSearch(
        hintText: "Tìm kiếm mã đơn, tên khách hàng...",
        backFunction: () {
          ref.read(billProvider.notifier).changeSearchStatus(false);
          ref.read(billProvider.notifier).getData(
                keyword: "",
              );
        },
        onChanged: (value) {
          deBouncer.value = value;
        },
      );
    } else {
      appbarWidget = AppBarCustom(
        title: "Hóa đơn",
        actionWidget: [
          InkWellWidget(
            onTap: () {
              ref.read(billProvider.notifier).changeSearchStatus(true);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              child: MyAssets.icons.iconSearchS24.svg(),
            ),
          ),
        ],
      );
    }

    return LayoutPage(
      appbar: appbarWidget,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildPage(state),
      ),
    );
  }

  Widget _buildPage(BillState state) {
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      return Column(
        children: [
          TabBarWidget(
            isScrollable: true,
            tabItems: listPage,
            tabController: tabController,
            tabAlignment: TabAlignment.start,
          ),
          DividerWidget(
            height: 8,
            color: BaseColors.backgroundGray,
          ),
          Expanded(
            child: TabBarView(
              controller: tabController,
              physics: const NeverScrollableScrollPhysics(),
              children: [
                _buildList(null),
                _buildList(BillType.rejected),
                _buildList(BillType.unPublished),
                _buildList(BillType.published),
                _buildList(BillType.cancel),
              ],
            ),
          ),
        ],
      );
    }
  }

  Future<void> refreshData() async {
    ref.read(billProvider.notifier).getData();
  }

  Widget _buildList(BillType? status) {
    var state = ref.watch(billProvider);

    if ((state.listBill ?? []).isEmpty) {
      return EmptyListWidget(
        title: 'Không tìm thấy hóa đơn nào',
        onRefresh: refreshData,
      );
    }

    return RefreshIndicatorWidget(
      onRefresh: refreshData,
      child: ListView.builder(
        controller: scrollController,
        shrinkWrap: true,
        itemCount: state.listBill?.length ?? 0,
        itemBuilder: (context, index) {
          var item = state.listBill![index];
          String createAt = (item.createdAt ?? '').isNotEmpty
              ? DateFormat(DateTimeFormater.dateTimeFormatView).format(
                  DateTime.parse(item.createdAt ?? ''),
                )
              : '';

          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              InkWellWidget(
                onTap: () {
                  if (item.source == SourceOrder.aio) {
                    RegExp regExp = RegExp(r'_(\d+)$');
                    Match? match = regExp.firstMatch(item.orderCode ?? "");
                    int contractId = 0;
                    if (match != null && match.groupCount >= 1) {
                      String result = match.group(1)!;
                      contractId = int.parse(result);
                      contractId += 1;
                    }
                    getInvoice(
                      contractId: contractId,
                      context: context,
                      billItem: item,
                      contractCode: item.orderCode,
                    );
                    return;
                  }

                  context.push(
                    RouterPaths.billDetail,
                    extra: BillDetailArguments(
                      mode: ActionTypeEnum.detail,
                      data: item.orderCode,
                      status: item.status,
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Text(
                            item.orderCode ?? "",
                            style: UITextStyle.body2SemiBold.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          InkWellWidget(
                            child: MyAssets.icons.copyClipBoard.svg(),
                            onTap: () {
                              AppUtils.copyToClipboard(
                                item.orderCode ?? "",
                              );
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        "Ngày tạo: $createAt",
                        style: UITextStyle.caption1Regular.copyWith(
                          color: BaseColors.textSubtitle,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: [
                          MyAssets.icons.circle.svg(),
                          const SizedBox(
                            width: 8,
                          ),
                          Text(
                            item.status?.display ?? "",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BillTypeExtension.getColorStatus(
                                item.status,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 6),
                      Row(
                        children: [
                          MyAssets.icons.iconUserS16.svg(),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              item.fullName ?? "",
                              style: UITextStyle.body2Regular.copyWith(
                                color: BaseColors.textLabel,
                              ),
                            ),
                          ),
                        ],
                      ),
                      if ((item.address ?? "").isNotEmpty)...[
                        const SizedBox(height: 6),
                        Row(
                          children: [
                            MyAssets.icons.location.svg(),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                item.address ?? "",
                                style: UITextStyle.body2Regular.copyWith(
                                  color: BaseColors.textLabel,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ],
                  ),
                ),
              ),
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
            ],
          );
        },
      ),
    );
  }

  void  getInvoice({
    required BuildContext context,
    required int? contractId,
    required String? contractCode,
    required BillEntity? billItem,
  }) async {
    AioInvoiceEntity? aioInvoice =
    await ref.read(aioContractDetailProvider.notifier).getDataForInvoiceForB2C(
      contractId: contractId,
    );

    List<AioInvoiceItemEntity>? listInvoiceItem =
        aioInvoice?.aioInvoiceItemDTOS ?? [];

    List<BillDetailItemEntity>? listItem = listInvoiceItem
        .map((it) => BillDetailItemEntity(
      taxPercent: it.taxPercent,
      name: it.goodsName,
      goodsId: it.goodsId,
      goodsCode: it.goodsCode,
      goodsName: it.goodsName,
      quantity: it.quantity,
      price: it.price,
      goodsUnitName: it.goodsUnitName,
      amountBeforeTax: it.amountBeforeTax,
      preTaxAmount: it.preTaxAmount,
      amount: it.amount,
      packageId: it.packageId,
    ))
        .toList();

    if (!context.mounted) return;

    if (billItem?.status != BillType.rejected) {
      BillDetailEntity? dataTmp = BillDetailEntity(
        orderCode: aioInvoice?.contractCode,
        totalMoney: aioInvoice?.totalAmount,
        address: aioInvoice?.address ?? "",
        customerName: aioInvoice?.customerName,
        email: aioInvoice?.email,
        phoneNumber: aioInvoice?.customerPhone ?? "",
        items: listItem,
        taxCode: aioInvoice?.taxCode ?? "",
        invoiceType: aioInvoice?.invoiceType,
      );

      dataTmp.items = listItem;
      context.push(
        RouterPaths.billDetail,
        extra: BillDetailArguments(
          mode: ActionTypeEnum.preview,
          previewData: dataTmp,
          isFromAioContract: true,
        ),
      );
      return;
    }

    context.push(
      RouterPaths.billUpdate,
      extra: BillUpdateArguments(
          isFromAioContract: true,
          data: BillDetailEntity(
            orderCode: aioInvoice?.contractCode,
            totalMoney: aioInvoice?.totalAmount,
            address: aioInvoice?.address ?? "",
            customerName: aioInvoice?.customerName,
            email: aioInvoice?.email,
            phoneNumber: aioInvoice?.customerPhone ?? "",
            items: listItem,
            taxCode: aioInvoice?.taxCode ?? "",
            invoiceType: aioInvoice?.invoiceType,
          ),
          onSaveData: (billDetailEntity) {
            ref.read(aioContractDetailProvider.notifier).updateInvoice(
              invoice: billDetailEntity.toAioInvoiceEntity(),
              listItem: listItem,
            );
          }),
    );
  }
}
