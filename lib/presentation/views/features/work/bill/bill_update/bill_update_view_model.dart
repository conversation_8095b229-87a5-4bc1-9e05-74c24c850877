import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/order/bill_detail_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_item_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';

part 'bill_update_state.dart';

final billUpdateProvider =
    StateNotifierProvider.autoDispose<BillUpdateViewModel, BillUpdateState>(
  (ref) => BillUpdateViewModel(ref: ref),
);

class BillUpdateViewModel extends StateNotifier<BillUpdateState> {
  final Ref ref;

  BillUpdateViewModel({
    required this.ref,
  }) : super(const BillUpdateState());

  List<int> tax = [0, 8, 10];

  Future<void> initData({
    BillDetailEntity? data,
    DetailOrderEntity? orderInfo,
  }) async {
    var listItem = (data?.items ?? []).map((it) {
      it.unitPrice = it.unitPrice ?? it.price;
      return it;
    }).toList();

    state = state.copyWith(
      listTax: tax,
      items: listItem,
      coupons: data?.coupons,
      notes: data?.notes,
      billInfo: data,
      orderInfo: orderInfo,
    );

    if (orderInfo != null) {
      await getBillCustomerInfo();
    }
  }

  void removeItem({
    required int index,
    required BillDetailItemEntity item,
  }) {
    var itemTmp = state.items;
    itemTmp?.removeAt(index);

    state = state.copyWith(
      items: itemTmp,
    );
  }

  void updateItem({
    required int index,
    required BillDetailItemEntity item,
  }) {
    var itemTmp = state.items;
    if (index >= 0) {
      itemTmp?[index] = item;
    }

    state = state.copyWith(
      items: itemTmp,
    );
  }

  void removeNote(int index) {
    var itemTmp = (state.notes ?? []);
    itemTmp.removeAt(index);

    state = state.copyWith(
      notes: itemTmp,
    );
  }

  void updateNote(int index, String data) {
    var itemTmp = state.notes;
    itemTmp?[index] = data;

    state = state.copyWith(
      notes: itemTmp,
    );
  }

  void addItem() {
    var itemTmp = state.items ?? [];

    itemTmp.add(
      BillDetailItemEntity(
        taxPercent: 10,
        quantity: 1,
      ),
    );

    state = state.copyWith(
      items: itemTmp,
    );
  }

  void addNote() {
    var itemTmp = state.notes ?? [];
    itemTmp.add("");

    state = state.copyWith(
      notes: itemTmp,
    );
  }

  void updateUI() {
    state = state.copyWith();
  }

  Future<void> updateBill({
    required String orderCode,
    String? customerName,
    String? email,
    String? bankName,
    String? bankAccount,
    String? description,
  }) async {
    var items = state.items;
    items?.forEach((item) {
      item.price ??= item.unitPrice;
      item.preTaxAmount = (item.preTaxAmount ?? 0).floor().toDouble();
      item.amountBeforeTax = (item.amountBeforeTax ?? 0).ceil().toDouble();
    });
     final data = BillDetailEntity(
      orderCode: orderCode,
      customerName: customerName,
      email: email,
      bankName: bankName,
      bankAccount: bankAccount,
      description: description,
      items: items,
      coupons: state.coupons,
      notes: state.notes,
    );
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().updateBill(
        data: data,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
            updateStatus: LoadStatus.failure,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getBillCustomerInfo() async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().billDetail(
        orderCode: state.orderInfo?.orderCode ?? "",
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            billInfo: data,
            items: data.items,
            coupons: data.coupons,
            notes: data.notes,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }
}
