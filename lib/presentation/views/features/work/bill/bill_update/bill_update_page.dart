import 'package:base_ui/base_ui.dart';
import 'package:base_ui/theme/theme.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/domain/entities/order/bill_detail_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/enums/action_type_enum.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_detail/bill_detail_page.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_update/widgets/item_coupon_widget.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_update/widgets/item_note_widget.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_update/widgets/item_service_supplies_widget.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

import 'bill_update_view_model.dart';

class BillUpdateArguments {
  final BillDetailEntity? data;
  final bool? isFromOrder;
  final bool? isFromAioContract;
  final Function(BillDetailEntity callBackData)? onSaveData;
  final DetailOrderEntity? orderInfo;

  BillUpdateArguments({
    this.data,
    this.isFromAioContract,
    this.isFromOrder,
    this.onSaveData,
    this.orderInfo,
  });
}

class BillUpdatePage extends StatefulHookConsumerWidget {
  final BillUpdateArguments? arguments;

  const BillUpdatePage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<BillUpdatePage> createState() => _BillUpdatePageState();
}

class _BillUpdatePageState extends ConsumerState<BillUpdatePage>
    with TickerProviderStateMixin {
  late TextEditingController quantityController;

  late TextEditingController orderCodeController;
  late TextEditingController billIdController;
  late TextEditingController customerNameController;
  late TextEditingController phoneController;
  late TextEditingController emailController;
  late TextEditingController companyNameController;
  late TextEditingController taxCodeController;
  late TextEditingController addressController;
  late TextEditingController bankAccountController;
  late TextEditingController bankNameController;
  late TextEditingController paymentMethodStrController;
  late TextEditingController descriptionController;
  late TextEditingController noteController;
  late TextEditingController statusController;
  late TextEditingController reasonController;

  @override
  void initState() {
    quantityController = TextEditingController();
    orderCodeController = TextEditingController();
    billIdController = TextEditingController();
    customerNameController = TextEditingController();
    phoneController = TextEditingController();
    companyNameController = TextEditingController();
    emailController = TextEditingController();
    taxCodeController = TextEditingController();
    addressController = TextEditingController();
    bankAccountController = TextEditingController();
    bankNameController = TextEditingController();
    paymentMethodStrController = TextEditingController();
    descriptionController = TextEditingController();
    noteController = TextEditingController();
    statusController = TextEditingController();
    reasonController = TextEditingController();

    Future(() async {
      await ref.read(billUpdateProvider.notifier).initData(
            data: widget.arguments?.data,
            orderInfo: widget.arguments?.orderInfo,
          );
      mapData();
    });
    super.initState();
  }

  @override
  void dispose() {
    orderCodeController.dispose();
    billIdController.dispose();
    customerNameController.dispose();
    phoneController.dispose();
    emailController.dispose();
    companyNameController.dispose();
    taxCodeController.dispose();
    addressController.dispose();
    bankAccountController.dispose();
    bankNameController.dispose();
    paymentMethodStrController.dispose();
    descriptionController.dispose();
    noteController.dispose();
    statusController.dispose();
    reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(billUpdateProvider);

    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Cập nhật hóa đơn",
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildPage(state),
      ),
      bottomAction: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 16,
        ),
        decoration: BoxDecoration(
          boxShadow: AppBoxShadows.shadowNormal,
          color: BaseColors.backgroundWhite,
        ),
        child: SafeArea(
          child: Row(
            children: [
              Expanded(
                child: BaseButton(
                  text: "Xem hóa đơn",
                  onTap: () {
                    var currentState = ref.watch(billUpdateProvider);
                    bool check = _checkValidate(context, state);
                    if (check) {
                      BillDetailEntity? dataTmp =
                          currentState.billInfo ?? BillDetailEntity();

                      state.items?.forEach((item) {
                        item.preTaxAmount =
                            (item.preTaxAmount ?? 0).floor().toDouble();
                        item.amountBeforeTax =
                            (item.amountBeforeTax ?? 0).ceil().toDouble();
                      });

                      dataTmp.items = state.items;
                      dataTmp.notes = state.notes;
                      dataTmp.coupons = state.coupons;
                      dataTmp.description = noteController.text;
                      dataTmp.bankAccount = bankAccountController.text;
                      dataTmp.bankName = bankNameController.text;
                      dataTmp.email = emailController.text;
                      dataTmp.customerName = customerNameController.text;

                      context.push(
                        RouterPaths.billDetail,
                        extra: BillDetailArguments(
                          mode: ActionTypeEnum.preview,
                          previewData: dataTmp,
                          isFromAioContract:
                              widget.arguments?.isFromAioContract,
                        ),
                      );
                    }
                  },
                  backgroundColor: BaseColors.secondaryBackground,
                  textColor: BaseColors.textLabel,
                ),
              ),
              const SizedBox(
                width: 12,
              ),
              Expanded(
                child: BaseButton(
                  text: "Lưu",
                  onTap: () async {
                    bool check = _checkValidate(context, state);

                    if (check) {
                      // Check total money Order == total money Bill
                      int totalMoney = 0;
                      for (int i = 0; i < (state.items ?? []).length; i++) {
                        totalMoney += (state.items?[i].amount ?? 0);
                      }
                      if (widget.arguments?.data?.totalMoney != null &&
                          widget.arguments?.data?.totalMoney != totalMoney) {
                        AppDialog.showDialogCenter(
                          context,
                          message:
                              "Tổng tiền sau thuế của các sản phẩm: ${StringUtils.formatMoney(totalMoney)} "
                              "không bằng tổng tiền phải thanh toán cho hợp đồng: "
                              "${StringUtils.formatMoney(widget.arguments?.data?.totalMoney ?? 0)}",
                          status: DialogStatus.error,
                        );
                        return;
                      }
                      if (widget.arguments?.isFromAioContract == true) {
                        widget.arguments?.onSaveData?.call(BillDetailEntity(
                          orderCode: widget.arguments?.data?.orderCode ?? "",
                          customerName: customerNameController.text,
                          email: emailController.text,
                          bankName: bankNameController.text,
                          bankAccount: bankAccountController.text,
                          description: descriptionController.text,
                          items: state.items,
                          coupons: state.coupons,
                          notes: state.notes,
                        ));
                        context.pop();
                        return;
                      }
                      if (widget.arguments?.isFromOrder == true) {
                        final data = BillDetailEntity(
                          orderCode: orderCodeController.text,
                          customerName: customerNameController.text,
                          email: emailController.text,
                          bankName: bankNameController.text,
                          bankAccount: bankAccountController.text,
                          description: descriptionController.text,
                          items: state.items,
                          coupons: state.coupons,
                          notes: state.notes,
                        );
                        widget.arguments?.onSaveData?.call(data);
                        context.pop(true);
                        return;
                      }
                      await ref.read(billUpdateProvider.notifier).updateBill(
                            orderCode: orderCodeController.text,
                            customerName: customerNameController.text,
                            email: emailController.text,
                            bankName: bankNameController.text,
                            bankAccount: bankAccountController.text,
                            description: descriptionController.text,
                          );

                      if (ref.watch(billUpdateProvider).updateStatus ==
                          LoadStatus.success) {
                        if (!context.mounted) return;
                        context.pop(true);
                        AppDialog.showDialogCenter(
                          context,
                          message: 'Lưu hóa đơn thành công',
                          status: DialogStatus.success,
                        );
                      }
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildPage(BillUpdateState state) {
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Thông tin xuất hóa đơn",
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
                const SizedBox(height: 12),
                TextFieldWidget(
                  controller: customerNameController,
                  labelText: "Họ và tên",
                  textInputAction: TextInputAction.done,
                  validator: (value) {
                    return ValidateUtils.onValidateUserNameNullAble(value);
                  },
                ),
                const SizedBox(height: 12),
                TextFieldWidget(
                  controller: phoneController,
                  labelText: "Số điện thoại",
                  enabled: false,
                ),
                const SizedBox(height: 12),
                TextFieldWidget(
                  controller: taxCodeController,
                  labelText: "Mã số thuế",
                  enabled: false,
                ),
                const SizedBox(height: 12),
                TextFieldWidget(
                  controller: emailController,
                  labelText: "Địa chỉ email",
                  textInputAction: TextInputAction.done,
                  isRequired: true,
                  validator: (value) {
                    return ValidateUtils.onValidateEmailNullAble(value);
                  },
                ),
                const SizedBox(height: 12),
                TextFieldWidget(
                  controller: companyNameController,
                  labelText: "Tên đơn vị",
                  enabled: false,
                ),
                const SizedBox(height: 12),
                TextFieldWidget.area(
                  controller: addressController,
                  hintText: "Địa chỉ",
                  enabled: true,
                  maxLines: 2,
                ),
                const SizedBox(height: 12),
                TextFieldWidget(
                  labelText: "Hình thức thanh toán",
                  enabled: false,
                  controller: paymentMethodStrController,
                ),
                const SizedBox(height: 12),
                TextFieldWidget(
                  controller: bankNameController,
                  labelText: "Ngân hàng",
                ),
                const SizedBox(height: 12),
                TextFieldWidget(
                  controller: bankAccountController,
                  labelText: "Số tài khoản",
                ),
                const SizedBox(height: 12),
                TextFieldWidget.area(
                  controller: noteController,
                  hintText: "Ghi chú",
                  maxLines: 4,
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp(
                        Patterns.note,
                        multiLine: true,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          DividerWidget(
            height: 8,
            color: BaseColors.backgroundGray,
          ),
          Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  child: Text(
                    "Danh sách sản phẩm/dịch vụ",
                    style: UITextStyle.body1SemiBold.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: state.items?.length ?? 0,
                  itemBuilder: (context, index) {
                    return ItemServiceSuppliesWidget(
                      index: index,
                      data: state.items![index],
                      removeItem: () {
                        if (state.items!.length > 1) {
                          ref.read(billUpdateProvider.notifier).removeItem(
                                index: index,
                                item: state.items![index],
                              );
                        }
                      },
                      onChangeItem: (item) {
                        ref.read(billUpdateProvider.notifier).updateItem(
                              index: index,
                              item: state.items![index],
                            );
                      },
                    );
                  },
                ),
                const SizedBox(height: 8),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: state.coupons?.length ?? 0,
                  padding: EdgeInsets.zero,
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (context, index) {
                    return ItemCouponWidget(
                      index: index,
                      data: state.coupons![index],
                      removeItem: () {
                        if (state.coupons!.length > 1) {
                          ref.read(billUpdateProvider.notifier).removeItem(
                                index: index,
                                item: state.coupons![index],
                              );
                        }
                      },
                      onChangeItem: (item) {
                        ref.read(billUpdateProvider.notifier).updateItem(
                              index: index,
                              item: state.coupons![index],
                            );
                      },
                    );
                  },
                ),
                const SizedBox(height: 8),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: (state.notes ?? []).length,
                  itemBuilder: (context, index) {
                    return ItemNoteWidget(
                      index: index,
                      data: state.notes![index],
                      removeItem: () {
                        ref.read(billUpdateProvider.notifier).removeNote(index);
                      },
                      onChangeItem: (item) {
                        ref
                            .read(billUpdateProvider.notifier)
                            .updateNote(index, item);
                      },
                    );
                  },
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Row(
                    children: [
                      Expanded(
                        child: BaseButton(
                          onTap: () {
                            ref.read(billUpdateProvider.notifier).addItem();
                          },
                          text: "Thêm DV/vật tư",
                          fontSize: 14,
                          insertPadding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 15,
                          ),
                          backgroundColor: BaseColors.primarySurface,
                          textColor: BaseColors.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: BaseButton(
                          onTap: () {
                            ref.read(billUpdateProvider.notifier).addNote();
                          },
                          text: "Thêm ghi chú",
                          fontSize: 14,
                          insertPadding: const EdgeInsets.symmetric(
                            vertical: 10,
                            horizontal: 15,
                          ),
                          backgroundColor: BaseColors.primarySurface,
                          textColor: BaseColors.primary,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // bool _checkValidateTotalTax(BillUpdateState state) {
  //   bool rs = true;
  //   double total = 0;
  //   for (int i = 0; i < (state.items ?? []).length; i++) {
  //     total += (state.items?[i].amountBeforeTax ?? 0);
  //   }
  //   if (widget.arguments?.data?.totalMoney != total) {
  //     rs = false;
  //   }
  //   return rs;
  // }

  bool _checkValidate(BuildContext context, BillUpdateState state) {
    if (ValidateUtils.onValidateEmail(emailController.text) != null) {
      AppDialog.showDialogCenter(
        context,
        message: ValidateUtils.onValidateEmail(emailController.text) ??
            "Email không hợp lệ",
        status: DialogStatus.error,
      );
      return false;
    }

    for (int i = 0; i < (state.items ?? []).length; i++) {
      String error = "";
      if (_checkNullOrBlank(state.items?[i].goodsName?.trim())) {
        error = "Tên dịch vụ, vật tư";
      }
      if (_checkNullOrBlank(state.items?[i].quantity)) {
        error = "Tên dịch vụ, vật tư";
      }

      if (_checkNullOrBlank(state.items?[i].quantity)) {
        error = "Số lượng";
      }

      if (_checkNullOrBlank(state.items?[i].goodsUnitName)) {
        error = "Đơn vị";
      }

      if (_checkNullOrBlank(
          (state.items?[i].price ?? state.items?[i].unitPrice))) {
        error = "Đơn giá";
      }

      if (_checkNullOrBlank(state.items?[i].amount)) {
        error = "Thành tiền sau thuế";
      }

      if (_checkNullOrBlank(state.items?[i].preTaxAmount)) {
        error = "Thành tiền trước thuế";
      }

      if (error.isNotEmpty) {
        AppDialog.showDialogCenter(
          context,
          message: '$error không được để trống',
          status: DialogStatus.error,
        );
        return false;
      }
    }

    if ((state.notes ?? []).isNotEmpty) {
      for (int i = 0; i < (state.notes ?? []).length; i++) {
        if (_checkNullOrBlank(state.notes?[i].trim())) {
          AppDialog.showDialogCenter(
            context,
            message: 'Các trường bắt buộc không được để trống',
            status: DialogStatus.error,
          );
          return false;
        }
      }
    }

    return true;
  }

  _checkNullOrBlank(value) {
    if (value == null || (value.runtimeType == String && value.isEmpty)) {
      return true;
    }
    return false;
  }

  void mapData() {
    final state = ref.watch(billUpdateProvider);
    final userInfo = widget.arguments?.orderInfo?.shippingInfo;

    if (userInfo != null) {
      if (userInfo.customerType == UserType.personal) {
        customerNameController.text = state.billInfo?.customerName ?? '';
        phoneController.text = userInfo.customerPhone ?? '';
      } else {
        customerNameController.text = state.billInfo?.contactName ?? '';
        phoneController.text = userInfo.customerPhone ?? '';
        taxCodeController.text = userInfo.customerId ?? '';
        companyNameController.text = state.billInfo?.companyName ?? '';
      }
    } else {
      // if (UserTypeExtension.fromString(state.billInfo?.customerType) ==
      //     UserType.personal) {
      customerNameController.text = state.billInfo?.customerName ?? '';
      phoneController.text = state.billInfo?.phoneNumber ?? '';
      taxCodeController.text = state.billInfo?.taxCode ?? '';
      companyNameController.text = state.billInfo?.customerName ?? '';
      // } else {
      //   customerNameController.text = state.billInfo?.contactName ?? '';
      //   phoneController.text = state.billInfo?.phoneNumber ?? '';
      //   taxCodeController.text = state.billInfo?.taxCode ?? '';
      //   companyNameController.text = state.billInfo?.companyName ?? '';
      // }
    }

    orderCodeController.text = state.billInfo?.orderCode ?? '';
    billIdController.text = state.billInfo?.billId ?? '';
    emailController.text = state.billInfo?.email ?? '';

    addressController.text = state.billInfo?.address ?? '';
    paymentMethodStrController.text = state.billInfo?.paymentType ?? "CK/TM";
    emailController.text = state.billInfo?.email ?? '';
    bankNameController.text = state.billInfo?.bankName ?? '';
    bankAccountController.text = state.billInfo?.bankAccount ?? '';
    noteController.text = state.billInfo?.note ?? '';
    descriptionController.text = state.billInfo?.description ?? '';

    ref.read(billUpdateProvider.notifier).updateUI();
  }
}
