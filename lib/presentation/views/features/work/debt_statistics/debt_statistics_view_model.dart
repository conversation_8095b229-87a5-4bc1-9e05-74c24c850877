import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/data/repositories/payment_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/one_pay_entity.dart';
import 'package:vcc/domain/entities/order/debt_entity.dart';
import 'package:vcc/domain/entities/viettel_pay_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/payment_provider.dart';
import 'package:vcc/domain/enums/transfer_type.dart';
import 'package:vcc/domain/responses/order/order_response.dart';
import 'package:vcc/utils/auth_utils.dart';
import 'package:vcc/utils/log_utils.dart';

part 'debt_statistics_state.dart';

final debtStatisticsProvider = StateNotifierProvider.autoDispose<
    DebtStatisticsViewModel,
    DebtStatisticsState>((ref) => DebtStatisticsViewModel(ref: ref));

class DebtStatisticsViewModel extends StateNotifier<DebtStatisticsState> {
  final Ref ref;

  DebtStatisticsViewModel({
    required this.ref,
  }) : super(
          const DebtStatisticsState(),
        );

  Future<void> getDebts({
    required TransferType transferType,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().getDebts(
        queryType: transferType.keyToServer,
      );

      await result?.when(
        success: (data) async {
          if (transferType == TransferType.collectionAccount) {
            state = state.copyWith(
              debtCNCT: data,
              loadStatus: LoadStatus.success,
            );
          }
          if (transferType == TransferType.payment) {
            state = state.copyWith(
              debtVT: data,
              loadStatus: LoadStatus.success,
            );
          }
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> createPayment({
    required String orderCode,
    PaymentProvider? paymentProvider,
  }) async {
    if (paymentProvider == PaymentProvider.onePay) {
      await createOnePayQRCode(
        OrderResponse(
          orderCode: orderCode,
        ),
      );
    } else {
      await createViettelPayQRCode(
        OrderResponse(
          orderCode: orderCode,
        ),
      );
    }
  }

  Future<void> createOnePayQRCode(
    OrderResponse orderInfo,
  ) async {
    state = state.copyWith(
      paymentStatus: LoadStatus.loading,
    );

    String encrypt = await AuthUtils.encrypt(
      orderInfo.toJson(),
    );

    LogUtils.d(encrypt);

    try {
      final result = await appLocator<PaymentRepository>().createOnePayCode(
        encrypt: encrypt,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            onePayInfo: data,
            paymentStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            paymentStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: "Có lỗi xảy ra, vui lòng thử lại sau!",
        paymentStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> createVietQRCode(
    OrderResponse orderInfo,
  ) async {
    state = state.copyWith(
      paymentStatus: LoadStatus.loading,
    );

    String encrypt = await AuthUtils.encrypt(
      orderInfo.toJson(),
    );

    LogUtils.d(encrypt);

    try {
      final result = await appLocator<PaymentRepository>().createOnePayCode(
        encrypt: encrypt,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            onePayInfo: data,
            paymentStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            paymentStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: "Có lỗi xảy ra, vui lòng thử lại sau!",
        paymentStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> createViettelPayQRCode(
    OrderResponse orderInfo,
  ) async {
    state = state.copyWith(
      paymentStatus: LoadStatus.loading,
    );

    String encrypt = await AuthUtils.encrypt(
      orderInfo.toJson(),
    );

    LogUtils.d(encrypt);

    try {
      final result = await appLocator<PaymentRepository>().createViettelPayCode(
        encrypt: encrypt,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            viettelPayInfo: data,
            paymentStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            paymentStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: "Có lỗi xảy ra, vui lòng thử lại sau!",
        paymentStatus: LoadStatus.failure,
      );
    }
  }
}
