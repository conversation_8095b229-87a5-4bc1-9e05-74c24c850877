import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/debt_statistics_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/payment_provider.dart';
import 'package:vcc/domain/enums/process_kpi_status.dart';
import 'package:vcc/domain/enums/transfer_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_payment_provider/select_payment_provider_view.dart';
import 'package:vcc/presentation/views/features/work/debt_statistics/detail_branch/detail_branch_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment/one_pay_view.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/pages/web_view_screen.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/tab_bar_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/show_loading_utils.dart';
import 'package:vcc/utils/string_utils.dart';

import 'debt_statistics_view_model.dart';

class DebtStatisticsArguments {
  final String? orderCode;
  final int? page;

  DebtStatisticsArguments({
    this.orderCode,
    this.page,
  });
}

class DebtStatisticsPage extends StatefulHookConsumerWidget {
  final DebtStatisticsArguments? arguments;

  const DebtStatisticsPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _DebtStatisticsPage();
}

class _DebtStatisticsPage extends ConsumerState<DebtStatisticsPage>
    with TickerProviderStateMixin {
  int position = 0;
  List<String> listPage = [
    "Đơn chuyển khoản",
    "Đơn qua CNCT",
  ];
  late TabController tabController;

  @override
  void initState() {
    tabController = TabController(
      length: listPage.length,
      initialIndex: widget.arguments?.page ?? 0,
      vsync: this,
    );

    Future(() async {
      await ref.read(debtStatisticsProvider.notifier).getDebts(
            transferType: TransferType.payment,
          );
      await ref.read(debtStatisticsProvider.notifier).getDebts(
            transferType: TransferType.collectionAccount,
          );
    }).whenComplete(() {
      for (var item
          in (ref.watch(debtStatisticsProvider).debtVT?.orderInfos ?? [])) {
        if (item.orderCode == widget.arguments?.orderCode) {
          final paymentProvider = PaymentProviderExtension.getType(
            item?.paymentMethod ?? "",
          );
          // WidgetsBinding.instance.addPostFrameCallback((_) {
          selectPayment(
            context: context,
            totalMoney: item.amount,
            paymentDefault: paymentProvider,
            orderCode: item.orderCode,
          );
          // });
        }
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    tabController.dispose();
    super.dispose();
  }

  Future<void> refreshData() async {
    ref.read(debtStatisticsProvider.notifier).getDebts(
          transferType: TransferType.payment,
        );
    ref.read(debtStatisticsProvider.notifier).getDebts(
          transferType: TransferType.collectionAccount,
        );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Thống kê công nợ",
      ),
      body: SafeArea(
        child: _buildPage(),
      ),
    );
  }

  Widget _buildPage() {
    var state = ref.watch(debtStatisticsProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TabBarWidget(
          tabItems: listPage,
          tabController: tabController,
        ),
        Expanded(
          child: TabBarView(
            controller: tabController,
            physics: const NeverScrollableScrollPhysics(),
            children: [
              _buildDebtFromViettelPay(),
              _buildDebtFromBranch(),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildDebtFromViettelPay() {
    var state = ref.watch(debtStatisticsProvider);
    return Column(
      children: [
        _buildTotalMoneyCard(
          total: state.debtVT?.totalDebt ?? 0,
        ),
        _buildListVT(state),
      ],
    );
  }

  Widget _buildListVT(DebtStatisticsState state) {
    return Expanded(
      child: RefreshIndicatorWidget(
        onRefresh: () async {
          ref.read(debtStatisticsProvider.notifier).getDebts(
                transferType: TransferType.payment,
              );
        },
        child: ListView.builder(
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: state.debtVT?.orderInfos?.length ?? 0,
          itemBuilder: (context, index) {
            var item = state.debtVT?.orderInfos?[index];
            final status =
                DebtStatisticsExtension.fromString(item?.paymentStatus);
            final paymentProvider = PaymentProviderExtension.getType(
              item?.paymentMethod ?? "",
            );

            return Column(
              children: [
                Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Row(
                              children: [
                                Flexible(
                                  child: Text(
                                    item?.orderCode ?? "",
                                    style: UITextStyle.body2SemiBold.copyWith(
                                      color: BaseColors.textLabel,
                                    ),
                                  ),
                                ),
                                const SizedBox(width: 4),
                                InkWell(
                                  child: MyAssets.icons.copyClipBoard.svg(),
                                  onTap: () {
                                    AppUtils.copyToClipboard(
                                        item?.orderCode ?? "");
                                  },
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(width: 8),
                          _buildStatus(item?.status),
                        ],
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      Text(
                        StringUtils.dateToString(
                            timestamp: item?.expirationDate ??
                                item?.debtAssignedDate ??
                                ""),
                        style: UITextStyle.caption1Regular.copyWith(
                          color: BaseColors.textSubtitle,
                        ),
                      ),
                      const SizedBox(
                        height: 8,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Phương thức thanh toán:",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                          Text(
                            PaymentProviderExtension.getPaymentName(
                              paymentProvider,
                            ),
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            "Mã giao dịch:",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                          Text(
                            item?.transferCode ?? "",
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.symmetric(
                          vertical: 8,
                        ),
                        child: DividerWidget(
                          height: 1,
                          color: BaseColors.backgroundGray,
                        ),
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                status?.value ?? "",
                                style: UITextStyle.body2Regular.copyWith(
                                  color: DebtStatisticsExtension
                                      .getBackgroundColor(
                                    status,
                                  ),
                                ),
                              ),
                              Text(
                                StringUtils.formatMoney(item?.amount ?? 0),
                                style: UITextStyle.body1SemiBold.copyWith(
                                  color: BaseColors.primary,
                                ),
                              ),
                            ],
                          ),
                          BaseButton(
                            insertPadding: const EdgeInsets.symmetric(
                              vertical: 8,
                              horizontal: 24,
                            ),
                            fontSize: 14,
                            text: "Thanh toán",
                            onTap: () {
                              selectPayment(
                                context: context,
                                totalMoney: item?.amount,
                                paymentDefault: paymentProvider,
                                orderCode: item?.orderCode,
                              );
                            },
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                DividerWidget(
                  height: 8,
                  color: BaseColors.backgroundGray,
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildListBranch(DebtStatisticsState state) {
    return Expanded(
      child: RefreshIndicatorWidget(
        onRefresh: () async {
          ref.read(debtStatisticsProvider.notifier).getDebts(
                transferType: TransferType.collectionAccount,
              );
        },
        child: ListView.separated(
          itemCount: (state.debtCNCT?.orderInfos ?? []).length,
          separatorBuilder: (_, __) {
            return DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            );
          },
          itemBuilder: (context, index) {
            var item = state.debtCNCT?.orderInfos?[index];
            final status = DebtStatisticsExtension.fromString(
              item?.paymentStatus,
            );

            return InkWellWidget(
              onTap: () {
                context.push(
                  RouterPaths.detailBranch,
                  extra: DetailBranchArguments(
                    item: item,
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  children: [
                    Row(
                      children: [
                        Text(
                          item?.orderCode ?? "",
                          style: UITextStyle.body2SemiBold.copyWith(
                            color: BaseColors.textLabel,
                          ),
                        ),
                        const SizedBox(
                          width: 4,
                        ),
                        InkWell(
                          child: MyAssets.icons.copyClipBoard.svg(),
                          onTap: () {
                            AppUtils.copyToClipboard(item?.orderCode ?? "");
                          },
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            "Ngày quá hạn:",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                        ),
                        Text(
                          StringUtils.dateToString(
                              timestamp: item?.expirationDate ?? "",
                              format: "dd/MM/yyyy"),
                          style: UITextStyle.body2Medium.copyWith(
                            color: BaseColors.textLabel,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 9),
                    DividerWidget(
                      height: 1,
                      color: BaseColors.backgroundGray,
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            status?.value ?? "",
                            style: UITextStyle.body2Regular.copyWith(
                              color: DebtStatisticsExtension.getBackgroundColor(
                                status,
                              ),
                            ),
                          ),
                        ),
                        Text(
                          StringUtils.formatMoney(item?.amount ?? 0),
                          style: UITextStyle.body1SemiBold.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  Widget _buildStatus(ProcessKpiStatus? status) {
    return Visibility(
      visible: status == ProcessKpiStatus.expired ||
          status == ProcessKpiStatus.deadline,
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 2,
            ),
            child: status == ProcessKpiStatus.expired
                ? MyAssets.icons.iconWarningRedS16.svg()
                : MyAssets.icons.iconWarningOrangeS16.svg(),
          ),
          const SizedBox(width: 8),
          Text(
            status?.title ?? "",
            style: UITextStyle.body2Regular.copyWith(
              color: status == ProcessKpiStatus.expired
                  ? BaseColors.required
                  : BaseColors.warning,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDebtFromBranch() {
    var state = ref.watch(debtStatisticsProvider);
    return Column(
      children: [
        _buildTotalMoneyCard(
          total: state.debtCNCT?.totalDebt ?? 0,
          isBranch: true,
        ),
        _buildListBranch(state),
      ],
    );
  }

  Widget _buildTotalMoneyCard({
    required int total,
    bool? isBranch,
  }) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: BaseColors.backgroundGray,
      ),
      child: Column(
        children: [
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.all(
                Radius.circular(8),
              ),
            ),
            child: Column(
              children: [
                Text(
                  "Tổng công nợ",
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.textSubtitle,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  StringUtils.formatMoney(total),
                  style: UITextStyle.body2SemiBold.copyWith(
                    color: BaseColors.textTitle,
                    fontSize: 20,
                  ),
                ),
              ],
            ),
          ),
          if (isBranch ?? false) ...[
            const SizedBox(height: 8),
            Text(
              "*Đơn hàng thanh toán qua tài khoản chi nhánh: Yêu cầu thanh toán về số tài khoản chuyên thu của CNCT",
              style: UITextStyle.caption1Regular.copyWith(
                color: BaseColors.textSubtitle,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ],
      ),
    );
  }

  void selectPayment({
    required BuildContext context,
    int? totalMoney,
    String? orderCode,
    PaymentProvider? paymentDefault,
  }) {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Phương thức thanh toán",
      height: MediaQuery.of(context).size.height * 0.65,
      child: SelectPaymentProviderView(
        totalMoney: totalMoney,
        paymentDefault: paymentDefault,
        onSelectPayment: (payment) async {
          ShowLoadingUtils.instance.turnOn();
          await ref.read(debtStatisticsProvider.notifier).createPayment(
                orderCode: orderCode ?? "",
                paymentProvider: payment,
              );

          if (!context.mounted) return;

          var state = ref.watch(debtStatisticsProvider);
          ShowLoadingUtils.instance.turnOff();
          if (state.paymentStatus != LoadStatus.success) return;

          if (!context.mounted) return;
          if (payment == PaymentProvider.onePay) {
            context.push(
              RouterPaths.onePayView,
              extra: OnePayArguments(
                onePayInfo: state.onePayInfo,
              ),
            );
          } else if (payment == PaymentProvider.viettelPay) {
            String path = state.viettelPayInfo?.target ?? '';
            context.push(
              RouterPaths.webView,
              extra: WebViewArguments(
                url: path,
                title: "Viettel Paygate",
              ),
            );
          }
        },
      ),
    );
  }
}
