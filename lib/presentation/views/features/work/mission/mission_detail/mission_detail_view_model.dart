import 'package:vcc/di/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/mission_repository.dart';
import 'package:vcc/domain/entities/mission/mission_entity.dart';
import 'package:vcc/domain/body/mission/complete_mission_body.dart';
import 'package:vcc/domain/body/mission/extension_implementation_time_body.dart';

part 'mission_detail_state.dart';

final missionDetailProvider = StateNotifierProvider.autoDispose<
    MissionDetailViewModel, MissionDetailState>(
  (ref) => MissionDetailViewModel(ref: ref),
);

class MissionDetailViewModel extends StateNotifier<MissionDetailState> {
  final Ref ref;

  MissionDetailViewModel({
    required this.ref,
  }) : super(const MissionDetailState());

  Future<void> getData(String code) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<MissionRepository>().getMissionDetail(
        taskCode: code,
      );
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            missionDetail: data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<bool> extensionImplementationTime({
    String? code,
    DateTime? renewalDate,
    String? renewalReason,
  }) async {
    bool isSuccess = false;
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );
    try {
      final result =
          await appLocator<MissionRepository>().extensionImplementationTime(
        taskCode: code,
        body: ExtensionImplementationTimeBody(
          renewalDate: renewalDate,
          renewalReason: renewalReason,
        ),
      );
      await result?.when(
        success: (data) async {
          isSuccess = true;
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
            updateStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
    return isSuccess;
  }

  Future<bool> completeMission({
    String? code,
    String? resultComplete,
    List<String>? imageUrls,
  }) async {
    bool isSuccess = false;
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<MissionRepository>().completeMission(
        taskCode: code,
        body: CompleteMissionBody(
          result: resultComplete,
          imageUrls: imageUrls,
        ),
      );
      await result?.when(
        success: (data) async {
          isSuccess = true;
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
            updateStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
    return isSuccess;
  }
}
