import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/utils/validate_utils.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_detail/widget/extension_implementation_time/extension_implementation_time_view_model.dart';

class ExtensionImplementationTime extends StatefulHookConsumerWidget {
  final String? reason;
  final DateTime? extensionTime;
  final DateTime? minExtensionTime;
  final LoadStatus? extensionImplementationTimeStatus;

  final Function({
    String? reason,
    DateTime? extensionTime,
  }) onConfirm;

  const ExtensionImplementationTime({
    super.key,
    this.reason,
    this.extensionTime,
    this.minExtensionTime,
    this.extensionImplementationTimeStatus,
    required this.onConfirm,
  });

  @override
  ConsumerState<ExtensionImplementationTime> createState() =>
      _ExtensionImplementationTime();
}

class _ExtensionImplementationTime
    extends ConsumerState<ExtensionImplementationTime> {
  late TextEditingController reasonController;
  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    reasonController = TextEditingController(
      text: widget.reason,
    );
    Future(() {
      ref
          .read(extensionImplementationTimeProvider.notifier)
          .changeExtensionTime(
            date: widget.extensionTime,
          );
    });
    super.initState();
  }

  @override
  void dispose() {
    reasonController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(extensionImplementationTimeProvider);

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: BaseColors.backgroundWhite,
      bottomNavigationBar: IgnorePointer(
        ignoring:
            widget.extensionImplementationTimeStatus == LoadStatus.loading,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
            horizontal: BaseSpacing.spacing4,
          ),
          child: Row(
            children: [
              Expanded(
                child: BaseButton(
                  text: 'Hủy bỏ',
                  textColor: BaseColors.textBody,
                  backgroundColor: BaseColors.backgroundGray,
                  onTap: () {
                    context.pop();
                  },
                ),
              ),
              const SizedBox(
                width: BaseSpacing.spacing3,
              ),
              Expanded(
                child: BaseButton(
                  text: 'Gia hạn',
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    if (formKey.currentState!.validate()) {
                      widget.onConfirm(
                        reason: reasonController.text,
                        extensionTime: state.extensionTime,
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: Stack(
        children: [
          Form(
            key: formKey,
            child: IgnorePointer(
              ignoring: widget.extensionImplementationTimeStatus ==
                  LoadStatus.loading,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(
                    BaseSpacing.spacing4,
                  ),
                  child: Column(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          bottom: BaseSpacing.spacing4,
                        ),
                        child: DropdownWidget(
                          isRequired: true,
                          labelText: 'Thời gian gia hạn',
                          content: state.extensionTime?.displayView(
                            format: DateTimeFormater.dateFormatVi,
                          ),
                          suffix: MyAssets.icons.iconCalendarS24.svg(),
                          onTap: () async {
                            openDatetimePicker(
                              minDate: widget.minExtensionTime,
                              date: state.extensionTime ?? DateTime.now(),
                              onSubmit: (date) {
                                ref
                                    .read(extensionImplementationTimeProvider
                                        .notifier)
                                    .changeExtensionTime(
                                      date: date,
                                    );
                              },
                            );
                          },
                          validator: (value) {
                            return ValidateUtils.onValidateNotNullV2(
                              value: value,
                              title: 'Thời gian gia hạn',
                            );
                          },
                        ),
                      ),
                      TextFieldWidget(
                        height: 150,
                        maxLines: 4,
                        isRequired: true,
                        labelText: 'Chi tiết lý do',
                        controller: reasonController,
                        alignment: Alignment.topLeft,
                        validator: (value) {
                          return ValidateUtils.onValidateNotNullV2(
                            value: value,
                            title: 'Chi tiết lý do',
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (widget.extensionImplementationTimeStatus ==
              LoadStatus.loading) ...[
            const Center(
              child: LoadingIndicatorWidget(),
            ),
          ],
        ],
      ),
    );
  }

  void openDatetimePicker({
    required DateTime date,
    DateTime? minDate,
    required Function(DateTime date) onSubmit,
  }) {
    DateTime? minDateTime;
    DateTime initialDateTime =
        DateTime(date.year, date.month, date.day, 0, 0, 0);
    if (minDate != null) {
      minDateTime = DateTime(minDate.year, minDate.month, minDate.day, 0, 0, 0);
      if (initialDateTime.isBefore(minDateTime)) {
        initialDateTime = minDateTime;
      }
    }

    CustomBottomPicker.date(
      height: MediaQuery.of(context).size.height * 0.40,
      title: "Chọn thời gian",
      buttonText: "Xác nhận",
      minDateTime: minDateTime,
      initialDateTime: initialDateTime,
      titleStyle: UITextStyle.body1SemiBold,
      dateOrder: DatePickerDateOrder.dmy,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      onSubmit: (date) => onSubmit(date),
    ).show(context);
  }
}
