part of 'complete_mission_view_model.dart';

class CompleteMissionState extends Equatable {
  final LoadStatus loadStatus;
  final String? message;
  final List<FileUploadEntity>? listImage;

  const CompleteMissionState({
    this.loadStatus = LoadStatus.initial,
    this.message,
    this.listImage,
  });

  CompleteMissionState copyWith({
    LoadStatus? loadStatus,
    String? message,
    List<FileUploadEntity>? listImage,
  }) {
    return CompleteMissionState(
      loadStatus: loadStatus ?? this.loadStatus,
      message: message ?? this.message,
      listImage: listImage ?? this.listImage,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        message,
        listImage,
      ];
}
