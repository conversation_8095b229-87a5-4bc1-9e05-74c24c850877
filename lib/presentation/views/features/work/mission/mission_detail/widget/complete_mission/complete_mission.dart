import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/utils/validate_utils.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:vcc/domain/entities/file_upload_entity.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_image/upload_image.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_detail/widget/complete_mission/complete_mission_view_model.dart';

class CompleteMission extends StatefulHookConsumerWidget {
  final String? result;
  final List<String>? listImage;
  final LoadStatus? completeMissionStatus;
  final Function({
    String? result,
    List<String>? listImage,
  }) onConfirm;

  const CompleteMission({
    super.key,
    this.result,
    this.listImage,
    this.completeMissionStatus,
    required this.onConfirm,
  });

  @override
  ConsumerState<CompleteMission> createState() => _CompleteMission();
}

class _CompleteMission extends ConsumerState<CompleteMission> {
  final formKey = GlobalKey<FormState>();
  late TextEditingController resultController;

  @override
  void initState() {
    resultController = TextEditingController(
      text: widget.result ?? '',
    );
    Future(() {
      ref
          .read(completeMissionProvider.notifier)
          .initData(listImage: widget.listImage);
    });
    super.initState();
  }

  @override
  void dispose() {
    resultController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(completeMissionProvider);
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: BaseColors.backgroundWhite,
      bottomNavigationBar: IgnorePointer(
        ignoring: widget.completeMissionStatus == LoadStatus.loading,
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
            horizontal: BaseSpacing.spacing4,
          ),
          child: Row(
            children: [
              Expanded(
                child: BaseButton(
                  text: 'Hủy bỏ',
                  textColor: BaseColors.textBody,
                  backgroundColor: BaseColors.backgroundGray,
                  onTap: () {
                    context.pop();
                  },
                ),
              ),
              const SizedBox(
                width: BaseSpacing.spacing3,
              ),
              Expanded(
                child: BaseButton(
                  text: 'Xác nhận',
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    if (formKey.currentState!.validate()) {
                      if ((state.listImage ?? []).isEmpty) {
                        ErrorDialog.showErrorDialog(
                          'Vui lòng chọn ảnh kết quả',
                        );
                        return;
                      }
                      widget.onConfirm(
                        result: resultController.text,
                        listImage: state.listImage
                            ?.map((image) => image.link ?? '')
                            .toList(),
                      );
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: Stack(
        children: [
          Form(
            key: formKey,
            child: IgnorePointer(
              ignoring: widget.completeMissionStatus == LoadStatus.loading,
              child: SingleChildScrollView(
                child: Padding(
                  padding: const EdgeInsets.all(
                    BaseSpacing.spacing4,
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          bottom: BaseSpacing.spacing4,
                        ),
                        child: TextFieldWidget(
                          height: 150,
                          maxLines: 5,
                          isRequired: true,
                          labelText: 'Kết quả',
                          controller: resultController,
                          alignment: Alignment.topLeft,
                          validator: (value) {
                            return ValidateUtils.onValidateNotNullV2(
                              value: value,
                              title: 'Kết quả hoàn thành nhiệm vụ',
                            );
                          },
                        ),
                      ),
                      Padding(
                        padding: const EdgeInsets.only(
                          bottom: BaseSpacing.spacing3,
                        ),
                        child: RichText(
                          text: TextSpan(
                            style: UITextStyle.body2Medium,
                            children: <TextSpan>[
                              const TextSpan(
                                text: 'Ảnh kết quả',
                              ),
                              TextSpan(
                                text: ' *',
                                style: UITextStyle.body2Medium.copyWith(
                                  color: BaseColors.primary,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                      buildImage(
                        images: state.listImage ?? [],
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
          if (widget.completeMissionStatus == LoadStatus.loading) ...[
            const Center(
              child: LoadingIndicatorWidget(),
            ),
          ],
        ],
      ),
    );
  }

  Widget buildImage({
    required List<FileUploadEntity> images,
  }) {
    var state = ref.watch(completeMissionProvider);
    return SizedBox(
      height: BaseSpacing.spacing20,
      child: ListView.separated(
        shrinkWrap: true,
        itemCount: images.length + 1,
        scrollDirection: Axis.horizontal,
        separatorBuilder: (_, __) => const SizedBox(
          width: BaseSpacing.spacing2,
        ),
        itemBuilder: (context, index) {
          if (index == images.length) {
            return InkWellWidget(
              onTap: () {
                onTakePicture();
              },
              child: MyAssets.icons.iconAddImageDashline.svg(),
            );
          }
          return Stack(
            children: [
              Container(
                width: 76,
                height: 76,
                padding: const EdgeInsets.only(
                  top: BaseSpacing.spacing1,
                  right: BaseSpacing.spacing1,
                ),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(
                    BaseSpacing.spacing2,
                  ),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(
                    BaseSpacing.spacing2,
                  ),
                  child: ImageWidget(
                    enableShowPreview: true,
                    images[index].link ?? '',
                  ),
                ),
              ),
              Positioned(
                top: 0,
                right: 0,
                child: InkWellWidget(
                  onTap: () {
                    ref
                        .read(completeMissionProvider.notifier)
                        .deleteFile(images[index]);
                  },
                  child: MyAssets.icons.iconCloseRed.svg(),
                ),
              ),
              if (state.loadStatus == LoadStatus.loading) ...[
                const Positioned.fill(
                  child: Center(
                    child: LoadingIndicatorWidget(),
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  void onTakePicture() async {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Upload ảnh",
      isFlexible: true,
      child: UploadImageBottomSheet(
        maxImageSelect: 100,
        onTakeImage: (file) async {
          ref.read(completeMissionProvider.notifier).uploadFileV2(file: file);
        },
        onPickMultiImage: (file) async {
          for (var item in file) {
            await ref
                .read(completeMissionProvider.notifier)
                .uploadFileV2(file: item);
          }
        },
      ),
    );
  }
}
