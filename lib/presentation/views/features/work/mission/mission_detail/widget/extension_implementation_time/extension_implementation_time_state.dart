part of 'extension_implementation_time_view_model.dart';

class ExtensionImplementationTimeState extends Equatable {
  final LoadStatus loadStatus;
  final String? message;
  final DateTime? extensionTime;

  const ExtensionImplementationTimeState({
    this.loadStatus = LoadStatus.initial,
    this.message,
    this.extensionTime,
  });

  ExtensionImplementationTimeState copyWith({
    LoadStatus? loadStatus,
    String? message,
    DateTime? extensionTime,
  }) {
    return ExtensionImplementationTimeState(
      loadStatus: loadStatus ?? this.loadStatus,
      message: message ?? this.message,
      extensionTime: extensionTime ?? this.extensionTime,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        message,
        extensionTime,
      ];
}
