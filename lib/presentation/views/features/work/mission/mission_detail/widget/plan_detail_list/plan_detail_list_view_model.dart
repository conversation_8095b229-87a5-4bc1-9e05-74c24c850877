import 'package:vcc/di/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/mission_repository.dart';
import 'package:vcc/domain/body/mission/save_plan_mission_body.dart';

part 'plan_detail_list_state.dart';

final planDetailListProvider = StateNotifierProvider.autoDispose<
    PlanDetailListViewModel, PlanDetailListState>(
  (ref) => PlanDetailListViewModel(ref: ref),
);

class PlanDetailListViewModel extends StateNotifier<PlanDetailListState> {
  final Ref ref;

  PlanDetailListViewModel({
    required this.ref,
  }) : super(const PlanDetailListState());

  void initData({
    List<String>? listPlan,
  }) {
    state = state.copyWith(
      listPlan: listPlan,
    );
  }

  void addPlan() {
    final listPlan = state.listPlan ?? [];
    listPlan.add('');
    state = state.copyWith(
      listPlan: listPlan,
    );
  }

  Future<List<String>> removePlan({int? index}) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    await Future.delayed(const Duration(milliseconds: 500));
    List<String> listPlan = List.from(state.listPlan ?? []);
    index != null
        ? listPlan.removeAt(index)
        : listPlan.removeWhere((plan) => plan.isEmpty);
    state = state.copyWith(
      listPlan: listPlan,
      loadStatus: LoadStatus.success,
    );
    return listPlan;
  }

  Future<bool> savePlanMission({
    String? code,
  }) async {
    bool isSuccess = false;
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<MissionRepository>().savePlanMission(
        taskCode: code,
        body: SavePlanMissionBody(
          taskPlans: state.listPlan,
        ),
      );
      await result?.when(
        success: (data) async {
          isSuccess = true;
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
            updateStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
    return isSuccess;
  }
}
