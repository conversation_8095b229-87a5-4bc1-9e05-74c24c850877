part of 'plan_detail_list_view_model.dart';

class PlanDetailListState extends Equatable {
  final LoadStatus loadStatus;
  final LoadStatus updateStatus;
  final String? message;
  final List<String>? listPlan;

  const PlanDetailListState({
    this.loadStatus = LoadStatus.initial,
    this.updateStatus = LoadStatus.initial,
    this.message,
    this.listPlan,
  });

  PlanDetailListState copyWith({
    LoadStatus? loadStatus,
    LoadStatus? updateStatus,
    String? message,
    List<String>? listPlan,
  }) {
    return PlanDetailListState(
      loadStatus: loadStatus ?? this.loadStatus,
      updateStatus: updateStatus ?? this.updateStatus,
      message: message ?? this.message,
      listPlan: listPlan ?? this.listPlan,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        updateStatus,
        message,
        listPlan,
      ];
}
