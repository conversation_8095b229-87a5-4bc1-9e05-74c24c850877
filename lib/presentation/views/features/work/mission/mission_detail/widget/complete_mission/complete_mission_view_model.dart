import 'dart:io';
import 'package:vcc/di/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/file_upload_entity.dart';
import 'package:vcc/data/repositories/resource_repository.dart';

part 'complete_mission_state.dart';

final completeMissionProvider = StateNotifierProvider.autoDispose<
    CompleteMissionViewModel,
    CompleteMissionState>((ref) => CompleteMissionViewModel(ref: ref));

class CompleteMissionViewModel extends StateNotifier<CompleteMissionState> {
  final Ref ref;

  CompleteMissionViewModel({
    required this.ref,
  }) : super(const CompleteMissionState());

  void initData({
    List<String>? listImage,
  }) {
    List<FileUploadEntity> files = [];
    for (var image in (listImage ?? [])) {
      files.add(
        FileUploadEntity(
          link: image,
        ),
      );
    }
    state = state.copyWith(
      listImage: files,
    );
  }

  Future<void> uploadFileV2({
    required File file,
  }) async {
    List<FileUploadEntity> files = [];
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<ResourceRepository>().uploadFileV2(
        file: file,
      );
      await result?.when(
        success: (data) async {
          for (var fileData in data) {
            files.add(FileUploadEntity(
              link: fileData.url,
              fileName: fileData.name,
              type: fileData.type,
              path: Uri.parse(fileData.url ?? '').pathSegments.first,
            ));
          }
          List<FileUploadEntity> listFile = state.listImage ?? [];
          listFile.addAll(files);
          state = state.copyWith(
            listImage: listFile,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: error.toString(),
      );
    }
  }

  void deleteFile(FileUploadEntity item) async {
    List<FileUploadEntity> listFile = state.listImage ?? [];
    listFile.remove(item);
    state = state.copyWith(
      listImage: listFile,
    );
  }
}
