import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_detail/widget/plan_detail_list/plan_detail_list_view_model.dart';

class PlanDetailListArguments {
  final bool isView;
  final String? code;
  final bool hasPlan;
  final List<String>? listPlan;
  final Function()? onRefreshDetail;

  PlanDetailListArguments({
    this.code,
    this.listPlan,
    this.onRefreshDetail,
    this.isView = false,
    this.hasPlan = false,
  });
}

class PlanDetailList extends StatefulHookConsumerWidget {
  final PlanDetailListArguments? arguments;

  const PlanDetailList({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<PlanDetailList> createState() => _PlanDetailListState();
}

class _PlanDetailListState extends ConsumerState<PlanDetailList> {
  final formKey = GlobalKey<FormState>();

  @override
  void initState() {
    Future(() {
      ref.read(planDetailListProvider.notifier).initData(
            listPlan: widget.arguments?.listPlan,
          );
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(planDetailListProvider);
    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      appBar: const AppBarCustom(
        title: 'Danh sách chi tiết kế hoạch',
      ),
      bottomNavigationBar: Visibility(
        visible: !(widget.arguments?.isView ?? false),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
            horizontal: BaseSpacing.spacing4,
          ),
          child: IgnorePointer(
            ignoring: state.updateStatus == LoadStatus.loading,
            child: BaseButton(
              text: 'Lưu',
              onTap: () async {
                if ((widget.arguments?.hasPlan ?? false) &&
                    (state.listPlan ?? []).isNotEmpty &&
                    (state.listPlan ?? []).every((plan) => plan.isEmpty)) {
                  if (!context.mounted) return;
                  return AppDialog.showDialogCenter(
                    context,
                    message: 'Vui lòng nhập kế hoạch',
                    status: DialogStatus.error,
                  );
                }

                List<String> listPlan = await ref
                    .read(planDetailListProvider.notifier)
                    .removePlan();
                if ((widget.arguments?.hasPlan ?? false) && listPlan.isEmpty) {
                  if (!context.mounted) return;
                  return AppDialog.showDialogCenter(
                    context,
                    message: 'Vui lòng thêm kế hoạch',
                    status: DialogStatus.error,
                  );
                }

                bool isSuccess = await ref
                    .read(planDetailListProvider.notifier)
                    .savePlanMission(code: widget.arguments?.code);
                if (!context.mounted) return;
                if (isSuccess) {
                  AppDialog.showDialogCenter(
                    context,
                    message: 'Lưu chi tiết kế hoạch thành công.',
                    status: DialogStatus.success,
                    onConfirm: () {
                      widget.arguments?.onRefreshDetail?.call();
                      context.pop();
                    },
                  );
                } else {
                  AppDialog.showDialogCenter(
                    context,
                    message: state.message ?? 'Đã có lỗi xảy ra!',
                    status: DialogStatus.error,
                  );
                }
              },
            ),
          ),
        ),
      ),
      body: state.loadStatus == LoadStatus.loading
          ? const Center(child: LoadingIndicatorWidget())
          : Stack(
              children: [
                Form(
                  key: formKey,
                  child: GestureDetector(
                    onTap: () => FocusScope.of(context).unfocus(),
                    child: IgnorePointer(
                      ignoring: state.updateStatus == LoadStatus.loading,
                      child: ListView(
                        children: <Widget>[
                          ListView.separated(
                            shrinkWrap: true,
                            itemCount: (state.listPlan ?? []).length,
                            physics: const NeverScrollableScrollPhysics(),
                            padding: const EdgeInsets.symmetric(
                              vertical: BaseSpacing.spacing4,
                            ),
                            separatorBuilder: (context, index) {
                              return widget.arguments?.isView ?? false
                                  ? const Padding(
                                      padding: EdgeInsets.all(
                                        BaseSpacing.spacing4,
                                      ),
                                      child: DividerWidget(),
                                    )
                                  : const SizedBox(
                                      height: BaseSpacing.spacing2,
                                    );
                            },
                            itemBuilder: (context, index) {
                              return Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: BaseSpacing.spacing4,
                                    ),
                                    child: Text(
                                      'Kế hoạch ${index + 1}',
                                      style: UITextStyle.body1SemiBold.copyWith(
                                        color: BaseColors.textBody,
                                      ),
                                    ),
                                  ),
                                  Padding(
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: BaseSpacing.spacing2,
                                    ),
                                    child: widget.arguments?.isView ?? false
                                        ? Padding(
                                            padding: const EdgeInsets.only(
                                              left: BaseSpacing.spacing2,
                                            ),
                                            child: Text(
                                              state.listPlan![index],
                                              style: UITextStyle.body1Regular
                                                  .copyWith(
                                                color: BaseColors.textBody,
                                              ),
                                            ),
                                          )
                                        : Stack(
                                            children: [
                                              Padding(
                                                padding: const EdgeInsets.all(
                                                  BaseSpacing.spacing2,
                                                ),
                                                child: TextFieldWidget.area(
                                                  minLines: 3,
                                                  maxLines: 5,
                                                  hintText: 'Nhập kế hoạch',
                                                  textInputAction:
                                                      TextInputAction.next,
                                                  hintTextStyle: UITextStyle
                                                      .body1Regular
                                                      .copyWith(
                                                    color: BaseColors
                                                        .textPlaceholder,
                                                  ),
                                                  onClear: () {
                                                    state.listPlan![index] = '';
                                                  },
                                                  onChanged: (value) {
                                                    state.listPlan![index] =
                                                        value;
                                                  },
                                                  controller:
                                                      TextEditingController(
                                                    text:
                                                        state.listPlan![index],
                                                  ),
                                                ),
                                              ),
                                              Positioned(
                                                top: 0,
                                                right: 0,
                                                child: InkWellWidget(
                                                  onTap: () {
                                                    ref
                                                        .read(
                                                            planDetailListProvider
                                                                .notifier)
                                                        .removePlan(
                                                            index: index);
                                                  },
                                                  child: MyAssets
                                                      .icons.iconCloseRed
                                                      .svg(),
                                                ),
                                              ),
                                            ],
                                          ),
                                  ),
                                ],
                              );
                            },
                          ),
                          Visibility(
                            visible: !(widget.arguments?.isView ?? false),
                            child: Padding(
                              padding: const EdgeInsets.only(
                                left: BaseSpacing.spacing4,
                                right: BaseSpacing.spacing4,
                                bottom: BaseSpacing.spacing4,
                              ),
                              child: BaseButton(
                                text: 'Thêm kế hoạch',
                                textColor: BaseColors.primary,
                                icon: MyAssets.icons.icAdd.svg(),
                                backgroundColor: BaseColors.primarySurface,
                                onTap: () {
                                  ref
                                      .read(planDetailListProvider.notifier)
                                      .addPlan();
                                },
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
                if (state.updateStatus == LoadStatus.loading) ...[
                  const Center(
                    child: LoadingIndicatorWidget(),
                  ),
                ],
              ],
            ),
    );
  }
}
