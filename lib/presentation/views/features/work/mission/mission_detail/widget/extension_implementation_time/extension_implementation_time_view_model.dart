import 'package:equatable/equatable.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

part 'extension_implementation_time_state.dart';

final extensionImplementationTimeProvider = StateNotifierProvider.autoDispose<
        ExtensionImplementationTimeViewModel, ExtensionImplementationTimeState>(
    (ref) => ExtensionImplementationTimeViewModel(ref: ref));

class ExtensionImplementationTimeViewModel
    extends StateNotifier<ExtensionImplementationTimeState> {
  final Ref ref;

  ExtensionImplementationTimeViewModel({
    required this.ref,
  }) : super(const ExtensionImplementationTimeState());

  void changeExtensionTime({
    DateTime? date,
  }) {
    state = state.copyWith(
      extensionTime: date,
    );
  }
}
