part of 'mission_detail_view_model.dart';

class MissionDetailState extends Equatable {
  final LoadStatus loadStatus;
  final LoadStatus updateStatus;
  final String? message;
  final MissionEntity? missionDetail;

  const MissionDetailState({
    this.loadStatus = LoadStatus.initial,
    this.updateStatus = LoadStatus.initial,
    this.message,
    this.missionDetail,
  });

  MissionDetailState copyWith({
    LoadStatus? loadStatus,
    LoadStatus? updateStatus,
    String? message,
    MissionEntity? missionDetail,
  }) {
    return MissionDetailState(
      loadStatus: loadStatus ?? this.loadStatus,
      updateStatus: updateStatus ?? this.updateStatus,
      message: message ?? this.message,
      missionDetail: missionDetail ?? this.missionDetail,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        updateStatus,
        message,
        missionDetail,
      ];
}
