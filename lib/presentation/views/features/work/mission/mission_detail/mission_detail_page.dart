import 'package:base_ui/base_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/domain/enums/mission/mission_kpi.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/domain/enums/mission/mission_status.dart';
import 'package:vcc/presentation/views/widgets/expandable.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/domain/enums/mission/mission_extension_status.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_detail/mission_detail_view_model.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_detail/widget/complete_mission/complete_mission.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_detail/widget/plan_detail_list/plan_detail_list.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_detail/widget/extension_implementation_time/extension_implementation_time.dart';

class MissionDetailArguments {
  final String? code;
  final Function()? onRefreshMission;

  MissionDetailArguments({
    this.code,
    this.onRefreshMission,
  });
}

class MissionDetailPage extends StatefulHookConsumerWidget {
  final MissionDetailArguments? arguments;

  const MissionDetailPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<MissionDetailPage> createState() => _MissionDetailPageState();
}

class _MissionDetailPageState extends ConsumerState<MissionDetailPage> {
  @override
  void initState() {
    Future(() async {
      ref
          .read(missionDetailProvider.notifier)
          .getData(widget.arguments?.code ?? '');
    });
    super.initState();
  }

  Future<void> onRefresh() async {
    ref
        .read(missionDetailProvider.notifier)
        .getData(widget.arguments?.code ?? '');
  }

  @override
  Widget build(BuildContext context) {
    return LayoutPage(
      appbar: const AppBarCustom(
        title: 'Chi tiết nhiệm vụ',
      ),
      body: buildBody(context),
      bottomAction: buildBottom(context),
    );
  }

  void onCompleteMission(BuildContext context) {
    final state = ref.watch(missionDetailProvider);
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: 'Hoàn thành nhiệm vụ',
      height: MediaQuery.of(context).size.height * 0.8,
      child: CompleteMission(
        completeMissionStatus: state.updateStatus,
        result: state.missionDetail?.result,
        listImage: List.from(
          state.missionDetail?.urls ?? [],
        ),
        onConfirm: ({
          String? result,
          List<String>? listImage,
        }) async {
          bool isSuccess =
              await ref.read(missionDetailProvider.notifier).completeMission(
                    code: widget.arguments?.code ?? '',
                    resultComplete: result,
                    imageUrls: listImage,
                  );
          if (!context.mounted) return;
          if (isSuccess) {
            AppDialog.showDialogCenter(
              context,
              message: 'Hoàn thành nhiệm vụ thành công.',
              barrierDismissible: false,
              status: DialogStatus.success,
              onConfirm: () {
                context.pop();
                onRefresh();
                widget.arguments?.onRefreshMission?.call();
              },
            );
          } else {
            AppDialog.showDialogCenter(
              context,
              message: state.message ?? 'Đã có lỗi xảy ra!',
              status: DialogStatus.error,
            );
          }
        },
      ),
    );
  }

  void onExtensionImplementationTime(BuildContext context) {
    final state = ref.watch(missionDetailProvider);
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: 'Gia hạn thời gian thực hiện',
      height: MediaQuery.of(context).size.height * 0.8,
      child: ExtensionImplementationTime(
        extensionImplementationTimeStatus: state.updateStatus,
        minExtensionTime: state.missionDetail?.endAt?.add(
          const Duration(days: 1),
        ),
        reason: state.missionDetail?.renewalStatus ==
                MissionExtensionStatusEnum.pending
            ? state.missionDetail?.pendingReason
            : null,
        extensionTime: state.missionDetail?.renewalStatus ==
                MissionExtensionStatusEnum.pending
            ? state.missionDetail?.pendingRenewalDate
            : null,
        onConfirm: ({
          String? reason,
          DateTime? extensionTime,
        }) async {
          bool isSuccess = await ref
              .read(missionDetailProvider.notifier)
              .extensionImplementationTime(
                code: widget.arguments?.code ?? '',
                renewalDate: extensionTime,
                renewalReason: reason,
              );
          if (!context.mounted) return;
          if (isSuccess) {
            AppDialog.showDialogCenter(
              context,
              message: 'Gia hạn thời gian thực hiện thành công.',
              barrierDismissible: false,
              status: DialogStatus.success,
              onConfirm: () {
                context.pop();
                onRefresh();
              },
            );
          }
        },
      ),
    );
  }

  Widget buildBottom(BuildContext context) {
    final state = ref.watch(missionDetailProvider);
    return Visibility(
      visible: state.loadStatus == LoadStatus.success &&
          state.missionDetail?.status == MissionStatus.inProgress,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: BaseSpacing.spacing2,
          horizontal: BaseSpacing.spacing4,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                bottom: BaseSpacing.spacing3,
              ),
              child: BaseButton(
                text: 'Hoàn thành',
                onTap: () {
                  if ((state.missionDetail?.hasPlan ?? false) &&
                      (state.missionDetail?.planQuantity ?? 0) < 1) {
                    AppDialog.showDialogCenter(
                      context,
                      message: 'Vui lòng thêm kế hoạch',
                      status: DialogStatus.error,
                    );
                    return;
                  }
                  onCompleteMission(context);
                },
              ),
            ),
            BaseButton(
              text: 'Gia hạn thời gian thực hiện',
              textColor: BaseColors.primary,
              backgroundColor: BaseColors.backgroundWhite,
              decoration: BoxDecoration(
                border: Border.all(
                  color: BaseColors.primary,
                ),
                borderRadius: BorderRadius.circular(
                  BaseSpacing.spacing3,
                ),
              ),
              onTap: () {
                onExtensionImplementationTime(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget buildBody(BuildContext context) {
    final state = ref.watch(missionDetailProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }
    if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: onRefresh,
      );
    }

    return RefreshIndicatorWidget(
      onRefresh: onRefresh,
      child: ListView(
        children: [
          buildStatus(),
          const DividerWidget(
            height: BaseSpacing.spacing2,
          ),
          buildInformation(),
          const DividerWidget(
            height: BaseSpacing.spacing2,
          ),
          buildImplementationPlan(),
          const DividerWidget(
            height: BaseSpacing.spacing2,
          ),
          if (state.missionDetail?.status != MissionStatus.inProgress) ...[
            buildImplementationResult(context),
            const DividerWidget(
              height: BaseSpacing.spacing2,
            ),
          ],
        ],
      ),
    );
  }

  Widget buildStatus() {
    final state = ref.watch(missionDetailProvider);
    String? fromDate = '';
    String? toDate = '';
    String? createDate = '';
    String? renewalDate = '';

    fromDate = state.missionDetail?.startAt != null
        ? state.missionDetail?.startAt?.display(
            format: DateTimeFormater.dateFormatVi,
          )
        : '';
    toDate = state.missionDetail?.endAt != null
        ? state.missionDetail?.endAt?.display(
            format: DateTimeFormater.dateFormatVi,
          )
        : '';
    createDate = state.missionDetail?.createdAt != null
        ? state.missionDetail?.createdAt?.display(
            format: DateTimeFormater.dateTimeFormatView,
          )
        : '';
    renewalDate = state.missionDetail?.renewalDate != null
        ? state.missionDetail?.renewalDate?.display(
            format: DateTimeFormater.dateFormatVi,
          )
        : '';
    return Container(
      padding: const EdgeInsets.all(
        BaseSpacing.spacing4,
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(
                        bottom: BaseSpacing.spacing2,
                      ),
                      child: Row(
                        children: [
                          Flexible(
                            child: Text(
                              state.missionDetail?.code ?? '',
                              style: UITextStyle.body2SemiBold,
                            ),
                          ),
                          const SizedBox(
                            width: BaseSpacing.spacing2,
                          ),
                          InkWellWidget(
                            onTap: () {
                              AppUtils.copyToClipboard(
                                state.missionDetail?.code ?? '',
                              );
                            },
                            child: MyAssets.icons.iconCopySmall.svg(),
                          ),
                        ],
                      ),
                    ),
                    Text(
                      'Ngày tạo: $createDate',
                      style: UITextStyle.caption1Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                    ),
                  ],
                ),
              ),
              Visibility(
                visible: state.missionDetail?.kpiType == MissionKpiEnum.overdue,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: BaseSpacing.spacing3,
                  ),
                  child: Row(
                    children: [
                      MyAssets.icons.iconWarningRedS16.svg(),
                      const SizedBox(
                        width: BaseSpacing.spacing1,
                      ),
                      Text(
                        state.missionDetail?.kpiType?.label ?? '',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.error,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
          const Padding(
            padding: EdgeInsets.symmetric(
              vertical: BaseSpacing.spacing3,
            ),
            child: DividerWidget(),
          ),
          Visibility(
            visible: state.missionDetail?.renewalStatus ==
                MissionExtensionStatusEnum.rejected,
            child: buildRejectInformation(
              title: 'Từ chối gia hạn',
              content: state.missionDetail?.renewalRejectReason,
            ),
          ),
          Visibility(
            visible: state.missionDetail?.closeStatus ==
                MissionExtensionStatusEnum.rejected,
            child: buildRejectInformation(
              title: 'Từ chối đóng nhiệm vụ',
              content: state.missionDetail?.closeRejectReason,
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing2,
            ),
            child: buildItemRow(
              title: 'Trạng thái',
              content: Text(
                state.missionDetail?.status?.label ?? '',
                style: UITextStyle.body2Regular.copyWith(
                  color: state.missionDetail?.status?.color,
                ),
              ),
            ),
          ),
          buildItemRow(
            title: 'Thời gian thực hiện',
            content: Text(
              '$fromDate - $toDate',
              style: UITextStyle.body2SemiBold.copyWith(
                color: BaseColors.textBody,
              ),
            ),
          ),
          Visibility(
            visible: state.missionDetail?.renewalDate != null,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing2,
              ),
              child: buildItemRow(
                title: 'Thời gian gia hạn',
                content: Text(
                  renewalDate ?? '',
                  style: UITextStyle.body2SemiBold.copyWith(
                    color: BaseColors.textBody,
                  ),
                ),
              ),
            ),
          ),
          Visibility(
            visible: (state.missionDetail?.renewalReason ?? '').isNotEmpty,
            child: buildItemRow(
              title: 'Lý do gia hạn',
              value: state.missionDetail?.renewalReason ?? '',
            ),
          ),
        ],
      ),
    );
  }

  Widget buildRejectInformation({
    String? title,
    String? content,
  }) {
    return Container(
      margin: const EdgeInsets.only(
        bottom: BaseSpacing.spacing3,
      ),
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
        horizontal: BaseSpacing.spacing3,
      ),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(
          BaseSpacing.spacing2,
        ),
        border: Border.all(
          color: BaseColors.info,
        ),
        color: BaseColors.infoSurface,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(
              top: BaseSpacing.spacingPx,
              right: BaseSpacing.spacing2,
            ),
            child: MyAssets.icons.iconInfoBlue.svg(),
          ),
          Expanded(
            child: RichText(
              text: TextSpan(
                text: title ?? '',
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
                children: <TextSpan>[
                  const TextSpan(
                    text: ' - ',
                  ),
                  TextSpan(
                    text: content ?? '',
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildInformation() {
    final state = ref.watch(missionDetailProvider);
    return ExpandablePanel(
      controller: ExpandableController(
        initialExpanded: true,
      ),
      theme: const ExpandableThemeData(
        headerAlignment: ExpandablePanelHeaderAlignment.center,
      ),
      collapsed: const SizedBox(),
      header: Padding(
        padding: const EdgeInsets.all(
          BaseSpacing.spacing4,
        ),
        child: Text(
          'Thông tin nhiệm vụ',
          style: UITextStyle.body1SemiBold.copyWith(
            color: BaseColors.textLabel,
          ),
        ),
      ),
      expanded: Column(
        children: [
          const Padding(
            padding: EdgeInsets.symmetric(
              horizontal: BaseSpacing.spacing4,
            ),
            child: DividerWidget(),
          ),
          Padding(
            padding: const EdgeInsets.all(
              BaseSpacing.spacing4,
            ),
            child: Column(
              children: [
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: BaseSpacing.spacing2,
                  ),
                  child: buildItemRow(
                    title: 'Tên nhiệm vụ',
                    value: state.missionDetail?.name ?? '',
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    bottom: BaseSpacing.spacing2,
                  ),
                  child: buildItemRow(
                    title: 'Chi tiết nhiệm vụ',
                    value: state.missionDetail?.detail ?? '',
                  ),
                ),
                buildItemRow(
                  title: 'Loại nhiệm vụ',
                  value: state.missionDetail?.type?.label ?? '',
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildImplementationPlan() {
    final state = ref.watch(missionDetailProvider);
    bool isView = [
      MissionStatus.closed,
      MissionStatus.canceled,
    ].contains(state.missionDetail?.status);

    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
        horizontal: BaseSpacing.spacing4,
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  'Kế hoạch thực hiện',
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              InkWellWidget(
                onTap: () {
                  context.push(
                    RouterPaths.planDetailList,
                    extra: PlanDetailListArguments(
                      isView: isView,
                      onRefreshDetail: onRefresh,
                      code: widget.arguments?.code,
                      hasPlan: state.missionDetail?.hasPlan ?? false,
                      listPlan: List.from(state.missionDetail?.taskPlans ?? []),
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: BaseSpacing.spacing2,
                  ),
                  child: Text(
                    isView ? 'XEM' : 'CẬP NHẬT',
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ),
              )
            ],
          ),
          const Padding(
            padding: EdgeInsets.only(
              top: BaseSpacing.spacing2,
              bottom: BaseSpacing.spacing4,
            ),
            child: DividerWidget(),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing2,
            ),
            child: buildItemRow(
              title: 'Loại kế hoạch',
              value: (state.missionDetail?.hasPlan ?? false) ? 'Có' : 'Không',
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing2,
            ),
            child: buildItemRow(
              title: 'Số kế hoạch',
              value: '${state.missionDetail?.planQuantity ?? ''}',
            ),
          ),
        ],
      ),
    );
  }

  Widget buildImplementationResult(BuildContext context) {
    final state = ref.watch(missionDetailProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
        horizontal: BaseSpacing.spacing4,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              Expanded(
                child: Text(
                  'Kết quả thực hiện',
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              Visibility(
                visible: state.missionDetail?.status == MissionStatus.completed,
                child: InkWellWidget(
                  onTap: () {
                    onCompleteMission(context);
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: BaseSpacing.spacing2,
                    ),
                    child: Text(
                      'CẬP NHẬT',
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.primary,
                      ),
                    ),
                  ),
                ),
              )
            ],
          ),
          const Padding(
            padding: EdgeInsets.only(
              top: BaseSpacing.spacing2,
              bottom: BaseSpacing.spacing4,
            ),
            child: DividerWidget(),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing2,
            ),
            child: buildItemRow(
              title: 'Kết quả',
              value: state.missionDetail?.result ?? '',
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing2,
            ),
            child: buildItemRow(
              title: 'Ảnh kết quả',
            ),
          ),
          Container(
            height: (state.missionDetail?.urls ?? []).isNotEmpty
                ? BaseSpacing.spacing20
                : 0,
            padding: const EdgeInsets.only(
              bottom: BaseSpacing.spacing2,
            ),
            child: ListView.separated(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount: (state.missionDetail?.urls ?? []).length,
              separatorBuilder: (_, __) => const SizedBox(
                width: BaseSpacing.spacing2,
              ),
              itemBuilder: (context, index) {
                return Container(
                  width: BaseSpacing.spacing20,
                  height: BaseSpacing.spacing20,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                  ),
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(
                      BaseSpacing.spacing2,
                    ),
                    child: ImageWidget(
                      enableShowPreview: true,
                      state.missionDetail?.urls?[index] ?? '',
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget buildItemRow({
    String? title,
    String? value,
    Widget? content,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Expanded(
          flex: 2,
          child: Text(
            '$title: ',
            style: UITextStyle.body2Regular.copyWith(
              color: BaseColors.textSubtitle,
            ),
          ),
        ),
        Expanded(
          flex: 3,
          child: content ??
              Text(
                value ?? '',
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
        ),
      ],
    );
  }
}
