part of 'mission_view_model.dart';

//ignore: must_be_immutable
class MissionState extends Equatable {
  final LoadStatus loadStatus;
  final String? message;
  final List<MissionEntity>? listMission;
  final MissionStatus? statusSelected;
  final String? keySearch;
  DateTime? startDate;
  DateTime? endDate;
  List<MissionTypeEnum>? missionType;
  List<MissionKpiEnum>? missionKpi;
  final int allTotalResult;
  final int allCurrentPage;
  final Map<String, int>? statusCountStatistics;

  MissionState({
    this.loadStatus = LoadStatus.initial,
    this.message,
    this.listMission,
    this.statusSelected = MissionStatus.all,
    this.keySearch,
    this.startDate,
    this.endDate,
    this.missionType,
    this.missionKpi,
    this.allTotalResult = 0,
    this.allCurrentPage = 0,
    this.statusCountStatistics,
  });

  MissionState copyWith({
    LoadStatus? loadStatus,
    String? message,
    List<MissionEntity>? listMission,
    MissionStatus? statusSelected,
    String? keySearch,
    DateTime? startDate,
    DateTime? endDate,
    List<MissionTypeEnum>? missionType,
    List<MissionKpiEnum>? missionKpi,
    int? allTotalResult,
    int? allCurrentPage,
    Map<String, int>? statusCountStatistics,
  }) {
    return MissionState(
      loadStatus: loadStatus ?? this.loadStatus,
      message: message ?? this.message,
      listMission: listMission ?? this.listMission,
      statusSelected: statusSelected ?? this.statusSelected,
      keySearch: keySearch ?? this.keySearch,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      missionType: missionType ?? this.missionType,
      missionKpi: missionKpi ?? this.missionKpi,
      allTotalResult: allTotalResult ?? this.allTotalResult,
      allCurrentPage: allCurrentPage ?? this.allCurrentPage,
      statusCountStatistics:
          statusCountStatistics ?? this.statusCountStatistics,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        message,
        listMission,
        statusSelected,
        keySearch,
        startDate,
        endDate,
        missionType,
        missionKpi,
        allTotalResult,
        allCurrentPage,
        statusCountStatistics,
      ];
}
