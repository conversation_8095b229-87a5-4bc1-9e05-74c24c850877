import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/domain/enums/mission/mission_kpi.dart';
import 'package:vcc/domain/entities/mission/mission_entity.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';

class BuildMissionItem extends StatelessWidget {
  final Function()? onTap;
  final MissionEntity item;

  const BuildMissionItem({
    super.key,
    this.onTap,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    String? fromDate = '';
    String? toDate = '';
    String? createDate = '';

    fromDate = item.startAt != null
        ? item.startAt?.display(
            format: DateTimeFormater.dayMonthFormat,
          )
        : '';
    toDate = item.endAt != null
        ? item.endAt?.display(
            format: DateTimeFormater.dayMonthFormat,
          )
        : '';
    createDate = item.createdAt != null
        ? item.createdAt?.display(
            format: DateTimeFormater.dateTimeFormatView,
          )
        : '';

    return Column(
      children: [
        const DividerWidget(
          height: BaseSpacing.spacing2,
        ),
        Container(
          color: BaseColors.backgroundWhite,
          padding: const EdgeInsets.all(
            BaseSpacing.spacing4,
          ),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(
                            bottom: BaseSpacing.spacing2,
                          ),
                          child: Row(
                            children: [
                              Flexible(
                                child: Text(
                                  item.code ?? '',
                                  style: UITextStyle.body2SemiBold,
                                ),
                              ),
                              const SizedBox(
                                width: BaseSpacing.spacing2,
                              ),
                              InkWellWidget(
                                onTap: () {
                                  AppUtils.copyToClipboard(
                                    item.code ?? '',
                                  );
                                },
                                child: MyAssets.icons.iconCopySmall.svg(),
                              ),
                            ],
                          ),
                        ),
                        Text(
                          'Ngày tạo: $createDate',
                          style: UITextStyle.caption1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: item.kpiType == MissionKpiEnum.overdue,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: BaseSpacing.spacing3,
                      ),
                      child: Row(
                        children: [
                          MyAssets.icons.iconWarningRedS16.svg(),
                          const SizedBox(
                            width: BaseSpacing.spacing1,
                          ),
                          Text(
                            item.kpiType?.label ?? '',
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.error,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: BaseSpacing.spacing4,
              ),
              InkWellWidget(
                onTap: onTap,
                child: Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(
                        BaseSpacing.spacing6,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        color: BaseColors.backgroundGray,
                        border: Border.all(
                          color: BaseColors.borderDivider,
                        ),
                      ),
                      child: Center(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Text(
                              fromDate ?? '',
                              style: UITextStyle.body1SemiBold,
                            ),
                            Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: BaseSpacing.spacing2,
                              ),
                              child: Text(
                                'đến',
                                style: UITextStyle.body2Regular
                                    .copyWith(color: BaseColors.textSubtitle),
                              ),
                            ),
                            Text(
                              toDate ?? '',
                              style: UITextStyle.body1SemiBold,
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: BaseSpacing.spacing2,
                    ),
                    Expanded(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          _buildStatusItemRow(
                            icon: MyAssets.icons.circle.svg(),
                            value: Text(
                              item.status?.label ?? '',
                              style: UITextStyle.body2Regular.copyWith(
                                color: item.status?.color,
                              ),
                            ),
                          ),
                          _buildItemRow(
                            maxLineValue: 2,
                            value: item.name ?? '',
                            icon: MyAssets.icons.iconAssignment.svg(),
                          ),
                          _buildItemRow(
                            value: item.type?.label ?? '',
                            icon: MyAssets.icons.iconPlan.svg(),
                          ),
                          // _buildItemRow(
                          //   value: '$fromDate - $toDate',
                          //   icon: MyAssets.icons.iconCalendarS16.svg(),
                          // ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildItemRow({
    required Widget icon,
    required String value,
    int maxLineValue = 1,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing15s,
      ),
      child: Row(
        children: <Widget>[
          icon,
          const SizedBox(
            width: BaseSpacing.spacing2,
          ),
          Flexible(
            child: Text(
              value,
              style: UITextStyle.bodyText2,
              maxLines: maxLineValue,
              overflow: TextOverflow.ellipsis,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatusItemRow({
    required Widget icon,
    required Widget value,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing15s,
      ),
      child: Row(
        children: <Widget>[
          icon,
          const SizedBox(
            width: BaseSpacing.spacing2,
          ),
          Flexible(
            child: value,
          ),
        ],
      ),
    );
  }
}
