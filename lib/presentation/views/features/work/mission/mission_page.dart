import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/domain/enums/mission/mission_status.dart';
import 'package:vcc/utils/deBouncer.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_view_model.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_mission/filter_mission_view.dart';
import 'package:vcc/presentation/views/features/work/mission/widget/build_mission_item.dart';
import 'package:vcc/presentation/views/features/work/mission/mission_detail/mission_detail_page.dart';

class MissionPage extends StatefulHookConsumerWidget {
  const MissionPage({
    super.key,
  });

  @override
  ConsumerState<MissionPage> createState() => _MissionPageState();
}

class _MissionPageState extends ConsumerState<MissionPage>
    with TickerProviderStateMixin {
  late ScrollController scrollController;
  late TextEditingController searchController;
  late Debounce<String> deBouncer;

  @override
  void initState() {
    searchController = TextEditingController();
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    deBouncer = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref
            .read(missionProvider.notifier)
            .changeKeySearch(keySearch: value ?? '');
      },
    );
    Future(() async {
      ref.read(missionProvider.notifier).getData();
    });
    super.initState();
  }

  Future<void> onRefresh() async {
    ref.read(missionProvider.notifier).getData();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(missionProvider.notifier).fetchNextData();
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(missionProvider);
    return LayoutPage(
      appbar: const AppBarCustom(
        title: 'Nhiệm vụ',
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: IgnorePointer(
          ignoring: state.loadStatus == LoadStatus.loading,
          child: Column(
            children: [
              buildStatus(),
              buildSearchZone(),
              Expanded(
                child: buildBody(),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildStatus() {
    final state = ref.watch(missionProvider);
    return SizedBox(
      height: 45,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: listStatus.length,
        itemBuilder: (context, index) {
          final item = listStatus[index];
          bool isSelected = state.statusSelected == item;
          return IntrinsicWidth(
            child: InkWellWidget(
              onTap: () {
                ref.read(missionProvider.notifier).changeMissionStatus(item);
              },
              child: Column(
                mainAxisSize: MainAxisSize.min,
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: BaseSpacing.spacing3,
                      horizontal: BaseSpacing.spacing4,
                    ),
                    child: Row(
                      children: <Widget>[
                        Padding(
                          padding: const EdgeInsets.only(
                            right: BaseSpacing.spacing1,
                          ),
                          child: Text(
                            item.label,
                            style: UITextStyle.body2Regular.copyWith(
                              color: isSelected
                                  ? BaseColors.primary
                                  : BaseColors.textBody,
                            ),
                          ),
                        ),
                        Visibility(
                          visible: [
                            MissionStatus.inProgress,
                            MissionStatus.completed,
                          ].contains(item),
                          child: Container(
                            height: BaseSpacing.spacing4,
                            padding: const EdgeInsets.symmetric(
                              horizontal: BaseSpacing.spacing1,
                            ),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8),
                              color: isSelected
                                  ? BaseColors.primary
                                  : BaseColors.secondary,
                            ),
                            child: Center(
                              child: Text(
                                '${state.statusCountStatistics?[item.count] ?? 0}',
                                style: UITextStyle.caption1Medium.copyWith(
                                  color: BaseColors.backgroundWhite,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Container(
                    height: 1,
                    width: double.infinity,
                    color: isSelected
                        ? BaseColors.primary
                        : BaseColors.borderDivider,
                  ),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget buildSearchZone() {
    var state = ref.watch(missionProvider);
    bool activeFilter = state.startDate != null ||
        state.endDate != null ||
        state.missionType != null ||
        state.missionKpi != null;
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: BaseSpacing.spacing2,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: BaseSpacing.spacing4,
            ),
            child: Row(
              children: [
                Expanded(
                  child: SearchTextFieldWidget(
                    controller: searchController,
                    hintText: 'Tên/mã nhiệm vụ',
                    onChanged: (value) {
                      deBouncer.value = value;
                    },
                  ),
                ),
                const SizedBox(
                  width: BaseSpacing.spacing4,
                ),
                InkWellWidget(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    var state = ref.watch(missionProvider);
                    AppBottomSheet.showNormalBottomSheet(
                      context,
                      title: "Bộ lọc",
                      height: MediaQuery.of(context).size.height * 0.8,
                      child: FilterMissionViewSheet(
                        startDate: state.startDate,
                        endDate: state.endDate,
                        missionTypeSelected: state.missionType,
                        missionKpiSelected: state.missionKpi,
                        onApply: (FilterArgumentCallback value) {
                          ref.read(missionProvider.notifier).changeFilter(
                                startDate: value.startDate,
                                endDate: value.endDate,
                                missionType: value.missionTypeSelected,
                                missionKpi: value.missionKpiSelected,
                              );
                        },
                      ),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: BaseSpacing.spacing2,
                    ),
                    child: Row(
                      children: [
                        activeFilter
                            ? MyAssets.icons.iconFilterActiveS24.svg()
                            : MyAssets.icons.filter.svg(),
                        Text(
                          'Lọc',
                          style: UITextStyle.caption1Medium.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildBody() {
    var state = ref.watch(missionProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const LoadingIndicatorWidget();
    }
    if (state.loadStatus == LoadStatus.failure ||
        (state.listMission ?? []).isEmpty) {
      return EmptyListWidget(
        title: 'Không có nhiệm vụ nào',
        onRefresh: onRefresh,
      );
    }

    return RefreshIndicatorWidget(
      onRefresh: onRefresh,
      child: ListView.builder(
        shrinkWrap: true,
        controller: scrollController,
        itemCount: state.listMission?.length,
        physics: const AlwaysScrollableScrollPhysics(),
        itemBuilder: (context, index) {
          return BuildMissionItem(
            item: state.listMission![index],
            onTap: () {
              context.push(
                RouterPaths.missionDetail,
                extra: MissionDetailArguments(
                  onRefreshMission: onRefresh,
                  code: state.listMission![index].code,
                ),
              );
            },
          );
        },
      ),
    );
  }
}
