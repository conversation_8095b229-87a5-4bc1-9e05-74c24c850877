import 'package:vcc/di/locator.dart';
import 'package:equatable/equatable.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/mission/mission_kpi.dart';
import 'package:vcc/domain/enums/mission/mission_type.dart';
import 'package:vcc/domain/enums/mission/mission_status.dart';
import 'package:vcc/data/repositories/mission_repository.dart';
import 'package:vcc/domain/entities/mission/mission_entity.dart';

part 'mission_state.dart';

final missionProvider =
    StateNotifierProvider.autoDispose<MissionViewModel, MissionState>(
  (ref) => MissionViewModel(ref: ref),
);

List<MissionStatus> listStatus = [
  MissionStatus.all,
  MissionStatus.inProgress,
  MissionStatus.completed,
  MissionStatus.closed,
  MissionStatus.canceled,
];

class MissionViewModel extends StateNotifier<MissionState> {
  final Ref ref;

  MissionViewModel({
    required this.ref,
  }) : super(MissionState());

  void changeMissionStatus(MissionStatus value) {
    state = state.copyWith(
      statusSelected: value,
    );
    getData();
  }

  void changeKeySearch({String? keySearch}) {
    state = state.copyWith(
      keySearch: keySearch?.trim(),
    );
    getData();
  }

  void changeFilter({
    DateTime? startDate,
    DateTime? endDate,
    List<MissionTypeEnum>? missionType,
    List<MissionKpiEnum>? missionKpi,
  }) {
    if (startDate == null) {
      state.startDate = null;
    }
    if (endDate == null) {
      state.endDate = null;
    }
    if (missionType == null) {
      state.missionType = null;
    }
    if (missionKpi == null) {
      state.missionKpi = null;
    }

    state = state.copyWith(
      startDate: startDate,
      endDate: endDate,
      missionType: missionType,
      missionKpi: missionKpi,
    );
    getData();
  }

  Future<void> getData() async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      allCurrentPage: BaseConstant.page,
    );
    try {
      final result = await appLocator<MissionRepository>().getListMission(
        keyword: state.keySearch,
        status: state.statusSelected == MissionStatus.all
            ? []
            : [state.statusSelected?.keyToServer ?? ''],
        kpiTypes: state.missionKpi?.map((kpi) => kpi.keyToServer).toList(),
        types: state.missionType?.map((type) => type.keyToServer).toList(),
        fromDate: state.startDate,
        toDate: state.endDate,
        page: BaseConstant.page,
        size: BaseConstant.size,
      );
      await result?.when(
        success: (data) async {
          getCountMission();
          state = state.copyWith(
            listMission: data.data,
            allTotalResult: data.total,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> fetchNextData() async {
    if (state.listMission!.length >= state.allTotalResult) {
      return;
    }
    if (state.loadStatus != LoadStatus.success) {
      return;
    }
    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );
    try {
      final result = await appLocator<MissionRepository>().getListMission(
        keyword: state.keySearch,
        status: state.statusSelected == MissionStatus.all
            ? []
            : [state.statusSelected?.keyToServer ?? ''],
        kpiTypes: state.missionKpi?.map((kpi) => kpi.keyToServer).toList(),
        types: state.missionType?.map((type) => type.keyToServer).toList(),
        fromDate: state.startDate,
        toDate: state.endDate,
        page: state.allCurrentPage + 1,
        size: BaseConstant.size,
      );
      await result?.when(
        success: (data) async {
          getCountMission();
          state = state.copyWith(
            listMission: state.listMission! + (data.data ?? []),
            allTotalResult: data.total,
            allCurrentPage: state.allCurrentPage + 1,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void getCountMission() async {
    try {
      final result = await appLocator<MissionRepository>().getCountMission(
        keyword: state.keySearch,
        kpiTypes: state.missionKpi?.map((kpi) => kpi.keyToServer).toList(),
        types: state.missionType?.map((type) => type.keyToServer).toList(),
        fromDate: state.startDate,
        toDate: state.endDate,
      );
      await result.when(
        success: (data) async {
          state = state.copyWith(
            statusCountStatistics: data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
    }
  }
}
