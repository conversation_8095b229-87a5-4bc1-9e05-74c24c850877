import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/stock_type.dart';
import 'package:vcc/domain/responses/list_data_response.dart';
import 'package:vcc/utils/log_utils.dart';

part 'add_supply_state.dart';

final addSupplyProvider =
    StateNotifierProvider.autoDispose<AddSupplyViewModel, AddSupplyState>(
  (ref) => AddSupplyViewModel(ref: ref),
);

class AddSupplyViewModel extends StateNotifier<AddSupplyState> {
  final Ref ref;

  AddSupplyViewModel({
    required this.ref,
  }) : super(const AddSupplyState());

  void initData({
    List<SupplyEntity>? supplies,
    String? stockType,
  }) async {
    state = state.copyWith(
      stockType: stockType,
    );

    await getListSupply();

    final listSupplies = state.supplyRequest ?? [];

    if (supplies != null) {
      for (var element in supplies) {
        final index = listSupplies.indexWhere((e) => e.code == element.code);
        if (index != -1) {
          listSupplies[index].isSelected = true;
        }
      }
    }

    state = state.copyWith(
      supplyRequest: listSupplies,
    );
  }

  void changeSearchStatus(bool value) {
    state = state.copyWith(
      isSearching: value,
    );
  }

  void selectSupply({
    required int index,
  }) {
    List<SupplyEntity> listSupplies = state.supplyRequest ?? [];
    final supply = listSupplies[index];

    if (supply.isSelected) {
      supply.isSelected = false;
    } else {
      supply.isSelected = true;
    }

    state = state.copyWith(
      supplyRequest: listSupplies,
    );
  }

  Future<void> getListSupply({
    String? keySearch,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      BaseResult<ArrayResponse<SupplyEntity>>? resultParam;

      if (state.stockType == StockType.retail.keyToServer) {
        resultParam = await appLocator<OrderRepository>().getListSupplyDistrict(
          keyword: keySearch,
          stockType: state.stockType,
        );
      } else {
        resultParam = await appLocator<OrderRepository>().getListSupply(
          keyword: keySearch,
          stockType: state.stockType,
        );
      }

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            supplyRequest: data.data,
            totalResult: data.total ?? 0,
            keySearch: keySearch,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> fetchNextSupply() async {
    if (state.supplyRequest!.length >= state.totalResult) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      BaseResult<ArrayResponse<SupplyEntity>>? resultParam;

      if (state.stockType == StockType.retail.keyToServer) {
        resultParam = await appLocator<OrderRepository>().getListSupplyDistrict(
          keyword: state.keySearch,
          stockType: state.stockType,
          page: state.currentPage + 1,
        );
      } else {
        resultParam = await appLocator<OrderRepository>().getListSupply(
          keyword: state.keySearch,
          stockType: state.stockType,
          page: state.currentPage + 1,
        );
      }

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            currentPage: state.currentPage + 1,
            supplyRequest: state.supplyRequest! + (data.data ?? []),
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          LogUtils.d(err.message);
        },
      );
    } catch (error) {
      LogUtils.d("Lỗi lấy về danh sách vật tư");
    }
  }
}
