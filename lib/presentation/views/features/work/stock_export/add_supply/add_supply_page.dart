import 'package:base_ui/base_ui.dart';
import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_search.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/utils/debouncer.dart';

import 'add_supply_view_model.dart';

class AddSupplyArguments {
  final String? stockType;
  final List<SupplyEntity>? supplyRequest;

  AddSupplyArguments({
    this.supplyRequest,
    this.stockType,
  });
}

class AddSupplyPage extends StatefulHookConsumerWidget {
  final AddSupplyArguments? arguments;

  const AddSupplyPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<AddSupplyPage> createState() => _AddSupplyPageState();
}

class _AddSupplyPageState extends ConsumerState<AddSupplyPage> {
  late TextEditingController searchController;
  late ScrollController scrollController;
  late Debounce<String> debouncer;

  @override
  void initState() {
    searchController = TextEditingController();
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    Future(() {
      ref.read(addSupplyProvider.notifier).initData(
            supplies: widget.arguments?.supplyRequest,
            stockType: widget.arguments?.stockType,
          );
    });
    debouncer = Debounce<String>(const Duration(milliseconds: 500), (value) {
      ref.read(addSupplyProvider.notifier).getListSupply(
            keySearch: value,
          );
    });

    super.initState();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(addSupplyProvider.notifier).fetchNextSupply();
    }
  }

  Future<void> refreshData() async {
    ref.read(addSupplyProvider.notifier).getListSupply();
  }

  @override
  void dispose() {
    searchController.dispose();
    scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var addSupplyState = ref.watch(addSupplyProvider);
    PreferredSizeWidget appbarWidget;

    if (addSupplyState.isSearching) {
      appbarWidget = AppBarSearch(
        hintText: "Tìm theo tên/mã vật tư",
        backFunction: () {
          ref.read(addSupplyProvider.notifier).changeSearchStatus(false);
          refreshData();
        },
        onChanged: (value) {
          debouncer.value = value;
        },
      );
    } else {
      appbarWidget = AppBarCustom(
        title: "Thêm vật tư",
        actionWidget: [
          InkWellWidget(
            onTap: () {
              ref.read(addSupplyProvider.notifier).changeSearchStatus(true);
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              child: MyAssets.icons.iconSearchS24.svg(),
            ),
          ),
        ],
      );
    }

    return LayoutPage(
      appbar: appbarWidget,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildListSupply(),
      ),
      bottomAction: _buildBottomBar(),
    );
  }

  Widget _buildListSupply() {
    var state = ref.watch(addSupplyProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      if ((state.supplyRequest ?? []).isEmpty) {
        return EmptyListWidget(
          title: 'Không có dữ liệu',
          onRefresh: refreshData,
        );
      }

      return ListView.separated(
        controller: scrollController,
        itemCount: state.supplyRequest?.length ?? 0,
        padding: const EdgeInsets.only(top: 4),
        separatorBuilder: (_, __) => DividerWidget(
          height: 4,
          color: BaseColors.backgroundGray,
        ),
        itemBuilder: (context, index) {
          var item = state.supplyRequest![index];

          return Container(
            padding: const EdgeInsets.fromLTRB(8, 8, 16, 8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                InkWellWidget(
                  onTap: () {
                    ref.read(addSupplyProvider.notifier).selectSupply(
                          index: index,
                        );
                  },
                  child: Padding(
                    padding: const EdgeInsets.all(8),
                    child: item.isSelected
                        ? MyAssets.icons.iconChecked.svg()
                        : MyAssets.icons.iconCheckboxS24.svg(),
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      const SizedBox(height: 8),
                      Text(
                        item.name ?? '',
                        style: UITextStyle.bodyText2,
                      ),
                      const SizedBox(height: 6),
                      RichText(
                        text: TextSpan(
                          text: 'Mã: ',
                          style: UITextStyle.caption1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                          children: <TextSpan>[
                            TextSpan(
                              text: item.code ?? '',
                              style: UITextStyle.caption1Medium.copyWith(
                                color: BaseColors.textBody,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(height: 4),
                      RichText(
                        text: TextSpan(
                          text: 'Đơn vị: ',
                          style: UITextStyle.caption1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                          children: <TextSpan>[
                            TextSpan(
                              text: item.unit ?? '',
                              style: UITextStyle.caption1Medium.copyWith(
                                color: BaseColors.textBody,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                )
              ],
            ),
          );
        },
      );
    }
  }

  Widget _buildBottomBar() {
    return SafeArea(
      top: false,
      child: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          boxShadow: AppBoxShadows.shadowNormal,
        ),
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: BaseButton(
          text: "Xác nhận",
          onTap: () {
            List<SupplyEntity> listSupplySelected = [];
            final listSupply = ref.watch(addSupplyProvider).supplyRequest;

            for (int i = 0; i < listSupply!.length; i++) {
              if (listSupply[i].isSelected) {
                listSupplySelected.add(listSupply[i]);
              }
            }

            if (listSupplySelected.isEmpty) {
              AppDialog.showDialogInfo(
                context,
                title: 'Thông báo',
                message: 'Vui lòng chọn ít nhất 1 vật tư',
              );
            }

            context.pop(listSupplySelected);
          },
        ),
      ),
    );
  }
}
