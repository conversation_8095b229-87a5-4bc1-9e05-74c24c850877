import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/enums/data_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/work/stock_export/add_supply/add_supply_page.dart';
import 'package:vcc/presentation/views/features/work/stock_export/request_branch_warehouse/request_branch_warehouse_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/plus_and_minus_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';

class RequestBranchWarehouseArguments {
  final DetailOrderEntity? order;
  final List<SupplyEntity>? listSupplySelected;

  RequestBranchWarehouseArguments({
    this.order,
    this.listSupplySelected,
  });
}

class RequestBranchWarehousePage extends StatefulHookConsumerWidget {
  final RequestBranchWarehouseArguments arguments;

  const RequestBranchWarehousePage({
    super.key,
    required this.arguments,
  });

  @override
  ConsumerState<RequestBranchWarehousePage> createState() =>
      _RequestBranchWarehousePageState();
}

class _RequestBranchWarehousePageState
    extends ConsumerState<RequestBranchWarehousePage> {
  late TextEditingController noteController;

  @override
  void initState() {
    noteController = TextEditingController();
    Future(() {
      ref.read(requestBranchWarehouseProvider.notifier).initData(
            widget.arguments.listSupplySelected ?? [],
            order: widget.arguments.order,
          );
    });

    super.initState();
  }

  Future<void> refreshData() async {}

  @override
  dispose() {
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutPage(
      backgroundColor: BaseColors.backgroundWhite,
      appbar: const AppBarCustom(
        title: "Xin hàng DVKT",
      ),
      bottomAction: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: BaseButton(
            text: "Tạo phiếu",
            onTap: () async {
              var state = ref.watch(requestBranchWarehouseProvider);

              if (noteController.text.isEmpty) {
                AppDialog.showDialogCenter(
                  context,
                  message: "Vui lòng nhập ghi chú",
                  status: DialogStatus.error,
                );
                return;
              }

              if (state.warehouseSelected?.stockType == null) {
                AppDialog.showDialogCenter(
                  context,
                  message: "Vui lòng chọn kho xuất",
                  status: DialogStatus.error,
                );
                return;
              }

              await ref
                  .read(requestBranchWarehouseProvider.notifier)
                  .createRequest(
                    note: noteController.text,
                  );

              if (!context.mounted) return;
              final isCreateSuccess = ref
                      .watch(requestBranchWarehouseProvider)
                      .createRequestStatus ==
                  LoadStatus.success;

              if (isCreateSuccess) {
                context.pop();
                AppDialog.showDialogCenter(
                  context,
                  message: "Tạo phiếu xin hàng DVKT thành công!",
                  status: DialogStatus.success,
                );
              }
            },
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildPage(),
      ),
    );
  }

  Widget _buildPage() {
    var state = ref.watch(requestBranchWarehouseProvider);

    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      return Stack(
        children: [
          ListView(
            padding: const EdgeInsets.only(top: 16),
            children: <Widget>[
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    DropdownWidget(
                      labelText: "Kho xuất",
                      isRequired: true,
                      enabled:
                          !(widget.arguments.order?.isOrderPartner ?? false),
                      suffix: MyAssets.icons.arrowDown.svg(),
                      content: state.warehouseSelected?.name ?? "",
                      onTap: onExportWarehouseSelected,
                    ),
                    const SizedBox(height: 16),
                    TextFieldWidget.area(
                      controller: noteController,
                      maxLines: 4,
                      maxLength: 2000,
                      hintText: "Ghi chú",
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(
                          RegExp(
                            Patterns.note,
                            multiLine: true,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                  ],
                ),
              ),
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.only(
                        top: 10,
                        bottom: 10,
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Text(
                              "Danh sách vật tư",
                              style: UITextStyle.body1SemiBold.copyWith(
                                color: BaseColors.textTitle,
                              ),
                            ),
                          ),
                          const SizedBox(width: 16),
                          AppTextButton(
                            title: "Thêm vật tư",
                            iconLeft: MyAssets.icons.iconAddCircle.svg(),
                            onTap: () async {
                              if (state.warehouseSelected?.stockType == null) {
                                AppDialog.showDialogCenter(
                                  context,
                                  message: "Vui lòng chọn kho xuất",
                                  status: DialogStatus.error,
                                );
                                return;
                              }

                              final result = await context.push(
                                RouterPaths.addSupply,
                                extra: AddSupplyArguments(
                                  supplyRequest: state.listSupply,
                                  stockType: state.warehouseSelected?.stockType,
                                ),
                              );

                              if (result is List<SupplyEntity>) {
                                ref
                                    .read(
                                        requestBranchWarehouseProvider.notifier)
                                    .selectSupply(result);
                              }
                            },
                          ),
                        ],
                      ),
                    ),
                    const DividerWidget(),
                    ListView.separated(
                      itemCount: state.listSupply?.length ?? 0,
                      shrinkWrap: true,
                      physics: const NeverScrollableScrollPhysics(),
                      padding: EdgeInsets.zero,
                      separatorBuilder: (_, __) {
                        return const DividerWidget();
                      },
                      itemBuilder: (context, index) {
                        final item = state.listSupply![index];

                        return _buildSupplyItem(
                          item: item,
                          index: index,
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
          Visibility(
            visible: state.createRequestStatus == LoadStatus.loading,
            child: const Center(
              child: LoadingIndicatorWidget(),
            ),
          ),
        ],
      );
    }
  }

  Widget _buildSupplyItem({
    required SupplyEntity item,
    required int index,
  }) {
    return Container(
      padding: const EdgeInsets.symmetric(
        vertical: 16,
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          MyAssets.icons.iconSupply.svg(),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  item.name ?? '',
                  style: UITextStyle.body2Medium,
                ),
                const SizedBox(height: 6),
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          RichText(
                            text: TextSpan(
                              text: 'Mã: ',
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                              children: <TextSpan>[
                                TextSpan(
                                  text: item.code ?? "",
                                  style: UITextStyle.caption1Medium.copyWith(
                                    color: BaseColors.textBody,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          const SizedBox(height: 4),
                          RichText(
                            text: TextSpan(
                              text: 'Đơn vị: ',
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                              children: <TextSpan>[
                                TextSpan(
                                  text: item.unit ?? "",
                                  style: UITextStyle.caption1Medium.copyWith(
                                    color: BaseColors.textBody,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 16),
                    PlusAndMinusWidget(
                      quantity: item.quantity,
                      minQuantity: item.minItem,
                      maxQuantity: item.maxItem,
                      step: item.step,
                      dataType: DataType.double,
                      onMinus: (value) {
                        if (value == 0) {
                          ref
                              .read(requestBranchWarehouseProvider.notifier)
                              .removeSupply(index: index);
                        } else {
                          ref
                              .read(requestBranchWarehouseProvider.notifier)
                              .changeSupplyQuantity(
                                index: index,
                                quantity: value,
                              );
                        }
                      },
                      onPlus: (value) {
                        ref
                            .read(requestBranchWarehouseProvider.notifier)
                            .changeSupplyQuantity(
                              index: index,
                              quantity: value,
                            );
                      },
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void onExportWarehouseSelected() async {
    var state = ref.watch(requestBranchWarehouseProvider);

    await AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Chọn kho xuất",
      height: MediaQuery.of(context).size.height * 0.6,
      child: ListView.builder(
        itemCount: state.warehouse?.length ?? 0,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        padding: EdgeInsets.zero,
        itemBuilder: (context, index) {
          final item = state.warehouse![index];

          return InkWellWidget(
            onTap: () {
              ref
                  .read(requestBranchWarehouseProvider.notifier)
                  .selectWarehouse(item);

              Navigator.pop(context);
            },
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Padding(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 16,
                  ),
                  child: Text(
                    item.name ?? "",
                    style: UITextStyle.body2Regular,
                  ),
                ),
                const DividerWidget(),
              ],
            ),
          );
        },
      ),
    );
  }
}
