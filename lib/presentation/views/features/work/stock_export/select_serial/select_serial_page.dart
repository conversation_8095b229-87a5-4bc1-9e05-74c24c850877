import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/order/serial_info_entity.dart';
import 'package:vcc/domain/enums/data_type.dart';
import 'package:vcc/domain/enums/stock_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/work/stock_export/select_serial/select_serial_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/plus_and_minus_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/debouncer.dart';
import 'package:vcc/utils/string_utils.dart';

class SelectSerialArguments {
  final String goodsId;
  final num? numRequest;
  final List<SerialInfoEntity>? serials;
  final StockType? stockType;
  final bool? isSelectOriginalPrice;

  SelectSerialArguments({
    required this.goodsId,
    this.numRequest,
    this.serials,
    this.stockType,
    this.isSelectOriginalPrice,
  });
}

class SelectSerialPage extends StatefulHookConsumerWidget {
  final SelectSerialArguments arguments;

  const SelectSerialPage({
    super.key,
    required this.arguments,
  });

  @override
  ConsumerState<SelectSerialPage> createState() => _SelectSerialPageState();
}

class _SelectSerialPageState extends ConsumerState<SelectSerialPage> {
  late TextEditingController searchController;
  late Debounce<String> debouncer;

  @override
  void initState() {
    searchController = TextEditingController();
    Future(() {
      ref.read(selectSerialProvider.notifier).initData(
            goodsId: widget.arguments.goodsId,
            numRequest: widget.arguments.numRequest,
            serials: widget.arguments.serials,
            stockType: widget.arguments.stockType,
            isSelectOriginalPrice: widget.arguments.isSelectOriginalPrice,
          );
    });
    debouncer = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref.read(selectSerialProvider.notifier).getData(
              goodsId: widget.arguments.goodsId,
              keySearch: value ?? '',
            );
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(selectSerialProvider);

    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      appBar: AppBarCustom(
        title: state.isSelectOriginalPrice ? "Chọn giá vốn" : "Chọn Serial",
      ),
      bottomNavigationBar: ((calculateTotalQuantity()) == state.numRequest)
          ? SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: BaseButton(
                  text: "Xác nhận",
                  onTap: () {
                    context.pop(state.serialSelected!);
                  },
                ),
              ),
            )
          : const SizedBox(),
      body: Column(
        children: <Widget>[
          if ((state.serialSelected ?? []).isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.fromLTRB(16, 8, 8, 16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          state.isSelectOriginalPrice
                              ? "Giá vốn được chọn"
                              : "Serial được chọn",
                          style: UITextStyle.body2Medium.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                      ),
                      AppTextButton(
                        title: "Thiết lập lại",
                        titleStyle: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.primary,
                        ),
                        onTap: () {
                          ref
                              .read(selectSerialProvider.notifier)
                              .resetDataSelected();
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  if ((state.serialSelected?.length ?? 0) > 0)
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: state.serialSelected!
                          .map(
                            (e) => Container(
                              decoration: BoxDecoration(
                                color: BaseColors.backgroundGray,
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                                vertical: 6,
                              ),
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  Text(
                                    state.isSelectOriginalPrice
                                        ? StringUtils.formatMoney(
                                            e.originalPrice ?? 0,
                                          )
                                        : e.serial ?? "",
                                    style: UITextStyle.body2Regular.copyWith(
                                      color: BaseColors.textLabel,
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  InkWellWidget(
                                    onTap: () {
                                      ref
                                          .read(selectSerialProvider.notifier)
                                          .deleteSerial(e);
                                    },
                                    child: MyAssets.icons.iconCloseBackS12.svg(
                                      height: 16,
                                      width: 16,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          )
                          .toList(),
                    ),
                ],
              ),
            ),
            DividerWidget(
              color: BaseColors.backgroundGray,
              height: 4,
            ),
          ],
          Container(
            padding: const EdgeInsets.fromLTRB(16, 16, 16, 8),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                if (state.numRequest != null) ...[
                  Text(
                    'Chọn ${state.serialSelected?.length ?? 0}/${state.numRequest}',
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textSubtitle,
                    ),
                  ),
                  const SizedBox(height: 4),
                ],
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                  ),
                  child: SearchTextFieldWidget(
                    controller: searchController,
                    hintText: "Tìm kiếm",
                    onChanged: (value) {
                      debouncer.value = value;
                    },
                  ),
                ),
              ],
            ),
          ),
          Expanded(
            child: ListView.separated(
              itemCount: state.serials?.length ?? 0,
              separatorBuilder: (_, __) {
                return const Padding(
                  padding: EdgeInsets.only(left: 16),
                  child: DividerWidget(),
                );
              },
              itemBuilder: (context, index) {
                var item = state.serials![index];
                if (item.serial == null) {
                  SerialInfoEntity itemSelected =
                      (state.serialSelected ?? []).firstWhere(
                    (it) => it.originalPrice == item.originalPrice,
                    orElse: () => SerialInfoEntity(),
                  );
                  item.quantity = itemSelected.quantity ?? 1;
                }
                bool isSelected = false;
                if (state.serialSelected != null) {
                  isSelected = state.serialSelected!.any((element) =>
                      element.serial != null
                          ? element.serial == item.serial
                          : element.originalPrice == item.originalPrice);
                }

                return Stack(
                  children: [
                    Padding(
                      padding: const EdgeInsets.fromLTRB(0, 0, 16, 0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          InkWellWidget(
                            onTap: () {
                              ref
                                  .read(selectSerialProvider.notifier)
                                  .selectSerial(item);
                            },
                            child: Row(
                              children: [
                                GestureDetector(
                                  child: Padding(
                                    padding: const EdgeInsets.all(16),
                                    child: isSelected
                                        ? MyAssets.icons.iconChecked.svg()
                                        : MyAssets.icons.iconCheckboxS24.svg(),
                                  ),
                                ),
                                Expanded(
                                  child: Text(
                                    item.serial ?? "",
                                    style: UITextStyle.body1Regular.copyWith(
                                      color: BaseColors.textBody,
                                    ),
                                  ),
                                ),
                                Text(
                                  StringUtils.formatMoney(
                                    item.originalPrice ?? 0,
                                  ),
                                  style: UITextStyle.body1Regular.copyWith(
                                    color: BaseColors.textBody,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          if (item.serial == null || item.serial == "") ...[
                            PlusAndMinusWidget(
                              quantity: item.quantity!,
                              dataType: DataType.double,
                              onMinus: (value) {
                                ref
                                    .read(selectSerialProvider.notifier)
                                    .changeQuantitySelected(
                                      quantity: value,
                                      originalPrice: item.originalPrice,
                                    );
                              },
                              onPlus: (value) {
                                ref
                                    .read(selectSerialProvider.notifier)
                                    .changeQuantitySelected(
                                      quantity: value,
                                      originalPrice: item.originalPrice,
                                    );
                              },
                            ),
                            const SizedBox(height: 16),
                          ],
                        ],
                      ),
                    ),
                    Visibility(
                      visible: (state.disableSelect ?? false) && !isSelected,
                      child: Positioned.fill(
                        child: Container(
                          color: Colors.white.withOpacity(0.5),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  num calculateTotalQuantity() {
    var state = ref.watch(selectSerialProvider);
    if (state.serialSelected == null || state.serialSelected!.isEmpty) {
      return 0;
    }

    return state.serialSelected!.fold<num>(
      0,
      (previousSum, item) => previousSum + (item.quantity ?? 0),
    );
  }
}
