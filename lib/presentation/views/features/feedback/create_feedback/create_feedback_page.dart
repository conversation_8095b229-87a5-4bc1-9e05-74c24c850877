import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/feedback_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_feedback_type/select_feedback_type_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_image/upload_image.dart';
import 'package:vcc/presentation/views/features/feedback/create_feedback/create_feedback_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/presentation/views/widgets/video_widget.dart';

class CreateFeedbackArguments {
  final String? source;

  const CreateFeedbackArguments({
    this.source,
  });
}

class CreateFeedbackPage extends StatefulHookConsumerWidget {
  final CreateFeedbackArguments? arguments;

  const CreateFeedbackPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _CreateFeedbackPageState();
}

class _CreateFeedbackPageState extends ConsumerState<CreateFeedbackPage> {
  late TextEditingController typeLevel2Controller;
  late TextEditingController contentController;

  @override
  void initState() {
    typeLevel2Controller = TextEditingController();
    contentController = TextEditingController();
    super.initState();
  }

  @override
  void dispose() {
    typeLevel2Controller.dispose();
    contentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(createFeedbackProvider);

    return LayoutPage(
      backgroundColor: BaseColors.backgroundWhite,
      appbar: const AppBarCustom(
        title: "Tạo đóng góp ý kiến",
      ),
      body: Container(
        color: BaseColors.backgroundWhite,
        child: InkWell(
          onTap: () {
            FocusScope.of(context).requestFocus(FocusNode());
          },
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                DropdownWidget(
                  labelText: "Loại đóng góp",
                  isRequired: true,
                  content: state.feedbackType?.name ?? "",
                  suffix: MyAssets.icons.arrowDown.svg(),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return "Vui lòng chọn loại đóng góp";
                    }
                    return null;
                  },
                  onTap: () {
                    FocusScope.of(context).requestFocus(FocusNode());

                    AppBottomSheet.showNormalBottomSheet(
                      context,
                      title: "Loại đóng góp",
                      height: MediaQuery.of(context).size.height * 0.6,
                      child: SelectFeedbackTypeView(
                        onSelectItem: (item) {
                          typeLevel2Controller.text = '';
                          contentController.text = '';
                          ref
                              .read(createFeedbackProvider.notifier)
                              .selectType(item);
                        },
                      ),
                    );
                  },
                ),
                if (state.feedbackType?.code == FeedbackType.system) ...[
                  const SizedBox(height: 16),
                  TextFieldWidget(
                    key: const ValueKey("system"),
                    controller: typeLevel2Controller,
                    isRequired: true,
                    labelText: "Chức năng hệ thống",
                    textInputAction: TextInputAction.done,
                  ),
                ],
                if (state.feedbackType?.code == FeedbackType.procedure) ...[
                  const SizedBox(height: 16),
                  TextFieldWidget(
                    key: const ValueKey("procedure"),
                    controller: typeLevel2Controller,
                    isRequired: true,
                    labelText: "Tên/mã quy trình",
                    textInputAction: TextInputAction.done,
                    textCapitalization: TextCapitalization.characters,
                  ),
                ],
                if (state.feedbackType?.code == FeedbackType.policy) ...[
                  const SizedBox(height: 16),
                  TextFieldWidget(
                    key: const ValueKey("policy"),
                    controller: typeLevel2Controller,
                    labelText: "Số chính sách",
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                  ),
                ],
                const SizedBox(height: 16),
                TextFieldWidget.area(
                  controller: contentController,
                  maxLines: 4,
                  maxLength: 2000,
                  hintText: "Chi tiết",
                  hintTextStyle: UITextStyle.body1Regular.copyWith(
                    color: BaseColors.textSubtitle,
                  ),
                  isRequired: true,
                  validator: (value) {
                    if (value.isEmpty) {
                      return "Vui lòng nhập chi tiết";
                    }
                    return null;
                  },
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp(
                        Patterns.note,
                        multiLine: true,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 24),
                Text(
                  "Hình ảnh/Video mô tả",
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
                Text(
                  "Tối đa 5 ảnh và 1 video (<100 Mb)",
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.textSubtitle,
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  height: 80,
                  child: ListView.separated(
                    itemCount: (state.listImage ?? []).length + 1,
                    scrollDirection: Axis.horizontal,
                    padding: const EdgeInsets.only(right: 16),
                    separatorBuilder: (_, __) => const SizedBox(width: 8),
                    itemBuilder: (context, index) {
                      if (index == (state.listImage ?? []).length) {
                        if (state.uploadImageStatus == LoadStatus.loading) {
                          return const SizedBox(
                            height: 80,
                            width: 80,
                            child: Padding(
                              padding: EdgeInsets.all(30),
                              child: Center(
                                child: LoadingCacheImageWidget(
                                  loadingSize: 25,
                                ),
                              ),
                            ),
                          );
                        }

                        return (state.listImage ?? []).length < 6
                            ? InkWellWidget(
                                onTap: () {
                                  onTakePicture(context);
                                },
                                child: MyAssets.icons.iconUploadImageDashLine
                                    .svg(),
                              )
                            : const SizedBox();
                      } else {
                        return Stack(
                          children: [
                            Container(
                              height: 80,
                              width: 80,
                              padding: const EdgeInsets.only(
                                top: 4,
                                right: 4,
                              ),
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: ClipRRect(
                                borderRadius: BorderRadius.circular(8),
                                child: (state.listImage![index].type ?? "")
                                        .contains("video")
                                    ? VideoWidget(
                                        state.listImage![index].link ?? "",
                                        enableShowPreview: false,
                                        size: const Size(76, 76),
                                      )
                                    : ImageWidget(
                                        state.listImage![index].link ?? "",
                                        enableShowPreview: false,
                                        size: const Size(76, 76),
                                      ),
                              ),
                            ),
                            Positioned(
                              top: 0,
                              right: 0,
                              child: InkWellWidget(
                                onTap: () {
                                  ref
                                      .read(createFeedbackProvider.notifier)
                                      .deleteImage(index: index);
                                },
                                child: MyAssets.icons.iconCloseRed.svg(),
                              ),
                            ),
                            Positioned(
                              top: 30,
                              right: 30,
                              child: (state.listImage![index].type ?? "")
                                      .contains("video")
                                  ? MyAssets.icons.iconVideoCircle.svg()
                                  : const SizedBox(),
                            )
                          ],
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
      bottomAction: Container(
        color: BaseColors.backgroundWhite,
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: BaseButton(
            text: "Gửi",
            isLoading: state.loadStatus == LoadStatus.loading,
            onTap: () async {
              if (contentController.text.isEmpty) {
                AppDialog.showDialogCenter(
                  context,
                  message: "Vui lòng nhập chi tiết",
                  status: DialogStatus.error,
                );
                return;
              }

              await ref.read(createFeedbackProvider.notifier).createFeedback(
                    contentLevel2: typeLevel2Controller.text,
                    detail:
                        "${contentController.text}_${widget.arguments?.source ?? ""}",
                  );

              if (!context.mounted) return;
              if (ref.watch(createFeedbackProvider).loadStatus ==
                  LoadStatus.success) {
                context.pop();
                AppDialog.showDialogCenter(
                  context,
                  message: "Gửi đóng góp ý kiến thành công",
                  status: DialogStatus.success,
                );
              } else {
                AppDialog.showDialogCenter(
                  context,
                  message: state.message ?? "Đã có lỗi xảy ra",
                  status: DialogStatus.error,
                );
              }
            },
          ),
        ),
      ),
    );
  }

  void onTakePicture(BuildContext context) async {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Upload ảnh/video mô tả",
      isFlexible: true,
      child: UploadImageBottomSheet(
        onPickImage: (file) {
          ref.read(createFeedbackProvider.notifier).upLoadImage(file);
        },
        onPickVideo: (file) {
          ref.read(createFeedbackProvider.notifier).upLoadImage(file);
        },
      ),
    );
  }
}
