import 'package:base_ui/base_ui.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/notification/notification_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/features/notification/detail_notification/detail_notification_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';

class DetailNotificationArguments {
  final String notificationId;

  DetailNotificationArguments({
    required this.notificationId,
  });
}

class DetailNotificationPage extends StatefulHookConsumerWidget {
  final Function? onNextPageCallBack;
  final DetailNotificationArguments? arguments;

  const DetailNotificationPage({
    super.key,
    this.onNextPageCallBack,
    this.arguments,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() =>
      _DetailNotificationPage();
}

class _DetailNotificationPage extends ConsumerState<DetailNotificationPage> {
  late ScrollController scrollController;
  late String notificationId;

  @override
  void initState() {
    Future(
      () {
        if (widget.arguments?.notificationId != null) {
          ref.read(detailNotificationProvider.notifier).notification(
                notificationId: widget.arguments!.notificationId,
              );
        }
      },
    );
    scrollController = ScrollController();
    super.initState();
  }

  Future<void> refreshData() async {
    if (widget.arguments?.notificationId != null) {
      ref.read(detailNotificationProvider.notifier).notification(
            notificationId: widget.arguments!.notificationId,
          );
    }
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(detailNotificationProvider);

    return LayoutPage(
      backgroundColor: BaseColors.backgroundWhite,
      appbar: AppBarCustom(
        title: state.notification?.title ?? '',
        backFunction: () {
          context.pop(true);
        },
      ),
      body: _buildNotificationDetail(state),
    );
  }

  Widget _buildNotificationDetail(state) {
    if (state.loadStatus == LoadStatus.loading) {
      return const LoadingIndicatorWidget();
    }
    if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    }
    if ((state.notification?.id ?? '').isEmpty) {
      return EmptyListWidget(
        title: "Không có dữ liệu",
        onRefresh: refreshData,
      );
    } else {
      NotificationEntity notification = state.notification!;
      return SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.only(
            left: 16,
            right: 16,
            top: 8,
          ),
          child: HtmlWidget(
            notification.htmlBody ?? '',
          ),
        ),
      );
    }
  }
}
