import 'package:base_ui/base_ui.dart';
import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/theme/theme.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/product/category_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/entities/service/service_request_entity.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/domain/enums/special_service_code.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/create_service_order_page.dart';
import 'package:vcc/presentation/views/features/store/service/other_service_info/other_service_info_view.dart';
import 'package:vcc/presentation/views/features/store/service/single_service/single_service_view_model.dart';
import 'package:vcc/presentation/views/features/store/service/widgets/service_list_item_widget.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_search.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/tab_bar_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/string_utils.dart';

class SingleServicesArguments {
  final String? parentId;
  final ServiceType? serviceType;
  final int? activeMenu;
  final List<ServiceInfoEntity>? services;
  final bool? isAddService;
  final bool isChangeOrderType;
  final String? startDate;
  final String? provinceCode;
  final String? districtCode;
  final DetailOrderEntity? orderInfo;

  SingleServicesArguments({
    this.parentId,
    this.serviceType,
    this.activeMenu,
    this.services,
    this.isAddService,
    this.startDate,
    this.provinceCode,
    this.districtCode,
    this.isChangeOrderType = false,
    this.orderInfo,
  });
}

class SingleServicesPage extends StatefulHookConsumerWidget {
  final SingleServicesArguments? arguments;

  const SingleServicesPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<SingleServicesPage> createState() => _SingleServicesPageState();
}

class _SingleServicesPageState extends ConsumerState<SingleServicesPage>
    with TickerProviderStateMixin {
  late ScrollController controller;
  TabController? tabCategoriesController;
  TabController? tabFilterSearchController;

  @override
  void initState() {
    controller = ScrollController();
    controller.addListener(_scrollListener);
    Future(
      () async {
        await initData();
        _changeTabController(
          ref.watch(singleServicesProvider).categories?.length ?? 1,
          ref.watch(singleServicesProvider).activeItem ?? 0,
        );
      },
    );

    super.initState();
  }

  void _changeTabController(int length, int initialIndex) {
    tabCategoriesController = TabController(
      length: length,
      initialIndex: initialIndex,
      vsync: this,
    );

    tabCategoriesController?.addListener(
      () {
        changeCategory(tabCategoriesController?.index ?? 0);
      },
    );
  }

  void _scrollListener() {
    final maxScroll = controller.position.maxScrollExtent;
    final currentScroll = controller.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref
          .read(singleServicesProvider.notifier)
          .fetchNextService(widget.arguments?.serviceType);
    }
  }

  Future<void> initData() async {
    await ref.read(singleServicesProvider.notifier).initData(
          serviceSelected: widget.arguments?.services,
          isAddService: widget.arguments?.isAddService ?? false,
          startDate: widget.arguments?.startDate,
          provinceCode: widget.arguments?.provinceCode,
          districtCode: widget.arguments?.districtCode,
        );

    tabFilterSearchController = TabController(
      length: ref.watch(singleServicesProvider).listCategoryItem?.length ?? 0,
      initialIndex: 0,
      vsync: this,
    );
    tabFilterSearchController?.addListener(
      () {
        changeCategory(tabFilterSearchController?.index ?? 0);
      },
    );

    await ref.read(singleServicesProvider.notifier).getServices(
          parentId: widget.arguments?.parentId,
          activeMenu: widget.arguments?.activeMenu ?? 0,
          serviceType: widget.arguments?.serviceType,
        );
  }

  Future<void> refreshData() async {
    ref.read(singleServicesProvider.notifier).getServices(
          parentId: widget.arguments?.parentId,
          serviceType: widget.arguments?.serviceType,
        );
  }

  Future<void> changeCategory(int activeItem) async {
    ref.read(singleServicesProvider.notifier).changeCategory(activeItem);
  }

  Future<void> changeActiveMenu(int activeMenu) async {
    await ref.read(singleServicesProvider.notifier).changeActiveMenu(
          activeMenu,
        );

    _changeTabController(
      ref.watch(singleServicesProvider).categories?.length ?? 0,
      0,
    );
  }

  Future<void> changeActiveItemService(int activeItem) async {
    ref.read(singleServicesProvider.notifier).changeActiveItemService(
          activeItem,
          widget.arguments?.serviceType,
        );
  }

  @override
  Widget build(BuildContext context) {
    var serviceState = ref.watch(singleServicesProvider);

    PreferredSizeWidget appbarWidget;

    if (serviceState.isSearching) {
      appbarWidget = AppBarSearch(
        backFunction: () {
          ref.read(singleServicesProvider.notifier).changeSearchStatus(false);
          ref
              .read(singleServicesProvider.notifier)
              .searchService(serviceState.request);
        },
        onChanged: (value) {
          ref.read(singleServicesProvider.notifier).searchService(
                ServiceRequestEntity(
                  keySearch: value.trim(),
                  menuId:
                      serviceState.listMenu?[serviceState.activeMenu ?? 0].code,
                ),
              );
        },
      );
    } else {
      appbarWidget = AppBarCustom(
        title: serviceState.isAddService
            ? "Thêm dịch vụ"
            : widget.arguments?.serviceType == ServiceType.combo
                ? ServiceType.combo.value
                : ServiceType.single.value,
        actionWidget: [
          InkWellWidget(
            onTap: () {
              ref
                  .read(singleServicesProvider.notifier)
                  .changeSearchStatus(true);

              ref.read(singleServicesProvider.notifier).searchService(
                    ServiceRequestEntity(
                      menuId: serviceState
                          .listMenu![serviceState.activeMenu ?? 0].code,
                    ),
                  );
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              child: MyAssets.icons.iconSearchS24.svg(),
            ),
          ),
        ],
      );
    }

    return LayoutPage(
      backgroundColor: BaseColors.backgroundGray,
      appbar: appbarWidget,
      bottomAction: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          boxShadow: AppBoxShadows.shadowNormal,
          color: BaseColors.backgroundWhite,
        ),
        child: SafeArea(
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Column(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    "Tổng tiền tạm tính",
                    style: UITextStyle.body2Regular.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  Text(
                    StringUtils.formatMoney(
                      ref.watch(singleServicesProvider).getTotalMoney,
                    ),
                    style: UITextStyle.body1SemiBold.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ],
              ),
              BaseButton(
                text: (serviceState.isAddService ||
                        (widget.arguments?.isChangeOrderType ?? false))
                    ? "Xác nhận"
                    : "Tạo đơn",
                onTap: () {
                  if (widget.arguments?.isChangeOrderType ?? false) {
                    onConfirmServices(
                      serviceState.listServiceSelected,
                    );
                  } else {
                    if (serviceState.isAddService) {
                      if ((serviceState.servicesDraft ?? []).isEmpty) {
                        AppDialog.showDialogCenter(
                          context,
                          message: "Bạn phải chọn ít nhất một dịch vụ",
                          status: DialogStatus.error,
                        );
                        return;
                      }

                      context.pop(serviceState.servicesDraft ?? []);
                      return;
                    } else {
                      AppUtils.checkLogin(
                        context,
                        onLogged: () {
                          if ((serviceState.listServiceSelected ?? [])
                              .isEmpty) {
                            AppDialog.showDialogCenter(
                              context,
                              message: "Bạn phải chọn ít nhất một dịch vụ",
                              status: DialogStatus.error,
                            );
                            return;
                          }

                          context.push(
                            RouterPaths.createServiceOrder,
                            extra: CreateServiceOrderArguments(
                              serviceType: widget.arguments?.serviceType ??
                                  ServiceType.single,
                              listSingleService:
                                  serviceState.listServiceSelected,
                            ),
                          );
                        },
                      );
                    }
                  }
                },
              )
            ],
          ),
        ),
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildListServices(serviceState),
      ),
    );
  }

  Widget _buildListServices(SingleServicesState state) {
    if (state.loadStatus == LoadStatus.success) {
      return _buildListService(state);
    }

    if (state.isSearching &&
        state.keySearch?.trim() == "" &&
        (state.searchHistory ?? []).isNotEmpty) {
      return _buildHistory(state);
    }

    return const Center(
      child: LoadingIndicatorWidget(),
    );
  }

  Widget _buildHistory(SingleServicesState state) {
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundGray,
        borderRadius: BorderRadius.circular(12),
      ),
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Tìm kiếm gần đây",
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              MyAssets.icons.iconTrashS18.svg(),
            ],
          ),
          const SizedBox(
            height: 16,
          ),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: (state.searchHistory ?? []).map(
                (searchHistoryItem) {
                  return IntrinsicWidth(
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        vertical: 3,
                        horizontal: 8,
                      ),
                      decoration: BoxDecoration(
                        color: BaseColors.backgroundGray,
                        borderRadius: BorderRadius.circular(100),
                      ),
                      child: Row(mainAxisSize: MainAxisSize.min, children: [
                        InkWellWidget(
                          onTap: () {
                            ref
                                .read(singleServicesProvider.notifier)
                                .searchService(
                                  ServiceRequestEntity(
                                    keySearch: searchHistoryItem,
                                  ),
                                );
                          },
                          child: Padding(
                            padding: const EdgeInsets.all(3),
                            child: Text(searchHistoryItem),
                          ),
                        ),
                        const SizedBox(
                          width: 3,
                        ),
                        InkWellWidget(
                          child: MyAssets.icons.iconCloseBackS12.svg(),
                        ),
                      ]),
                    ),
                  );
                },
              ).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListService(SingleServicesState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildSearchCondition(state),
        if (state.isSearching && (state.keySearch ?? '').isNotEmpty) ...[
          Container(
            padding: const EdgeInsets.all(16),
            child: Text(
              "Tìm thấy ${state.services?.length} kết quả",
              style: UITextStyle.body2Medium,
            ),
          ),
        ],
        Expanded(
          child: _buildListItem(state),
        ),
      ],
    );
  }

  Widget _buildActiveItemService({
    required SingleServicesState state,
    required int index,
    required ServiceInfoEntity category,
  }) {
    return SizedBox(
      height: 32,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: InkWell(
          onTap: () {
            changeActiveItemService(index);
          },
          child: Container(
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: state.activeItemService == index
                  ? BaseColors.primarySurface
                  : BaseColors.backgroundGray,
              borderRadius: BorderRadius.circular(360),
            ),
            padding: const EdgeInsets.symmetric(
              vertical: 6,
              horizontal: 12,
            ),
            child: Text(
              (state.listCategoryItem ?? [])
                      .map((it) => it.name)
                      .toList()[index] ??
                  '',
              style: UITextStyle.body2Regular.copyWith(
                color: state.activeItemService == index
                    ? BaseColors.primary
                    : BaseColors.textBody,
              ),
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSearchCondition(SingleServicesState state) {
    if (state.isSearching) {
      var category = (state.listCategoryItem ?? []);

      return TabBarWidget(
        tabItems: category.map((category) => category.name ?? "").toList(),
        isScrollable: true,
        tabAlignment: TabAlignment.start,
        tabController: tabFilterSearchController,
      );
    }

    List<CategoryEntity> menu = [];
    if (widget.arguments?.serviceType == ServiceType.single) {
      menu = (state.listMenu ?? [])
          .where((item) => item.code != ServiceType.combo.code)
          .toList();
    }

    return Column(
      children: [
        if (widget.arguments?.serviceType == ServiceType.single) ...[
          DividerWidget(
            height: 8,
            color: BaseColors.backgroundGray,
          ),
          Container(
            width: double.infinity,
            height: 40,
            decoration: BoxDecoration(
              color: BaseColors.backgroundGray,
            ),
            child: ListView.builder(
              shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              itemCount: menu.length,
              itemBuilder: (context, index) {
                return _buildMenu(
                  index: index,
                  menu: menu,
                );
              },
            ),
          ),
        ],
        if ((state.categories ?? []).isNotEmpty) ...[
          TabBarWidget(
            tabItems: (state.categories ?? [])
                .map((category) => category.name ?? "")
                .toList(),
            isScrollable: true,
            tabAlignment: TabAlignment.start,
            tabController: tabCategoriesController,
          ),
        ],
        Container(
          color: BaseColors.backgroundWhite,
          width: double.infinity,
          height: 48,
          child: ListView.builder(
            shrinkWrap: true,
            scrollDirection: Axis.horizontal,
            itemCount: state.listCategoryItem?.length ?? 0,
            itemBuilder: (context, index) {
              var category =
                  state.listCategoryItem![state.activeItemService ?? 0];
              return _buildActiveItemService(
                state: state,
                index: index,
                category: category,
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildMenu({
    required int index,
    required List<CategoryEntity> menu,
  }) {
    if (ref.watch(singleServicesProvider).activeMenu == index) {
      return InkWell(
        onTap: () {
          changeActiveMenu(index);
        },
        child: Row(
          children: [
            MyAssets.icons.menuLeftRadius.svg(),
            Container(
              alignment: Alignment.center,
              decoration: const BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.zero,
              ),
              child: Row(
                children: [
                  ImageWidget(
                    menu[index].iconUrl ?? BaseConstant.imageDefaultServiceTest,
                    size: const Size(24, 24),
                  ),
                  const SizedBox(width: 4),
                  Text(
                    menu[index].name ?? "",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ],
              ),
            ),
            MyAssets.images.menuRightRadius.image(),
          ],
        ),
      );
    }

    return InkWell(
      onTap: () {
        changeActiveMenu(index);
      },
      child: Container(
        alignment: Alignment.center,
        decoration: BoxDecoration(
          color: BaseColors.backgroundGray,
        ),
        padding: const EdgeInsets.symmetric(horizontal: 12),
        child: Row(
          children: [
            ImageWidget(
              menu[index].iconUrl ?? BaseConstant.imageDefaultServiceTest,
              size: const Size(24, 24),
              borderRadius: BorderRadius.circular(4),
            ),
            const SizedBox(width: 4),
            Text(
              menu[index].name ?? "",
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textBody,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildListItem(SingleServicesState state) {
    if (state.loadStatusService == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra',
        onRefresh: refreshData,
      );
    }

    if (state.loadStatusService == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    }

    if (state.loadStatusService == LoadStatus.success) {
      if ((state.services ?? []).isEmpty) {
        return EmptyListWidget(
          title: "Không tìm thấy dịch vụ nào",
          onRefresh: refreshData,
        );
      } else {
        return ListView.separated(
          shrinkWrap: true,
          itemCount: state.services?.length ?? 0,
          padding: const EdgeInsets.only(top: 4),
          separatorBuilder: (_, __) => DividerWidget(
            height: 4,
            color: BaseColors.backgroundGray,
          ),
          itemBuilder: (context, index) {
            var item = state.services![index];

            if (item.code == SpecialServiceCode.otherService.codeToServer &&
                (state.draftOtherService?.code ?? '').isNotEmpty) {
              state.services![index] = state.draftOtherService!;
            }
            return ServiceListItemWidget(
              service: item,
              onSelect: () async {
                if (item.code == SpecialServiceCode.otherService.codeToServer &&
                    (item.price == 0 || item.price == null)) {
                  await AppBottomSheet.showNormalBottomSheet(
                    context,
                    title: "Thông tin dịch vụ khác",
                    isFlexible: true,
                    child: OtherServiceInfoView(
                      item: item,
                      catalogCode:
                          state.listMenu?[state.activeMenu ?? 0].code ?? '',
                      onConfirm: (service) {
                        ref.read(singleServicesProvider.notifier).modifyService(
                              otherService: service,
                              catalogOtherServiceCode:
                                  state.listMenu?[state.activeMenu ?? 0].code ??
                                      '',
                              indexService: index,
                            );
                        ref
                            .read(singleServicesProvider.notifier)
                            .selectService(service);
                      },
                    ),
                  );
                } else {
                  ref.read(singleServicesProvider.notifier).selectService(item);
                }
              },
              changeQuantityService: (quantity) {
                ref
                    .read(singleServicesProvider.notifier)
                    .changeChangeQuantityOrder(quantity, index);
              },
              catalogCode: state.listMenu?[state.activeMenu ?? 0].code ?? '',
              modifyService:
                  ref.read(singleServicesProvider.notifier).modifyService,
              refreshData: refreshData,
              indexService: index,
            );
          },
        );
      }
    }

    return const Center(
      child: LoadingIndicatorWidget(),
    );
  }

  void onConfirmServices(
    List<ServiceInfoEntity>? listServiceSelected,
  ) {
    String message = '';

    if (widget.arguments?.orderInfo?.paymentInfo?.isGetBill ?? false) {
      message += '- Mã giảm giá\n';
    }

    if (widget.arguments?.orderInfo?.isHotOrder ?? false) {
      message += '- Triển khai nhanh 3 giờ\n';
    }

    if (message.isNotEmpty) {
      message =
          'Đơn hàng đang được áp dụng:\n$messageĐổi loại đơn sẽ không thể tiếp tục áp dụng.\nBạn xác nhận muốn đổi loại đơn?';
    } else {
      message =
          'Đổi loại đơn sẽ thay đổi dịch vụ và vật tư đã chọn.\nBạn xác nhận muốn đổi loại đơn?';
    }

    //show popup with content
    AppDialog.showDialogConfirm(
      context,
      title: 'Xác nhận',
      widgetContent: SizedBox(
        width: double.infinity,
        child: Text(
          message,
          style: UITextStyle.body2Regular.copyWith(
            color: BaseColors.textBody,
          ),
        ),
      ),
      onConfirmAction: () {
        context.pop(listServiceSelected);
        return;
      },
    );
  }

  @override
  void dispose() {
    tabCategoriesController?.dispose();
    tabFilterSearchController?.dispose();
    super.dispose();
  }
}
