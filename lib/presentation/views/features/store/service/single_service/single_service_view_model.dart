import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/base/base_result.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/order/other_service_entity.dart';
import 'package:vcc/domain/entities/product/category_entity.dart';
import 'package:vcc/domain/entities/product/category_package_entity.dart';
import 'package:vcc/domain/entities/service/service_cat_entity.dart';
import 'package:vcc/domain/entities/service/service_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/entities/service/service_request_entity.dart';
import 'package:vcc/domain/enums/list_category_item.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/menu_type.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/domain/responses/list_data_response.dart';
import 'package:vcc/utils/log_utils.dart';

part 'single_service_state.dart';

final singleServicesProvider = StateNotifierProvider.autoDispose<
    ListComboServicesViewModel, SingleServicesState>(
  (ref) => ListComboServicesViewModel(ref: ref),
);

class ListComboServicesViewModel extends StateNotifier<SingleServicesState> {
  final Ref ref;

  ListComboServicesViewModel({
    required this.ref,
  }) : super(const SingleServicesState());

  Future<void> initData({
    List<ServiceInfoEntity>? serviceSelected,
    bool isAddService = false,
    String? startDate,
    String? provinceCode,
    String? districtCode,
  }) async {
    List<ServiceInfoEntity> list = [];
    list.addAll(serviceSelected ?? []);

    state = state.copyWith(
      servicesDraft: list,
      listCategoryItem: listCategoryItemDefault,
      isAddService: isAddService,
      categories: [
        ServiceEntity(
          name: ServiceType.all.value,
          data: null,
        )
      ],
    );

    if (isAddService) {
      state = state.copyWith(
        startDate: startDate,
        provinceCode: provinceCode,
        districtCode: districtCode,
      );
    }
    await getCategories();
  }

  Future<void> getCategories() async {
    List<CategoryPackageEntity> categories = [];

    try {
      BaseResult<ArrayResponse<CategoryPackageEntity>>? result;
      final isLogin = GlobalData.instance.userInfo != null;

      if (isLogin) {
        result = await appLocator<OrderRepository>().getServiceCategories(
          MenuType.service,
        );
      } else {
        result = await appLocator<OrderRepository>().getNoAuthServiceCategories(
          menuType: MenuType.service,
          provinceCode: state.provinceCode ??
              GlobalData.instance.addressDefault?.province?.code,
        );
      }

      await result?.when(
        success: (data) async {
          final items = data.data ?? [];
          categories = items
              .where(
                (item) => item.type == ServiceType.single.code,
              )
              .toList();

          state = state.copyWith(
            listMenu: categories,
          );
        },
        error: (err) {},
      );
    } catch (error) {
      LogUtils.e("ERROR: $error");
    }
  }

  Future<void> getServices({
    String? parentId,
    int? activeMenu,
    ServiceType? serviceType,
  }) async {
    String? defaultMenu = '';
    switch (serviceType?.code) {
      case "COMBO":
        defaultMenu = state.listMenu
            ?.where((element) => element.code == ServiceType.combo.code)
            .toList()[0]
            .code;
        break;
      default:
        // defaultMenu = state.listMenu?.first.code;
        break;
    }
    String parent = parentId ?? defaultMenu ?? "";

    state = state.copyWith(
      request: ServiceRequestEntity(
        menuId: parent,
      ),
    );

    try {
      if (serviceType == ServiceType.package) {
        state = state.copyWith(
          loadStatus: LoadStatus.success,
        );

        searchServicePackage(
          ServiceRequestEntity(
            menuId: parent,
          ),
        );
      } else {
        state = state.copyWith(
          loadStatus: LoadStatus.loading,
        );

        BaseResult<ServiceCatEntity>? result;
        final isLogin = GlobalData.instance.userInfo != null;

        if (isLogin) {
          result = await appLocator<OrderRepository>().getChildService(
            parentId: parent,
          );
        } else {
          result = await appLocator<OrderRepository>().getNoAuthChildService(
            parentId: parent,
            provinceCode: state.provinceCode ??
                GlobalData.instance.addressDefault?.province?.code,
          );
        }

        await result?.when(
          success: (data) async {
            if ((data.data ?? []).isNotEmpty) {
              data.data!
                  .map((item) => {
                        item.data?.insert(
                          0,
                          ServiceInfoEntity(
                            name: ServiceType.all.value,
                            code: ServiceType.all.code,
                          ),
                        ),
                      })
                  .toList();

              var listCategory = [
                ServiceEntity(
                  name: ServiceType.all.value,
                  data: data.data!.first.data,
                )
              ];

              listCategory.addAll(data.data!);

              state = state.copyWith(
                categories: listCategory,
                activeMenu: activeMenu ?? 0,
                loadStatus: LoadStatus.success,
              );
            } else {
              state = state.copyWith(
                loadStatus: LoadStatus.success,
              );
            }
          },
          error: (err) {
            state = state.copyWith(
              loadStatus: LoadStatus.failure,
              message: err.message,
            );
          },
        );
        searchService(
          ServiceRequestEntity(
            menuId: parent,
          ),
        );
      }
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void changeSearchStatus(bool status) {
    state = state.copyWith(
      isSearching: status,
    );
  }

  void changeChangeQuantityOrder(num quantityOrder, int index) {
    List<ServiceInfoEntity> listService = [];

    if (state.isAddService) {
      listService = state.servicesDraft ?? [];
    } else {
      listService = state.listServiceSelected ?? [];
    }
    var servicesTmp = state.services;
    if (servicesTmp?[index] != null) {
      servicesTmp?[index].quantity = quantityOrder;
      bool hasDelete = removeBaseOnQuantity(
        quantityOrder: quantityOrder,
        servicesTmp: servicesTmp?[index],
        indexService: index,
      );
      if (hasDelete) {
        state.services?[index].isSelected = false;
      }
    }

    if (state.isAddService) {
      state = state.copyWith(
        servicesDraft: listService,
        services: servicesTmp,
      );
    } else {
      state = state.copyWith(
        listServiceSelected: listService,
        services: servicesTmp,
      );
    }
  }

  bool removeBaseOnQuantity({
    required num quantityOrder,
    ServiceInfoEntity? servicesTmp,
    required int indexService,
  }) {
    final listFilter = state.listServiceSelected ?? [];
    if (listFilter.isEmpty) return false;
    if ((servicesTmp?.code ?? '').isNotEmpty) {
      final data = listFilter
          .where((element) => element.code == servicesTmp!.code)
          .toList();

      if (quantityOrder == 0 && data.isNotEmpty) {
        listFilter.remove(data.first);
        state = state.copyWith(
          listServiceSelected: listFilter,
        );
        return true;
      } else {
        int? index = listFilter.indexOf(data.first);
        listFilter[index].quantity = quantityOrder;
      }

      state = state.copyWith(
        listServiceSelected: listFilter,
      );
    }
    return false;
  }

  void changeCategory(int index) {
    ServiceRequestEntity? param;
    if (state.isSearching) {
      param = ServiceRequestEntity(
        keySearch: state.keySearch,
        menuId: state.request?.menuId,
        serviceType: listCategoryItemDefault[index].code,
      );
    } else {
      state = state.copyWith(
        activeItem: index,
        request: ServiceRequestEntity(
          categoryId: state.categories![index].categoryId,
          menuId: state.request?.menuId,
          serviceType: state.request?.serviceType,
        ),
      );
      param = state.request;
    }

    searchService(param);
  }

  Future<void> changeActiveMenu(
    int index,
  ) async {
    state = state.copyWith(
      activeMenu: index,
      activeItem: 0,
      request: ServiceRequestEntity(
        menuId: state.listMenu![index].code,
        categoryId: null,
      ),
    );

    await getListCategoryItem(
      index: index,
    );
    searchService(state.request);
  }

  Future<void> getListCategoryItem({
    required int index,
  }) async {
    try {
      BaseResult<ServiceCatEntity>? result;
      final isLogin = GlobalData.instance.userInfo != null;

      if (isLogin) {
        result = await appLocator<OrderRepository>().getChildService(
          parentId: state.listMenu![index].code.toString(),
        );
      } else {
        result = await appLocator<OrderRepository>().getNoAuthChildService(
          parentId: state.listMenu![index].code.toString(),
          provinceCode: state.provinceCode ??
              GlobalData.instance.addressDefault?.province?.code,
        );
      }

      // state = state.copyWith(
      //   categories: [
      //     ServiceEntity(
      //       name: ServiceType.all.value,
      //       data: null,
      //     )
      //   ],
      // );

      await result?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            List<ServiceEntity> listCategory = [
              ServiceEntity(
                name: ServiceType.all.value,
                data: null,
              )
            ];

            listCategory.addAll(data.data ?? []);
            state = state.copyWith(
              categories: listCategory,
            );
          } else {
            state = state.copyWith(
              categories: [],
            );
          }
        },
        error: (err) {
          state = state.copyWith(
            categories: [],
          );
        },
      );
    } catch (e) {
      LogUtils.e("ERROR: $e");
      state = state.copyWith(
        categories: [],
      );
    }
  }

  void changeActiveItemService(
    int index,
    ServiceType? serviceType,
  ) {
    state = state.copyWith(
      activeItemService: index,
      request: state.request?.copyWith(
        serviceType: listCategoryItemDefault[index].code,
      ),
      services: [],
    );

    if (serviceType == ServiceType.package) {
      searchServicePackage(state.request);
    } else {
      searchService(state.request);
    }
  }

  Future<void> searchService(
    ServiceRequestEntity? request,
  ) async {
    if (state.isAddService) {
      request = request?.copyWith(
        startTime: state.startDate,
        provinceCode: state.provinceCode,
        districtCode: state.districtCode,
      );
    }

    state = state.copyWith(
      keySearch: request?.keySearch ?? "",
      loadStatusService: LoadStatus.loading,
    );

    try {
      BaseResult<ArrayResponse<ServiceInfoEntity>>? result;
      final isLogin = GlobalData.instance.userInfo != null;

      if (isLogin) {
        result = await appLocator<OrderRepository>().searchService(
          request: request,
        );
      } else {
        request = request?.copyWith(
          provinceCode: GlobalData.instance.addressDefault?.province?.code,
          districtCode: GlobalData.instance.addressDefault?.district?.code,
        );

        result = await appLocator<OrderRepository>().searchNoAuthService(
          request: request,
        );
      }

      await result?.when(
        success: (data) async {
          List<ServiceInfoEntity> listService = [];

          if (state.isAddService) {
            listService = state.servicesDraft ?? [];
          } else {
            listService = state.listServiceSelected ?? [];
          }

          if (listService.isNotEmpty && (data.data ?? []).isNotEmpty) {
            data.data?.forEach((element) {
              var checkExist = listService
                  .where(
                    (e) => e.code == element.code,
                  )
                  .toList();
              if (checkExist.isNotEmpty) {
                element.isSelected = true;
                element.quantity = checkExist[0].quantity;
              }
            });
          }

          state = state.copyWith(
            services: data.data ?? [],
            loadStatusService: LoadStatus.success,
          );
        },
        error: (err) {
          LogUtils.e("ERROR: $err");
          state = state.copyWith(
            loadStatusService: LoadStatus.failure,
          );
        },
      );
    } catch (e) {
      LogUtils.e("ERROR: $e");
      state = state.copyWith(
        loadStatusService: LoadStatus.failure,
      );
    }
  }

  Future<void> searchServicePackage(
    ServiceRequestEntity? request,
  ) async {
    try {
      if (state.isAddService) {
        request = request?.copyWith(
          startTime: state.startDate,
          provinceCode: state.provinceCode,
          districtCode: state.districtCode,
        );
      }

      state = state.copyWith(
        loadStatusService: LoadStatus.loading,
      );

      final result =
          await appLocator<OrderRepository>().getServicePackageAddition(
        keyword: request?.keySearch ?? '',
        parentCode: "GTT",
        serviceType: request?.serviceType ?? '',
        startTime: request?.startTime ?? '',
        provinceCode: request?.provinceCode,
        districtCode: request?.districtCode,
        wardCode: request?.wardCode,
        page: state.currentPage,
        size: 20,
        sort: ["order", "code"],
      );

      await result?.when(
        success: (data) async {
          List<ServiceInfoEntity> listService = [];

          if (state.isAddService) {
            listService = state.servicesDraft ?? [];
          } else {
            listService = state.listServiceSelected ?? [];
          }

          if (listService.isNotEmpty && (data.data ?? []).isNotEmpty) {
            data.data?.forEach((element) {
              var checkExist = listService
                  .where(
                    (e) => e.code == element.code,
                  )
                  .toList();
              if (checkExist.isNotEmpty) {
                element.isSelected = true;
                element.quantity = checkExist[0].quantity;
              }
            });
          }

          state = state.copyWith(
            services: data.data ?? [],
            loadStatusService: LoadStatus.success,
          );
        },
        error: (err) {
          LogUtils.e("ERROR: $err");
          state = state.copyWith(
            loadStatusService: LoadStatus.failure,
          );
        },
      );
    } catch (e) {
      LogUtils.e("ERROR: $e");
      state = state.copyWith(
        loadStatusService: LoadStatus.failure,
      );
    }
  }

  void selectService(ServiceInfoEntity service) {
    List<ServiceInfoEntity> listService = [];

    if (state.isAddService) {
      listService = state.servicesDraft ?? [];
    } else {
      listService = state.listServiceSelected ?? [];
    }

    if (listService.isEmpty) {
      service.isSelected = true;
      if (service.quantity == 0) {
        service.quantity = 1;
      }
      listService.add(service);
    } else {
      final checkExist =
          listService.where((e) => e.code == service.code).toList();

      if (checkExist.isNotEmpty) {
        service.isSelected = false;
        listService.remove(checkExist.first);
      } else {
        service.isSelected = true;
        if (service.quantity == 0) {
          service.quantity = 1;
        }
        listService.add(service);
      }
    }

    if (state.isAddService) {
      state = state.copyWith(
        servicesDraft: listService,
      );
    } else {
      state = state.copyWith(
        listServiceSelected: listService,
      );
    }
  }

  void modifyService({
    required ServiceInfoEntity otherService,
    required String catalogOtherServiceCode,
    required int indexService,
  }) {
    List<ServiceInfoEntity> listService = [];

    if (state.isAddService) {
      listService = state.servicesDraft ?? [];
    } else {
      listService = state.listServiceSelected ?? [];
    }
    if (listService.isNotEmpty &&
        (state.services![indexService].code ?? '').isNotEmpty &&
        state.services![indexService].price != 0) {
      var checkExist = listService
          .where(
            (e) =>
                e.code == state.services![indexService].code &&
                e.price == state.services![indexService].price,
          )
          .toList();
      if (checkExist.isNotEmpty) {
        listService.remove(checkExist[0]);
        if (state.isAddService) {
          state = state.copyWith(
            servicesDraft: listService,
            draftOtherService: otherService,
            catalogOtherServiceCode: catalogOtherServiceCode,
          );
        } else {
          state = state.copyWith(
            listServiceSelected: listService,
            draftOtherService: otherService,
            catalogOtherServiceCode: catalogOtherServiceCode,
          );
        }
      }
      return;
    }
    state.services![indexService] = otherService;
    state = state.copyWith(
      draftOtherService: otherService,
      catalogOtherServiceCode: catalogOtherServiceCode,
    );
  }

  void fetchNextService(ServiceType? serviceType) async {
    if (state.services!.length >= (state.totalResult ?? 0)) {
      return;
    }

    if (state.loadStatusService != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatusService: LoadStatus.loadMore,
    );

    try {
      ServiceRequestEntity? request = state.request?.copyWith(
        menuId: state.listMenu?[state.activeMenu ?? 0].code,
        serviceType: listCategoryItemDefault[state.activeItemService ?? 0].code,
        keySearch: state.keySearch,
        startTime: state.startDate,
        provinceCode: state.provinceCode,
        districtCode: state.districtCode,
        page: state.currentPage,
        size: 20,
        sort: ["order", "code"],
      );

      if (serviceType == ServiceType.package) {
        final result =
            await appLocator<OrderRepository>().getServicePackageAddition(
          keyword: request?.keySearch ?? '',
          parentCode: "GTT",
          serviceType: ServiceType.package.code,
          startTime: request?.startTime ?? '',
          provinceCode: request?.provinceCode,
          districtCode: request?.districtCode,
          wardCode: request?.wardCode,
          page: request?.page,
          size: request?.size,
          sort: ["order", "code"],
        );

        await result?.when(
          success: (data) async {
            List<ServiceInfoEntity> listService = [];

            if (state.isAddService) {
              listService = state.servicesDraft ?? [];
            } else {
              listService = state.listServiceSelected ?? [];
            }

            if (listService.isNotEmpty && (data.data ?? []).isNotEmpty) {
              data.data?.forEach((element) {
                var checkExist = listService
                    .where(
                      (e) => e.code == element.code,
                    )
                    .toList();
                if (checkExist.isNotEmpty) {
                  element.isSelected = true;
                  element.quantity = checkExist[0].quantity;
                }
              });
            }

            state = state.copyWith(
              services: data.data ?? [],
              loadStatusService: LoadStatus.success,
            );
          },
          error: (err) {
            LogUtils.e("ERROR: $err");
            state = state.copyWith(
              loadStatusService: LoadStatus.failure,
            );
          },
        );
      } else {
        final result = await appLocator<OrderRepository>().searchService(
          request: request,
        );

        await result?.when(
          success: (data) async {
            List<ServiceInfoEntity> listService = [];

            if (state.isAddService) {
              listService = state.servicesDraft ?? [];
            } else {
              listService = state.listServiceSelected ?? [];
            }

            if (listService.isNotEmpty && (data.data ?? []).isNotEmpty) {
              data.data?.forEach((element) {
                var checkExist = listService
                    .where(
                      (e) => e.code == element.code,
                    )
                    .toList();
                if (checkExist.isNotEmpty) {
                  element.isSelected = true;
                  element.quantity = checkExist[0].quantity;
                }
              });
            }

            state = state.copyWith(
              services: data.data ?? [],
              loadStatusService: LoadStatus.success,
            );
          },
          error: (err) {
            LogUtils.e("ERROR: $err");
            state = state.copyWith(
              loadStatusService: LoadStatus.failure,
            );
          },
        );
      }
    } catch (error) {
      state = state.copyWith(
        loadStatusService: LoadStatus.failure,
      );
    }
  }
}
