import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/order/other_service_entity.dart';
import 'package:vcc/domain/entities/product/category_entity.dart';
import 'package:vcc/domain/entities/product/category_package_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/entities/service/service_request_entity.dart';
import 'package:vcc/domain/enums/list_category_item.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/menu_type.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/utils/log_utils.dart';

part 'partner_service_state.dart';

final partnerServicesProvider = StateNotifierProvider.autoDispose<
    PartnerServicesViewModel, PartnerServicesState>(
  (ref) => PartnerServicesViewModel(ref: ref),
);

class PartnerServicesViewModel extends StateNotifier<PartnerServicesState> {
  final Ref ref;

  PartnerServicesViewModel({
    required this.ref,
  }) : super(const PartnerServicesState());

  Future<void> initData({
    List<ServiceInfoEntity>? serviceSelected,
    bool isAddService = false,
    String? startDate,
    String? provinceCode,
    String? districtCode,
    String? tenantId,
  }) async {
    List<ServiceInfoEntity> list = [];
    list.addAll(serviceSelected ?? []);

    state = state.copyWith(
      servicesDraft: list,
      listCategoryItem: listCategoryItemDefault,
      isAddService: isAddService,
    );

    if (isAddService) {
      state = state.copyWith(
        startDate: startDate,
        provinceCode: provinceCode,
        districtCode: districtCode,
      );
    }
    await getCategories(tenantId);
  }

  Future<void> getCategories(String? tenantId) async {
    List<CategoryPackageEntity> categories = [];

    try {
      final result = await appLocator<OrderRepository>().getServiceCategories(
        MenuType.service,
        tenantId: tenantId,
      );

      await result?.when(
        success: (data) async {
          final items = data.data ?? [];
          categories = items
              .where(
                (item) => item.type == ServiceType.single.code,
              )
              .toList();

          state = state.copyWith(
            listMenu: categories,
          );
        },
        error: (err) {},
      );
    } catch (error) {
      LogUtils.e("ERROR: $error");
    }
  }

  Future<void> getServices({
    String? parentId,
    int? activeMenu,
    String? tenantId,
  }) async {
    String? defaultMenu = '';
    defaultMenu = state.listMenu?.first.code;
    String parent = parentId ?? defaultMenu ?? "";

    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      request: ServiceRequestEntity(
        menuId: parent,
        tenantId: tenantId,
      ),
    );

    try {
      searchService(
        ServiceRequestEntity(
          menuId: parent,
          tenantId: tenantId,
        ),
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void changeSearchStatus(bool status) {
    state = state.copyWith(
      isSearching: status,
    );
  }

  void changeChangeQuantityOrder(num quantityOrder, int index) {
    List<ServiceInfoEntity> listService = [];

    if (state.isAddService) {
      listService = state.servicesDraft ?? [];
    } else {
      listService = state.listServiceSelected ?? [];
    }
    var servicesTmp = state.services;
    if (servicesTmp?[index] != null) {
      servicesTmp?[index].quantity = quantityOrder;
      bool hasDelete = removeBaseOnQuantity(
        quantityOrder: quantityOrder,
        servicesTmp: servicesTmp?[index],
        indexService: index,
      );
      if (hasDelete) {
        state.services?[index].isSelected = false;
      }
    }

    if (state.isAddService) {
      state = state.copyWith(
        servicesDraft: listService,
        services: servicesTmp,
      );
    } else {
      state = state.copyWith(
        listServiceSelected: listService,
        services: servicesTmp,
      );
    }
  }

  bool removeBaseOnQuantity({
    required num quantityOrder,
    ServiceInfoEntity? servicesTmp,
    required int indexService,
  }) {
    final listFilter = state.listServiceSelected ?? [];
    if (listFilter.isEmpty) return false;
    if ((servicesTmp?.code ?? '').isNotEmpty) {
      final data = listFilter
          .where((element) => element.code == servicesTmp!.code)
          .toList();

      if (quantityOrder == 0 && data.isNotEmpty) {
        listFilter.remove(data.first);
        state = state.copyWith(
          listServiceSelected: listFilter,
        );
        return true;
      } else {
        int? index = listFilter.indexOf(data.first);
        listFilter[index].quantity = quantityOrder;
      }

      state = state.copyWith(
        listServiceSelected: listFilter,
      );
    }
    return false;
  }

  void changeActiveItemService(
    int index,
    String? tenantId,
  ) {
    state = state.copyWith(
      activeItemService: index,
      request: state.request?.copyWith(
        menuId: state.listMenu![index].code,
        tenantId: tenantId,
      ),
    );
    searchService(state.request);
  }

  Future<void> searchService(
    ServiceRequestEntity? request,
  ) async {
    if (state.isAddService) {
      request = request?.copyWith(
        startTime: state.startDate,
        provinceCode: state.provinceCode,
        districtCode: state.districtCode,
      );
    }

    state = state.copyWith(
      keySearch: request?.keySearch ?? "",
    );

    final result = await appLocator<OrderRepository>().searchService(
      request: request,
    );

    await result?.when(
      success: (data) async {
        List<ServiceInfoEntity> listService = [];

        if (state.isAddService) {
          listService = state.servicesDraft ?? [];
        } else {
          listService = state.listServiceSelected ?? [];
        }

        if (listService.isNotEmpty && (data.data ?? []).isNotEmpty) {
          data.data?.forEach((element) {
            var checkExist = listService
                .where(
                  (e) => e.code == element.code,
                )
                .toList();
            if (checkExist.isNotEmpty) {
              element.isSelected = true;
              element.quantity = checkExist[0].quantity;
            }
          });
        }

        state = state.copyWith(
          services: data.data ?? [],
          loadStatus: LoadStatus.success,
        );
      },
      error: (err) {
        LogUtils.e("ERROR: $err");
      },
    );
  }

  void selectService(ServiceInfoEntity service) {
    List<ServiceInfoEntity> listService = [];

    if (state.isAddService) {
      listService = state.servicesDraft ?? [];
    } else {
      listService = state.listServiceSelected ?? [];
    }

    if (listService.isEmpty) {
      service.isSelected = true;
      if (service.quantity == 0) {
        service.quantity = 1;
      }
      listService.add(service);
    } else {
      final checkExist =
          listService.where((e) => e.code == service.code).toList();

      if (checkExist.isNotEmpty) {
        service.isSelected = false;
        listService.remove(checkExist.first);
      } else {
        service.isSelected = true;
        if (service.quantity == 0) {
          service.quantity = 1;
        }
        listService.add(service);
      }
    }

    if (state.isAddService) {
      state = state.copyWith(
        servicesDraft: listService,
      );
    } else {
      state = state.copyWith(
        listServiceSelected: listService,
      );
    }
  }

  void modifyService({
    required ServiceInfoEntity otherService,
    required String catalogOtherServiceCode,
    required int indexService,
  }) {
    List<ServiceInfoEntity> listService = [];

    if (state.isAddService) {
      listService = state.servicesDraft ?? [];
    } else {
      listService = state.listServiceSelected ?? [];
    }
    if (listService.isNotEmpty &&
        (state.services![indexService].code ?? '').isNotEmpty &&
        state.services![indexService].price != 0) {
      var checkExist = listService
          .where(
            (e) =>
                e.code == state.services![indexService].code &&
                e.price == state.services![indexService].price,
          )
          .toList();
      if (checkExist.isNotEmpty) {
        listService.remove(checkExist[0]);
        if (state.isAddService) {
          state = state.copyWith(
            servicesDraft: listService,
            draftOtherService: otherService,
            catalogOtherServiceCode: catalogOtherServiceCode,
          );
        } else {
          state = state.copyWith(
            listServiceSelected: listService,
            draftOtherService: otherService,
            catalogOtherServiceCode: catalogOtherServiceCode,
          );
        }
      }
      return;
    }
    state.services![indexService] = otherService;
    state = state.copyWith(
      draftOtherService: otherService,
      catalogOtherServiceCode: catalogOtherServiceCode,
    );
  }

  void fetchNextService({String? tenantId}) async {
    if (state.services!.length >= (state.totalResult ?? 0)) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );
    try {
      ServiceRequestEntity? request = state.request?.copyWith(
        menuId: state.listMenu?[state.activeMenu ?? 0].code,
        serviceType: listCategoryItemDefault[state.activeItemService ?? 0].code,
        keySearch: state.keySearch,
        startTime: state.startDate,
        provinceCode: state.provinceCode,
        districtCode: state.districtCode,
        page: state.currentPage,
        size: 20,
        sort: ["order", "code"],
        tenantId: tenantId,
      );
      final result = await appLocator<OrderRepository>().searchService(
        request: request,
      );

      await result?.when(
        success: (data) async {
          List<ServiceInfoEntity> listService = [];

          if (state.isAddService) {
            listService = state.servicesDraft ?? [];
          } else {
            listService = state.listServiceSelected ?? [];
          }

          if (listService.isNotEmpty && (data.data ?? []).isNotEmpty) {
            data.data?.forEach((element) {
              var checkExist = listService
                  .where(
                    (e) => e.code == element.code,
                  )
                  .toList();
              if (checkExist.isNotEmpty) {
                element.isSelected = true;
                element.quantity = checkExist[0].quantity;
              }
            });
          }

          state = state.copyWith(
            services: data.data ?? [],
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          LogUtils.e("ERROR: $err");
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }
}
