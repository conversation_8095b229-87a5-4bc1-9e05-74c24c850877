import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/product/product_info_entity.dart';
import 'package:vcc/domain/enums/gender_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/features/store/save_contact_info/save_contact_info_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/cus360_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/input_formatter_utils.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class SaveContactInfoArguments {
  final ProductInfoEntity? productInfo;

  SaveContactInfoArguments({
    this.productInfo,
  });
}

class SaveContactInfoPage extends StatefulHookConsumerWidget {
  final SaveContactInfoArguments? arguments;

  const SaveContactInfoPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<SaveContactInfoPage> createState() =>
      _SaveContactInfoPageState();
}

class _SaveContactInfoPageState extends ConsumerState<SaveContactInfoPage> {
  late TextEditingController userPhoneNumber;
  late TextEditingController userName;

  late TextEditingController taxCompany;
  late TextEditingController companyName;
  late TextEditingController companyUserContactName;
  late TextEditingController companyUserContactPhoneNumber;

  late TextEditingController noteController;
  late FocusNode userPhoneFocusNode;
  late FocusNode userNameFocusNode;

  final formUserKey = GlobalKey<FormState>();
  final formCompanyKey = GlobalKey<FormState>();

  @override
  void initState() {
    userPhoneNumber = TextEditingController();
    userName = TextEditingController();
    taxCompany = TextEditingController();
    companyName = TextEditingController();
    companyUserContactName = TextEditingController();
    companyUserContactPhoneNumber = TextEditingController();
    noteController = TextEditingController();

    userPhoneFocusNode = FocusNode();
    userNameFocusNode = FocusNode();
    super.initState();
  }

  @override
  void dispose() {
    userPhoneNumber.dispose();
    userName.dispose();
    taxCompany.dispose();
    companyName.dispose();
    companyUserContactName.dispose();
    companyUserContactPhoneNumber.dispose();
    noteController.dispose();

    userPhoneFocusNode.dispose();
    userNameFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(saveContactInfoProvider);

    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Để lại liên hệ",
      ),
      bottomAction: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: BaseButton(
            text: "Lưu",
            onTap: () {
              onSaveInfo(
                context,
                state.userType,
              );
            },
          ),
        ),
      ),
      body: Stack(
        children: [
          GestureDetector(
            onTap: () {
              FocusScope.of(context).unfocus();
            },
            child: SizedBox(
              height: double.infinity,
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    RichText(
                      text: TextSpan(
                        text: 'Loại khách hàng ',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                        children: <TextSpan>[
                          TextSpan(
                            text: '*',
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.primary,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 12),
                    Row(
                      children: <Widget>[
                        Expanded(
                          child: RadioWidget<UserType>(
                            value: UserType.personal,
                            groupValue: state.userType,
                            onChanged: (value) {
                              ref
                                  .read(saveContactInfoProvider.notifier)
                                  .changeUserType(value);
                            },
                            displayWidget: (context, item) {
                              return _displayUserText(item);
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioWidget<UserType>(
                            value: UserType.company,
                            groupValue: state.userType,
                            onChanged: (value) {
                              ref
                                  .read(saveContactInfoProvider.notifier)
                                  .changeUserType(value);
                            },
                            displayWidget: (context, item) {
                              return _displayUserText(item);
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 20),
                    state.userType == UserType.personal
                        ? _buildUserInfo()
                        : _buildCompanyInfo(),
                  ],
                ),
              ),
            ),
          ),
          state.loadCustomerStatus == LoadStatus.loading
              ? const Center(child: LoadingIndicatorWidget())
              : const SizedBox(),
        ],
      ),
    );
  }

  Widget _buildUserInfo() {
    var state = ref.watch(saveContactInfoProvider);
    String? address =
        ref.watch(saveContactInfoProvider).address?.getFullAddress;
    return Form(
      key: formUserKey,
      autovalidateMode: state.autoValidate,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            controller: userPhoneNumber,
            isRequired: true,
            labelText: "Số điện thoại",
            keyboardType: TextInputType.phone,
            maxLength: 11,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            suffix: !state.enableUserName
                ? Cus360Widget(
                    customerId: state.customerInfo?.customerId,
                    customerPhone: state.customerInfo?.phone,
                    customerType: UserType.personal.keyToServer,
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
            onFieldSubmitted: (value) {
              getCustomerInfo365(
                phoneNumber: value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                getCustomerInfo365(
                  phoneNumber: userPhoneNumber.text,
                );
              }
            },
          ),
          const SizedBox(height: 20),
          TextFieldWidget(
            controller: userName,
            isRequired: true,
            labelText: "Họ và tên",
            textInputAction: TextInputAction.done,
            enabled: state.enableUserName,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
              ),
            ],
            onFocusChange: (isFocus) {
              if (!isFocus) {
                userName.text = StringUtils.capitalizeEachWord(
                  userName.text,
                );
              }
            },
            validator: (value) {
              return ValidateUtils.onValidateUserName(value);
            },
          ),
          const SizedBox(height: 20),
          DropdownWidget(
            labelText: "Địa chỉ",
            isRequired: true,
            enabled: state.enableUserAddress,
            content: address,
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            validator: (value) {
              return ValidateUtils.onValidateAddress(value);
            },
            onTap: onSelectAddress,
          ),
          const SizedBox(height: 20),
          Text(
            'Giới tính',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: <Widget>[
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.male,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(saveContactInfoProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.female,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(saveContactInfoProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<GenderType?>(
                  value: GenderType.different,
                  groupValue: state.genderType,
                  onChanged: (value) {
                    ref
                        .read(saveContactInfoProvider.notifier)
                        .changeGenderType(value!);
                  },
                  displayWidget: (context, item) {
                    return _displayGenderText(item!);
                  },
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          TextFieldWidget.area(
            controller: noteController,
            maxLines: 4,
            maxLength: 2000,
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                RegExp(
                  Patterns.note,
                  multiLine: true,
                ),
              ),
            ],
            hintText: "Ghi chú",
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyInfo() {
    var state = ref.watch(saveContactInfoProvider);
    const key = ValueKey("company");

    return Form(
      key: formCompanyKey,
      autovalidateMode: state.autoValidate,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          TextFieldWidget(
            controller: taxCompany,
            isRequired: true,
            labelText: "Mã số thuế doanh nghiệp",
            maxLength: 14,
            textInputAction: TextInputAction.done,
            maxSizeSuffix: true,
            suffix: state.usedCus365
                ? Cus360Widget(
                    taxCode: state.companyInfo?.taxCode,
                    customerId: state.companyInfo?.customerId,
                    customerType: UserType.company.keyToServer,
                  )
                : null,
            validator: (value) {
              return ValidateUtils.onValidateTax(value);
            },
            onFieldSubmitted: (value) {
              getCompanyInfo365(
                taxCode: value,
              );
            },
            onFocusChange: (isFocus) {
              if (!isFocus) {
                getCompanyInfo365(
                  taxCode: taxCompany.text,
                );
              }
            },
          ),
          const SizedBox(height: 20),
          TextFieldWidget(
            controller: companyName,
            labelText: "Tên doanh nghiệp",
            enabled: false,
            textInputAction: TextInputAction.done,
          ),
          const SizedBox(height: 20),
          TextFieldWidget(
            controller: companyUserContactPhoneNumber,
            isRequired: true,
            labelText: "Số điện thoại người liên hệ",
            textInputAction: TextInputAction.done,
            keyboardType: TextInputType.phone,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
          ),
          const SizedBox(height: 20),
          TextFieldWidget(
            controller: companyUserContactName,
            isRequired: true,
            labelText: "Họ và tên người liên hệ",
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
              ),
            ],
            onFocusChange: (isFocus) {
              if (!isFocus) {
                companyUserContactName.text = StringUtils.capitalizeEachWord(
                  companyUserContactName.text,
                );
              }
            },
            validator: (value) {
              return ValidateUtils.onValidateUserName(value);
            },
          ),
          const SizedBox(height: 20),
          DropdownWidget(
            labelText: "Địa chỉ doanh nghiệp",
            isRequired: true,
            content: ref
                .watch(saveContactInfoProvider)
                .companyAddress
                ?.getFullAddress,
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            validator: (value) {
              return ValidateUtils.onValidateAddress(value);
            },
            onTap: onSelectAddress,
          ),
          const SizedBox(height: 20),
          TextFieldWidget.area(
            controller: noteController,
            maxLines: 4,
            maxLength: 2000,
            hintText: "Ghi chú",
            inputFormatters: [
              FilteringTextInputFormatter.allow(
                RegExp(
                  Patterns.note,
                  multiLine: true,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _displayGenderText(GenderType gender) {
    return Text(
      gender.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _displayUserText(UserType user) {
    return Text(
      user.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  void onSaveInfo(
    BuildContext context,
    UserType userType,
  ) async {
    bool? checkResult;

    if (userType == UserType.personal) {
      final form = formUserKey.currentState;

      if (form!.validate()) {
        form.save();
        checkResult =
            await ref.read(saveContactInfoProvider.notifier).saveUserInfo(
                  productCode: widget.arguments?.productInfo?.code ?? '',
                  userName: userName.text,
                  phoneNumber: userPhoneNumber.text,
                  noteContent: noteController.text,
                );
      } else {
        ref.read(saveContactInfoProvider.notifier).onValidate();
        return;
      }
    } else {
      final form = formCompanyKey.currentState;

      if (form!.validate()) {
        form.save();
        checkResult =
            await ref.read(saveContactInfoProvider.notifier).saveUserInfo(
                  productCode: widget.arguments?.productInfo?.code ?? '',
                  userName: companyUserContactName.text,
                  phoneNumber: companyUserContactPhoneNumber.text,
                  taxCode: taxCompany.text,
                  businessName: "companyName.text",
                  noteContent: noteController.text,
                );
      } else {
        ref.read(saveContactInfoProvider.notifier).onValidate();
        return;
      }
    }

    if (context.mounted) {
      if (checkResult ?? false) {
        context.pop(true);
      } else {
        AppDialog.showDialogInfo(
          context,
          message: ref.watch(saveContactInfoProvider).message ?? '',
        );
      }
    }
  }

  void onSelectAddress() async {
    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        onSelectAddress: (address) {
          ref.read(saveContactInfoProvider.notifier).selectAddress(address);
        },
      ),
    );
  }

  void getCustomerInfo365({
    String? phoneNumber,
  }) async {
    if ((phoneNumber ?? '').validatePhone()) {
      final customerInfo =
          await ref.read(saveContactInfoProvider.notifier).getCustomerInfo365(
                phoneNumber: phoneNumber,
              );

      if (customerInfo != null) {
        userName.text = customerInfo.fullName ?? '';
      }
    } else {
      ref.read(saveContactInfoProvider.notifier).enableEditForm();
    }
  }

  void getCompanyInfo365({
    String? taxCode,
  }) async {
    if ((taxCode ?? '').validateTax()) {
      final customerInfo =
          await ref.read(saveContactInfoProvider.notifier).getCompanyInfo365(
                taxCode: taxCode,
              );

      if (customerInfo != null) {
        companyName.text = customerInfo.fullName ?? '';
      }
    } else {
      ref.read(saveContactInfoProvider.notifier).cancelUse365Info();
      companyName.text = "";
    }
  }
}
