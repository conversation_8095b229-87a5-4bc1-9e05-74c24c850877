import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/body/contract_draft/create_business_plan_body.dart';
import 'package:vcc/domain/body/contract_draft/proposal_body.dart';
import 'package:vcc/domain/entities/contract_draft/business_proposal_entity.dart';
import 'package:vcc/domain/entities/contract_draft/product_contract_draft_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/features/contract_draft/add_product_service/add_product_service_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/change_approved_leader/change_approve_leader_page.dart';
import 'package:vcc/presentation/views/features/contract_draft/create_business_plan_page/create_business_plan_view_model.dart';
import 'package:vcc/presentation/views/features/contract_draft/create_business_plan_page/widget/material_item.dart';
import 'package:vcc/presentation/views/features/contract_draft/create_business_plan_page/widget/payment_information.dart';
import 'package:vcc/presentation/views/features/contract_draft/create_contract_draft_page/widget/upload_document_widget.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/card_custom_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/validate_utils.dart';

class CreateBusinessPlanArguments {
  final String? code;

  CreateBusinessPlanArguments({
    this.code,
  });
}

class CreateBusinessPlanPage extends StatefulHookConsumerWidget {
  final CreateBusinessPlanArguments? arguments;

  const CreateBusinessPlanPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<CreateBusinessPlanPage> createState() =>
      _CreateBusinessPlanPageState();
}

class _CreateBusinessPlanPageState
    extends ConsumerState<CreateBusinessPlanPage> {
  late TextEditingController presentationCodeController;
  final GlobalKey<FormState> _formKey = GlobalKey<FormState>();
  bool isUpdate = false;

  @override
  void initState() {
    Future(() async {
      presentationCodeController = TextEditingController();
      await ref.read(createBusinessPlanProvider.notifier).getDetailData(
            widget.arguments?.code ?? '',
          );

      isUpdate = ref
              .watch(createBusinessPlanProvider)
              .contractDraftDetail
              ?.createBusinessProposalStatus ??
          false;
      ref.read(createBusinessPlanProvider.notifier).getPercentCapacity();
      ref.read(createBusinessPlanProvider.notifier).setTotalContractValue(ref
          .watch(createBusinessPlanProvider)
          .contractDraftDetail
          ?.totalPriceBeforeVat);
      List<ProductEntityDraftContract> items = [];
      ref.watch(createBusinessPlanProvider).contractDraftDetail?.items?.forEach(
        (element) {
          items.add(
            ProductEntityDraftContract(
              vat: 0,
              price: 0,
              unitPrice: 0,
              itemCode: element.itemCode,
              itemName: element.itemName,
              quantity: element.quantity,
              deptCode: element.deptCode,
              isMainItem: element.isMainItem,
            ),
          );
        },
      );
      ref.read(createBusinessPlanProvider.notifier).updateAllProduct(
            products: isUpdate
                ? ref
                        .watch(createBusinessPlanProvider)
                        .businessProposal
                        ?.listBusinessProposalDetail ??
                    []
                : items,
            isUpdate: isUpdate,
          );
      if (isUpdate) {
        ref.read(createBusinessPlanProvider.notifier).initDataBusinessPlan(
            businessProposal:
                ref.watch(createBusinessPlanProvider).businessProposal ??
                    BusinessProposalEntity());
        presentationCodeController.text = ref
                .watch(createBusinessPlanProvider)
                .businessProposal
                ?.proposalResponse
                ?.code ??
            '';
      }
    });
    super.initState();
  }

  @override
  void dispose() {
    presentationCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createBusinessPlanProvider);
    return Form(
      key: _formKey,
      child: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: LayoutPage(
          appbar: AppBarCustom(
            title: '${isUpdate ? "Cập nhật" : "Tạo"} phương án triển khai',
            leadingWidget: MyAssets.icons.iconCloseS24.svg(),
          ),
          bottomAction: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Expanded(
                  child: _buttonSave(
                    isUpdate: isUpdate,
                  ),
                ),
                if (!isUpdate) ...[
                  const SizedBox(
                    width: 2,
                  ),
                ],
                if (!isUpdate) ...[
                  Expanded(
                    child: _buttonSave(
                      title: "Lưu & Tr.Ký",
                      isSaveAndSign: true,
                    ),
                  ),
                ],
              ],
            ),
          ),
          body: state.loadStatus == LoadStatus.loading
              ? const Center(
                  child: LoadingIndicatorWidget(),
                )
              : buildBody(),
        ),
      ),
    );
  }

  Widget buildBody() {
    final state = ref.watch(createBusinessPlanProvider);
    final rf = ref.read(createBusinessPlanProvider.notifier);
    return Stack(
      children: [
        SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Text(
                        'Thông tin tờ trình',
                        style: UITextStyle.body1SemiBold.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                    ),
                    InkWellWidget(
                      onTap: () => rf.changeSaleInternal(),
                      child: (state.saleInternal)
                          ? MyAssets.icons.iconChecked.svg()
                          : MyAssets.icons.iconCheckbox.svg(),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: 8,
                      ),
                      child: Text(
                        'Tờ trình riêng',
                        style: UITextStyle.body1Regular.copyWith(
                          color: BaseColors.textBody,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              if (state.saleInternal) ...[
                buildPresentationInformation(),
                UploadDocumentWidget(
                  title: 'Tờ trình đính kèm',
                  listFileDocument: state.listFileDocument,
                  deleteFile: (file) {
                    ref
                        .read(createBusinessPlanProvider.notifier)
                        .deleteFile(file);
                  },
                  onPickFile: (file) {
                    ref.read(createBusinessPlanProvider.notifier).uploadFileV2(
                          file: file,
                        );
                  },
                ),
              ],
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              PaymentInformation(
                businessProposal: isUpdate ? state.businessProposal : null,
              ),
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              buildMaterial(),
            ],
          ),
        ),
        if (state.updateStatus == LoadStatus.loading) ...[
          const Center(
            child: LoadingIndicatorWidget(),
          ),
        ],
      ],
    );
  }

  Widget buildPresentationInformation() {
    final state = ref.watch(createBusinessPlanProvider);
    String date = state.timeSetup != null
        ? DateFormat(DateTimeFormater.dateFormatVi).format(state.timeSetup!)
        : '';
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(
              bottom: 16,
            ),
            child: TextFieldWidget(
              controller: presentationCodeController,
              labelText: 'Mã tờ trình',
              isRequired: true,
              validator: (value) {
                return ValidateUtils.onValidateNotNullV2(
                  value: value,
                  title: 'Mã tờ trình',
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: 16,
            ),
            child: CardCustomWidget(
              padding: const EdgeInsets.all(16),
              titleWidget: Row(
                children: <Widget>[
                  Expanded(
                    child: state.approveLeaderSelected != null
                        ? Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                'Thủ trưởng: ${state.approveLeaderSelected?.fullName ?? ''}',
                                style: UITextStyle.body1Regular,
                              ),
                              Text(
                                state.approveLeaderSelected?.positionName ?? '',
                                style: UITextStyle.caption1Medium.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                            ],
                          )
                        : Row(
                            children: [
                              Text(
                                'Thủ trưởng phê duyệt',
                                style: UITextStyle.body1Regular,
                              ),
                              Text(
                                ' *',
                                style: UITextStyle.body1Regular.copyWith(
                                  color: BaseColors.primary,
                                ),
                              ),
                            ],
                          ),
                  ),
                  const SizedBox(width: 8),
                  MyAssets.icons.iconSearchS20.svg(),
                ],
              ),
              onPress: () async {
                AppBottomSheet.showNormalBottomSheet(
                  context,
                  title: 'Thủ trưởng phê duyệt',
                  height: MediaQuery.of(context).size.height * 0.95,
                  child: ChangeApprovedLeaderPage(
                    arguments: ChangeApprovedLeaderArguments(
                      approveLeaderSelected: state.approveLeaderSelected,
                      onSelectApproveLeader: (approveLeaders) {
                        ref
                            .read(createBusinessPlanProvider.notifier)
                            .updateApproveLeader(
                              approveLeaders: approveLeaders,
                            );
                      },
                    ),
                  ),
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.only(
              bottom: 16,
            ),
            child: DropdownWidget(
              labelText: 'Ngày ban hành',
              content: date,
              isRequired: true,
              suffix: MyAssets.icons.iconCalendarS24.svg(),
              validator: (value) {
                return ValidateUtils.onValidateNotNullV2(
                  value: value,
                  title: 'Ngày ban hành',
                );
              },
              onTap: () async {
                DateTime now = DateTime.now();
                openDatetimePicker(
                  date: state.timeSetup ?? now,
                  minDate: now,
                  onSubmit: (date) {
                    ref
                        .read(createBusinessPlanProvider.notifier)
                        .setUpTime(date);
                  },
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  void openDatetimePicker({
    required DateTime date,
    DateTime? minDate,
    DateTime? maxDate,
    required Function(DateTime date) onSubmit,
  }) {
    DateTime? minDateTime;
    DateTime? maxDateTime;
    DateTime initialDateTime =
        DateTime(date.year, date.month, date.day, 0, 0, 0);
    if (minDate != null) {
      minDateTime = DateTime(minDate.year, minDate.month, minDate.day, 0, 0, 0);
      if (initialDateTime.isBefore(minDateTime)) {
        initialDateTime = minDateTime;
      }
    }
    if (maxDate != null) {
      maxDateTime =
          DateTime(maxDate.year, maxDate.month, maxDate.day, 23, 59, 59);
      if (initialDateTime.isAfter(maxDateTime)) {
        initialDateTime = maxDateTime;
      }
    }

    CustomBottomPicker.date(
      height: MediaQuery.of(context).size.height * 0.40,
      title: 'Chọn thời gian',
      buttonText: 'Xác nhận',
      minDateTime: minDateTime,
      maxDateTime: maxDateTime,
      initialDateTime: initialDateTime,
      titleStyle: UITextStyle.body1SemiBold,
      dateOrder: DatePickerDateOrder.dmy,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      onSubmit: (date) => onSubmit(date),
    ).show(context);
  }

  Widget buildMaterial() {
    final state = ref.watch(createBusinessPlanProvider);
    final rf = ref.read(createBusinessPlanProvider.notifier);
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.all(
            16,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Danh sách vật tư',
                style: UITextStyle.body1SemiBold.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
              InkWellWidget(
                onTap: () async {
                  await context.push(
                    RouterPaths.addProductServicePage,
                    extra: AddProductServiceArguments(
                      isCreateBusinessPlan: true,
                      listSelected: List.from(state.products ?? []),
                      onSelect: (managementUnit) {
                        rf.setListProductSelected(
                          listProductSelected: managementUnit,
                        );
                      },
                    ),
                  );
                },
                child: Row(
                  children: [
                    MyAssets.icons.iconAddCircle.svg(
                      colorFilter: ColorFilter.mode(
                        BaseColors.primary,
                        BlendMode.srcIn,
                      ),
                    ),
                    Padding(
                      padding: const EdgeInsets.only(
                        left: BaseSpacing.spacing1,
                      ),
                      child: Text(
                        'Thêm vật tư',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
        const Padding(
          padding: EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: DividerWidget(),
        ),
        state.updateProductStatus == LoadStatus.loading
            ? const Center(child: LoadingIndicatorWidget())
            : ListView.separated(
                itemCount: (state.products ?? []).length,
                shrinkWrap: true,
                padding: const EdgeInsets.all(
                  16,
                ),
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (BuildContext context, int index) {
                  ProductEntityDraftContract product = state.products![index];
                  return MaterialItem(
                    index: index,
                    isUpdate: isUpdate,
                    product: product,
                    onDelete: () {
                      AppDialog.showDialogConfirm(
                        context,
                        title: "Xác nhận",
                        message:
                            "Bạn chắn chắn xóa sản phẩm ${product.itemCode ?? ''}?",
                        onConfirmAction: () {
                          rf.onDeleteProduct(
                            index: index,
                          );
                        },
                      );
                    },
                    onChangeItem: (product) {
                      ref
                          .read(createBusinessPlanProvider.notifier)
                          .updateProduct(
                            index: index,
                            item: product,
                          );
                    },
                  );
                },
                separatorBuilder: (BuildContext context, int index) {
                  return const SizedBox(
                    height: 24,
                  );
                },
              ),
      ],
    );
  }

  Widget _buttonSave({
    bool? isSaveAndSign,
    bool? isUpdate,
    String? title,
  }) {
    final state = ref.watch(createBusinessPlanProvider);
    final rf = ref.watch(createBusinessPlanProvider.notifier);
    return BaseButton(
      isLoading: state.loadStatus == LoadStatus.loading,
      text: title ?? "Lưu",
      onTap: () async {
        FocusScope.of(context).unfocus();
        if (!_formKey.currentState!.validate()) {
          return;
        }
        if (state.saleInternal && state.approveLeaderSelected == null) {
          return ErrorDialog.showErrorDialog(
            'Vui lòng chọn thủ trưởng.',
          );
        }
        if(state.saleInternal && (state.listFileDocument ?? []).isEmpty){
          return ErrorDialog.showErrorDialog(
            'Vui lòng tải lên tờ trình đính kèm.',
          );
        }
        if (state.companyBranchRatio == 0) {
          return ErrorDialog.showErrorDialog(
            'Vui lòng nhập tỉ lệ định mức hạch toán tại CNCT.',
          );
        }
        if (state.materialRatio == null || (state.products ?? []).isEmpty) {
          return ErrorDialog.showErrorDialog(
            'Vui lòng thêm sản phẩm.',
          );
        }

        // Tính tổng KHÔNG làm tròn để so sánh chính xác
        double total = (state.materialRatio ?? 0) +
            (state.workerRatio ?? 0) +
            (state.commissionRatio ?? 0) +
            (state.otherRatio ?? 0);

        // So sánh với độ chính xác cao (chấp nhận sai số rất nhỏ do floating point)
        if ((total - (state.companyBranchRatio ?? 0)).abs() > 0.0001) {
          return ErrorDialog.showErrorDialog(
            'Tổng định mức C1+C2+C3+C4 phải bằng C.',
          );
        }

        ProposalBody proposalBody = ProposalBody(
          code: presentationCodeController.text.trim(),
          approvedBy: state.approveLeaderSelected?.sysUserId,
          approvedCode: state.approveLeaderSelected?.username,
          issueDate: state.timeSetup,
          urls: state.listFileDocument?.map((file) => file.link ?? '').toList(),
        );
        CreateBusinessPlanBody body = CreateBusinessPlanBody(
          draftContractCode: state.contractDraftDetail?.code,
          corporateAccountingRatio: state.accountingForTheEntireConstruction,
          corporateVat: state.accountingForTheEntireConstructionVAT,
          branchAccountingRatio: state.companyBranchRatio ?? 0,
          aioConfigureSolarRateId: state.aioConfigureSolarRateId,
          branchVat: state.companyBranchVAT,
          materialCostRatio: state.materialRatio ?? 0,
          materialVat: state.materialVAT,
          laborRatio: state.workerRatio ?? 0,
          laborVat: state.workerFee,
          commissionRate: state.commissionRatio ?? 0,
          commissionVat: state.commissionFee,
          otherCost: state.otherRatio ?? 0,
          otherCostVat: state.otherFee,
          isIndividualProposal: state.saleInternal,
          listBusinessProposalDetail: state.products ?? [],
          proposal: state.saleInternal == true ? proposalBody : null,
          createSign: isSaveAndSign,
        );
        bool? isSaveBusinessPlan;
        if (isUpdate ?? false) {
          isSaveBusinessPlan = await rf.updateBusinessPlan(
            body,
          );
        } else {
          isSaveBusinessPlan = await rf.onSaveBusinessPlan(
            body,
          );
        }
        if (!mounted) return;
        if (isSaveBusinessPlan ?? false) {
          AppDialog.showDialogInfo(
            barrierDismissible: false,
            context,
            message: isSaveAndSign ?? false
                ? 'Trình ký phương án triển khai thành công.'
                : 'Lưu phương án triển khai thành công.',
            onConfirmAction: () => context.pop(
              true,
            ),
          );
        }
      },
    );
  }

  double roundTo2Decimal(double value) {
    return (value * 100).roundToDouble() / 100;
  }
}
