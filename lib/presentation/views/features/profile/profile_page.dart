import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/generated/l10n.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/authentication/verify_otp/verify_otp_page.dart';
import 'package:vcc/presentation/views/features/profile/profile_view_model.dart';
import 'package:vcc/presentation/views/features/profile/widgets/menu_item.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';

class ProfileArguments {
  final Function? onLogout;

  ProfileArguments({
    this.onLogout,
  });
}

class ProfilePage extends StatefulHookConsumerWidget {
  final ProfileArguments? arguments;

  const ProfilePage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<ProfilePage> createState() => _ProfilePageState();
}

class _ProfilePageState extends ConsumerState<ProfilePage> {
  @override
  void initState() {
    Future(() {
      ref.read(profileProvider.notifier).getProfile();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(profileProvider);

    return LayoutPage(
      backgroundColor: BaseColors.backgroundGray,
      appbar: AppBarCustom(
        isShowNavigation: false,
        title: AppStrings.current.profile,
      ),
      bottomAction: Padding(
        padding: const EdgeInsets.only(bottom: 36),
        child: Text(
          "Phiên bản: ${GlobalData.instance.version ?? ''}",
          style: UITextStyle.body2Regular.copyWith(
            color: BaseColors.textBody,
          ),
          textAlign: TextAlign.center,
        ),
      ),
      body: ListView(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 16,
            ),
            child: InkWellWidget(
              onTap: () {
                context.push(
                  RouterPaths.userInfo,
                );
              },
              child: Container(
                decoration: BoxDecoration(
                  color: BaseColors.backgroundWhite,
                  borderRadius: BorderRadius.circular(12),
                ),
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Text(
                            state.userInfo?.fullName ?? '',
                            style: UITextStyle.heading3SemiBold.copyWith(
                              color: BaseColors.textTitle,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            "Tài khoản: ${state.userInfo?.username ?? ''}",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(width: 8),
                    MyAssets.icons.iconArrowRightS20.svg(),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            "Thiết lập",
            style: UITextStyle.body2SemiBold.copyWith(
              color: BaseColors.textSubtitle,
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: BaseColors.backgroundWhite,
              borderRadius: BorderRadius.circular(12),
            ),
            margin: const EdgeInsets.symmetric(
              vertical: 16,
            ),
            child: Column(
              children: <Widget>[
                MenuItem(
                  icon: MyAssets.icons.iconSettingS24.svg(),
                  title: "Cài đặt quyền",
                  isShowDivider: false,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(16),
                    topRight: Radius.circular(16),
                  ),
                  onTap: () {
                    context.push(
                      RouterPaths.permissionSetting,
                    );
                  },
                ),
                MenuItem(
                  icon: MyAssets.icons.iconLock.svg(),
                  title: "Đổi mật khẩu",
                  isShowDivider: false,
                  onTap: () async {
                    final result = await context.push(
                      RouterPaths.changePassword,
                    );

                    if (result == true) {
                      if (!context.mounted) return;
                      AppDialog.showDialogInfo(
                        context,
                        message:
                            "Đổi mật khẩu thành công.\nVui lòng đăng nhập lại!",
                        barrierDismissible: false,
                        buttonNameConfirm: "Xác nhận",
                        onConfirmAction: () async {
                          if (GlobalData.instance.userInfo == null) {
                            widget.arguments?.onLogout?.call();
                          } else {
                            await ref.read(profileProvider.notifier).doLogout();
                            GlobalData.instance.userInfo = null;
                            widget.arguments?.onLogout?.call();
                          }
                        },
                      );
                    }
                  },
                ),
                MenuItem(
                  icon: MyAssets.icons.iconNotificationS24.svg(),
                  title: "Cài đặt thông báo",
                  isShowDivider: false,
                  onTap: () {},
                ),
                MenuItem(
                  icon: MyAssets.icons.iconDeleteAccount.svg(),
                  title: "Xoá tài khoản",
                  isShowDivider: false,
                  onTap: () {
                    context.push(
                      RouterPaths.verifyOtp,
                      extra: VerifyOtpArguments(
                        title: "Xóa tài khoản",
                        phoneNumber: state.userInfo?.phoneNumber ?? '',
                        onVerifySuccess: () {},
                      ),
                    );
                  },
                ),
                MenuItem(
                  icon: MyAssets.icons.iconLogout.svg(
                    colorFilter: ColorFilter.mode(
                      BaseColors.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                  title: "Đăng xuất",
                  textStyle: UITextStyle.body1Medium.copyWith(
                    color: BaseColors.primary,
                  ),
                  isShowDivider: false,
                  isRightWidget: false,
                  borderRadius: const BorderRadius.only(
                    bottomLeft: Radius.circular(16),
                    bottomRight: Radius.circular(16),
                  ),
                  onTap: () {
                    AppDialog.showDialogConfirm(
                      context,
                      message:
                          "Bạn chắc chắn muốn đăng xuất tài khoản ${state.userInfo?.username ?? ''}?",
                      onConfirmAction: () async {
                        if (GlobalData.instance.userInfo == null) {
                          widget.arguments?.onLogout?.call();
                        } else {
                          await ref.read(profileProvider.notifier).doLogout();
                          GlobalData.instance.userInfo = null;
                          widget.arguments?.onLogout?.call();
                        }
                      },
                    );
                  },
                ),
              ],
            ),
          )
        ],
      ),
    );
  }
}
