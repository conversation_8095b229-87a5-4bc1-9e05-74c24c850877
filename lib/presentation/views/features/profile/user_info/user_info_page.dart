import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/enums/address_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/gender_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/position_user_type.dart';
import 'package:vcc/domain/enums/user_company_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/bottom_sheet/change_gender/change_gender_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/transit_center_address/transit_center_address_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_job/select_job_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_image/upload_image.dart';
import 'package:vcc/presentation/views/features/profile/user_info/user_info_view_model.dart';
import 'package:vcc/presentation/views/features/profile/user_info/widgets/row_item.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/expandable.dart';
import 'package:vcc/presentation/views/widgets/expandable_form.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/pages/pdf_view_screen.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/show_loading_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class UserInfoPage extends StatefulHookConsumerWidget {
  const UserInfoPage({
    super.key,
  });

  @override
  ConsumerState<UserInfoPage> createState() => _UserInfoPageState();
}

class _UserInfoPageState extends ConsumerState<UserInfoPage> {
  late ExpandableController _userInfoController;
  late ExpandableController _userIdentityController;
  late ExpandableController _managerCompanyController;
  late ExpandableController _workAddressController;
  late ExpandableController _groupSaleController;
  late ExpandableController _collaboratorController;

  @override
  void initState() {
    _userInfoController = ExpandableController(
      initialExpanded: true,
    );
    _userIdentityController = ExpandableController(
      initialExpanded: false,
    );
    _managerCompanyController = ExpandableController(
      initialExpanded: false,
    );
    _workAddressController = ExpandableController(
      initialExpanded: false,
    );
    _groupSaleController = ExpandableController(
      initialExpanded: false,
    );
    _collaboratorController = ExpandableController(
      initialExpanded: false,
    );

    Future(() async {
      await ref.read(userInfoProvider.notifier).getProfile();
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(userInfoProvider);

    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      appBar: const AppBarCustom(
        title: 'Hồ sơ',
      ),
      body: state.loadStatus == LoadStatus.loading
          ? const Center(
              child: LoadingIndicatorWidget(),
            )
          : Stack(
              children: [
                ListView(
                  children: <Widget>[
                    _buildUserInfo(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    _buildManagerCompany(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    _buildWordAddress(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    _buildSaleGroup(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    _buildUserIdentity(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    if (state.userInfo?.personalInfo?.isGroupCollaborator ??
                        false) ...[
                      _buildCollaboratorInfo(),
                      DividerWidget(
                        height: 8,
                        color: BaseColors.backgroundGray,
                      ),
                    ],
                  ],
                ),
                if (state.updateStatus == LoadStatus.loading)
                  const Center(
                    child: LoadingIndicatorWidget(),
                  ),
              ],
            ),
    );
  }

  Widget _buildUserInfo() {
    var state = ref.watch(userInfoProvider);
    var userInfo = state.userInfo?.personalInfo;

    return ExpandableForm(
      controller: _userInfoController,
      title: "Thông tin cá nhân",
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Container(),
          RowItem(
            title: "Mã nhân viên/ Tên đăng nhập",
            content: userInfo?.username,
          ),
          RowItem(
            title: "Đối tượng",
            customContent: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                Text(
                  userInfo?.title ?? '',
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
                if ((userInfo?.isSalePoint ?? false) &&
                    (userInfo?.position?.displayGeneralTitle ?? '')
                        .isNotEmpty) ...[
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: BaseColors.borderDefault,
                      ),
                    ),
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    child: Text(
                      userInfo?.position?.displayGeneralTitle ?? '',
                    ),
                  ),
                ],
              ],
            ),
          ),
          RowItem(
            title: "Chức danh",
            content: userInfo?.positionName,
            visible: !(userInfo?.isSatelliteCollaborator ?? false),
          ),
          RowItem(
            title: "Họ và tên",
            content: userInfo?.fullName,
          ),
          RowItem(
            title: "Số điện thoại",
            content: userInfo?.phoneNumber,
          ),
          RowItem(
            title: "Email",
            content: userInfo?.email,
            showEdit: userInfo?.userType != UserCompanyType.internal,
            onTap: () {
              onChangeEmail(context);
            },
          ),
          RowItem(
            title: "Địa chỉ",
            content: userInfo?.address,
            showEdit: true,
            onTap: () {
              onChangeAddress(context);
            },
          ),
          RowItem(
            title: "Công việc",
            content: userInfo?.jobName,
            showEdit: true,
            visible: state.userInfo?.personalInfo?.userType ==
                UserCompanyType.external,
            onTap: () {
              onSelectJob(context);
            },
          ),
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Text(
              "Ảnh yêu cầu khác:",
              style: UITextStyle.body2Regular.copyWith(
                color: BaseColors.textSubtitle,
              ),
            ),
          ),
          const SizedBox(height: 12),
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: SizedBox(
              height: 80,
              child: ListView.separated(
                itemCount: (state.listImage ?? []).length + 1,
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(right: 16),
                separatorBuilder: (_, __) => const SizedBox(width: 8),
                itemBuilder: (context, index) {
                  if (index == (state.listImage ?? []).length) {
                    if (state.uploadImageStatus == LoadStatus.loading) {
                      return const SizedBox(
                        height: 80,
                        width: 80,
                        child: Padding(
                          padding: EdgeInsets.all(30),
                          child: Center(
                            child: LoadingCacheImageWidget(
                              loadingSize: 25,
                            ),
                          ),
                        ),
                      );
                    } else {
                      return (state.listImage ?? []).length < 5
                          ? InkWellWidget(
                              onTap: () {
                                onTakePicture(context);
                              },
                              child:
                                  MyAssets.icons.iconUploadImageDashLine.svg(),
                            )
                          : const SizedBox();
                    }
                  } else {
                    return Stack(
                      children: [
                        Container(
                          height: 80,
                          width: 80,
                          padding: const EdgeInsets.only(
                            top: 4,
                            right: 4,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: ImageWidget(
                              state.listImage![index],
                              enableShowPreview: true,
                              size: const Size(76, 76),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: InkWellWidget(
                            onTap: () {
                              ref
                                  .read(userInfoProvider.notifier)
                                  .deleteImage(index);
                            },
                            child: MyAssets.icons.iconCloseRed.svg(),
                          ),
                        ),
                      ],
                    );
                  }
                },
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserIdentity() {
    var state = ref.watch(userInfoProvider);
    var userIdentity = state.userInfo?.userIdentity;
    GenderType? gender = GenderTypeExtension.fromString(userIdentity?.gender);

    return ExpandableForm(
      controller: _userIdentityController,
      title: "Thông tin CCCD",
      child: Column(
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: Text(
                    "Ảnh CCCD",
                    style: UITextStyle.body2Regular.copyWith(
                      color: BaseColors.textSubtitle,
                    ),
                  ),
                ),
                AppTextButton(
                  title: "Cập nhật",
                  titleStyle: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.info,
                  ),
                  onTap: () {
                    onChangeIdentityUser(context);
                  },
                ),
              ],
            ),
          ),
          Container(
            margin: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            padding: const EdgeInsets.only(
              top: 12,
              bottom: 8,
            ),
            child: Row(
              children: <Widget>[
                Expanded(
                  child: Container(
                    height: 108,
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: BaseColors.borderDefault,
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: ImageWidget(
                      userIdentity?.frontImageUrl ?? '',
                      enableShowPreview: true,
                      size: const Size(76, 76),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Container(
                    height: 108,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: BaseColors.borderDefault,
                      ),
                    ),
                    child: ImageWidget(
                      userIdentity?.backImageUrl ?? '',
                      enableShowPreview: true,
                      size: const Size(76, 76),
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ],
            ),
          ),
          RowItem(
            title: "Số CCCD",
            content: userIdentity?.cardNumber,
            showEdit: true,
            onTap: () {
              onChangeIdentityNumber(context);
            },
          ),
          RowItem(
            title: "Ngày sinh",
            content: userIdentity?.dob ?? '',
            showEdit: true,
            onTap: () {
              selectDateOfBirth();
            },
          ),
          RowItem(
            title: "Giới tính",
            content: gender?.display,
            showEdit: true,
            onTap: () {
              onChangeGender(context);
            },
          ),
          RowItem(
            title: "Ngày cấp",
            content: userIdentity?.issuedDate,
            showEdit: true,
            onTap: () {
              selectDateIdentity();
            },
          ),
          RowItem(
            title: "Nơi cấp",
            content: userIdentity?.issuedPlace,
            showEdit: true,
            onTap: () {
              onChangeIdentityAddress(context);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildManagerCompany() {
    var state = ref.watch(userInfoProvider);
    var companyInfo = state.userInfo?.vccInfo;

    return ExpandableForm(
      controller: _managerCompanyController,
      title: "Thông tin quản lý VCC",
      child: Column(
        children: <Widget>[
          RowItem(
            title: "Người giới thiệu",
            content: companyInfo?.referrerFullName,
            visible:
                state.userInfo?.personalInfo?.isSatelliteCollaborator ?? false,
          ),
          RowItem(
            title: "Quản lý/ trưởng nhóm",
            content:
                "${companyInfo?.managerFullName ?? ''} - ${companyInfo?.managerPhoneNumber ?? ''}",
          ),
          RowItem(
            title: "Đơn vị quản lý",
            content:
                "${companyInfo?.managementUnitInfo?.level2 ?? ''} - ${companyInfo?.managementUnitInfo?.level3 ?? ''}",
          ),
          RowItem(
            title: "ĐKKD/Trụ",
            content: companyInfo?.businessRegistration,
            visible:
                (state.userInfo?.personalInfo?.isGroupCollaborator ?? false) ||
                    (state.userInfo?.personalInfo?.isSatelliteCollaborator ??
                        false),
          ),
        ],
      ),
    );
  }

  Widget _buildWordAddress() {
    var state = ref.watch(userInfoProvider);

    return ExpandableForm(
      controller: _workAddressController,
      title: "Thông tin địa điểm làm việc",
      child: Column(
        children: <Widget>[
          RowItem(
            title: "Đơn vị",
            content:
                "${state.userInfo?.workingLocation?.workingUnitInfo?.level2 ?? ''} - ${state.userInfo?.workingLocation?.workingUnitInfo?.level3 ?? ''}",
          ),
        ],
      ),
    );
  }

  Widget _buildSaleGroup() {
    var state = ref.watch(userInfoProvider);
    var saleGroupInfo = state.userInfo?.saleGroupInfo;

    return ExpandableForm(
      controller: _groupSaleController,
      title: "Thông tin nhóm bán hàng",
      child: Column(
        children: <Widget>[
          RowItem(
            title: "Điểm bán hàng",
            content: "Có",
            visible:
                state.userInfo?.personalInfo?.isSatelliteCollaborator ?? false,
          ),
          RowItem(
            title: "Tên nhóm",
            content: saleGroupInfo?.groupCode,
          ),
          RowItem(
            title: "Loại nhóm",
            content: saleGroupInfo?.saleGroupTypeName ?? '',
          ),
          RowItem(
            title: "Quản lý điểm bán",
            content: saleGroupInfo?.isSalePoint == true ? "Có" : "Không",
            visible: !(state.userInfo?.personalInfo?.isSatelliteCollaborator ??
                false),
          ),
        ],
      ),
    );
  }

  Widget _buildCollaboratorInfo() {
    var state = ref.watch(userInfoProvider);
    var collaboratorInfo = state.userInfo?.collaboratorProfile;

    return ExpandableForm(
      controller: _collaboratorController,
      title: "Hồ sơ CTV",
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          RowItem(
            title: "Số điện thoại Viettel Money",
            content: collaboratorInfo?.vtMoneyPhoneNumber ?? '',
          ),
          Padding(
            padding: const EdgeInsets.only(
              left: 16,
              top: 10,
            ),
            child: Text(
              "Ảnh hộ khẩu:",
              style: UITextStyle.body2Regular.copyWith(
                color: BaseColors.textSubtitle,
              ),
            ),
          ),
          const SizedBox(height: 12),
          if ((collaboratorInfo?.householdRegistryUrls?.length ?? 0) > 0) ...[
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: SizedBox(
                height: 80,
                child: ListView.separated(
                  itemCount:
                      collaboratorInfo?.householdRegistryUrls?.length ?? 0,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(right: 16),
                  separatorBuilder: (_, __) => const SizedBox(width: 8),
                  itemBuilder: (context, index) {
                    final item =
                        collaboratorInfo!.householdRegistryUrls![index];

                    return Container(
                      height: 76,
                      width: 76,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: ImageWidget(
                          item,
                          enableShowPreview: true,
                          size: const Size(76, 76),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
            const SizedBox(height: 10),
          ],
          _buildListDocument(
            title: "File hợp đồng",
            listData: collaboratorInfo?.contractFileUrls,
          ),
          _buildListDocument(
            title: "File phụ lục",
            listData: collaboratorInfo?.appendixContractUrls,
          ),
          _buildListDocument(
            title: "File hợp đồng bổ sung",
            listData: collaboratorInfo?.additionalContractUrls,
          ),
          RowItem(
            title: "Người đại diện",
            content: collaboratorInfo?.representativeName,
          ),
          RowItem(
            title: "Ngân hàng",
            content: collaboratorInfo?.bankAccountInfo?.bankName,
          ),
          RowItem(
            title: "Chi nhánh",
            content: collaboratorInfo?.bankAccountInfo?.branchName,
          ),
          RowItem(
            title: "Số tài khoản",
            content: collaboratorInfo?.bankAccountInfo?.accountNumber,
          ),
          RowItem(
            title: "Ngành hàng",
            content: collaboratorInfo?.businessType,
          ),
          RowItem(
            title: "Ngày đăng ký",
            content: (collaboratorInfo?.registrationDate ?? '').isNotEmpty
                ? DateFormat(DateTimeFormater.dateFormatVi).format(
                    DateTime.parse(collaboratorInfo?.registrationDate ?? ''),
                  )
                : '',
          ),
        ],
      ),
    );
  }

  Widget _buildListDocument({
    required String title,
    List<String>? listData,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 10,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.only(left: 16),
            child: Text(
              "$title:",
              style: UITextStyle.body2Regular.copyWith(
                color: BaseColors.textSubtitle,
              ),
            ),
          ),
          if ((listData?.length ?? 0) > 0) ...[
            const SizedBox(height: 12),
            ListView.separated(
              itemCount: listData?.length ?? 0,
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              separatorBuilder: (_, __) => const SizedBox(height: 8),
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                final item = listData![index];
                final fileName = item.split('/').last;

                return InkWellWidget(
                  onTap: () {
                    context.push(
                      RouterPaths.viewPdf,
                      extra: PdfViewArguments(
                        fileName: title,
                        url: item,
                      ),
                    );
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 14,
                    ),
                    decoration: BoxDecoration(
                      color: BaseColors.backgroundGray1,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      children: <Widget>[
                        MyAssets.icons.iconPdfGray.svg(),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            fileName,
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        MyAssets.icons.iconEyeRedBorder.svg(),
                      ],
                    ),
                  ),
                );
              },
            ),
          ],
        ],
      ),
    );
  }

  void onChangeEmail(BuildContext context) async {
    TextEditingController emailController = TextEditingController(
      text: ref.read(userInfoProvider).userInfo?.personalInfo?.email,
    );

    GlobalKey<FormState> formKey = GlobalKey<FormState>();

    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Email",
      isFlexible: true,
      child: Form(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextFieldWidget(
                controller: emailController,
                textInputAction: TextInputAction.done,
                autofocus: true,
                hintText: "Nhập email",
                validator: (validator) {
                  return ValidateUtils.onValidateEmail(emailController.text);
                },
              ),
            ),
            const SizedBox(height: 8),
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: BaseButton(
                  text: "Cập nhật",
                  onTap: () async {
                    final form = formKey.currentState;
                    if (form!.validate()) {
                      await ref.read(userInfoProvider.notifier).updateUserInfo(
                            email: emailController.text,
                          );

                      if (ref.watch(userInfoProvider).updateStatus ==
                          LoadStatus.success) {
                        if (!context.mounted) return;

                        context.pop();
                        AppDialog.showDialogCenter(
                          context,
                          message: "Cập nhật email thành công!",
                          status: DialogStatus.success,
                        );
                      }
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void onChangeAddress(BuildContext context) {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Cập nhật địa chỉ",
      height: MediaQuery.of(context).size.height * 0.95,
      child: TransitCenterAddressView(
        maxSelect: AddressType.fullAddress,
        onSelectAddress: (address) async {
          await ref.read(userInfoProvider.notifier).updateUserInfo(
                address: address,
              );

          if (ref.watch(userInfoProvider).updateStatus == LoadStatus.success) {
            if (!context.mounted) return;

            AppDialog.showDialogCenter(
              context,
              message: "Cập nhật địa chỉ thành công!",
              status: DialogStatus.success,
            );
          }
        },
      ),
    );
  }

  void onSelectJob(BuildContext context) async {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Công việc",
      height: MediaQuery.of(context).size.height * 0.95,
      child: SelectJobView(
        isFromUserProfile: true,
        jobSelected: null,
        onSelect: (data) async {
          await ref.read(userInfoProvider.notifier).updateUserInfo(
                job: data,
              );

          if (ref.watch(userInfoProvider).updateStatus == LoadStatus.success) {
            if (!context.mounted) return;

            AppDialog.showDialogCenter(
              context,
              message: "Cập nhật công việc thành công!",
              status: DialogStatus.success,
            );
          }
        },
      ),
    );
  }

  void onChangeIdentityUser(BuildContext context) async {
    final result = await context.push(
      RouterPaths.takeImageIdentifier,
    );

    if (result is List) {
      ShowLoadingUtils.instance.turnOn();
      await ref.read(userInfoProvider.notifier).verifyIdentityUser(
            frontImageUrl: result[0],
            backImageUrl: result[1],
            // frontImageUrl:
            //     "https://apis.congtrinhviettel.com.vn/hsdt/file/static/2025/02/06/1344525B74C1BBA366E3964545C9B9800FE40C.jpg",
            // backImageUrl:
            //     "https://apis.congtrinhviettel.com.vn/hsdt/file/static/2025/02/06/134455A5CED24C022333AA961E3914D6C2D5D3.jpg",
          );

      ShowLoadingUtils.instance.turnOff();

      if (ref.watch(userInfoProvider).updateStatus == LoadStatus.success &&
          ref.watch(userInfoProvider).verifyIdentityUserStatus ==
              LoadStatus.success) {
        if (!context.mounted) return;

        AppDialog.showDialogCenter(
          context,
          message: "Cập nhật CCCD thành công!",
          status: DialogStatus.success,
        );
      }
    }
  }

  void onChangeIdentityNumber(BuildContext context) async {
    TextEditingController identityNumberController = TextEditingController(
      text: ref.read(userInfoProvider).userInfo?.userIdentity?.cardNumber,
    );
    GlobalKey<FormState> formKey = GlobalKey<FormState>();

    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Số CCCD",
      isFlexible: true,
      child: Form(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextFieldWidget(
                controller: identityNumberController,
                isRequired: true,
                labelText: "Số CCCD",
                maxLength: 12,
                autofocus: true,
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.done,
                validator: (value) {
                  if (value.isEmpty) {
                    return "Vui lòng nhập số CCCD";
                  } else if (value.length != 9 && value.length != 12) {
                    return "CCCD không đúng định dạng";
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 8),
            SafeArea(
              child: Padding(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                child: BaseButton(
                  text: "Cập nhật",
                  onTap: () async {
                    final form = formKey.currentState;
                    if (form!.validate()) {
                      await ref.read(userInfoProvider.notifier).updateUserInfo(
                            cardNumber: identityNumberController.text,
                          );

                      if (ref.watch(userInfoProvider).updateStatus ==
                          LoadStatus.success) {
                        if (!context.mounted) return;

                        context.pop();
                        AppDialog.showDialogCenter(
                          context,
                          message: "Cập nhật số CCCD thành công!",
                          status: DialogStatus.success,
                        );
                      }
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void onChangeIdentityAddress(BuildContext context) async {
    TextEditingController identityAddressController = TextEditingController(
      text: ref.read(userInfoProvider).userInfo?.userIdentity?.issuedPlace,
    );
    GlobalKey<FormState> formKey = GlobalKey<FormState>();

    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Nơi cấp",
      isFlexible: true,
      child: Form(
        key: formKey,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: <Widget>[
            Padding(
              padding: const EdgeInsets.all(16),
              child: TextFieldWidget(
                controller: identityAddressController,
                textInputAction: TextInputAction.done,
                autofocus: true,
                validator: (value) {
                  if (value.isEmpty) {
                    return "Vui lòng nhập nơi cấp CCCD";
                  }
                  return null;
                },
              ),
            ),
            const SizedBox(height: 8),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              child: SafeArea(
                child: BaseButton(
                  text: "Cập nhật",
                  onTap: () async {
                    final form = formKey.currentState;
                    if (form!.validate()) {
                      await ref.read(userInfoProvider.notifier).updateUserInfo(
                            issuedPlace: identityAddressController.text,
                          );

                      if (ref.watch(userInfoProvider).updateStatus ==
                          LoadStatus.success) {
                        if (!context.mounted) return;

                        context.pop();
                        AppDialog.showDialogCenter(
                          context,
                          message: "Cập nhật số CCCD thành công!",
                          status: DialogStatus.success,
                        );
                      }
                    }
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void selectDateIdentity() {
    var dob = DateTime.now().subtract(const Duration(days: 1));

    CustomBottomPicker.date(
      height: MediaQuery.of(context).size.height * 0.40,
      title: "Ngày cấp",
      buttonText: "Xác nhận",
      initialDateTime: dob,
      minDateTime: DateTime(2016, 1, 1),
      maxDateTime: DateTime.now(),
      titleStyle: UITextStyle.body1SemiBold,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      dateOrder: DatePickerDateOrder.dmy,
      onSubmit: (date) {
        ref.read(userInfoProvider.notifier).updateUserInfo(
              issuedDate: date,
            );
      },
    ).show(context);
  }

  void selectDateOfBirth() {
    var dob = DateTime.now().subtract(const Duration(days: 1));

    CustomBottomPicker.date(
      height: MediaQuery.of(context).size.height * 0.40,
      title: "Ngày sinh",
      buttonText: "Xác nhận",
      initialDateTime: dob.subtract(const Duration(days: 1)),
      maxDateTime: dob.add(const Duration(seconds: 1)),
      titleStyle: UITextStyle.body1SemiBold,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      dateOrder: DatePickerDateOrder.dmy,
      onSubmit: (date) {
        ref.read(userInfoProvider.notifier).changeDateOfBirth(date);
      },
    ).show(context);
  }

  void onChangeGender(BuildContext context) async {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Giới tính",
      isFlexible: true,
      child: ChangeGenderView(
        gender: ref.watch(userInfoProvider).genderType,
        onUpdate: (gender) async {
          await ref.read(userInfoProvider.notifier).updateUserInfo(
                gender: gender.keyToServer,
              );

          if (ref.watch(userInfoProvider).updateStatus == LoadStatus.success) {
            if (!context.mounted) return;

            context.pop();
            AppDialog.showDialogCenter(
              context,
              message: "Cập nhật giới tính thành công!",
              status: DialogStatus.success,
            );
          }
        },
      ),
    );
  }

  void onTakePicture(BuildContext context) async {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Upload ảnh",
      isFlexible: true,
      child: UploadImageBottomSheet(
        onPickImage: (file) async {
          ref.read(userInfoProvider.notifier).uploadImage(
                file,
              );
        },
        onTakeImage: (file) async {
          ref.read(userInfoProvider.notifier).uploadImage(
                file,
              );
        },
      ),
    );
  }
}
