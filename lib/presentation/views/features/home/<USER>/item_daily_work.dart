import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/order/work_data_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_status_type.dart';
import 'package:vcc/domain/enums/collection_status.dart';
import 'package:vcc/domain/enums/complain/enum_complain_status.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/process_kpi_status.dart';
import 'package:vcc/domain/enums/requirement_warranty/requirement_warranty_status_enum.dart';
import 'package:vcc/domain/enums/work_schedule_enum.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_package/aio_contract_package_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/order_single_service_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/detail_requirement_warranty/detail_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/detail_info_customer_collection/detail_info_customer_collection_page.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/complain_detail_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';

class ItemDailyWork extends StatelessWidget {
  final double width;
  final WorkDataEntity? workData;
  final Function? onBackFunction;
  final WorkScheduleEnum? workScheduleEnum;

  const ItemDailyWork({
    super.key,
    this.width = 320,
    this.workData,
    this.onBackFunction,
    this.workScheduleEnum,
  });

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        onTapItem(
          context,
        );
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: BaseColors.borderDivider,
            width: 1,
          ),
        ),
        width: width,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                bottom: 8,
              ),
              child: _buildHeader(),
            ),
            _buildRowItemDailyWork(
              icon: MyAssets.icons.iconBookS16.svg(),
              text: workData?.code,
            ),
            _buildRowItemDailyWork(
              icon: _buildIconSpecial(),
              text: workData?.name,
            ),
            _buildRowItemDailyWork(
              icon: MyAssets.icons.iconLocationS16.svg(),
              text: workData?.address,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildRowItemDailyWork({
    String? text,
    required Widget icon,
    Color? color,
  }) {
    return Visibility(
      visible: (text ?? '').isNotEmpty,
      child: Padding(
        padding: const EdgeInsets.only(
          bottom: 6,
        ),
        child: Row(
          children: [
            icon,
            const SizedBox(
              width: 8,
            ),
            Expanded(
              child: Text(
                text ?? '',
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
                style: BaseStyle.bodySmall.copyWith(
                  color: color ?? BaseColors.textLabel,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildIconSpecial() {
    switch (workScheduleEnum) {
      case WorkScheduleEnum.deploymentOrder:
        return MyAssets.icons.iconRepairMaintainS16.svg();
      case WorkScheduleEnum.customerInformationGather:
        return MyAssets.icons.iconConnectS16.svg();
      case WorkScheduleEnum.warrantyRequest:
        return MyAssets.icons.iconBoxS16.svg();
      default:
        return MyAssets.icons.iconRepairMaintainS16.svg();
    }
  }

  Widget _buildHeader() {
    String title = "";
    Color? colorTitle;
    String dateTimeStr = "";

    switch (workScheduleEnum) {
      case WorkScheduleEnum.deploymentOrder:
        title = OrderStatusExtension.fromString(workData?.status)?.label ?? "";
        colorTitle = OrderStatusExtension.fromString(workData?.status)?.color;
        dateTimeStr = '${workData?.startTime?.display(
              format: DateTimeFormater.hourMinute,
            ) ?? ""} - ${workData?.endTime?.display(
              format: DateTimeFormater.hourMinute,
            ) ?? ""}';
        break;
      case WorkScheduleEnum.bundleOrder:
        title = AioContractStatusTypeExtension.checkStatus(
                int.tryParse(workData?.status ?? ''))
            .display;
        colorTitle = AioContractStatusTypeExtension.checkStatus(
                int.tryParse(workData?.status ?? ''))
            .color;
        dateTimeStr = '${workData?.startTime?.display(
              format: DateTimeFormater.dayMonthFormat,
            ) ?? ""} - ${workData?.endTime?.display(
              format: DateTimeFormater.dateFormatVi,
            ) ?? ""}';
        break;
      case WorkScheduleEnum.customerInformationGather:
        title = CollectionStatusExtension.fromString(workData?.status).display;
        colorTitle =
            CollectionStatusExtension.fromString(workData?.status).textColor;
        break;
      case WorkScheduleEnum.warrantyRequest:
        title =
            EnumRequirementWarrantyStatusExtension.fromString(workData?.status)
                .label;
        colorTitle =
            EnumRequirementWarrantyStatusExtension.fromString(workData?.status)
                .color;
        break;
      case WorkScheduleEnum.customerClaim:
        title = ComplainStatusEnumExtension.fromStatus(
                int.tryParse(workData?.status ?? ""))
            .title;
        colorTitle = ComplainStatusEnumExtension.fromStatus(
                int.tryParse(workData?.status ?? ""))
            .textColor;
        dateTimeStr = '${workData?.startTime?.display(
              format: DateTimeFormater.dayMonthFormat,
            ) ?? ""} - ${workData?.endTime?.display(
              format: DateTimeFormater.dateFormatVi,
            ) ?? ""}';
        break;
      default:
        break;
    }

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          title,
          style: BaseStyle.labelSmall.copyWith(
            color: colorTitle,
          ),
        ),
        Visibility(
          visible: dateTimeStr.isNotEmpty,
          child: Row(
            children: [
              if (ProcessKpiStatusExtension.fromString(
                      workData?.processKPIStatus) ==
                  ProcessKpiStatus.expired) ...[
                Padding(
                  padding: const EdgeInsets.only(
                    right: BaseSpacing.spacing1,
                  ),
                  child: MyAssets.icons.iconWarningRedS16.svg(),
                ),
              ],
              Text(
                dateTimeStr,
                style: BaseStyle.headlineSmall.copyWith(
                  color: ProcessKpiStatusExtension.fromString(
                          workData?.processKPIStatus)
                      .backgroundTextColor,
                ),
              )
            ],
          ),
        ),
      ],
    );
  }

  void onTapItem(BuildContext context) async {
    switch (workScheduleEnum) {
      case WorkScheduleEnum.deploymentOrder:
        await context.push(
          RouterPaths.orderSingleServiceDetail,
          extra: OrderSingleServiceArguments(
            orderCode: workData?.code ?? "",
          ),
        );
        onBackFunction?.call();
        break;

      case WorkScheduleEnum.bundleOrder:
        context.push(
          RouterPaths.aioContractPackage,
          extra: AioContractPackageArguments(
            data: "",
            aioContractEntity: AioContractEntity(
              contractId: workData?.id,
            ),
            backFunction: () {
              onBackFunction?.call();
            },
          ),
        );
        break;

      case WorkScheduleEnum.customerInformationGather:
        await context.push(
          RouterPaths.detailInfoCustomerCollection,
          extra: DetailInfoCustomerCollectionArguments(
            collectionCode: workData?.code ?? "",
          ),
        );
        onBackFunction?.call();
        break;

      case WorkScheduleEnum.warrantyRequest:
        await context.push(
          RouterPaths.detailRequirementWarrantyClaim,
          extra: RequirementWarrantyDetailArguments(
            code: workData?.code ?? "",
            isManage: false,
          ),
        );
        onBackFunction?.call();
        break;

      case WorkScheduleEnum.customerClaim:
        await context.push(
          RouterPaths.detailComplain,
          extra: ComplainDetailArguments(
            code: workData?.id,
            isManage: false,
          ),
        );
        onBackFunction?.call();
        break;

      case null:
        break;
    }
  }
}
