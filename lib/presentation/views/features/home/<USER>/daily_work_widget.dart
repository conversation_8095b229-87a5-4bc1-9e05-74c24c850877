import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/domain/entities/filter_collection_entity.dart';
import 'package:vcc/domain/entities/order/daily_work_entity.dart';
import 'package:vcc/domain/entities/order/work_data_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_order_type.dart';
import 'package:vcc/domain/enums/collection_status.dart';
import 'package:vcc/domain/enums/complain/enum_complain_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/participant_type_enum.dart';
import 'package:vcc/domain/enums/requirement_warranty/requirement_warranty_status_enum.dart';
import 'package:vcc/domain/enums/work_schedule_enum.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_page.dart';
import 'package:vcc/presentation/views/features/home/<USER>/item_daily_work.dart';
import 'package:vcc/presentation/views/features/home/<USER>';
import 'package:vcc/presentation/views/features/order/list_order/list_order_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/requirement_warranty_home/requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/work/collection_customer_info/collection_info_warehouse/collection_info_warehouse_page.dart';
import 'package:vcc/presentation/views/features/work/complain/complain_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/utils/date_time.dart';

class DailyWorkWidget extends ConsumerStatefulWidget {
  const DailyWorkWidget({super.key});

  @override
  ConsumerState<DailyWorkWidget> createState() => _DailyWorkWidgetState();
}

class _DailyWorkWidgetState extends ConsumerState<DailyWorkWidget> {
  List<OrderType>? orderTypes;
  List<EnumRequirementWarrantyStatus>? initRequirementWarrantyStatus;
  List<ComplainStatusEnum>? initComplainStatus;
  List<CollectionStatus>? collectionCustomerInfoStatus;
  List<String>? initParticipantTypes;
  List<AioContractOrderType>? aioContractOrderTypes;

  @override
  void initState() {
    orderTypes = [
      OrderType.service,
      OrderType.supply,
      OrderType.package,
      OrderType.combo,
      OrderType.maintenance,
      OrderType.salePoint,
      OrderType.salePointSingle,
      OrderType.salePointCombo,
      OrderType.partnerWarrantyService,
      OrderType.partnerNotWarrantyService,
      OrderType.partnerOperate,
      OrderType.partnerMaintenance,
      OrderType.partnerResolveProblem,
    ];
    initRequirementWarrantyStatus = EnumRequirementWarrantyStatus.values
        .where((e) => e != EnumRequirementWarrantyStatus.completeWarranty)
        .toList();
    initComplainStatus = ComplainStatusEnum.values
        .where((e) =>
            e != ComplainStatusEnum.close && e != ComplainStatusEnum.completed)
        .toList();
    collectionCustomerInfoStatus = [
      CollectionStatus.inRequest,
    ];

    initParticipantTypes =
        ParticipantTypeEnum.values.map((e) => e.keyToServer ?? '').toList();

    aioContractOrderTypes = [
      AioContractOrderType.create,
      AioContractOrderType.deploy
    ];

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(homeProvider);
    return state.loadDailyWorkStatus == LoadStatus.loading
        ? const LoadingIndicatorWidget()
        : body();
  }

  Widget body() {
    final state = ref.watch(homeProvider);
    final rf = ref.read(homeProvider.notifier);
    return Stack(
      children: [
        Container(
          decoration: BoxDecoration(
            color: BaseColors.backgroundWhite,
            borderRadius: BorderRadius.circular(12),
            boxShadow: AppBoxShadows.shadowCard,
          ),
          margin: const EdgeInsets.all(16),
          padding: const EdgeInsets.all(12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  const Text(
                    'Công việc hôm nay',
                    style: BaseStyle.headlineLarge,
                  ),
                  InkWellWidget(
                    onTap: () {
                      context
                          .push(
                            RouterPaths.workSchedule,
                          )
                          .whenComplete(
                            () => rf.getWorkCount(),
                          );
                    },
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          'Xem lịch',
                          style: BaseStyle.labelMedium.copyWith(
                            color: BaseColors.info,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 8.0,
                          ),
                          child: MyAssets.icons.iconCalendarS16.svg(
                            colorFilter: ColorFilter.mode(
                              BaseColors.info,
                              BlendMode.srcIn,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              buildWork(),
            ],
          ),
        ),
        if (state.updateDailyWorkStatus == LoadStatus.loading) ...[
          Positioned.fill(
            child: Container(
              color: Colors.white.withOpacity(0.35),
              child: const Center(
                child: LoadingIndicatorWidget(),
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget buildWork() {
    final state = ref.watch(homeProvider);
    final rf = ref.read(homeProvider.notifier);

    final dailyWorks = state.dailyWork ?? [];

    return Padding(
      padding: const EdgeInsets.only(
        top: 4,
      ),
      child: ListView.builder(
        shrinkWrap: true,
        itemCount: dailyWorks.length,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, indexDailyWork) {
          final DailyWorkEntity item = dailyWorks[indexDailyWork];
          final List<WorkDataEntity> workDataList = item.workData ?? [];
          return Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  top: 12,
                ),
                child: InkWellWidget(
                  onTap: () {
                    rf.updateState(
                      index: indexDailyWork,
                    );
                    if (dailyWorks[indexDailyWork].isExpand) {
                      rf.getDailyWorkData(
                        dataTypeEnum: item.workTypeKey,
                      );
                    }
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 16,
                    ),
                    decoration: BoxDecoration(
                      color: dailyWorks[indexDailyWork].isExpand
                          ? const Color(0xFFFFEEEF)
                          : BaseColors.backgroundGray1,
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        item.icon ?? const SizedBox(),
                        Padding(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                          ),
                          child: Text(
                            (item.orderCount ?? 0).toString(),
                            style: BaseStyle.labelLargePromient,
                          ),
                        ),
                        Expanded(
                          child: Text(
                            item.title ?? '',
                            style: BaseStyle.captionVeryLarge,
                            maxLines: 2,
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(
                            left: 6,
                          ),
                          child: dailyWorks[indexDailyWork].isExpand
                              ? const Icon(Icons.keyboard_arrow_up)
                              : const Icon(Icons.keyboard_arrow_down),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
              if (dailyWorks[indexDailyWork].isExpand) ...[
                Padding(
                  padding: const EdgeInsets.only(
                    top: 12.0,
                  ),
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Visibility(
                        visible: workDataList.isNotEmpty,
                        child: SizedBox(
                          height: 135,
                          child: ListView.separated(
                            itemCount: workDataList.length,
                            itemBuilder: (context, indexWorkDataOfDailyWork) {
                              WorkDataEntity? workData =
                                  workDataList[indexWorkDataOfDailyWork];
                              return ItemDailyWork(
                                width: MediaQuery.of(context).size.width -
                                    ((16 + 12) * 2) -
                                    (workDataList.length > 1 ? 24 : 0),
                                workData: workData,
                                workScheduleEnum:
                                    WorkScheduleEnumExtension.fromKey(
                                  item.workTypeKey,
                                ),
                                onBackFunction: rf.getWorkCount,
                              );
                            },
                            scrollDirection: Axis.horizontal,
                            shrinkWrap: true,
                            separatorBuilder:
                                (context, indexWorkDataOfDailyWork) {
                              return const SizedBox(width: 12);
                            },
                          ),
                        ),
                      ),
                      buildViewAllButton(
                        onTap: () {
                          onTapViewAll(
                            context,
                            onBackFunction: rf.getWorkCount,
                            workScheduleEnum: WorkScheduleEnumExtension.fromKey(
                              item.workTypeKey,
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ],
            ],
          );
        },
      ),
    );
  }

  Widget buildViewAllButton({
    Function()? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      child: Padding(
        padding: const EdgeInsets.only(
          top: 12.0,
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            const Text(
              'Xem tất cả',
              style: BaseStyle.labelSmall,
            ),
            MyAssets.icons.iconArrowRightS18.svg(
              colorFilter: ColorFilter.mode(
                BaseColors.textLabel,
                BlendMode.srcIn,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void onTapViewAll(
    BuildContext context, {
    WorkScheduleEnum? workScheduleEnum,
    Function? onBackFunction,
  }) async {
    final DateTime nowDate = DateTime.now();

    final DateTime startFilterDate = nowDate.startDay;

    final DateTime endFilterDate = nowDate.endDay;

    switch (workScheduleEnum) {
      case WorkScheduleEnum.deploymentOrder:
        await context.push(
          RouterPaths.allOrder,
          extra: ListOrderArguments(
            initStartDate: startFilterDate,
            initEndDate: endFilterDate,
            orderTypes: orderTypes,
            initParticipantTypes: initParticipantTypes,
          ),
        );
        onBackFunction?.call();
        break;
      case WorkScheduleEnum.bundleOrder:
        await context.push(
          RouterPaths.aioContract,
          extra: AioContractArguments(
            data: "",
            orderTypeSelected: aioContractOrderTypes,
            initStartDate: startFilterDate,
            initEndDate: endFilterDate,
          ),
        );
        onBackFunction?.call();
        break;
      case WorkScheduleEnum.customerInformationGather:
        await context.push(
          RouterPaths.collectionInfoWarehouse,
          extra: CollectionInfoWarehouseArguments(
            initFilterCollection: FilterCollectionEntity(
              collectionStatus: collectionCustomerInfoStatus,
              startDate: startFilterDate,
              endDate: endFilterDate,
            ),
          ),
        );
        onBackFunction?.call();
        break;
      case WorkScheduleEnum.warrantyRequest:
        await context.push(
          RouterPaths.requirementWarranty,
          extra: RequirementWarrantyArguments(
            initStartDate: startFilterDate,
            initEndDate: endFilterDate,
            initRequirementWarrantyStatus: initRequirementWarrantyStatus,
          ),
        );
        onBackFunction?.call();
        break;
      case WorkScheduleEnum.customerClaim:
        await context.push(
          RouterPaths.complain,
          extra: ComplainArguments(
            initStartDate: startFilterDate,
            initEndDate: endFilterDate,
            initStatus: initComplainStatus,
          ),
        );
        onBackFunction?.call();
        break;
      case null:
        break;
    }
  }
}
