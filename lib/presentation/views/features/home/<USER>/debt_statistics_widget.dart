import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/domain/entities/order/statistic_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/transfer_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/home/<USER>';
import 'package:vcc/presentation/views/features/work/debt_statistics/debt_statistics_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/utils/string_utils.dart';

class DebtStatisticsWidget extends ConsumerStatefulWidget {
  const DebtStatisticsWidget({super.key});

  @override
  ConsumerState<DebtStatisticsWidget> createState() =>
      _DebtStatisticsWidgetState();
}

class _DebtStatisticsWidgetState extends ConsumerState<DebtStatisticsWidget> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(homeProvider);
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundWhite,
        borderRadius: BorderRadius.circular(12),
        boxShadow: AppBoxShadows.shadowCard,
      ),
      margin: const EdgeInsets.only(
        top: 16,
        right: 16,
        left: 16,
      ),
      padding: const EdgeInsets.all(
        12,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Text(
            'Công nợ',
            style: BaseStyle.headlineLarge,
          ),
          Padding(
            padding: const EdgeInsets.only(
              top: 4.0,
            ),
            child: LayoutBuilder(
              builder: (context, constraints) {
                double itemWidth = (constraints.maxWidth - 14) / 2;
                return state.loadDebtStatisticsStatus == LoadStatus.loading
                    ? const Center(
                        child: LoadingIndicatorWidget(),
                      )
                    : Wrap(
                        spacing: 14,
                        runSpacing: 14,
                        children: List.generate(
                            (state.debtStatistics ?? []).length, (index) {
                          final StatisticEntity statisticItem =
                              (state.debtStatistics ?? [])[index];
                          return SizedBox(
                            width: itemWidth,
                            child: Container(
                              decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: itemStatistics(
                                statisticItem: statisticItem,
                              ),
                            ),
                          );
                        }),
                      );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget itemStatistics({
    StatisticEntity? statisticItem,
  }) {
    return InkWellWidget(
      onTap: () {
        navigator(statisticItem?.code ?? '');
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(
          top: 8,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: BaseColors.borderDivider,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                statisticItem?.icon ?? const SizedBox(),
                MyAssets.icons.iconCircleArrowRight.svg(),
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(
                top: 12.0,
              ),
              child: Text(
                statisticItem?.title ?? '',
                style: BaseStyle.bodySmall.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
            ),
            Text(
              StringUtils.formatFlexibleDouble(
                  (statisticItem?.orderCount ?? 0).toDouble()),
              style: BaseStyle.headlineMedium,
            ),
          ],
        ),
      ),
    );
  }

  void navigator(String code) {
    context.push(
      RouterPaths.debtStatistics,
      extra: DebtStatisticsArguments(
        orderCode: code,
        page: code == TransferType.payment.keyToServer ? 0 : 1,
      ),
    );
  }
}
