import 'package:equatable/equatable.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/list_order_body.dart';
import 'package:vcc/domain/entities/order/daily_work_entity.dart';
import 'package:vcc/domain/entities/order/order_info_complete_entity.dart';
import 'package:vcc/domain/entities/order/statistic_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/role_order_shipping_enum.dart';
import 'package:vcc/domain/enums/transfer_type.dart';
import 'package:vcc/domain/enums/work_menu_type.dart';
import 'package:vcc/domain/enums/work_schedule_enum.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/utils/log_utils.dart';

part 'home_state.dart';

final homeProvider =
    StateNotifierProvider.autoDispose<HomeViewModel, HomeState>(
  (ref) => HomeViewModel(ref: ref),
);

class HomeViewModel extends StateNotifier<HomeState> {
  final Ref ref;

  HomeViewModel({
    required this.ref,
  }) : super(const HomeState());

  Future<void> getCountOrders() async {
    final result = await appLocator<OrderRepository>().getCountOrder(
      body: ListOrderBody(
        participantTypes: [
          RoleOrderShippingEnum.worker.serverKey,
        ],
        ordersType: [
          state.chartOrderType?.keyToServer ?? '',
        ],
      ),
    );

    try {
      await result?.when(
        success: (data) async {
          bool isOrderTypeProduct = state.chartOrderType == OrderType.smart ||
              state.chartOrderType == OrderType.setup;

          List<PieChartSectionData> pieChartSectionData = [];
          if (isOrderTypeProduct) {
            pieChartSectionData = [
              PieChartSectionData(
                title: OrderStatus.registered.label,
                color: OrderStatus.registered.color,
                radius: 80,
                showTitle: false,
                value: data.registeredCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.confirmWaiting.label,
                color: OrderStatus.confirmWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.confirmWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.goodWarrant.label,
                color: OrderStatus.goodWarrant.color,
                radius: 80,
                showTitle: false,
                value: data.goodWarrantCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.receptionWaiting.label,
                color: OrderStatus.receptionWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.receptionWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processWaiting.label,
                color: OrderStatus.processWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.processWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processing.label,
                color: OrderStatus.processing.color,
                radius: 80,
                showTitle: false,
                value: data.processingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processed.label,
                color: OrderStatus.processed.color,
                radius: 80,
                showTitle: false,
                value: data.processedCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.complete.label,
                color: OrderStatus.complete.color,
                radius: 80,
                showTitle: false,
                value: data.completeCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.cancel.label,
                color: OrderStatus.cancel.color,
                radius: 80,
                showTitle: false,
                value: data.cancelCount?.toDouble(),
              ),
            ];
          } else {
            pieChartSectionData = [
              PieChartSectionData(
                title: OrderStatus.registered.label,
                color: OrderStatus.registered.color,
                radius: 80,
                showTitle: false,
                value: data.registeredCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.receptionWaiting.label,
                color: OrderStatus.receptionWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.receptionWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processWaiting.label,
                color: OrderStatus.processWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.processWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processing.label,
                color: OrderStatus.processing.color,
                radius: 80,
                showTitle: false,
                value: data.processingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processed.label,
                color: OrderStatus.processed.color,
                radius: 80,
                showTitle: false,
                value: data.processedCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.complete.label,
                color: OrderStatus.complete.color,
                radius: 80,
                showTitle: false,
                value: data.completeCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.cancel.label,
                color: OrderStatus.cancel.color,
                radius: 80,
                showTitle: false,
                value: data.cancelCount?.toDouble(),
              ),
            ];
          }
          state = state.copyWith(
            listChart: pieChartSectionData,
          );
        },
        error: (err) {},
      );
    } catch (error) {
      LogUtils.d(error.toString());
    }
  }

  Future<void> getTotalCompleteOrders() async {
    final result = await appLocator<OrderRepository>().getTotalCompleteOrders(
      startTime: state.selectedDate?.toIso8601String(),
    );

    try {
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            completeOrders: data.completeOrders,
          );
        },
        error: (err) {},
      );
    } catch (error) {
      LogUtils.d(error.toString());
    }
  }

  Future<void> getWorkCount() async {
    List<DailyWorkEntity> dailyWork = [];
    final dateNow = DateTime.now().display(
      format: DateTimeFormater.dateTimeFormat,
    );
    state = state.copyWith(
      loadDailyWorkStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<OrderRepository>().getWorkCount(
        startTime: dateNow,
        endTime: dateNow,
      );
      await result?.when(
        success: (data) async {
          dailyWork = [
            DailyWorkEntity(
              title: 'Đơn triển khai',
              orderCount: data.deploymentOrderCount,
              icon: MyAssets.icons.iconEngineer.svg(),
              workTypeKey: WorkScheduleEnum.deploymentOrder.keyToServer,
            ),
            DailyWorkEntity(
              title: 'Đơn gói giá ',
              orderCount: data.bundleOrderCount,
              icon: MyAssets.icons.iconPackage.svg(),
              workTypeKey: WorkScheduleEnum.bundleOrder.keyToServer,
            ),
            DailyWorkEntity(
              title: 'Thu thập thông tin khách hàng',
              orderCount: data.customerInformationGatherCount,
              icon: MyAssets.icons.iconGroup.svg(),
              workTypeKey:
                  WorkScheduleEnum.customerInformationGather.keyToServer,
            ),
            DailyWorkEntity(
              title: 'Yêu cầu bảo hành',
              orderCount: data.warrantyRequestCount,
              icon: MyAssets.icons.iconShield.svg(),
              workTypeKey: WorkScheduleEnum.warrantyRequest.keyToServer,
            ),
            DailyWorkEntity(
              title: 'Khiếu nại phản ánh',
              orderCount: data.customerClaimCount,
              icon: MyAssets.icons.iconComplain.svg(),
              workTypeKey: WorkScheduleEnum.customerClaim.keyToServer,
            ),
          ];
          final dailyWorkVisible =
              dailyWork.where((e) => (e.orderCount ?? 0) > 0).toList();
          state = state.copyWith(
            loadDailyWorkStatus: LoadStatus.success,
            dailyWork: dailyWorkVisible,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadDailyWorkStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadDailyWorkStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getDailyWorkData({
    String? dataTypeEnum,
  }) async {
    final dateNow = DateTime.now().display(
      format: DateTimeFormater.dateTimeFormat,
    );
    state = state.copyWith(
      updateDailyWorkStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<OrderRepository>().getWorkData(
        startTime: dateNow,
        endTime: dateNow,
        dataTypeEnum: dataTypeEnum,
      );
      await result?.when(
        success: (data) async {
          final updatedList = state.dailyWork?.map((e) {
            if (e.workTypeKey == dataTypeEnum) {
              return e.copyWith(
                workData: data.data,
              );
            }
            return e;
          }).toList();

          state = state.copyWith(
            dailyWork: updatedList,
            updateDailyWorkStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            updateDailyWorkStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateDailyWorkStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getDataStatistic() async {
    state = state.copyWith(
      loadStatisticsStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<OrderRepository>().getDataStatistic(
        dateTime: (state.statisticsDate ?? DateTime.now()).display(
          format: DateTimeFormater.dateTimeFormat,
        ),
      );
      List<StatisticEntity> statistics = [];

      await result?.when(
        success: (data) async {
          statistics.addAll(
            [
              StatisticEntity(
                title: 'Nguồn việc',
                orderCount: data.ordersCreatedCount,
                label: 'Tôi tạo',
                orderCreatedSum: data.ordersCreatedSum,
                icon: MyAssets.icons.iconCreateUser.svg(),
              ),
              StatisticEntity(
                orderCount: data.ordersDeployedCount,
                label: 'Tôi triển khai',
                orderCreatedSum: data.ordersDeployedSum,
                icon: MyAssets.icons.iconWorkerGreen.svg(),
              ),
              StatisticEntity(
                title: 'Doanh thu',
                label: 'Bán hàng',
                orderCount: data.ordersSaleCount,
                orderCreatedSum: data.ordersSaleSum,
                icon: MyAssets.icons.iconOrderPackage.svg(),
              ),
              StatisticEntity(
                orderCount: data.ordersWorkerCount,
                label: 'Thực hiện',
                orderCreatedSum: data.ordersWorkerSum,
                icon: MyAssets.icons.iconSetup.svg(),
              ),
              // todo có thông tin thì sưửa lại code
              // StatisticEntity(
              //   title: 'Thu nhập',
              //   orderCreatedSum: data.totalCommission,
              //   label: 'Hoa hồng',
              //   code: WorkMenuType.confirmCommission.value,
              //   isOrder: false,
              //   icon: MyAssets.icons.iconDiscountOrder.svg(),
              // ),
              // StatisticEntity(
              //   orderCreatedSum: data.totalIncome,
              //   label: 'Lương',
              //   code: WorkMenuType.confirmCommission.value,
              //   isOrder: false,
              //   icon: MyAssets.icons.iconMoneyGreen.svg(),
              // ),
            ],
          );
          state = state.copyWith(
            loadStatisticsStatus: LoadStatus.success,
            statistics: statistics,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatisticsStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatisticsStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getDebtPaymentViaCollectionAccount() async {
    try {
      final result = await appLocator<OrderRepository>().getDebts(
        queryType: TransferType.payment.keyToServer,
      );
      List<StatisticEntity> debtStatistics = [];

      await result?.when(
        success: (data) async {
          debtStatistics.add(
            StatisticEntity(
              title: 'Đơn chuyển khoản',
              orderCount: data.totalDebt,
              code: TransferType.payment.keyToServer,
              icon: MyAssets.icons.iconPaymentCollection.svg(),
            ),
          );
          state = state.copyWith(
            debtStatistics: debtStatistics,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: error.toString(),
      );
    }
  }

  Future<void> getDebtPaymentViaBank() async {
    try {
      final result = await appLocator<OrderRepository>().getDebts(
        queryType: TransferType.collectionAccount.keyToServer,
      );
      List<StatisticEntity> debtStatistics = state.debtStatistics ?? [];
      await result?.when(
        success: (data) async {
          debtStatistics.add(
            StatisticEntity(
              title: 'Đơn qua CNCT',
              orderCount: data.totalDebt,
              code: TransferType.collectionAccount.keyToServer,
              icon: MyAssets.icons.iconPaymentBank.svg(),
            ),
          );
          state = state.copyWith(
            debtStatistics: debtStatistics,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: error.toString(),
      );
    }
  }

  void updateState({required int index}) {
    final List<DailyWorkEntity> dailyWork = List.from(
      state.dailyWork ?? [],
    );
    dailyWork[index].isExpand = !dailyWork[index].isExpand;
    state = state.copyWith(
      dailyWork: dailyWork,
    );
  }

  void selectStatisticDate(date) {
    state = state.copyWith(
      statisticsDate: date,
    );
    getDataStatistic();
  }

  Future<void> initStaticData() async {
    state = state.copyWith(
      loadDebtStatisticsStatus: LoadStatus.loading,
    );
    await ref.read(homeProvider.notifier).getDebtPaymentViaCollectionAccount();
    await ref.read(homeProvider.notifier).getDebtPaymentViaBank();
    ref.read(homeProvider.notifier).getCountOrders();
    ref.read(homeProvider.notifier).getTotalCompleteOrders();
    ref.read(homeProvider.notifier).getWorkCount();
    ref.read(homeProvider.notifier).getDataStatistic();
    state = state.copyWith(
      loadDebtStatisticsStatus: LoadStatus.success,
    );
  }
}
