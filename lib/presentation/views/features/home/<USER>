import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/features/home/<USER>/csat_chart_widget.dart';
import 'package:vcc/presentation/views/features/store/store_view_model.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/home/<USER>';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/utils/string_utils.dart';

class HomePage extends StatefulHookConsumerWidget {
  const HomePage({super.key});

  @override
  ConsumerState<HomePage> createState() => _HomePageState();
}

class _HomePageState extends ConsumerState<HomePage> {
  @override
  void initState() {
    Future(() {
      ref.read(homeProvider.notifier).getCountOrders();
      ref.read(homeProvider.notifier).getTotalCompleteOrders();
    });
    ref.read(storeProvider.notifier).getConfigDocument();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(homeProvider);

    return LayoutPage(
      backgroundColor: BaseColors.backgroundWhite,
      appbar: AppBarCustom(
        title: "AIO Partner",
        centerTitle: false,
        isShowNavigation: false,
        actionWidget: [
          InkWellWidget(
            onTap: () {
              context.push(
                RouterPaths.notification,
              );
            },
            child: MyAssets.icons.iconNotificationHome.svg(),
          ),
          const SizedBox(width: 16),
          if(GlobalData.instance.userInfo?.isTimekeepingIconVisible ?? false) ...[
            InkWellWidget(
              onTap: () {
                context.push(
                  RouterPaths.timekeeping,
                );
              },
              child: MyAssets.icons.iconCalendarHome.svg(),
            ),
            const SizedBox(width: 16),
          ],
        ],
      ),
      // floatingActionButton: FloatingActionButton(
      //   onPressed: () async {
      //     final result = await MobileCallFunc.startCall(
      //       callerPhoneNumber: "0344849988",
      //       callerName: "Trần Văn Hiệp",
      //       customerNumber: "0971235701",
      //       customerName: "Vu Thanh Cong",
      //       code: "",
      //       content: "",
      //     );
      //
      //     if (result != null) {
      //       print(result.toJson());
      //       // MobileCallRequest request = MobileCallRequest(
      //       //   callType: 1,
      //       //   status: resultCall.status,
      //       //   callerNumber: resultCall.callerNumber,
      //       //   callID: resultCall.callID,
      //       //   duration: resultCall.duration,
      //       //   endTime: Platform.isIOS
      //       //       ? DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.endTime * 1000).round())
      //       //       : DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.endTime)),
      //       //   errorType: resultCall.errorType,
      //       //   ringingTime: Platform.isIOS
      //       //       ? DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.ringingTime * 1000).round())
      //       //       : DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.ringingTime)),
      //       //   startTime: Platform.isIOS
      //       //       ? DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.startTime * 1000).round())
      //       //       : DateTime.fromMillisecondsSinceEpoch(
      //       //       (resultCall.startTime)),
      //       //   state: detailContactController.currentContact?.value?.state,
      //       //   tangentCustomerId: detailContactController.currentContact?.value?.tangentCustomerId,
      //       //   type: "TANGENT_CUSTOMER",
      //       // );
      //
      //       // detailContactController.postResultMobileCall(request);
      //     }
      //   },
      //   child: const Icon(Icons.call),
      // ),
      body: ListView(
        physics: const ClampingScrollPhysics(),
        children: <Widget>[
          Container(
            decoration: BoxDecoration(
              color: BaseColors.backgroundWhite,
              borderRadius: BorderRadius.circular(12),
              boxShadow: AppBoxShadows.shadowCard,
            ),
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            "Thống kê",
                            style: UITextStyle.body1SemiBold.copyWith(
                              color: BaseColors.textBody,
                            ),
                          ),
                          const SizedBox(height: 6),
                          Text(
                            "Theo ngày hoàn thành",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                const DividerWidget(),
                ListView.separated(
                  itemCount: state.completeOrders?.length ?? 0,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: const EdgeInsets.only(top: 16),
                  separatorBuilder: (_, __) => const SizedBox(height: 16),
                  itemBuilder: (context, index) {
                    final item = state.completeOrders![index];

                    return Container(
                      decoration: BoxDecoration(
                        color: BaseColors.backgroundGray,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                      child: Row(
                        children: <Widget>[
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Text(
                                  item.orderType?.label ?? "",
                                  style: UITextStyle.body2Medium.copyWith(
                                    color: BaseColors.textLabel,
                                  ),
                                ),
                                Text(
                                  "${item.count ?? 0} đơn",
                                  style: UITextStyle.body2Regular.copyWith(
                                    color: BaseColors.textBody,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Text(
                            StringUtils.formatMoney(item.amount ?? 0),
                            style: UITextStyle.body2SemiBold.copyWith(
                              color: BaseColors.primary,
                            ),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: BaseColors.backgroundWhite,
              borderRadius: BorderRadius.circular(12),
              boxShadow: AppBoxShadows.shadowCard,
            ),
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                Text(
                  "Đơn hàng thường",
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textBody,
                  ),
                ),
                const SizedBox(height: 6),
                Text(
                  "Thống kê theo ngày tạo đơn",
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.textSubtitle,
                  ),
                ),
                const SizedBox(height: 12),
                const DividerWidget(),
                Container(
                  height: 160,
                  width: double.infinity,
                  margin: const EdgeInsets.only(
                    top: 16,
                    bottom: 24,
                  ),
                  alignment: Alignment.center,
                  child: PieChart(
                    PieChartData(
                      borderData: FlBorderData(
                        show: false,
                      ),
                      sectionsSpace: 0,
                      centerSpaceRadius: 0,
                      sections: state.listChart,
                    ),
                  ),
                ),
                SizedBox(
                  width: double.infinity,
                  child: Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                    ),
                    child: Wrap(
                      direction: Axis.horizontal,
                      alignment: WrapAlignment.start,
                      crossAxisAlignment: WrapCrossAlignment.start,
                      spacing: 8,
                      runSpacing: 8,
                      children: (state.listChart ?? []).map<Widget>(
                        (item) {
                          return SizedBox(
                            width: (MediaQuery.of(context).size.width - 72) / 2,
                            child: Row(
                              children: <Widget>[
                                Container(
                                  height: 8,
                                  width: 8,
                                  decoration: BoxDecoration(
                                    color: item.color,
                                    borderRadius: BorderRadius.circular(4),
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Flexible(
                                  child: Text(
                                    item.title,
                                    style: UITextStyle.body2Regular.copyWith(
                                      color: BaseColors.textBody,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        },
                      ).toList(),
                    ),
                  ),
                ),
              ],
            ),
          ),
          const CsatChartWidget(),
        ],
      ),
    );
  }
}
