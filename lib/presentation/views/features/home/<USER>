import 'package:equatable/equatable.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/list_order_body.dart';
import 'package:vcc/domain/entities/order/order_info_complete_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/role_order_shipping_enum.dart';
import 'package:vcc/utils/log_utils.dart';

part 'home_state.dart';

final homeProvider =
    StateNotifierProvider.autoDispose<HomeViewModel, HomeState>(
  (ref) => HomeViewModel(ref: ref),
);

class HomeViewModel extends StateNotifier<HomeState> {
  final Ref ref;

  HomeViewModel({
    required this.ref,
  }) : super(const HomeState());

  Future<void> getCountOrders() async {
    final result = await appLocator<OrderRepository>().getCountOrder(
      body: ListOrderBody(
        participantTypes: [
          RoleOrderShippingEnum.worker.serverKey,
        ],
        ordersType: [
          state.chartOrderType?.keyToServer ?? '',
        ],
      ),
    );

    try {
      await result?.when(
        success: (data) async {
          bool isOrderTypeProduct = state.chartOrderType == OrderType.smart ||
              state.chartOrderType == OrderType.setup;

          List<PieChartSectionData> pieChartSectionData = [];
          if (isOrderTypeProduct) {
            pieChartSectionData = [
              PieChartSectionData(
                title: OrderStatus.registered.label,
                color: OrderStatus.registered.color,
                radius: 80,
                showTitle: false,
                value: data.registeredCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.confirmWaiting.label,
                color: OrderStatus.confirmWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.confirmWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.goodWarrant.label,
                color: OrderStatus.goodWarrant.color,
                radius: 80,
                showTitle: false,
                value: data.goodWarrantCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.receptionWaiting.label,
                color: OrderStatus.receptionWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.receptionWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processWaiting.label,
                color: OrderStatus.processWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.processWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processing.label,
                color: OrderStatus.processing.color,
                radius: 80,
                showTitle: false,
                value: data.processingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processed.label,
                color: OrderStatus.processed.color,
                radius: 80,
                showTitle: false,
                value: data.processedCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.complete.label,
                color: OrderStatus.complete.color,
                radius: 80,
                showTitle: false,
                value: data.completeCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.cancel.label,
                color: OrderStatus.cancel.color,
                radius: 80,
                showTitle: false,
                value: data.cancelCount?.toDouble(),
              ),
            ];
          } else {
            pieChartSectionData = [
              PieChartSectionData(
                title: OrderStatus.registered.label,
                color: OrderStatus.registered.color,
                radius: 80,
                showTitle: false,
                value: data.registeredCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.receptionWaiting.label,
                color: OrderStatus.receptionWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.receptionWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processWaiting.label,
                color: OrderStatus.processWaiting.color,
                radius: 80,
                showTitle: false,
                value: data.processWaitingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processing.label,
                color: OrderStatus.processing.color,
                radius: 80,
                showTitle: false,
                value: data.processingCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.processed.label,
                color: OrderStatus.processed.color,
                radius: 80,
                showTitle: false,
                value: data.processedCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.complete.label,
                color: OrderStatus.complete.color,
                radius: 80,
                showTitle: false,
                value: data.completeCount?.toDouble(),
              ),
              PieChartSectionData(
                title: OrderStatus.cancel.label,
                color: OrderStatus.cancel.color,
                radius: 80,
                showTitle: false,
                value: data.cancelCount?.toDouble(),
              ),
            ];
          }
          state = state.copyWith(
            listChart: pieChartSectionData,
          );
        },
        error: (err) {},
      );
    } catch (error) {
      LogUtils.d(error.toString());
    }
  }

  Future<void> getTotalCompleteOrders() async {
    final result = await appLocator<OrderRepository>().getTotalCompleteOrders(
      startTime: state.selectedDate?.toIso8601String(),
    );

    try {
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            completeOrders: data.completeOrders,
          );
        },
        error: (err) {},
      );
    } catch (error) {
      LogUtils.d(error.toString());
    }
  }
}
