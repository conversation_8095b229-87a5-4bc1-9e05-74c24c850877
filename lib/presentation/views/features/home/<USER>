part of 'home_view_model.dart';

class HomeState extends Equatable {
  final LoadStatus loadStatus;
  final List<PieChartSectionData>? listChart;
  final OrderType? chartOrderType;
  final DateTime? selectedDate;
  final List<OrderInfoCompleteEntity>? completeOrders;

  const HomeState({
    this.loadStatus = LoadStatus.initial,
    this.listChart,
    this.chartOrderType = OrderType.service,
    this.selectedDate,
    this.completeOrders,
  });

  HomeState copyWith({
    LoadStatus? loadStatus,
    List<PieChartSectionData>? listChart,
    OrderType? chartOrderType,
    DateTime? selectedDate,
    List<OrderInfoCompleteEntity>? completeOrders,
  }) {
    return HomeState(
      loadStatus: loadStatus ?? this.loadStatus,
      listChart: listChart ?? this.listChart,
      chartOrderType: chartOrderType ?? this.chartOrderType,
      selectedDate: selectedDate ?? this.selectedDate,
      completeOrders: completeOrders ?? this.completeOrders,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        listChart,
        chartOrderType,
        selectedDate,
        completeOrders,
      ];
}
