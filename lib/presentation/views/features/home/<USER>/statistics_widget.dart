import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/domain/entities/order/statistic_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/work_menu_type.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/features/home/<USER>';
import 'package:vcc/presentation/views/features/work/confirm_commission/confirm_commission_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/utils/date_time.dart';
import 'package:vcc/utils/string_utils.dart';

class StatisticsWidget extends ConsumerStatefulWidget {
  const StatisticsWidget({super.key});

  @override
  ConsumerState<StatisticsWidget> createState() => _StatisticsWidgetState();
}

class _StatisticsWidgetState extends ConsumerState<StatisticsWidget> {
  @override
  Widget build(BuildContext context) {
    final state = ref.watch(homeProvider);
    final rf = ref.read(homeProvider.notifier);
    return state.loadStatisticsStatus == LoadStatus.loading
        ? const Center(
            child: LoadingIndicatorWidget(),
          )
        : Container(
            decoration: BoxDecoration(
              color: BaseColors.backgroundWhite,
              borderRadius: BorderRadius.circular(12),
              boxShadow: AppBoxShadows.shadowCard,
            ),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            padding: const EdgeInsets.all(12),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text(
                      'Thống kê',
                      style: BaseStyle.headlineLarge,
                    ),
                    InkWellWidget(
                      onTap: () {
                        var dob = DateTime.now();
                        CustomBottomPicker.month(
                          height: MediaQuery.of(context).size.height * 0.40,
                          title: "Thời gian",
                          buttonText: "Xác nhận",
                          initialDateTime: state.statisticsDate ?? dob,
                          titleStyle: UITextStyle.body1SemiBold,
                          pickerTextStyle: TextStyle(
                            fontSize: 16,
                            color: BaseColors.backgroundBlack,
                          ),
                          dateOrder: DatePickerDateOrder.dmy,
                          onSubmit: (date) {
                            rf.selectStatisticDate(
                              date,
                            );
                          },
                        ).show(context);
                      },
                      child: Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 8,
                        ),
                        decoration: BoxDecoration(
                            color: BaseColors.secondaryBackground,
                            borderRadius: BorderRadius.circular(8)),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              'Tháng ${(state.statisticsDate ?? DateTime.now()).displayView(
                                format: monthYearFormat,
                              )}',
                            ),
                            Padding(
                              padding: const EdgeInsets.only(
                                left: 8.0,
                              ),
                              child: MyAssets.icons.iconCalendarS16.svg(
                                colorFilter: ColorFilter.mode(
                                  BaseColors.textBody,
                                  BlendMode.srcIn,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
                const Padding(
                  padding: EdgeInsets.symmetric(
                    vertical: 16.0,
                  ),
                  child: DividerWidget(),
                ),
                LayoutBuilder(
                  builder: (context, constraints) {
                    double itemWidth = (constraints.maxWidth - 14) /
                        2; // spacing chỉ 1 lần giữa 2 ô
                    return Wrap(
                      spacing: 14,
                      runSpacing: 14,
                      children: List.generate((state.statistics ?? []).length,
                          (index) {
                        StatisticEntity? item = (state.statistics ?? [])[index];
                        return SizedBox(
                          width: itemWidth,
                          child: Container(
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  item.title ?? '',
                                  style: BaseStyle.labelMedium,
                                ),
                                itemStatistics(
                                  isShowOrder: item.isOrder,
                                  item: item,
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                    );
                  },
                ),
              ],
            ),
          );
  }

  Widget itemStatistics({
    bool? isShowOrder = true,
    required StatisticEntity item,
  }) {
    return InkWellWidget(
      onTap: () {
        navigator(item.code ?? '');
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        margin: const EdgeInsets.only(
          top: 8,
        ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: BaseColors.borderDivider,
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                item.icon ?? const SizedBox(),
                !isShowOrder!
                    ? MyAssets.icons.iconCircleArrowRight.svg()
                    : Column(
                        children: [
                          Text(
                            "${item.orderCount ?? 0}",
                            style: BaseStyle.headlineLarge,
                          ),
                          const Text(
                            'đơn hàng',
                            style: BaseStyle.captionSmall,
                          ),
                        ],
                      )
              ],
            ),
            Padding(
              padding: const EdgeInsets.only(
                top: 12.0,
              ),
              child: Text(
                item.label ?? '',
                style: BaseStyle.bodySmall.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
            ),
            Text(
              StringUtils.formatFlexibleDouble(
                  (item.orderCreatedSum ?? 0).toDouble()),
              style: BaseStyle.headlineMedium,
            ),
          ],
        ),
      ),
    );
  }

  void navigator(String code) {
    if (code == WorkMenuType.confirmCommission.value) {
      context.push(
        RouterPaths.confirmCommission,
        extra: ConfirmCommissionArguments(data: ""),
      );
    }
  }
}
