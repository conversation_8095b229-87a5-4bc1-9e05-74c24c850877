// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'csat_chart_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$CsatChartState {
  List<PieChartSectionData> get chartData => throw _privateConstructorUsedError;
  bool get isLoading => throw _privateConstructorUsedError;
  String? get errorMessage => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $CsatChartStateCopyWith<CsatChartState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $CsatChartStateCopyWith<$Res> {
  factory $CsatChartStateCopyWith(
          CsatChartState value, $Res Function(CsatChartState) then) =
      _$CsatChartStateCopyWithImpl<$Res, CsatChartState>;
  @useResult
  $Res call(
      {List<PieChartSectionData> chartData,
      bool isLoading,
      String? errorMessage});
}

/// @nodoc
class _$CsatChartStateCopyWithImpl<$Res, $Val extends CsatChartState>
    implements $CsatChartStateCopyWith<$Res> {
  _$CsatChartStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chartData = null,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_value.copyWith(
      chartData: null == chartData
          ? _value.chartData
          : chartData // ignore: cast_nullable_to_non_nullable
              as List<PieChartSectionData>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$CsatChartStateImplCopyWith<$Res>
    implements $CsatChartStateCopyWith<$Res> {
  factory _$$CsatChartStateImplCopyWith(_$CsatChartStateImpl value,
          $Res Function(_$CsatChartStateImpl) then) =
      __$$CsatChartStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call(
      {List<PieChartSectionData> chartData,
      bool isLoading,
      String? errorMessage});
}

/// @nodoc
class __$$CsatChartStateImplCopyWithImpl<$Res>
    extends _$CsatChartStateCopyWithImpl<$Res, _$CsatChartStateImpl>
    implements _$$CsatChartStateImplCopyWith<$Res> {
  __$$CsatChartStateImplCopyWithImpl(
      _$CsatChartStateImpl _value, $Res Function(_$CsatChartStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? chartData = null,
    Object? isLoading = null,
    Object? errorMessage = freezed,
  }) {
    return _then(_$CsatChartStateImpl(
      chartData: null == chartData
          ? _value._chartData
          : chartData // ignore: cast_nullable_to_non_nullable
              as List<PieChartSectionData>,
      isLoading: null == isLoading
          ? _value.isLoading
          : isLoading // ignore: cast_nullable_to_non_nullable
              as bool,
      errorMessage: freezed == errorMessage
          ? _value.errorMessage
          : errorMessage // ignore: cast_nullable_to_non_nullable
              as String?,
    ));
  }
}

/// @nodoc

class _$CsatChartStateImpl implements _CsatChartState {
  const _$CsatChartStateImpl(
      {final List<PieChartSectionData> chartData = const [],
      this.isLoading = false,
      this.errorMessage})
      : _chartData = chartData;

  final List<PieChartSectionData> _chartData;
  @override
  @JsonKey()
  List<PieChartSectionData> get chartData {
    if (_chartData is EqualUnmodifiableListView) return _chartData;
    // ignore: implicit_dynamic_type
    return EqualUnmodifiableListView(_chartData);
  }

  @override
  @JsonKey()
  final bool isLoading;
  @override
  final String? errorMessage;

  @override
  String toString() {
    return 'CsatChartState(chartData: $chartData, isLoading: $isLoading, errorMessage: $errorMessage)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$CsatChartStateImpl &&
            const DeepCollectionEquality()
                .equals(other._chartData, _chartData) &&
            (identical(other.isLoading, isLoading) ||
                other.isLoading == isLoading) &&
            (identical(other.errorMessage, errorMessage) ||
                other.errorMessage == errorMessage));
  }

  @override
  int get hashCode => Object.hash(runtimeType,
      const DeepCollectionEquality().hash(_chartData), isLoading, errorMessage);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$CsatChartStateImplCopyWith<_$CsatChartStateImpl> get copyWith =>
      __$$CsatChartStateImplCopyWithImpl<_$CsatChartStateImpl>(
          this, _$identity);
}

abstract class _CsatChartState implements CsatChartState {
  const factory _CsatChartState(
      {final List<PieChartSectionData> chartData,
      final bool isLoading,
      final String? errorMessage}) = _$CsatChartStateImpl;

  @override
  List<PieChartSectionData> get chartData;
  @override
  bool get isLoading;
  @override
  String? get errorMessage;
  @override
  @JsonKey(ignore: true)
  _$$CsatChartStateImplCopyWith<_$CsatChartStateImpl> get copyWith =>
      throw _privateConstructorUsedError;
}
