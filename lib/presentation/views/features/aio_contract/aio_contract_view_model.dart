import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_param.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';

part 'aio_contract_state.dart';

final aioContractProvider =
    StateNotifierProvider.autoDispose<AioContractViewModel, AioContractState>(
  (ref) => AioContractViewModel(ref: ref),
);

class AioContractViewModel extends StateNotifier<AioContractState> {
  final Ref ref;

  AioContractViewModel({
    required this.ref,
  }) : super(const AioContractState());

  Future<void> getData({
    String? keySearch,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      keySearch: keySearch,
    );
    try {
      final resultParam =
          await appLocator<AioOrderRepository>().findListTotalContract(
        params: AioContractEntity(
          detailFinished: 0,
          sysUserId: GlobalData.instance.userInfo?.sysUserId ??
              GlobalData.instance.userInfo?.userId,
          sysGroupId: GlobalData.instance.userInfo?.sysGroupId,
          keySearch: keySearch,
        ),
      );

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            listAioContract: data.data,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getDataPerformer({
    String? keySearch,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      keySearch: keySearch,
    );
    try {
      final resultParam =
          await appLocator<AioOrderRepository>().getContractOfPerformer(
        params: AioContractParam(
          sysUserRequest: SysUserRequest(
            sysUserId: GlobalData.instance.userInfo?.sysUserId,
            departmentId: GlobalData.instance.userInfo?.sysGroupId,
            sysGroupId: GlobalData.instance.userInfo?.sysGroupId,
          ),
          keySearch: keySearch,
        ),
      );

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            listAioContract: data.data,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }
}
