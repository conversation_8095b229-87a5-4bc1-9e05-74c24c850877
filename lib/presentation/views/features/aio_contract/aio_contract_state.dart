part of 'aio_contract_view_model.dart';

class AioContractState extends Equatable {
  final LoadStatus loadStatus;
  final String? message;
  final String? keySearch;
  final List<AioContractEntity>? listAioContract;

  const AioContractState({
    this.loadStatus = LoadStatus.initial,
    this.message,
    this.keySearch,
    this.listAioContract,
  });

  AioContractState copyWith({
    LoadStatus? loadStatus,
    String? message,
    String? keySearch,
    List<AioContractEntity>? listAioContract,
  }) {
    return AioContractState(
      loadStatus: loadStatus ?? this.loadStatus,
      message: message ?? this.message,
      keySearch: keySearch ?? this.keySearch,
      listAioContract: listAioContract ?? this.listAioContract,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        message,
        keySearch,
        listAioContract,
      ];
}
