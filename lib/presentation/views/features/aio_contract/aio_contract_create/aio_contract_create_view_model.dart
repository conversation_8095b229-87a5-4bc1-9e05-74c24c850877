import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/data/repositories/user_profile_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/aio_contract/aio_commission_rate_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_config_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_result_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_area_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_collection_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_param_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_param_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_survey_customer_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_survey_entity.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/customer_info_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/entities/order/drop_down_list_entity.dart';
import 'package:vcc/domain/entities/user/implement_user_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_confirm_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_info_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_method_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_date_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_payment_method_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_transport_type.dart';
import 'package:vcc/domain/enums/aio_contract/drop_down_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_customer_type.dart';
import 'package:vcc/domain/enums/contract_resource_type.dart';
import 'package:vcc/domain/enums/customer_gender.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/params/aio_contract/aio_check_deployment.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_digital_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_update_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_image.dart';

part 'aio_contract_create_state.dart';

final aioContractCreateProvider = StateNotifierProvider.autoDispose<
    AioContractCreateViewModel, AioContractCreateState>(
  (ref) => AioContractCreateViewModel(ref: ref),
);

class AioContractCreateViewModel extends StateNotifier<AioContractCreateState> {
  final Ref ref;

  AioContractCreateViewModel({
    required this.ref,
  }) : super(AioContractCreateState());

  Future<void> getData() async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      customerLevel: DropDownListEntity(id: 1582, code: "1582", name: "Bạc"),
    );
    try {
      final resultParam =
          await appLocator<AioOrderRepository>().findDropDownData();
      final resultTransportMethod = (await appLocator<AioOrderRepository>()
          .getTransportMethod(params: AioContractUpdateParam()));

      resultTransportMethod?.when(success: (data) {
        state = state.copyWith(
          provinceNameVTPost: data.data?.provinceNameVTPost,
        );
      });

      await resultParam?.when(
        success: (data) async {
          if (data.data != null) {
            var listCustomerLevel = (data.data ?? [])
                .where((element) =>
                    element.parType == DropDownType.customerType.code)
                .toList();
            var listGeographical = (data.data ?? [])
                .where((element) =>
                    element.parType == DropDownType.geographical.code)
                .toList();
            var listEmploymentField = (data.data ?? [])
                .where((element) =>
                    element.parType == DropDownType.employmentField.code)
                .toList();
            var listStaffNumber = (data.data ?? [])
                .where((element) =>
                    element.parType == DropDownType.staffNumber.code)
                .toList();
            var listPositionContact = (data.data ?? [])
                .where((element) =>
                    element.parType == DropDownType.positionContact.code)
                .toList();
            var listPositionRepresent = (data.data ?? [])
                .where((element) =>
                    element.parType == DropDownType.positionRepresent.code)
                .toList();
            var listService = (data.data ?? [])
                .where((element) =>
                    element.parType == DropDownType.serviceType.code)
                .toList();
            // var listServiceOriginal = (data.data ?? [])
            //     .where((element) =>
            //         element.parType == DropDownType.serviceType.code)
            //     .toList();

            List<DropDownListEntity> listIndustry = [];
            for (int i = 0; i < listService.length; i++) {
              if (!listIndustry.any(
                  (item) => item.industryCode == listService[i].industryCode)) {
                listIndustry.add(DropDownListEntity(
                  code: listService[i].industryCode,
                  industryCode: listService[i].industryCode,
                  id: i,
                ));
              }
            }
            List<DropDownListEntity>? listCapitalSource = [
              DropDownListEntity(name: "Ngân sách nhà nước", id: 1, code: "1"),
              DropDownListEntity(
                  name: "Doanh nghiệp nhà nước", id: 2, code: "2"),
              DropDownListEntity(
                  name: "Doanh nghiệp tư nhân", id: 3, code: "3"),
            ];
            List<DropDownListEntity>? listFormSignContract = [
              DropDownListEntity(name: "Chỉ định đấu thầu", id: 1, code: "1"),
              DropDownListEntity(name: "Đấu thầu rộng rãi", id: 2, code: "2"),
              DropDownListEntity(
                  name: "Chào hàng cạnh tranh", id: 3, code: "3"),
              DropDownListEntity(name: "Chào giá cạnh tranh", id: 4, code: "4"),
            ];
            List<DropDownListEntity>? listDeploymentMethod = [
              DropDownListEntity(name: "Tự triển khai", id: 1, code: "1"),
              DropDownListEntity(name: "Thuê ngoài", id: 2, code: "2"),
            ];

            List<DropDownListEntity>? listRegencyOwner = [
              DropDownListEntity(code: "1", id: 1, name: "Tổng Giám đốc"),
              DropDownListEntity(code: "2", id: 2, name: "Phó Tổng GĐ"),
              DropDownListEntity(code: "3", id: 3, name: "Giám đốc"),
            ];

            state = state.copyWith(
              loadStatus: LoadStatus.success,
              listCustomerLevel: listCustomerLevel,
              listGeographical: listGeographical,
              listEmploymentField: listEmploymentField,
              listStaffNumber: listStaffNumber,
              listPositionContact: listPositionContact,
              listPositionRepresent: listPositionRepresent,
              listIndustry: listIndustry,
              listService: listService,
              listServiceOriginal: listService,
              listCapitalSource: listCapitalSource,
              listFormSignContract: listFormSignContract,
              listDeploymentMethod: listDeploymentMethod,
              listRegencyOwner: listRegencyOwner,
              industry: listIndustry[0],
              service: listService[0],
            );
          } else {
            state = state.copyWith(
              loadStatus: LoadStatus.failure,
            );
          }
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );

      final aioConfigResult =
          await appLocator<AioOrderRepository>().getCodeAioConfigService(
              params: InternalStaffEntity(
        sysUserId: GlobalData.instance.userInfo?.sysUserId,
      ));
      await aioConfigResult?.when(
        success: (data) async {
          List<DropDownListEntity> listIndustry = [];
          var listService = data.aioConfigServiceMobileDTO ?? [];
          for (int i = 0; i < listService.length; i++) {
            if (!listIndustry.any(
                (item) => item.industryCode == listService[i].industryCode)) {
              listIndustry.add(DropDownListEntity(
                code: listService[i].industryCode,
                industryCode: listService[i].industryCode,
                id: i,
              ));
            }
          }
          if (listIndustry.isNotEmpty) {
            state.industry = listIndustry[0];
          }

          var listServiceTmp = state.listService;
          for (var element in (listServiceTmp ?? [])) {
            for (var elementApi in (data.aioConfigServiceMobileDTO ?? [])) {
              if (element.code == elementApi.code) {
                element.name = elementApi.name;
              }
            }
          }
          var service = state.service;
          if ((listServiceTmp ?? []).isNotEmpty) {
            service = listServiceTmp
                ?.where((itx) =>
                    itx.industryCode == state.industry?.code &&
                    itx.code != state.industry?.code)
                .toList()[0];
          }
          state = state.copyWith(
            aioConfigService: data,
            listService: listServiceTmp,
            listServiceOriginal: listServiceTmp,
            service: service,
            listIndustry: listIndustry,
          );
        },
        error: (err) async {},
      );

      final listGoodsType = await appLocator<AioOrderRepository>()
          .getListGoodsType(params: DropDownListEntity());
      await listGoodsType?.when(
        success: (data) async {
          state = state.copyWith(
            listGoodsType: data.data,
          );
        },
        error: (err) async {},
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<AioContractResponseEntity?> saveNewContract({
    required AioContractParam params,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    AioContractResponseEntity? rs;
    try {
      final resultParam = await appLocator<AioOrderRepository>()
          .saveNewContract(params: params);

      resultParam?.when(
        success: (data) async {
          rs = data;
          if (data.resultInfo?.status != BaseConstant.ok) {
            state = state.copyWith(
              loadStatus: LoadStatus.success,
              message: data.resultInfo?.message,
            );
          } else {
            state = state.copyWith(
              loadStatus: LoadStatus.success,
            );
          }
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      ).toString();
      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
    }
    return rs;
  }

  void changeSaleInternal() {
    if (state.saleInternal == false) {
      state = state.copyWith(
        saleInternal: true,
        customerType: AioCustomerType.customer,
      );
    } else {
      state.internalStaff = null;
      state = state.copyWith(
        saleInternal: false,
        internalStaff: null,
      );
    }
  }

  void changeContactFrom() {
    if (state.contactFrom == "") {
      state = state.copyWith(
        contactFrom: AioContractType.collectionInfo.code,
      );
    } else {
      state = state.copyWith(
        contactFrom: "",
      );
    }
  }

  void deleteInternalStaff() {
    state.internalStaff = null;
    state = state.copyWith(
      internalStaff: null,
    );
  }

  void changeCustomerLevel(DropDownListEntity data) {
    state = state.copyWith(
      customerLevel: data,
    );
  }

  void changeCapitalSource(DropDownListEntity data) {
    state = state.copyWith(
      capitalSource: data,
    );
  }

  void changeFormSignContract(DropDownListEntity data) {
    state = state.copyWith(
      formSignContract: data,
    );
  }

  void changeDeploymentMethod(DropDownListEntity data) {
    state = state.copyWith(
      deploymentMethod: data,
    );
  }

  void changeGeographical(DropDownListEntity data) {
    state = state.copyWith(
      geographical: data,
    );
  }

  void changeGoodsType(DropDownListEntity data) {
    state = state.copyWith(
      goodsType: data,
    );
    autoChangeContractInfoType();
  }

  void changeEmploymentField(DropDownListEntity data) {
    state = state.copyWith(
      employmentField: data,
    );
  }

  void changeRegencyOwner(DropDownListEntity data) {
    state = state.copyWith(
      regencyOwner: data,
    );
  }

  void changeRegencyContact(DropDownListEntity data) {
    state = state.copyWith(
      regencyContact: data,
    );
  }

  void changeStaffNumber(DropDownListEntity data) {
    state = state.copyWith(
      staffNumber: data,
    );
  }

  void changePositionContact(DropDownListEntity data) {
    state = state.copyWith(
      positionContact: data,
    );
  }

  void changeIndustry(DropDownListEntity data) async {
    DropDownListEntity? service;
    var listServiceTemp = (state.listService
            ?.where(
                (itx) => itx.industryCode == data.code && itx.code != data.code)
            .toList() ??
        []);

    state.service = null;
    if (listServiceTemp.isNotEmpty) {
      service = listServiceTemp
          .where(
              (itx) => itx.industryCode == data.code && itx.code != data.code)
          .toList()[0];
    }

    state = state.copyWith(
      industry: data,
      service: service,
      packages: [],
    );
    calculatorTotalMoney();
  }

  void changeService(DropDownListEntity data) {
    state = state.copyWith(
      service: data,
    );
    autoChangeContractInfoType();
  }

  void changePositionRepresent(DropDownListEntity data) {
    state = state.copyWith(
      positionRepresent: data,
    );
  }

  Future<void> selectStaff({
    required InternalStaffEntity staff,
    bool isUserPropose = false,
  }) async {
    if (isUserPropose) {
      state = state.copyWith(
        internalStaff: staff,
      );
    } else {
      state = state.copyWith(
        internalStaff: staff,
      );
    }
    if (staff.phoneNumber != null) {
      getCustomerInfo360(
        phoneNumber: staff.phoneNumber,
        type: UserType.personal,
      );
    }
  }

  Future<void> selectReferUser({
    required InternalStaffEntity data,
  }) async {
    state = state.copyWith(
      referUser: data,
    );
  }

  void deleteReferUser() {
    state.referUser = null;
    state = state.copyWith(
      referUser: null,
    );
  }

  void selectPerformUser({
    required InternalStaffEntity data,
  }) {
    state = state.copyWith(
      performUser: data,
    );
  }

  void deletePerformUser() {
    state.performUser = null;
    state = state.copyWith(
      performUser: null,
    );
  }

  void deleteSurvey() {
    state.survey = null;
    state = state.copyWith(
      survey: null,
    );
  }

  void selectSurvey({
    required AioSurveyEntity data,
  }) {
    state.surveyCustomer = null;
    state = state.copyWith(
      survey: data,
      surveyCustomer: null,
    );
  }

  void deleteSurveyCustomer() {
    state.surveyCustomer = null;
    state = state.copyWith(
      surveyCustomer: null,
    );
  }

  void selectSurveyCustomer({
    required AioSurveyCustomerEntity data,
  }) {
    state = state.copyWith(
      surveyCustomer: data,
    );
  }

  void deleteCollectionInfo() {
    state.collectionInfo = null;
    state = state.copyWith(
      collectionInfo: null,
    );
  }

  void selectCollectionInfo({
    required AioCollectionInfoEntity data,
  }) {
    state = state.copyWith(
      collectionInfo: data,
    );
  }

  void changeContractResourceType(ContractResourceType type) {
    state = state.copyWith(
      contractResourceType: type,
    );
  }

  void changeCustomerType(AioCustomerType type) {
    state = state.copyWith(
      customerType: type,
      showCus360: false,
    );
  }

  Future<AioResultInfoEntity?> changeTransportType(
      AioTransportType type) async {
    state = state.copyWith(
      loadStatusDeployContract: LoadStatus.loading,
    );
    final resultCheck = await appLocator<AioOrderRepository>()
        .checkDeploymentMethod(
            param: AioCheckDeployment(
                sysUserId: GlobalData.instance.userInfo?.sysUserId,
                deploymentMethodContract: int.parse(type.code),
                // areaId: state.customerAddress?.ward?.code,
                areaCode: state.customerAddress?.ward?.code));
    state = state.copyWith(
      loadStatusDeployContract: LoadStatus.success,
    );
    AioResultInfoEntity? rs;
    await resultCheck.when(
      success: (data) async {
        rs = data?.resultInfo;
      },
      error: (err) async {
        rs = null;
      },
    );
    if (rs?.status != BaseConstant.ok) {
      return rs;
    }

    state = state.copyWith(
      deploymentMethodContract: type,
      packages: [],
    );
    if (type == AioTransportType.vcc) {
      if (state.contractInfoType == AioContractInfoType.package) {
        state = state.copyWith(
          xcare: true,
          transportType: AioTransportType.vcc,
        );
      }
      state.performUser = null;
    }
    if (type == AioTransportType.self) {
      state = state.copyWith(
        transportType: AioTransportType.vcc,
        xcare: false,
        performUser: InternalStaffEntity(
          username: GlobalData.instance.userInfo?.username,
          userId: GlobalData.instance.userInfo?.sysUserId,
          fullName: GlobalData.instance.userInfo?.fullName,
          sysUserId: GlobalData.instance.userInfo?.sysUserId,
          employeeCode: GlobalData.instance.userInfo?.employeeCode,
        ),
      );
    }
    if (type == AioTransportType.vtp) {
      state = state.copyWith(
        transportType: AioTransportType.vcc,
        xcare: false,
      );
    }
    calculatorTotalMoney();
    return rs;
  }

  void changeIsBill(AioConfirmType type) {
    state = state.copyWith(
      isBill: type,
    );
  }

  changeXCare(bool xcare) {
    state = state.copyWith(
      transportType: AioTransportType.vcc,
      xcare: xcare,
    );
  }

  xCareOff(bool xcare) {
    state = state.copyWith(
      xcare: xcare,
    );
  }

  void changeIsViettelPay(AioPaymentMethodType type) {
    state = state.copyWith(
      isViettelPay: type,
    );
  }

  void changeCustomerGender(CustomerGender data) {
    state = state.copyWith(
      customerGender: data,
    );
  }

  void changeRegenGender(CustomerGender data) {
    state = state.copyWith(
      sexRegencyOwner: data,
    );
  }

  void changeRegenGenderContact(CustomerGender data) {
    state = state.copyWith(
      sexRegencyContact: data,
    );
  }

  void changeObjectCustomerType(AioCustomerType type) {
    state.positionContact = null;
    state = state.copyWith(
      objectCustomer: type,
    );
  }

  void changeIsShowOwner() {
    state = state.copyWith(
      isShowOwner: !state.isShowOwner,
    );
  }

  void changeIsShowContact() {
    state = state.copyWith(
      isShowContact: !state.isShowContact,
    );
  }

  void changeAioContractInfoType(AioContractInfoType type) async {
    bool xcare = false;
    state.deploymentMethodContract = AioTransportType.nan;
    if (type == AioContractInfoType.commerce) {
      xcare = false;
    } else if (type == AioContractInfoType.service) {
      xcare = false;
    } else if (type == AioContractInfoType.package) {
      if ((state.listService ?? []).isNotEmpty) {
        xcare = true;
      } else {
        xcare = false;
      }
    }

    if (state.deploymentMethodContract == AioTransportType.vcc &&
        type == AioContractInfoType.commerce) {
      xcare = false;
    }

    state.performUser = null;
    state = state.copyWith(
      packages: [],
      performUser: null,
    );
    if (!(state.industry?.code == "NLMT" && state.service?.status == "2")) {
      state = state.copyWith(
        contractInfoType: type,
        xcare: xcare,
        packages: [],
      );
    } else {
      state = state.copyWith(
        contractInfoType: AioContractInfoType.package,
      );
    }
    calculatorTotalMoney();
  }

  autoChangeContractInfoType() {
    if (state.industry?.code == "NLMT" && state.service?.status == "2") {
      state = state.copyWith(
        contractInfoType: AioContractInfoType.package,
      );
    }
  }

  void changeAioContractMethodType(AioContractMethodType type) {
    state = state.copyWith(
      contractMethodType: type,
    );
  }

  void selectAddress(AddressEntity address) {
    state.packages = [];
    state.performUser = null;
    state = state.copyWith(
      customerAddress: address,
      packages: [],
      deploymentMethodContract: AioTransportType.nan,
      performUser: null,
    );
  }

  void selectAddressOwner(AddressEntity address) {
    state = state.copyWith(
      addressRegencyOwner: address,
    );
  }

  void selectAddressContact(AddressEntity address) {
    state = state.copyWith(
      addressRegencyContact: address,
    );
  }

  String setUpTime(DateTime dateSelect, {AioDateType? type}) {
    String error = "";
    if (type == AioDateType.businessEstablish) {
      state = state.copyWith(
        businessEstablish: dateSelect,
      );
    } else if (type == AioDateType.dateStartContract) {
      state = state.copyWith(
        dateStartContract: dateSelect,
      );
    } else if (type == AioDateType.scheduleTime) {
      state = state.copyWith(
        scheduleTime: dateSelect,
      );
    } else if (type == AioDateType.birthRegencyOwner) {
      state = state.copyWith(
        birthRegencyOwner: dateSelect,
      );
    } else if (type == AioDateType.birthRegencyContact) {
      state = state.copyWith(
        birthRegencyContact: dateSelect,
      );
    } else if (type == AioDateType.startDateDeployment) {
      state = state.copyWith(
        startDateDeployment: dateSelect,
      );
    } else if (type == AioDateType.endDateDeployment) {
      state = state.copyWith(
        endDateDeployment: dateSelect,
      );
    } else {
      state = state.copyWith(
        customerDob: dateSelect,
      );
    }
    return error;
  }

  void uploadImage(AioImage image) async {
    state = state.copyWith(
      uploadImageStatus: LoadStatus.loading,
    );
    List<AioImage>? listImages = state.listImage ?? [];
    listImages.add(image);
    state = state.copyWith(
      listImage: listImages,
      uploadImageStatus: LoadStatus.success,
    );
  }

  void deleteImage(int index) async {
    List<AioImage> listImage = state.listImage ?? [];
    listImage.removeAt(index);

    state = state.copyWith(
      listImage: listImage,
    );
  }

  updatePackages(List<AioPackageEntity>? packages) async {
    state = state.copyWith(
      packages: packages,
    );
    await calculatorTotalMoney();
  }

  void updateInvoice(AioInvoiceEntity invoice) async {
    state = state.copyWith(
      invoiceDto: invoice,
    );
    calculatorTotalMoney();
  }

  void updatePackagesService(AioPackageEntity? package) async {
    var packages = state.packages;

    for (int i = 0; i < (packages ?? []).length; i++) {
      if (package?.aioPackageDetailId == packages?[i].aioPackageDetailId) {
        packages?[i].serviceItem = package?.serviceItem;
      }
    }

    state = state.copyWith(
      packages: packages,
    );
    calculatorTotalMoney();
  }

  void changeQuantityPackage({
    AioPackageEntity? package,
    int? index,
    int? quantity,
  }) async {
    var listPackages = state.packages ?? [];

    var package = listPackages[index ?? 0];
    if (quantity == 0) {
      return;
    }

    package.quantity = quantity;

    for (int i = 0; i < listPackages.length; i++) {
      final item = listPackages[i];
      if (item.aioPackageId == package.aioPackageId) {
        listPackages[i] = item.copyWith(
          quantity: quantity,
        );
        break;
      }
    }

    state = state.copyWith(
      packages: listPackages,
    );
    calculatorTotalMoney();
  }

  Future<InternalStaffEntity?> checkInternalStaff({
    String? phoneNumber,
  }) async {
    state.internalStaff = null;
    state = state.copyWith(
      saleInternal: false,
      internalStaff: null,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListStaff(
        phoneNumber: phoneNumber,
        userType: "INTERNAL",
      );

      InternalStaffEntity? checkStaff;

      await result.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            checkStaff = data.data?[0];
            state = state.copyWith(
              saleInternal: true,
            );
          }
          state = state.copyWith(
            internalStaff: checkStaff,
          );
        },
        error: (err) async {
          checkStaff = null;
        },
      );

      return checkStaff;
    } catch (error) {
      return null;
    }
  }

  Future<List<AioInvoiceItemEntity>?> getListInvoiceItem({
    required List<int?> listPackageId,
  }) async {
    try {
      final result = await appLocator<AioOrderRepository>().getListInvoiceItem(
        listPackageId: listPackageId,
      );
      List<AioInvoiceItemEntity>? listInvoiceItem = [];
      await result?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            listInvoiceItem = data.data;
          }
        },
        error: (err) async {
          listInvoiceItem = null;
        },
      );

      return listInvoiceItem;
    } catch (error) {
      return null;
    }
  }

  Future<AioCommissionRateEntity?> getNewCommissionRate({
    required AioContractParam param,
  }) async {
    try {
      state = state.copyWith(
        loadStatus: LoadStatus.loading,
      );
      final result =
          await appLocator<AioOrderRepository>().getNewCommissionRate(
        params: param,
      );

      AioCommissionRateEntity? rs;

      await result?.when(
        success: (data) async {
          if (data.data != null) {
            rs = data;
          }
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          rs = null;
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
        },
      );

      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  Future<List<AioInvoiceItemEntity>?> dataOrderRequestForContract({
    required AioContractEntity param,
  }) async {
    try {
      state = state.copyWith(
        loadStatus: LoadStatus.loading,
      );
      final result =
          await appLocator<AioOrderRepository>().dataOrderRequestForContract(
        param: param,
      );

      List<AioInvoiceItemEntity>? rs = [];

      await result?.when(
        success: (data) async {
          if (data.data != null) {
            rs = data.data;
          }
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          rs = null;
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
        },
      );

      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  Future<CustomerInfoEntity?> getCustomerInfo360({
    String? phoneNumber,
    String? taxCode,
    bool? isInternalStaff,
    required UserType type,
  }) async {
    try {
      state.customerInfo = null;
      state = state.copyWith(
        showCus360: false,
      );
      final result = await appLocator<UserProfileRepository>().getCustomerInfo(
        customerType: type,
        taxCode: taxCode,
        phoneNumber: phoneNumber,
      );

      CustomerInfoEntity? customerInfo;

      await result?.when(
        success: (data) async {
          if (data.phone != null) {
            customerInfo = data;
            AddressEntity? address;

            if (data.address != null) {
              address = AddressEntity(
                addressDetail: data.addressDetail,
                province: CodeEntity(
                  name: data.provinceName,
                  code: data.provinceCode,
                ),
                district: CodeEntity(
                  name: data.districtName,
                  code: data.districtCode,
                ),
                ward: CodeEntity(
                  name: data.wardName,
                  code: data.wardCode,
                ),
              );
            }

            state = state.copyWith(
              customerAddress: address,
              customerInfo: data,
              showCus360: true,
            );

            // get performer
            AioAreaEntity aioAreaDTO = AioAreaEntity(
              areaId: address?.ward?.id,
              code: address?.ward?.id == null ? address?.ward?.code : null,
              provinceId: address?.province?.id,
              configServiceCode: state.industry?.code,
              field: state.employmentField?.code ?? "TT.GPTH",
              industryCode: state.industry?.code,
              text: "VCC",
              type: 2,
              contractType: int.parse(state.contractInfoType.keyToServer),
              sysUserId: GlobalData.instance.userInfo?.sysUserId,
            );

            getPackage(
                param: AioPackageParamEntity(
              aioAreaDTO: aioAreaDTO,
            ));
          }
        },
        error: (err) async {},
      );
      return customerInfo;
    } catch (error) {
      return null;
    }
  }

  void removePackage({
    required int index,
    required AioPackageEntity package,
  }) {
    var listSelected = state.packages ?? [];
    var packages = state.packages ?? [];
    packages[index].isSelect = false;

    for (int i = 0; i < listSelected.length; i++) {
      final item = listSelected[i];
      if (item.aioPackageId == package.aioPackageId) {
        listSelected.remove(item);
        break;
      }
    }

    state = state.copyWith(
      packages: listSelected,
    );
    calculatorTotalMoney();
  }

  calculatorTotalMoney() {
    int totalMoney = 0;
    if ((state.packages ?? []).isNotEmpty) {
      for (int i = 0; i < state.packages!.length; i++) {
        final item = state.packages![i];
        var money = (item.quantity ?? 1) * (item.price ?? 0) +
            (item.quantity ?? 1) *
                (item.serviceItem?.price ?? 0) *
                (item.serviceItem?.amount ?? 0);
        totalMoney = totalMoney + money.toInt();
      }
    }

    state = state.copyWith(
      totalMoney: totalMoney,
    );
  }

  businessAddressSetNull() async {
    state.customerAddress = null;
    state = state.copyWith(
      customerAddress: null,
      showCus360: false,
    );
  }

  showCus360() async {
    state = state.copyWith(
      showCus360: true,
    );
  }

  Future<void> getPackage({
    AioPackageParamEntity? param,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<AioOrderRepository>()
          .getDataPackageDetail(params: param);

      await result?.when(
        success: (data) async {
          InternalStaffEntity? performUser = InternalStaffEntity(
            username: data.aioContractMobileDTO?.performerCode,
            userId: data.aioContractMobileDTO?.performerId,
            fullName: data.aioContractMobileDTO?.performerName,
            sysUserId: data.aioContractMobileDTO?.performerId,
          );
          state = state.copyWith(
            implementUser: data.aioContractMobileDTO,
            performUser: performUser,
            loadStatus: LoadStatus.success,
            packages: [],
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
    }
  }

  Future<AioContractResponseEntity?> getUrlToRequestSubmitMoney(
      AioContractDigitalParam? param) async {
    AioContractResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result = await appLocator<AioOrderRepository>()
        .getUrlToRequestSubmitMoney(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  Future<List<AioServiceEntity>?> getServices({
    List<AioPackageEntity>? packageSelected,
  }) async {
    List<AioServiceEntity>? rs;
    state = state.copyWith(
      loadStatusPackage: LoadStatus.loading,
    );
    try {
      final result = await appLocator<AioOrderRepository>().getServiceHs(
        params: AioServiceParamEntity(
          detailDTOS: packageSelected,
          detailFinished: 0,
          subAction: 0,
          done: 0,
          sizeTimePay: 0,
          notDone: 0,
          canOrderRequest: false,
          canProposeCancel: false,
          canDeleteProposeCancel: false,
          utilAttachDocumentId: 0,
          statusImage: 0,
          obstructedId: 0,
          isStaffPhoneNumber: false,
        ),
      );

      await result?.when(
        success: (data) async {
          if (data.resultInfo?.status == BaseConstant.ok) {
            rs = data.data;
          } else {
            rs = [
              AioServiceEntity(
                displayCode: BaseConstant.notOK,
                displayName: data.resultInfo?.message,
              ),
            ];
          }
        },
        error: (err) {
          return null;
        },
      );
    } catch (error) {
      return null;
    }
    state = state.copyWith(
      loadStatusPackage: LoadStatus.success,
    );
    return rs;
  }
}
