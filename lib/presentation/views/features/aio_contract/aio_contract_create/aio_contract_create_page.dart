import 'dart:convert';
import 'dart:io';
import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pattern_formatter/pattern_formatter.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_result_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_area_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_param_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_service_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/entities/one_pay_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_item_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_confirm_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_info_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_method_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_date_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_payment_method_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_transport_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_customer_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_user_select_type.dart';
import 'package:vcc/domain/enums/contract_resource_type.dart';
import 'package:vcc/domain/enums/customer_gender.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/enums/work_menu_type.dart';
import 'package:vcc/domain/params/aio_contract/aio_area.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_digital_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_customer.dart';
import 'package:vcc/domain/params/aio_contract/aio_image.dart';
import 'package:vcc/domain/params/aio_contract/app_param.dart';
import 'package:vcc/domain/params/aio_contract/authentication_info.dart';
import 'package:vcc/domain/params/aio_contract/cat_province.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_association/choose_association_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_collection_info/choose_collection_info_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_staff/choose_staff_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_survey_customer/choose_survey_customer_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/aio_contract/add_package/add_package_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/add_service/add_service_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment/one_pay_view.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/pages/web_view_screen.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_update/bill_update_page.dart';
import 'package:vcc/presentation/views/features/work/request_buy_product/create_request_buy_product/create_request_buy_product_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/card_custom_widget.dart';
import 'package:vcc/presentation/views/widgets/cus360_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_list_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/image_base64_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/plus_and_minus_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/normal_text_field.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/deBouncer.dart';
import 'package:vcc/utils/media_utils.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';
import 'aio_contract_create_view_model.dart';

class AioContractCreateArguments {
  final String? data;

  AioContractCreateArguments({
    this.data,
  });
}

class AioContractCreatePage extends StatefulHookConsumerWidget {
  final AioContractCreateArguments? arguments;

  const AioContractCreatePage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<AioContractCreatePage> createState() =>
      _AioContractCreatePageState();
}

class _AioContractCreatePageState extends ConsumerState<AioContractCreatePage>
    with TickerProviderStateMixin {
  late TextEditingController customerPhoneNumberController;
  late TextEditingController socialController;
  late TextEditingController customerNameController;
  late TextEditingController contactPhoneController;
  late TextEditingController companyTaxController;
  late TextEditingController companyNameController;
  late TextEditingController companyAddressController;
  late TextEditingController customerEmailController;
  late TextEditingController customerLevelController;
  late TextEditingController customerFieldController;
  late TextEditingController customerAreaController;
  late TextEditingController businessTaxNumberController;
  late TextEditingController businessNameController;
  late TextEditingController businessAddressController;
  late TextEditingController businessPhoneNumberController;
  late TextEditingController businessContactPhoneNumberController;
  late TextEditingController businessEmailController;
  late TextEditingController businessEstablishController;
  late TextEditingController contentController;
  late TextEditingController noteController;
  late TextEditingController percentController;
  late TextEditingController contractCodeController;
  late TextEditingController positionController;
  late TextEditingController representativeController;
  late TextEditingController phoneRegencyOwnerController;
  late TextEditingController emailRegencyOwnerController;
  late TextEditingController customerContactController;
  late TextEditingController phoneRegencyContactController;
  late TextEditingController emailRegencyContactController;
  late TextEditingController collectedMoneyController;
  late TextEditingController receivablesMoneyController;

  final FocusNode customerNameFocusNode = FocusNode();
  final FocusNode positionFocusNode = FocusNode();
  final FocusNode customerPhoneFocusNode = FocusNode();
  final FocusNode contactPhoneFocusNode = FocusNode();
  final FocusNode socialFocusNode = FocusNode();
  final FocusNode companyTaxFocusNode = FocusNode();
  final FocusNode businessEmailFocusNode = FocusNode();
  final FocusNode customerEmailFocusNode = FocusNode();
  final FocusNode noteFocusNode = FocusNode();
  final FocusNode contentFocusNode = FocusNode();
  final FocusNode businessTaxNumberFocusNode = FocusNode();
  final FocusNode businessNameFocusNode = FocusNode();
  final FocusNode businessPhoneNumberFocusNode = FocusNode();
  final FocusNode contractCodeFocusNode = FocusNode();

  late Debounce<String> deBouncerCusB2B;
  late Debounce<String> deBouncerCusB2C;

  bool isStaff = false;
  final FocusNode phoneNumberFocus = FocusNode();

  @override
  void initState() {
    Future(() {
      ref.read(aioContractCreateProvider.notifier).getData();
    });

    customerPhoneNumberController = TextEditingController();
    representativeController = TextEditingController();
    socialController = TextEditingController();
    customerNameController = TextEditingController();
    contactPhoneController = TextEditingController();
    companyTaxController = TextEditingController();
    companyNameController = TextEditingController();
    companyAddressController = TextEditingController();
    customerEmailController = TextEditingController();
    customerLevelController = TextEditingController();
    customerFieldController = TextEditingController();
    customerAreaController = TextEditingController();
    businessTaxNumberController = TextEditingController();
    businessNameController = TextEditingController();
    businessAddressController = TextEditingController();
    businessPhoneNumberController = TextEditingController();
    businessContactPhoneNumberController = TextEditingController();
    businessEmailController = TextEditingController();
    businessEstablishController = TextEditingController();
    noteController = TextEditingController();
    contentController = TextEditingController();
    percentController = TextEditingController(text: "100");
    contractCodeController = TextEditingController();
    positionController = TextEditingController();
    phoneRegencyOwnerController = TextEditingController();
    emailRegencyOwnerController = TextEditingController();
    customerContactController = TextEditingController();
    phoneRegencyContactController = TextEditingController();
    emailRegencyContactController = TextEditingController();
    collectedMoneyController = TextEditingController(text: "0");
    receivablesMoneyController = TextEditingController();

    deBouncerCusB2C = Debounce<String>(
      const Duration(milliseconds: 1000),
      (value) {
        getCusB2C();
      },
    );

    deBouncerCusB2B = Debounce<String>(
      const Duration(milliseconds: 1500),
      (value) {
        if ((value ?? "").length > 9 && (value ?? "").length < 15) {
          getCusB2B();
        }
      },
    );

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(aioContractCreateProvider);
    if (collectedMoneyController.text == "0") {
      receivablesMoneyController.text =
          StringUtils.formatNumber(state.totalMoney ?? 0);
    }
    if (state.saleInternal && state.internalStaff != null) {
      // customerNameController.text = state.internalStaff?.fullName ?? "";
      // customerPhoneNumberController.text =
      //     state.internalStaff?.phoneNumber ?? "";
      // socialController.text = state.internalStaff?.phoneNumber ?? "";
      // contactPhoneController.text = state.internalStaff?.phoneNumber ?? "";
    }
    PreferredSizeWidget appbarWidget;

    appbarWidget = AppBarCustom(
      title: "Tạo đơn gói giá",
      actionWidget: [
        InkWellWidget(
          onTap: () {},
        ),
      ],
    );

    return LayoutPage(
      appbar: appbarWidget,
      body: GestureDetector(
        onTap: () {
          // FocusScope.of(context).unfocus();
        },
        child: _buildPage(state),
      ),
      bottomAction: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
        ),
        decoration: BoxDecoration(
          color: BaseColors.backgroundWhite,
          boxShadow: AppBoxShadows.shadowNormal,
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              if (state.loadStatus == LoadStatus.success) ...[
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 6,
                  ),
                  child: Row(
                    children: <Widget>[
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Tổng tiền đơn hàng",
                              style: UITextStyle.body2Regular.copyWith(
                                color: BaseColors.textLabel,
                              ),
                            ),
                            Text(
                              StringUtils.formatMoney(state.totalMoney ?? 0),
                              style: UITextStyle.body1SemiBold.copyWith(
                                color: BaseColors.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: BaseButton(
                          text: "Tạo đơn",
                          onTap: () async {
                            _saveContractNew(
                              state: state,
                              context: context,
                            );
                          },
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _saveContractNew({
    required AioContractCreateState state,
    required BuildContext context,
  }) async {
    String err = _validateInput(state);
    if (err == "") {
      AioContractParam param = _getParam(state);
      if (state.isBill == AioConfirmType.yes &&
          state.contractInfoType == AioContractInfoType.commerce &&
          state.transportType == AioTransportType.vtp &&
          state.invoiceDto == null) {
        AppDialog.showDialogConfirm(
          context,
          title: "Xuất hoá đơn?",
          message: "Bạn đã xuất hóa đơn cho hợp đồng này. "
              "Hóa đơn sẽ được xuất dưới thông tin khách hàng lấy hóa đơn. \n"
              "Vui lòng nhập đầy đủ thông tin",
          onConfirmAction: () async {
            getInvoice(
              state,
              context: context,
            );
          },
        );
        return;
      }
      if (state.isBill == AioConfirmType.no &&
          state.contractInfoType == AioContractInfoType.commerce &&
          state.transportType == AioTransportType.vtp) {
        AppDialog.showDialogConfirm(
          context,
          title: "Xuất hoá đơn?",
          message:
              "Bạn đã không xuất hóa đơn. Hóa đơn sẽ được xuất dưới thông tin khách hàng không lấy hóa đơn. "
              "Sau khi hóa đơn được xuất nếu người dùng muốn thay đổi thông tin trên hóa đơn sẽ phải nộp thêm tiền. "
              "Bạn xác nhận không xuất hóa đơn?",
          onConfirmAction: () async {
            checkCommissionRate(
              state: state,
              param: param,
              context: context,
            );
          },
        );
      } else if (state.contractInfoType == AioContractInfoType.commerce &&
          state.isBill == AioConfirmType.yes &&
          state.transportType == AioTransportType.vtp) {
        AppDialog.showDialogConfirm(
          context,
          title: "Xuất hoá đơn?",
          message:
              "Bạn đã xuất hóa đơn cho hợp đồng này. Hóa đơn sẽ được xuất dưới thông tin khách hàng lấy hóa đơn.",
          onConfirmAction: () async {
            checkCommissionRate(
              state: state,
              param: param,
              context: context,
            );
          },
        );
      } else {
        checkCommissionRate(state: state, param: param, context: context);
      }
      // End: Check Xuất hóa đơn
    } else {
      AppDialog.showDialogCenter(
        context,
        message: err,
        status: DialogStatus.error,
      );
    }
  }

  void dataOrderRequestForContract({
    required int contractId,
    required String contractCode,
  }) async {
    await ref
        .read(aioContractCreateProvider.notifier)
        .dataOrderRequestForContract(
          param: AioContractEntity(),
        );
  }

  createContract({
    required AioContractCreateState state,
    required BuildContext context,
  }) async {
    AioContractParam param = _getParam(state);
    AioContractResponseEntity? response =
        await ref.read(aioContractCreateProvider.notifier).saveNewContract(
              params: param,
            );
    if (response?.resultInfo?.status == BaseConstant.ok) {
      // Lấy danh sách vật tư để tạo yêu cầu mua hàng
      List<AioInvoiceItemEntity>? requestData = await ref
          .read(aioContractCreateProvider.notifier)
          .dataOrderRequestForContract(
            param: AioContractEntity(
              sysUserId: GlobalData.instance.userInfo?.sysUserId,
              contractId: response?.aioContractMobileDTO?.contractId,
              contractCode: response?.aioContractMobileDTO?.contractCode,
            ),
          );

      var listItemSupply = (requestData ?? []).map((e) {
        var rs = e.toSupplyRequestProductEntity();
        rs.quantity = (e.amount ?? 0).toInt();
        return rs;
      }).toList();

      if (!context.mounted) return;

      AppDialog.showDialogFeedback(
        context,
        source: "Tạo đơn gói giá thành công",
      );
      AppDialog.showDialogCenter(
        context,
        message: "Tạo đơn thành công",
        status: DialogStatus.success,
      );
      bool toMng = true;
      // Start: check logic sau khi tạo đơn thành công
      // 1. Đối với trường hợp chọn "TK chuyên thu CNCT":
      // Ra popup cần nộp bao nhiêu tiền
      // => Đã xử lý trong popup thông báo lỗi
      // 2. Đối với thu trước tiền, ko có hàng CN: Ra màn hình thanh toán Onepay/ViettelPay
      // 3. Đối với không cần thu thu tiền trước, có hàng CN: Ra màn hình đặt hàng
      // 4. Đối với thu tiền trước, có hàng CN: Ra màn hình đặt hàng, xong ra màn hình thanh toán
      String collectedMoneyStr = collectedMoneyController.text.isNotEmpty
          ? collectedMoneyController.text.replaceAll(".", "")
          : "0";
      int collectedMoney = int.parse(collectedMoneyStr);

      if (listItemSupply.isNotEmpty) {
        context.push(RouterPaths.createRequestBuyProduct,
            extra: CreateRequestBuyProductArguments(
                listSupply: listItemSupply,
                contractInfo: AioContractInfoEntity(
                  contractId: response?.aioContractMobileDTO?.contractId,
                  contractCode: response?.aioContractMobileDTO?.contractCode,
                ),
                onSave: () {
                  context.pop();
                  if (toMng) {
                    goToOrderContractMng();
                  }
                }));
      } else {
        if (toMng) {
          context.pop();
          goToOrderContractMng();
        }
      }

      if (state.contractInfoType == AioContractInfoType.commerce &&
          state.isViettelPay == AioPaymentMethodType.viettelpay &&
          collectedMoney != 0) {
        getUrlToRequestSubmitMoney(
          contractId: response?.aioContractMobileDTO?.contractId,
          context: context,
        );
      } else if (state.contractInfoType == AioContractInfoType.commerce &&
          state.isViettelPay == AioPaymentMethodType.onePayCollect &&
          collectedMoney != 0) {
        context.push(
          RouterPaths.onePayView,
          extra: OnePayArguments(
            backFunction: () {
              context.pop();
              context.pop();
              goToOrderContractMng();
            },
            fromAioContract: true,
            onePayInfo: OnePayEntity(
              orderCode: response?.data,
              contractId: response?.aioContractMobileDTO?.contractId,
            ),
          ),
        );
        // getUrlOnePay();
      }
      // End: check logic sau khi tạo đơn thành công
    } else {
      if (!context.mounted) return;
      AppDialog.showDialogCenter(
        context,
        message: response?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
  }

  goToOrderContractMng() {
    context.push(
      RouterPaths.aioContract,
      extra: AioContractArguments(
        data: WorkMenuType.contractSale.value,
      ),
    );
  }

  checkCommissionRate({
    required AioContractCreateState state,
    required AioContractParam param,
    required BuildContext context,
  }) async {
    var data = await ref
        .read(aioContractCreateProvider.notifier)
        .getNewCommissionRate(param: param);

    if (!context.mounted) return;
    if (data?.data != null &&
        (data?.data?.alertMessRateCommission ?? '').isNotEmpty) {
      AppDialog.showDialogConfirm(
        context,
        title: "Thông báo",
        message: '${data?.data?.alertMessRateCommission}',
        onConfirmAction: () async {
          createContract(
            state: state,
            context: context,
          );
        },
      );
      return;
    } else {
      createContract(
        state: state,
        context: context,
      );
    }
  }

  String _validateInput(AioContractCreateState state) {
    String error = "";
    if ((state.totalMoney ?? 0) > 20000000) {
      if ((state.listImage ?? []).isEmpty) {
        error =
            "Với hợp đồng có giá trị lớn hơn 20.000.000đ, bắt buộc chụp ảnh thông tin hợp đồng.";
      }
      if (contractCodeController.text.isEmpty ||
          state.endDateDeployment == null ||
          state.startDateDeployment == null) {
        error = "Vui lòng nhập đầy đủ thông tin bổ sung HD";
      } else {
        if ((state.endDateDeployment?.millisecondsSinceEpoch ?? 0) <
            (state.startDateDeployment?.millisecondsSinceEpoch ?? 0)) {
          error = "Ngày triển khai HĐ phải nhỏ hơn ngày kết thúc HĐ";
        }
      }
    }
    if ((state.packages ?? []).isEmpty) {
      error = "Gói bán hàng không được để trống";
    }
    if (state.customerType == AioCustomerType.customer &&
        customerPhoneNumberController.text.isEmpty) {
      error = "Số điện thoại (người mua hàng) không được để trống";
    }
    if (state.customerType == AioCustomerType.customer &&
        customerNameController.text.isEmpty) {
      error = "Họ và tên không được để trống";
    }
    if (state.customerType == AioCustomerType.customer &&
        contactPhoneController.text.isEmpty) {
      error = "Số điện thoại liên hệ không được để trống";
    }
    if (state.dateStartContract == null) {
      error = "Thời gian bắt đầu không được để trống";
    }
    if (state.customerAddress == null) {
      error = "Địa chỉ không được để trống";
    }
    if (contentController.text.isEmpty) {
      error = "Nội dung không được để trống";
    }
    // Loại KH: KH doanh nghiệp + Đối tượng KH: KH thường
    if (state.customerType == AioCustomerType.bussiness &&
        state.objectCustomer == AioCustomerType.normal) {
      if (representativeController.text.isEmpty) {
        error = "Người liên hệ trực tiếp không được để trống";
      }
      if (phoneRegencyContactController.text.isEmpty) {
        error = "Số điện thoại đầu mối liên hệ không được để trống";
      }
      if (state.capitalSource == null) {
        error = "Nguồn gốc không được để trống";
      }
      if (state.formSignContract == null) {
        error = "Hình thức ký hợp đồng không được để trống";
      }
      if (state.deploymentMethod == null) {
        error = "Hình thức triển khai không được để trống";
      }
    }

    if (state.customerType == AioCustomerType.bussiness) {
      if (businessTaxNumberController.text.isEmpty) {
        error = "Mã số thuế doanh nghiệp không được để trống";
      }
      if (state.customerAddress?.getFullAddress == null) {
        error = "Địa chỉ doanh nghiệp không được để trống";
      }
      if (businessPhoneNumberController.text.isEmpty) {
        error = "Số điện thoại không được để trống";
      }
    }

    // if (state.contractInfoType == AioContractInfoType.package) {
    //   if (state.contractInfoType == AioContractInfoType.package &&
    //       (state.packages ?? []).isNotEmpty) {
    //     for (int i = 0; i < (state.packages ?? []).length; i++) {
    //       if (state.packages?[i].serviceItem == null) {
    //         error = "Vui lòng chọn dịch vụ HS";
    //       }
    //     }
    //   }
    // }

    if (collectedMoneyController.text.isNotEmpty) {
      String collectedMoneyStr =
          collectedMoneyController.text.replaceAll(".", "");
      if (int.parse(collectedMoneyStr) > (state.totalMoney ?? 0)) {
        error = "Số tiền thu trước không được vượt quá số tiền hợp đồng!";
        receivablesMoneyController.text = "";
        collectedMoneyController.text = "";
      }
    }

    return error;
  }

  AioContractParam _getParam(AioContractCreateState state) {
    int collectedMoney = 0;
    int receivablesMoney = 0;
    if (collectedMoneyController.text.isNotEmpty) {
      String collectedMoneyStr =
          collectedMoneyController.text.replaceAll(".", "");
      collectedMoney = int.parse(collectedMoneyStr);
    }
    if (receivablesMoneyController.text.isNotEmpty) {
      String receivablesMoneyStr =
          receivablesMoneyController.text.replaceAll(".", "");
      receivablesMoney = int.parse(receivablesMoneyStr);
    }

    SysUserRequest sysUserRequest = SysUserRequest(
      authenticationInfo: AuthenticationInfo(
        type: 0,
        username: GlobalData.instance.userInfo?.username,
      ),
      sysUserId: GlobalData.instance.userInfo?.sysUserId,
      departmentId: GlobalData.instance.userInfo?.sysGroupId,
      flag: 0,
      isTtqh: false,
      ft: false,
    );

    int? contractSurveyCustomerId;
    int? contractOrdersId;
    // Check for YCKS
    if (state.contractResourceType == ContractResourceType.collectInfo) {
      contractSurveyCustomerId = null;
      contractOrdersId = state.collectionInfo?.aioOrdersId;
    }
    // Check for YCTX
    if (state.contractResourceType == ContractResourceType.association) {
      contractOrdersId = null;
      contractSurveyCustomerId = state.surveyCustomer?.aioSurveyCustomerId;
    }
    // Start: Check for ViettelPost
    int payType = (state.contractInfoType == AioContractInfoType.commerce &&
            ((state.valueMoneyPrePayment ?? '0') == '0') &&
            state.transportType == AioTransportType.vtp)
        ? 3
        : state.isViettelPay == AioPaymentMethodType.viettelpay
            ? 1
            : state.isViettelPay == AioPaymentMethodType.onePayCollect
                ? 5
                : 2;
    if (state.deploymentMethodContract == AioTransportType.vtp &&
        (state.totalMoney ?? 0) > 0 &&
        collectedMoneyController.text.isNotEmpty &&
        int.parse(collectedMoneyController.text.replaceAll(".", "")) == 0) {
      payType = 3;
    }
    // End: Check for ViettelPost
    AioContractEntity aioContractDTO = AioContractEntity(
      collectedMoney:
          collectedMoneyController.text.isNotEmpty ? collectedMoney : null,
      receivablesMoney:
          receivablesMoneyController.text.isNotEmpty ? receivablesMoney : null,
      deploymentMethodContract: int.parse(state.deploymentMethodContract.code),
      transportType: state.contractInfoType == AioContractInfoType.commerce
          ? (int.parse(state.transportType.code))
          : null,
      ordersId: state.collectionInfo?.aioOrdersId,
      surveyCustomerId: state.surveyCustomer?.aioSurveyCustomerId,
      aioOrdersId: state.collectionInfo?.aioOrdersId,
      contractId: 0,
      customerAddress: state.customerAddress?.getFullAddress,
      contractOrdersId: contractOrdersId,
      contractSurveyCustomerId: contractSurveyCustomerId,
      configServiceStatus: state.service?.code == "PINNLMT" ? 2 : 0,
      field: "TT.GPTH",
      internalStaff: isStaff ? 1 : null,
      serviceCode: state.service?.code,
      contractAmount: state.totalMoney,
      name: state.service?.name,
      type: 2,
      contractCode: state.customerType == AioCustomerType.customer
          ? "KHCN_TT.GPTH_${state.aioConfigService?.sysGroupDto?.provinceCode ?? ""}"
          : "KHDN_TT.GPTH_${state.aioConfigService?.sysGroupDto?.provinceCode ?? ""}",
      performerId: state.performUser?.sysUserId,
      industryCode: state.industry?.code,
      dateStartContract: state.dateStartContract?.display(),
      scheduleTime: state.scheduleTime
          ?.display(format: DateTimeFormater.dateTimeFormatView),
      contractContent: contentController.text,
      description: noteController.text,
      contactPhone: state.customerType == AioCustomerType.customer
          ? contactPhoneController.text
          : phoneRegencyContactController.text,
      contractType: int.parse(state.contractInfoType.keyToServer),
      customerTypeId: state.customerLevel?.id,
      customerTypeName: state.customerLevel?.name,
      payType: payType,
      listImage: state.listImage,
      isInternal: state.saleInternal ? 1 : null,
      staffCode: state.saleInternal
          ? (state.internalStaff?.employeeCode ??
              state.internalStaff?.loginName ??
              state.internalStaff?.username)
          : null,
      isCompany:
          state.contractMethodType == AioContractMethodType.external ? 2 : 1,
      contractCodeHard: (state.totalMoney ?? 0) > 20000000
          ? contractCodeController.text
          : null,
      startContractHard: (state.totalMoney ?? 0) > 20000000
          ? state.startDateDeployment?.display()
          : null,
      endContractHard: (state.totalMoney ?? 0) > 20000000
          ? state.endDateDeployment?.display()
          : null,
      referralCode: state.referUser?.employeeCode,
      xcare: state.xcare == true ? 1 : null,
      capitalSource: state.capitalSource?.code != null
          ? int.parse(state.capitalSource?.code ?? "")
          : null,
      formSignContract: state.formSignContract?.code != null
          ? int.parse(state.formSignContract?.code ?? "")
          : null,
      deploymentMethod: state.deploymentMethod?.code != null
          ? int.parse(state.deploymentMethod?.code ?? "")
          : null,
    );

    CatProvince catProvinceDTO = CatProvince(
      code: state.aioConfigService?.sysGroupDto?.provinceCode,
      catProvinceId: state.aioConfigService?.sysGroupDto?.provinceId,
    );
    AioArea aioAreaDTO = AioArea(
      areaId: state.customerAddress?.ward?.id ?? -1,
      code: state.customerAddress?.ward?.id == null
          ? state.customerAddress?.ward?.code
          : null,
      isInvoice: state.isBill == AioConfirmType.yes ? 1 : 0,
    );
    // AIO Customer
    AioCustomer aioCustomer = AioCustomer(
      // Todo trường hợp tạo hợp đồng khi đi từ màn tiếp xúc sang
      // if (aioSurveyCustomerId != null) {
      //   aioCustomer?.aioSurveyCustomerId = aioSurveyCustomerId;
      // }
      address: state.customerAddress?.getFullAddress,
      name: state.customerType == AioCustomerType.customer
          ? customerNameController.text
          : businessNameController.text,
      email: state.customerType == AioCustomerType.customer
          ? customerEmailController.text
          : businessEmailController.text,
      geographical: state.geographical?.name,
      employmentField: state.employmentField?.name,
      phone: state.customerType == AioCustomerType.customer
          ? customerPhoneNumberController.text
          : businessPhoneNumberController.text,
      diplomaticCustomer: state.objectCustomer == AioCustomerType.communication
          ? AioCustomerType.communication.display
          : AioCustomerType.normal.display,
      positionDiplomatic: state.objectCustomer == AioCustomerType.communication
          ? state.positionContact?.name
          : null,
      // Todo Check trường này sau
      // contractPartnerType:

      zaloNumber: state.customerType == AioCustomerType.customer
          ? socialController.text
          : null,
      birth: state.customerType == AioCustomerType.customer
          ? state.customerDob?.display()
          : state.birthRegencyOwner?.display(),
      gender: state.customerType == AioCustomerType.customer
          ? _checkGender(state.customerGender)
          : _checkGender(state.sexRegencyOwner),
      genderContact: state.customerType == AioCustomerType.bussiness
          ? _checkGender(state.sexRegencyContact)
          : null,
      customerContact: state.customerType == AioCustomerType.bussiness
          ? customerContactController.text
          : null,
      positionContact: state.customerType == AioCustomerType.bussiness
          ? state.positionContact?.code
          : null,
      position: state.customerType == AioCustomerType.bussiness &&
              state.objectCustomer == AioCustomerType.communication
          ? positionController.text
          : null,
      foundingDay: state.customerType == AioCustomerType.bussiness
          ? state.businessEstablish?.display()
          : null,
      contactBirth: state.customerType == AioCustomerType.bussiness
          ? state.birthRegencyContact?.display()
          : null,
      contactPhone: state.customerType == AioCustomerType.bussiness
          ? phoneRegencyContactController.text
          : null,
      emailContact: state.customerType == AioCustomerType.bussiness
          ? emailRegencyContactController.text
          : null,
      addressContact: state.customerType == AioCustomerType.bussiness
          ? state.addressRegencyContact?.addressDetail
          : null,
      addressRepresent: state.customerType == AioCustomerType.bussiness
          ? state.addressRegencyOwner?.addressDetail
          : null,
      phoneRepresent: state.customerType == AioCustomerType.bussiness
          ? phoneRegencyOwnerController.text
          : null,
      taxCode: state.customerType == AioCustomerType.bussiness
          ? businessTaxNumberController.text
          : null,
      // Gán nếu có thông tin từ CIM
      provinceName: state.customerInfo?.provinceName,
      provinceIdToCIM: state.customerInfo?.provinceId,
      provinceCode: state.customerInfo?.provinceCode,
      districtName: state.customerInfo?.districtName,
      wardsName: state.customerInfo?.wardName,
      addressToCIM: state.customerInfo?.address,
      customerId: state.customerInfo?.customerId,
      individualId:
          state.customerInfo?.individualId ?? state.customerInfo?.customerId,
      organizationId: state.customerInfo?.organizationId,
      fullName: state.customerInfo?.fullName,
      dataSource: "AIO",
      customerType: state.customerType == AioCustomerType.customer
          ? UserType.personal.keyToServer
          : UserType.company.keyToServer,
    );
    List<AioServiceEntity?>? hsServiceList = [];
    if ((state.packages ?? []).isNotEmpty) {
      for (int i = 0; i < (state.packages ?? []).length; i++) {
        if (state.packages?[i].serviceItem != null) {
          state.packages?[i].serviceItem?.quantity =
              (state.packages?[i].serviceItem?.amount ?? 0) *
                  (state.packages?[i].quantity ?? 1).toInt();
          hsServiceList.add(state.packages?[i].serviceItem);
        }
      }
    }

    return AioContractParam(
      sysUserRequest: sysUserRequest,
      aioContractDTO: aioContractDTO,
      catProvinceDTO: catProvinceDTO,
      aioAreaDTO: aioAreaDTO,
      lstAIOPackageDetail: state.packages,
      aioCustomerDTO: aioCustomer,
      hsServiceList: hsServiceList,
      appParam: AppParam(
        areaId: state.aioConfigService?.sysGroupDto?.provinceId,
        areaName: state.aioConfigService?.sysGroupDto?.areaCode,
      ),
      listEmployee:
          '${GlobalData.instance.userInfo?.employeeCode}~${GlobalData.instance.userInfo?.fullName}~100.0;',
    );
  }

  int _checkGender(CustomerGender? gender) {
    if (gender == CustomerGender.aioMale) {
      return 1;
    }
    if (gender == CustomerGender.aioFemale) {
      return 0;
    }
    return 2;
  }

  Widget _buildPage(AioContractCreateState state) {
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      return SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            InkWellWidget(
              onTap: () {
                ref
                    .read(aioContractCreateProvider.notifier)
                    .changeSaleInternal();
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    (state.saleInternal)
                        ? MyAssets.icons.iconChecked.svg()
                        : MyAssets.icons.iconCheckbox.svg(),
                    const SizedBox(
                      width: 16,
                    ),
                    Text(
                      'Chương trình bán hàng nội bộ',
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textBody,
                      ),
                    ),
                  ],
                ),
              ),
            ),
            if (state.saleInternal) ...[
              _buildInternalUser(state),
            ],
            if (!state.saleInternal) ...[
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              InkWellWidget(
                onTap: () {
                  ref
                      .read(aioContractCreateProvider.notifier)
                      .changeContactFrom();
                },
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Row(
                    children: [
                      (state.contactFrom != "")
                          ? MyAssets.icons.iconChecked.svg()
                          : MyAssets.icons.iconCheckbox.svg(),
                      const SizedBox(
                        width: 16,
                      ),
                      Text(
                        'Hợp đồng từ YCKS/YCTX',
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.textBody,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            if (!state.saleInternal && state.contactFrom != "") ...[
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Text(
                  'Nguồn hợp đồng',
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: RadioWidget<ContractResourceType>(
                        value: ContractResourceType.collectInfo,
                        groupValue: state.contractResourceType,
                        onChanged: (value) {
                          ref
                              .read(aioContractCreateProvider.notifier)
                              .changeContractResourceType(value);
                        },
                        displayWidget: (context, item) {
                          return _displayText(item);
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioWidget<ContractResourceType>(
                        value: ContractResourceType.association,
                        groupValue: state.contractResourceType,
                        onChanged: (value) {
                          ref
                              .read(aioContractCreateProvider.notifier)
                              .changeContractResourceType(value);
                        },
                        displayWidget: (context, item) {
                          return _displayText(item);
                        },
                      ),
                    ),
                  ],
                ),
              ),
              if (!state.saleInternal &&
                  state.contractResourceType ==
                      ContractResourceType.collectInfo) ...[
                _buildCollectInfo(state),
              ],
              if (!state.saleInternal &&
                  state.contractResourceType ==
                      ContractResourceType.association) ...[
                _buildAssociation(state),
                if (state.survey != null) ...[
                  _buildAssociationCustomer(state),
                ],
              ],
            ],
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Thông tin khách hàng',
                    style: UITextStyle.body1SemiBold.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      'Loại khách hàng',
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: RadioWidget<AioCustomerType>(
                            value: AioCustomerType.customer,
                            groupValue: state.customerType,
                            onChanged: (value) {
                              if (!state.saleInternal) {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .changeCustomerType(value);
                              }
                            },
                            displayWidget: (context, item) {
                              return _displayText(item);
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioWidget<AioCustomerType>(
                            value: AioCustomerType.bussiness,
                            groupValue: state.customerType,
                            onChanged: (value) {
                              if (!state.saleInternal) {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .changeCustomerType(value);
                              }
                            },
                            displayWidget: (context, item) {
                              return _displayText(item);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  if (AioCustomerType.bussiness == state.customerType) ...[
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listCapitalSource ?? [],
                      title: "Nguồn gốc",
                      isRequired: true,
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeCapitalSource(value);
                      },
                      selected: state.capitalSource,
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listFormSignContract ?? [],
                      title: "Hình thức ký hợp đồng",
                      isRequired: true,
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeFormSignContract(value);
                      },
                      selected: state.formSignContract,
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listDeploymentMethod ?? [],
                      title: "Hình thức triển khai",
                      isRequired: true,
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeDeploymentMethod(value);
                      },
                      selected: state.deploymentMethod,
                    ),
                    const SizedBox(height: 8),
                  ],
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Text(
                      'Đối tượng khách hàng',
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: RadioWidget<AioCustomerType>(
                            value: AioCustomerType.normal,
                            groupValue: state.objectCustomer,
                            onChanged: (value) {
                              ref
                                  .read(aioContractCreateProvider.notifier)
                                  .changeObjectCustomerType(value);
                            },
                            displayWidget: (context, item) {
                              return _displayText(item);
                            },
                          ),
                        ),
                        Expanded(
                          child: RadioWidget<AioCustomerType>(
                            value: AioCustomerType.communication,
                            groupValue: state.objectCustomer,
                            onChanged: (value) {
                              ref
                                  .read(aioContractCreateProvider.notifier)
                                  .changeObjectCustomerType(value);
                            },
                            displayWidget: (context, item) {
                              return _displayText(item);
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  if (state.objectCustomer ==
                      AioCustomerType.communication) ...[
                    TextFieldWidget(
                      controller: positionController,
                      labelText: "Chức vụ khách hàng",
                      enabled: true,
                      focusNode: positionFocusNode,
                      onFocusChange: (isFocus) {
                        if (!isFocus) {
                          positionFocusNode.unfocus();
                        }
                      },
                    ),
                  ],
                  const SizedBox(
                    height: 16,
                  ),
                  if (state.customerType == AioCustomerType.customer) ...[
                    TextFieldWidget(
                      controller: customerPhoneNumberController,
                      isRequired: true,
                      labelText: "Số điện thoại (người mua hàng)",
                      validator: (value) {
                        return ValidateUtils.onValidatePhone(value);
                      },
                      labelTextStyle: TextStyle(
                        color: BaseColors.textSubtitle,
                      ),
                      onChanged: (value) {
                        deBouncerCusB2C.value = value;
                      },
                      keyboardType: TextInputType.number,
                      focusNode: customerPhoneFocusNode,
                      clear: false,
                      maxSizeSuffix: true,
                      suffix: state.showCus360
                          ? Cus360Widget(
                              customerId: state.customerInfo?.customerId,
                              customerPhone: state.customerInfo?.phone,
                              customerType: UserType.personal.keyToServer,
                            )
                          : null,
                    ),
                    const SizedBox(height: 16),
                    TextFieldWidget(
                      controller: customerNameController,
                      labelText: "Họ và tên",
                      enabled: true,
                      isRequired: true,
                      readOnly: state.showCus360,
                      focusNode: customerNameFocusNode,
                      onFocusChange: (isFocus) {
                        if (!isFocus) {
                          customerNameFocusNode.unfocus();
                        }
                      },
                      validator: (value) {
                        return ValidateUtils.onValidateUserName(value);
                      },
                    ),
                    const SizedBox(height: 16),
                    DropdownWidget(
                      labelText: "Địa chỉ",
                      isRequired: true,
                      content: state.customerAddress?.getFullAddress,
                      suffix: MyAssets.icons.iconArrowRightS20.svg(),
                      validator: (value) {
                        return ValidateUtils.onValidateAddress(value);
                      },
                      onTap: () {
                        onSelectAddress(
                          address: state.customerAddress,
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFieldWidget(
                      controller: contactPhoneController,
                      isRequired: true,
                      labelText: "Số điện thoại liên hệ",
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        return ValidateUtils.onValidatePhone(value);
                      },
                      focusNode: contactPhoneFocusNode,
                      onFocusChange: (isFocus) {
                        if (!isFocus) {
                          contactPhoneFocusNode.unfocus();
                        }
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    TextFieldWidget(
                      controller: socialController,
                      labelText: "Whatsapp/Zalo",
                      focusNode: socialFocusNode,
                      onFocusChange: (isFocus) {
                        if (!isFocus) {
                          socialFocusNode.unfocus();
                        }
                      },
                      keyboardType: TextInputType.number,
                      validator: (value) {
                        return ValidateUtils.onValidatePhone(value);
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Text(
                      "Giới tính",
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        SizedBox(
                          width: (MediaQuery.of(context).size.width - 32) / 3,
                          child: RadioWidget<CustomerGender>(
                            value: CustomerGender.aioMale,
                            groupValue: state.customerGender,
                            onChanged: (CustomerGender value) {
                              ref
                                  .read(aioContractCreateProvider.notifier)
                                  .changeCustomerGender(value);
                            },
                            displayWidget: (context, item) {
                              return _displayText(item);
                            },
                          ),
                        ),
                        SizedBox(
                          width: (MediaQuery.of(context).size.width - 32) / 3,
                          child: RadioWidget<CustomerGender>(
                            value: CustomerGender.aioFemale,
                            groupValue: state.customerGender,
                            onChanged: (CustomerGender value) {
                              ref
                                  .read(aioContractCreateProvider.notifier)
                                  .changeCustomerGender(value);
                            },
                            displayWidget: (context, item) {
                              return _displayText(item);
                            },
                          ),
                        ),
                        SizedBox(
                          width: (MediaQuery.of(context).size.width - 32) / 3,
                          child: RadioWidget<CustomerGender>(
                            value: CustomerGender.aioOther,
                            groupValue: state.customerGender,
                            onChanged: (CustomerGender value) {
                              ref
                                  .read(aioContractCreateProvider.notifier)
                                  .changeCustomerGender(value);
                            },
                            displayWidget: (context, item) {
                              return _displayText(item);
                            },
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 16),
                    DropdownWidget(
                      labelText: "Ngày sinh",
                      content: state.customerDob?.display(),
                      suffix: MyAssets.icons.iconCalendarS24.svg(),
                      onTap: () async {
                        openDatetimePicker(date: state.customerDob);
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFieldWidget(
                      controller: customerEmailController,
                      labelText: "Email",
                      textInputAction: TextInputAction.done,
                      focusNode: customerEmailFocusNode,
                      onFocusChange: (isFocus) {
                        if (!isFocus) {
                          customerEmailFocusNode.unfocus();
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listCustomerLevel ?? [],
                      title: "Hạng khách hàng",
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeCustomerLevel(value);
                      },
                      selected: state.customerLevel,
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listGeographical ?? [],
                      title: "Khu vực địa lý",
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeGeographical(value);
                        _unFocusAll();
                      },
                      selected: state.geographical,
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listEmploymentField ?? [],
                      title: "Lĩnh vực làm việc",
                      isSearch: true,
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeEmploymentField(value);
                        _unFocusAll();
                      },
                      selected: state.employmentField,
                    ),
                  ],
                  if (state.customerType == AioCustomerType.bussiness) ...[
                    TextFieldWidget(
                      controller: businessTaxNumberController,
                      isRequired: true,
                      labelText: "Mã số thuế doanh nghiệp",
                      validator: (value) {
                        return ValidateUtils.onValidateTax(value);
                      },
                      onChanged: (value) {
                        deBouncerCusB2B.value = value;
                      },
                      clear: false,
                      maxSizeSuffix: true,
                      suffix: state.showCus360
                          ? Cus360Widget(
                              customerId: state.customerInfo?.customerId,
                              customerPhone: state.customerInfo?.phone,
                              customerType: UserType.company.keyToServer,
                              taxCode: businessTaxNumberController.text,
                            )
                          : null,
                      focusNode: businessTaxNumberFocusNode,
                    ),
                    const SizedBox(height: 16),
                    TextFieldWidget(
                      controller: businessNameController,
                      labelText: "Tên doanh nghiệp",
                      enabled: true,
                      isRequired: true,
                      readOnly: state.showCus360,
                      validator: (value) {
                        return ValidateUtils.onValidateCompanyName(value);
                      },
                      focusNode: businessNameFocusNode,
                      onFocusChange: (isFocus) {
                        if (!isFocus) {
                          businessNameFocusNode.unfocus();
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    DropdownWidget(
                      labelText: "Địa chỉ doanh nghiệp",
                      isRequired: true,
                      content: state.customerAddress?.getFullAddress,
                      suffix: MyAssets.icons.iconArrowRightS20.svg(),
                      validator: (value) {
                        return ValidateUtils.onValidateAddress(value);
                      },
                      onTap: () {
                        onSelectAddress(
                          address: state.customerAddress,
                        );
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFieldWidget(
                      controller: businessPhoneNumberController,
                      isRequired: true,
                      labelText: "Số điện thoại",
                      validator: (value) {
                        return ValidateUtils.onValidatePhone(value);
                      },
                      readOnly: state.showCus360,
                      keyboardType: TextInputType.number,
                      focusNode: businessPhoneNumberFocusNode,
                      onFocusChange: (isFocus) {
                        if (!isFocus) {
                          businessPhoneNumberFocusNode.unfocus();
                        }
                      },
                    ),
                    const SizedBox(height: 16),
                    TextFieldWidget(
                      controller: businessEmailController,
                      labelText: "Email",
                      textInputAction: TextInputAction.done,
                      validator: (value) {
                        return ValidateUtils.onValidateEmailNullAble(value);
                      },
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listCustomerLevel ?? [],
                      title: "Hạng khách hàng",
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeCustomerLevel(value);
                      },
                      selected: state.customerLevel,
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listGeographical ?? [],
                      title: "Khu vực địa lý",
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeGeographical(value);
                      },
                      selected: state.geographical,
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listEmploymentField ?? [],
                      title: "Lĩnh vực làm việc",
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeEmploymentField(value);
                      },
                      selected: state.employmentField,
                    ),
                    const SizedBox(height: 16),
                    DropDownListWidget(
                      data: state.listStaffNumber ?? [],
                      title: "Số lượng lao động",
                      onChangeItem: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeStaffNumber(value);
                      },
                      selected: state.staffNumber,
                    ),
                    const SizedBox(height: 16),
                    DropdownWidget(
                      labelText: "Ngày thành lập",
                      content: state.businessEstablish?.display(),
                      suffix: MyAssets.icons.iconCalendarS24.svg(),
                      onTap: () async {
                        openDatetimePicker(
                          type: AioDateType.businessEstablish,
                          date: state.businessEstablish,
                        );
                      },
                    ),
                  ],
                ],
              ),
            ),
            if (AioCustomerType.bussiness == state.customerType) ...[
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          ref
                              .read(aioContractCreateProvider.notifier)
                              .changeIsShowOwner();
                        },
                        child: Text(
                          'Thông tin chủ doanh nghiệp',
                          style: UITextStyle.body1SemiBold.copyWith(
                            color: BaseColors.textLabel,
                          ),
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeIsShowOwner();
                      },
                      child: state.isShowOwner
                          ? MyAssets.icons.arrowUp.svg()
                          : MyAssets.icons.arrowDown.svg(),
                    )
                  ],
                ),
              ),
              if (state.isShowOwner) ...[
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: TextFieldWidget(
                    controller: representativeController,
                    labelText: "Người đại diện",
                    enabled: true,
                    onFocusChange: (isFocus) {},
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: DropDownListWidget(
                    data: state.listPositionRepresent ?? [],
                    title: "Chức vụ",
                    onChangeItem: (value) {
                      ref
                          .read(aioContractCreateProvider.notifier)
                          .changeRegencyOwner(value);
                    },
                    selected: state.regencyOwner,
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Giới tính",
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: (MediaQuery.of(context).size.width - 32) / 3,
                            child: RadioWidget<CustomerGender>(
                              value: CustomerGender.aioMale,
                              groupValue: state.sexRegencyOwner,
                              onChanged: (CustomerGender value) {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .changeRegenGender(value);
                              },
                              displayWidget: (context, item) {
                                return _displayText(item);
                              },
                            ),
                          ),
                          SizedBox(
                            width: (MediaQuery.of(context).size.width - 32) / 3,
                            child: RadioWidget<CustomerGender>(
                              value: CustomerGender.aioFemale,
                              groupValue: state.sexRegencyOwner,
                              onChanged: (CustomerGender value) {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .changeRegenGender(value);
                              },
                              displayWidget: (context, item) {
                                return _displayText(item);
                              },
                            ),
                          ),
                          SizedBox(
                            width: (MediaQuery.of(context).size.width - 32) / 3,
                            child: RadioWidget<CustomerGender>(
                              value: CustomerGender.aioOther,
                              groupValue: state.sexRegencyOwner,
                              onChanged: (CustomerGender value) {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .changeRegenGender(value);
                              },
                              displayWidget: (context, item) {
                                return _displayText(item);
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: DropdownWidget(
                    labelText: "Ngày sinh",
                    content: state.birthRegencyOwner?.display(),
                    suffix: MyAssets.icons.iconCalendarS24.svg(),
                    onTap: () async {
                      openDatetimePicker(
                        type: AioDateType.birthRegencyOwner,
                        date: state.birthRegencyOwner,
                      );
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: TextFieldWidget(
                    controller: phoneRegencyOwnerController,
                    labelText: "Số điện thoại",
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidatePhoneNullAble(value);
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: TextFieldWidget(
                    controller: emailRegencyOwnerController,
                    labelText: "Email",
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidateEmailNullAble(value);
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: DropdownWidget(
                    labelText: "Địa chỉ",
                    content: state.addressRegencyOwner?.getFullAddress,
                    suffix: MyAssets.icons.iconArrowRightS20.svg(),
                    validator: (value) {
                      return ValidateUtils.onValidateAddress(value);
                    },
                    onTap: () {
                      onSelectAddressOwner(
                        address: state.addressRegencyOwner,
                      );
                    },
                  ),
                ),
              ],
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Row(
                  children: [
                    Expanded(
                      child: InkWell(
                        onTap: () {
                          ref
                              .read(aioContractCreateProvider.notifier)
                              .changeIsShowContact();
                        },
                        child: Row(
                          children: [
                            Text(
                              'Đầu mối liên hệ',
                              style: UITextStyle.body1SemiBold.copyWith(
                                color: BaseColors.textLabel,
                              ),
                            ),
                            Text(
                              ' *',
                              style: UITextStyle.body1SemiBold.copyWith(
                                color: BaseColors.primary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeIsShowContact();
                      },
                      child: state.isShowContact
                          ? MyAssets.icons.arrowUp.svg()
                          : MyAssets.icons.arrowDown.svg(),
                    )
                  ],
                ),
              ),
              if (state.isShowContact) ...[
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: TextFieldWidget(
                    controller: representativeController,
                    labelText: "Người liên hệ trực tiếp",
                    enabled: true,
                    isRequired: true,
                    onFocusChange: (isFocus) {},
                    validator: (value) {
                      return ValidateUtils.onValidateNotNull(
                        title: "Người liên hệ trực tiếp",
                        value: value,
                      );
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: DropDownListWidget(
                    data: state.listPositionContact ?? [],
                    title: "Chức vụ",
                    onChangeItem: (value) {
                      ref
                          .read(aioContractCreateProvider.notifier)
                          .changeRegencyContact(value);
                    },
                    selected: state.regencyContact,
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Giới tính",
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          SizedBox(
                            width: (MediaQuery.of(context).size.width - 32) / 3,
                            child: RadioWidget<CustomerGender>(
                              value: CustomerGender.aioMale,
                              groupValue: state.sexRegencyContact,
                              onChanged: (CustomerGender value) {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .changeRegenGenderContact(value);
                              },
                              displayWidget: (context, item) {
                                return _displayText(item);
                              },
                            ),
                          ),
                          SizedBox(
                            width: (MediaQuery.of(context).size.width - 32) / 3,
                            child: RadioWidget<CustomerGender>(
                              value: CustomerGender.aioFemale,
                              groupValue: state.sexRegencyContact,
                              onChanged: (CustomerGender value) {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .changeRegenGenderContact(value);
                              },
                              displayWidget: (context, item) {
                                return _displayText(item);
                              },
                            ),
                          ),
                          SizedBox(
                            width: (MediaQuery.of(context).size.width - 32) / 3,
                            child: RadioWidget<CustomerGender>(
                              value: CustomerGender.aioOther,
                              groupValue: state.sexRegencyContact,
                              onChanged: (CustomerGender value) {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .changeRegenGenderContact(value);
                              },
                              displayWidget: (context, item) {
                                return _displayText(item);
                              },
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: DropdownWidget(
                    labelText: "Ngày sinh",
                    content: state.birthRegencyContact?.display(),
                    suffix: MyAssets.icons.iconCalendarS24.svg(),
                    onTap: () async {
                      openDatetimePicker(
                        type: AioDateType.birthRegencyContact,
                        date: state.birthRegencyContact,
                      );
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: TextFieldWidget(
                    controller: phoneRegencyContactController,
                    labelText: "Số điện thoại",
                    isRequired: true,
                    keyboardType: TextInputType.number,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidatePhone(value);
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: TextFieldWidget(
                    controller: emailRegencyContactController,
                    labelText: "Email",
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidateEmailNullAble(value);
                    },
                  ),
                ),
                Padding(
                  padding:
                      const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                  child: DropdownWidget(
                    labelText: "Địa chỉ",
                    content: state.addressRegencyContact?.getFullAddress,
                    suffix: MyAssets.icons.iconArrowRightS20.svg(),
                    validator: (value) {
                      return ValidateUtils.onValidateAddress(value);
                    },
                    onTap: () {
                      onSelectAddressContact(
                        address: state.addressRegencyContact,
                      );
                    },
                  ),
                ),
              ],
            ],
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Thông tin hợp đồng',
                style: UITextStyle.body1SemiBold.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Text(
                'Loại hợp đồng',
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Expanded(
                    child: RadioWidget<AioContractInfoType>(
                      value: AioContractInfoType.commerce,
                      groupValue: state.contractInfoType,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeAioContractInfoType(value);
                      },
                      displayWidget: (context, item) {
                        return _displayText(item);
                      },
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  Expanded(
                    child: RadioWidget<AioContractInfoType>(
                      value: AioContractInfoType.package,
                      groupValue: state.contractInfoType,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeAioContractInfoType(value);
                      },
                      displayWidget: (context, item) {
                        return _displayText(item);
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Text(
                'Phương thức hợp đồng',
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: RadioWidget<AioContractMethodType>(
                      value: AioContractMethodType.internal,
                      groupValue: state.contractMethodType,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeAioContractMethodType(value);
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioWidget<AioContractMethodType>(
                      value: AioContractMethodType.external,
                      groupValue: state.contractMethodType,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeAioContractMethodType(value);
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: DropDownListWidget(
                data: state.listIndustry ?? [],
                title: "Ngành hàng",
                displayCode: true,
                onChangeItem: (value) {
                  ref
                      .read(aioContractCreateProvider.notifier)
                      .changeIndustry(value);
                },
                selected: state.industry,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: DropDownListWidget(
                data: state.listService
                        ?.where((itx) =>
                            itx.industryCode == state.industry?.code &&
                            itx.code != state.industry?.code)
                        .toList() ??
                    [],
                title: "Dịch vụ, sản phẩm",
                onChangeItem: (value) {
                  ref
                      .read(aioContractCreateProvider.notifier)
                      .changeService(value);
                },
                selected: state.service,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: DropDownListWidget(
                data: state.listGoodsType ?? [],
                title: "Loại tài sản",
                isSearch: true,
                onChangeItem: (value) {
                  ref
                      .read(aioContractCreateProvider.notifier)
                      .changeGoodsType(value);
                },
                selected: state.goodsType,
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: DropdownWidget(
                labelText: "Thời gian bắt đầu",
                isRequired: true,
                content: state.dateStartContract?.display(),
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                onTap: () async {
                  openDatetimePicker(
                      type: AioDateType.dateStartContract,
                      date: state.dateStartContract);
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: DropdownWidget(
                labelText: "Lịch hẹn",
                content: state.scheduleTime
                    ?.display(format: DateTimeFormater.dateTimeFormatView),
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                onTap: () async {
                  openDatetimePicker(
                    type: AioDateType.scheduleTime,
                    date: state.scheduleTime,
                  );
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: TextFieldWidget(
                controller: contentController,
                labelText: "Nội dung",
                textInputAction: TextInputAction.done,
                maxLines: 3,
                height: 110,
                alignment: Alignment.topLeft,
                isRequired: true,
                onChanged: (value) {},
                focusNode: contentFocusNode,
                onFocusChange: (isFocus) {
                  if (!isFocus) {
                    contentFocusNode.unfocus();
                  }
                },
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
              child: TextFieldWidget.area(
                controller: noteController,
                labelText: "Ghi chú",
                hintText: "Ghi chú",
                hintTextStyle: TextStyle(
                  color: BaseColors.textSubtitle,
                ),
                textInputAction: TextInputAction.done,
                maxLines: 3,
                height: 110,
                alignment: Alignment.topLeft,
                onChanged: (value) {},
                focusNode: noteFocusNode,
                inputFormatters: [
                  FilteringTextInputFormatter.allow(
                    RegExp(
                      Patterns.note,
                      multiLine: true,
                    ),
                  ),
                ],
                onFocusChange: (isFocus) {
                  if (!isFocus) {
                    noteFocusNode.unfocus();
                  }
                },
              ),
            ),

            // if (state.contractInfoType == AioContractInfoType.package) ...[
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
              child: Text(
                'Phương thức triển khai hợp đồng',
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  if (GlobalData.instance.userInfo?.position != "HKDCN" &&
                      GlobalData.instance.userInfo?.position != "HKDDN" &&
                      GlobalData.instance.userInfo?.position != "OFT") ...[
                    RadioWidget<AioTransportType>(
                      value: AioTransportType.self,
                      groupValue: state.deploymentMethodContract,
                      onChanged: (value) {
                        changeTransportType(
                          value,
                          context: context,
                        );
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                  ],
                  RadioWidget<AioTransportType>(
                    value: AioTransportType.vcc,
                    groupValue: state.deploymentMethodContract,
                    onChanged: (value) {
                      changeTransportType(
                        value,
                        context: context,
                      );
                    },
                    displayWidget: (context, item) {
                      return _displayName(item);
                    },
                  ),
                  if (state.contractInfoType !=
                      AioContractInfoType.package) ...[
                    const SizedBox(
                      height: 16,
                    ),
                    RadioWidget<AioTransportType>(
                      value: AioTransportType.vtp,
                      groupValue: state.deploymentMethodContract,
                      onChanged: (value) {
                        changeTransportType(
                          value,
                          context: context,
                        );
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                  ],
                ],
              ),
            ),
            // ],
            const SizedBox(
              height: 8,
            ),
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            Padding(
              padding: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      "Danh sách gói bán hàng",
                      style: UITextStyle.body1SemiBold.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ),
                  AppTextButton(
                    iconLeft: MyAssets.icons.iconAddCircle.svg(),
                    title: "Thêm gói",
                    onTap: () {
                      if (state.customerAddress == null) {
                        AppDialog.showDialogCenter(
                          context,
                          message: "Vui lòng chọn địa chỉ khách hàng",
                          status: DialogStatus.error,
                        );
                      }
                      if (state.deploymentMethodContract ==
                          AioTransportType.nan) {
                        AppDialog.showDialogCenter(
                          context,
                          message:
                              "Vui lòng chọn phương thức triển khai hợp đồng",
                          status: DialogStatus.error,
                        );
                      } else {
                        AioAreaEntity param = AioAreaEntity(
                            areaId: state.customerAddress?.ward?.id,
                            code: state.customerAddress?.ward?.id == null
                                ? state.customerAddress?.ward?.code
                                : null,
                            provinceId: state.customerAddress?.province?.id,
                            configServiceCode: state.service?.code,
                            industryCode: state.industry?.code,
                            field: state.service?.code == "PINNLMT"
                                ? null
                                : state.employmentField?.code ?? "TT.GPTH",
                            text: "VCC",
                            type: 2,
                            isInternal: state.saleInternal == true ? 1 : 0,
                            contractType:
                                int.parse(state.contractInfoType.keyToServer),
                            sysUserId: GlobalData.instance.userInfo?.sysUserId,
                            deploymentMethodContract:
                                state.deploymentMethodContract.code);

                        context.push(
                          RouterPaths.aioAddPackage,
                          extra: AddPackageArguments(
                            param: param,
                            onSelected: (data) {
                              updatePackages(
                                data,
                                context: context,
                              );
                            },
                            packagesSelected: state.packages,
                          ),
                        );
                      }
                    },
                  ),
                ],
              ),
            ),
            if ((state.packages ?? []).isNotEmpty) ...[
              const SizedBox(
                height: 8,
              ),
              DividerWidget(
                height: 4,
                color: BaseColors.backgroundGray,
              ),
            ],
            if (state.xcare == true &&
                (state.packages ?? []).isNotEmpty &&
                state.contractInfoType == AioContractInfoType.package &&
                !(state.industry?.code == "NLMT" &&
                    state.service?.status == "2")) ...[
              const SizedBox(
                height: 8,
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                margin: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 12,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: BaseColors.infoPressed),
                  color: BaseColors.infoSurface,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(12),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 4,
                      ),
                      child: MyAssets.icons.infoCircle.svg(
                        width: 14,
                        height: 14,
                        colorFilter: ColorFilter.mode(
                          BaseColors.info,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 12,
                    ),
                    Expanded(
                      child: Text(
                        "Hợp đồng này thuộc loại hợp đồng liên thông AIO HS, nhân viên không chọn danh sách gói dịch vụ trên hệ thống AIO mà chọn trong danh sách dịch vụ của hệ thống HS",
                        style: UITextStyle.bodyText2.copyWith(
                          color: BaseColors.textTitle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
            if (state.loadStatusPackage == LoadStatus.loading) ...[
              const SizedBox(
                height: 250,
                child: Center(
                  child: LoadingIndicatorWidget(),
                ),
              ),
            ],
            if (state.loadStatusPackage != LoadStatus.loading) ...[
              ListView.separated(
                shrinkWrap: true,
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                ),
                physics: const NeverScrollableScrollPhysics(),
                itemCount: state.packages?.length ?? 0,
                separatorBuilder: (context, index) {
                  return DividerWidget(
                    height: 4,
                    color: BaseColors.backgroundGray,
                  );
                },
                itemBuilder: (context, index) {
                  var item = state.packages![index];
                  return _buildPackage(
                    package: item,
                    index: index,
                  );
                },
              ),
            ],
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Thông tin thanh toán',
                style: UITextStyle.body1SemiBold.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ),
            if (state.transportType == AioTransportType.vtp) ...[
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                margin: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 12,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: BaseColors.infoPressed),
                  color: BaseColors.infoSurface,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(12),
                  ),
                ),
                child: Row(
                  children: <Widget>[
                    MyAssets.icons.infoCircle.svg(
                      width: 14,
                      height: 14,
                      colorFilter: ColorFilter.mode(
                        BaseColors.info,
                        BlendMode.srcIn,
                      ),
                    ),
                    const SizedBox(
                      width: 12,
                    ),
                    Text(
                      "Áp dụng thí điểm cho ${state.provinceNameVTPost}",
                      style: UITextStyle.bodyText2.copyWith(
                        color: BaseColors.textTitle,
                      ),
                    ),
                  ],
                ),
              ),
            ],
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
              child: Text(
                "Xuất hóa đơn",
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: RadioWidget<AioConfirmType>(
                      value: AioConfirmType.yes,
                      groupValue: state.isBill,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeIsBill(value);
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                  ),
                  Expanded(
                    child: RadioWidget<AioConfirmType>(
                      value: AioConfirmType.no,
                      groupValue: state.isBill,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeIsBill(value);
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                  ),
                ],
              ),
            ),
            if (state.isBill == AioConfirmType.yes &&
                state.transportType == AioTransportType.vtp) ...[
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                child: Align(
                  alignment: Alignment.centerRight,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      InkWell(
                        onTap: () {
                          getInvoice(
                            state,
                            context: context,
                          );
                        },
                        child: RichText(
                          text: TextSpan(
                            text: 'Thông tin xuất hóa đơn',
                            style: UITextStyle.caption1SemiBold.copyWith(
                              color: BaseColors.primary,
                            ),
                          ),
                        ),
                      ),
                      MyAssets.icons.arrowRight.svg(
                        colorFilter: ColorFilter.mode(
                          BaseColors.primary,
                          BlendMode.srcIn,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
            if (state.deploymentMethodContract == AioTransportType.vtp &&
                (state.totalMoney ?? 0) > 0 &&
                collectedMoneyController.text.isNotEmpty &&
                int.parse(collectedMoneyController.text.replaceAll(".", "")) ==
                    0) ...[
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: Text(
                  'Phương thức thanh toán',
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: RadioWidget<AioPaymentMethodType>(
                  value: AioPaymentMethodType.cod,
                  groupValue: AioPaymentMethodType.cod,
                  onChanged: (value) {},
                  displayWidget: (context, item) {
                    return _displayName(item);
                  },
                ),
              ),
            ],
            if (state.deploymentMethodContract != AioTransportType.vtp ||
                (state.deploymentMethodContract == AioTransportType.vtp &&
                    (state.totalMoney ?? 0) > 0 &&
                    (collectedMoneyController.text.isNotEmpty &&
                        int.parse(collectedMoneyController.text
                                .replaceAll(".", "")) >
                            0))) ...[
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: Text(
                  'Phương thức thanh toán',
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              Padding(
                padding:
                    const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    RadioWidget<AioPaymentMethodType>(
                      value: AioPaymentMethodType.viettelpay,
                      groupValue: state.isViettelPay,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeIsViettelPay(value);
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    RadioWidget<AioPaymentMethodType>(
                      value: AioPaymentMethodType.onePayCollect,
                      groupValue: state.isViettelPay,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeIsViettelPay(value);
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    RadioWidget<AioPaymentMethodType>(
                      value: AioPaymentMethodType.ctcn,
                      groupValue: state.isViettelPay,
                      onChanged: (value) {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .changeIsViettelPay(value);
                      },
                      displayWidget: (context, item) {
                        return _displayName(item);
                      },
                    ),
                  ],
                ),
              ),
            ],
            if (state.xcare == true &&
                state.deploymentMethodContract != AioTransportType.self &&
                state.isViettelPay != AioPaymentMethodType.ctcn &&
                (state.totalMoney ?? 0) > 0) ...[
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                child: Row(
                  children: [
                    Expanded(
                      child: TextFieldWidget(
                        controller: collectedMoneyController,
                        labelText: "Số tiền đã thu",
                        enabled: true,
                        onFocusChange: (isFocus) {
                          if (!isFocus) {}
                        },
                        onChanged: (value) {
                          value = value.replaceAll(".", "");
                          if (value.isNotEmpty &&
                              int.parse(value) > (state.totalMoney ?? 0)) {
                            AppDialog.showDialogCenter(
                              context,
                              message:
                                  "Số tiền đã thu không được vượt quá số tiền hợp đồng!",
                              status: DialogStatus.error,
                            );
                            receivablesMoneyController.text = "";
                            collectedMoneyController.text = "";
                          } else {
                            receivablesMoneyController.text =
                                StringUtils.formatNumber(
                                    (state.totalMoney ?? 0) - int.parse(value));
                          }
                        },
                        keyboardType: TextInputType.number,
                        textInputAction: TextInputAction.done,
                        inputFormatters: [
                          ThousandsFormatter(allowFraction: true),
                        ],
                      ),
                    ),
                    const SizedBox(
                      width: 16,
                    ),
                    Expanded(
                      child: TextFieldWidget(
                        controller: receivablesMoneyController,
                        textInputAction: TextInputAction.done,
                        labelText: "Số tiền còn lại",
                        enabled: true,
                        readOnly: true,
                        onFocusChange: (isFocus) {
                          if (!isFocus) {}
                        },
                        inputFormatters: [
                          ThousandsFormatter(allowFraction: true),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
              child: Text(
                'Ảnh thông tin hợp đồng',
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 8,
                horizontal: 16,
              ),
              child: SizedBox(
                height: 80,
                child: ListView.separated(
                  itemCount: (state.listImage?.length ?? 0) + 1,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(right: 16),
                  separatorBuilder: (_, __) => const SizedBox(width: 8),
                  itemBuilder: (context, index) {
                    if (index == (state.listImage?.length ?? 0)) {
                      return InkWellWidget(
                        onTap: () {
                          onTakePicture(index);
                        },
                        child: MyAssets.icons.iconAddImageDashline.svg(),
                      );
                    }
                    return Stack(
                      children: [
                        Container(
                          height: 80,
                          width: 80,
                          padding: const EdgeInsets.only(
                            top: 4,
                            right: 4,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: ImageBase64Widget(
                              base64Image:
                                  state.listImage![index].base64String ?? "",
                            ),
                          ),
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Visibility(
                            visible: true,
                            child: InkWellWidget(
                              onTap: () {
                                ref
                                    .read(aioContractCreateProvider.notifier)
                                    .deleteImage(index);
                              },
                              child: MyAssets.icons.iconCloseRed.svg(),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'Đội ngũ tham gia',
                style: UITextStyle.body1SemiBold.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ),
            Container(
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              decoration: BoxDecoration(
                color: BaseColors.backgroundWhite,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: BaseColors.borderDefault,
                ),
              ),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        Row(
                          children: [
                            Text(
                              GlobalData.instance.userInfo?.fullName ?? "",
                              style: UITextStyle.body1Medium.copyWith(
                                color: BaseColors.textTitle,
                              ),
                            ),
                            Text(
                              ' (Người tạo)',
                              style: UITextStyle.caption1Medium.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 2),
                        Text(
                          GlobalData.instance.userInfo?.username ?? "",
                          style: UITextStyle.caption1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 42,
                    child: NormalTextField(
                      controller: percentController,
                      showClearIcon: false,
                      height: 30,
                      textInputAction: TextInputAction.done,
                      keyboardType: TextInputType.number,
                      borderColor: Colors.transparent,
                      borderRadius: BorderRadius.zero,
                      textAlign: TextAlign.end,
                      readOnly: true,
                      suffixText: "%",
                      maxLength: 3,
                      contentPadding: const EdgeInsets.only(top: 2),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              height: 8,
            ),
            _buildReferUser(state),
            _buildPerformUser(state),
            if ((state.totalMoney ?? 0) > 20000000) ...[
              DividerWidget(
                height: 8,
                color: BaseColors.backgroundGray,
              ),
              Padding(
                padding: const EdgeInsets.all(16),
                child: Text(
                  'Bổ sung thông tin hợp đồng',
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                child: TextFieldWidget(
                  controller: contractCodeController,
                  labelText: "Mã hợp đồng ký cứng",
                  enabled: true,
                  textInputAction: TextInputAction.done,
                  focusNode: contractCodeFocusNode,
                  onFocusChange: (isFocus) {
                    if (!isFocus) {
                      contractCodeFocusNode.unfocus();
                    }
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                child: DropdownWidget(
                  labelText: "Ngày triển khai",
                  content: state.startDateDeployment?.display(),
                  suffix: MyAssets.icons.iconCalendarS24.svg(),
                  onTap: () async {
                    openDatetimePicker(
                      type: AioDateType.startDateDeployment,
                      date: state.startDateDeployment,
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                child: DropdownWidget(
                  labelText: "Ngày kết thúc",
                  content: state.endDateDeployment?.display(),
                  suffix: MyAssets.icons.iconCalendarS24.svg(),
                  onTap: () async {
                    openDatetimePicker(
                        type: AioDateType.endDateDeployment,
                        date: state.endDateDeployment);
                  },
                ),
              ),
              if (state.industry?.status == "2") ...[
                Padding(
                  padding: const EdgeInsets.symmetric(
                    vertical: 8,
                    horizontal: 16,
                  ),
                  child: DropdownWidget(
                    labelText: "Ngày COD",
                    content: state.dateCOD?.display(),
                    suffix: MyAssets.icons.iconCalendarS24.svg(),
                    onTap: () async {
                      openDatetimePicker(
                          type: AioDateType.dateCOD, date: state.dateCOD);
                    },
                  ),
                ),
              ],
            ],
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
          ],
        ),
      );
    }
  }

  Future<void> refreshData() async {}

  Widget _buildInternalUser(AioContractCreateState state) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: CardCustomWidget(
        padding: const EdgeInsets.all(16),
        titleWidget: state.internalStaff != null
            ? Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Text(
                          "Nhân viên nội bộ",
                          style: UITextStyle.caption1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          state.internalStaff?.fullName ?? '',
                          style: UITextStyle.body1Regular,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          state.internalStaff?.username ??
                              state.internalStaff?.employeeCode ??
                              '',
                          style: UITextStyle.caption1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: !state.disableInputForm,
                    child: InkWellWidget(
                      onTap: () {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .deleteInternalStaff();
                      },
                      child: MyAssets.icons.iconCloseCircle.svg(),
                    ),
                  ),
                ],
              )
            : Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      "Nhân viên nội bộ",
                      style: UITextStyle.body1Regular,
                    ),
                  ),
                  const SizedBox(width: 8),
                  MyAssets.icons.iconSearchS20.svg(),
                ],
              ),
        onPress: () {
          AppBottomSheet.showNormalBottomSheet(
            context,
            title: "Nhân viên nội bộ",
            height: MediaQuery.of(context).size.height * 0.95,
            child: ChooseStaffPage(
              aioUserSelectType: AioUserSelectType.internalStaff,
              onSelectStaff: (staff) async {
                await ref.read(aioContractCreateProvider.notifier).selectStaff(
                      staff: staff,
                      isUserPropose: true,
                    );
                setDataFromStaff(state);
                customerNameController.text = staff.fullName ?? "";
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildReferUser(AioContractCreateState state) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
      child: CardCustomWidget(
        padding: const EdgeInsets.all(16),
        titleWidget: state.referUser != null
            ? Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Text(
                          "Người giới thiệu",
                          style: UITextStyle.caption1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          state.referUser?.fullName ?? '',
                          style: UITextStyle.body1Regular,
                        ),
                        const SizedBox(height: 2),
                        Text(
                          state.referUser?.username ??
                              state.referUser?.employeeCode ??
                              '',
                          style: UITextStyle.caption1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: !state.disableInputForm,
                    child: InkWellWidget(
                      onTap: () {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .deleteReferUser();
                      },
                      child: MyAssets.icons.iconCloseCircle.svg(),
                    ),
                  ),
                ],
              )
            : Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      "Người giới thiệu",
                      style: UITextStyle.body1Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  MyAssets.icons.iconSearchS20.svg(),
                ],
              ),
        onPress: () {
          AppBottomSheet.showNormalBottomSheet(
            context,
            title: "Người giới thiệu",
            height: MediaQuery.of(context).size.height * 0.95,
            child: ChooseStaffPage(
              aioUserSelectType: AioUserSelectType.referUser,
              onSelectStaff: (data) async {
                await ref
                    .read(aioContractCreateProvider.notifier)
                    .selectReferUser(
                      data: data,
                    );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildPerformUser(AioContractCreateState state) {
    if (state.deploymentMethodContract == AioTransportType.vcc) {
      return Padding(
        padding: const EdgeInsets.fromLTRB(16, 8, 16, 8),
        child: CardCustomWidget(
          padding: const EdgeInsets.all(16),
          titleWidget: state.performUser != null
              ? Row(
                  children: [
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        mainAxisSize: MainAxisSize.min,
                        children: <Widget>[
                          Text(
                            "Người thực hiện",
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            state.performUser?.fullName ?? '',
                            style: UITextStyle.body1Regular,
                          ),
                          const SizedBox(height: 2),
                          Text(
                            state.performUser?.username ??
                                state.performUser?.employeeCode ??
                                state.performUser?.loginName ??
                                '',
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                          const SizedBox(height: 4),
                        ],
                      ),
                    ),
                    Visibility(
                      visible: state.deploymentMethodContract !=
                          AioTransportType.self,
                      child: InkWellWidget(
                        onTap: () {
                          if (state.deploymentMethodContract ==
                              AioTransportType.self) {
                            return;
                          }
                          ref
                              .read(aioContractCreateProvider.notifier)
                              .deletePerformUser();
                        },
                        child: MyAssets.icons.iconCloseCircle.svg(),
                      ),
                    ),
                  ],
                )
              : Row(
                  children: <Widget>[
                    Expanded(
                      child: Text(
                        "Người thực hiện",
                        style: UITextStyle.body1Regular.copyWith(
                          color: BaseColors.textSubtitle,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    MyAssets.icons.iconSearchS20.svg(),
                  ],
                ),
          onPress: () {
            if (AioTransportType.self == state.deploymentMethodContract
                // && state.contractInfoType == AioContractInfoType.package
                ) {
              ref.read(aioContractCreateProvider.notifier).selectPerformUser(
                    data: InternalStaffEntity(
                      sysUserId: GlobalData.instance.userInfo?.sysUserId,
                      username: GlobalData.instance.userInfo?.employeeCode,
                      employeeCode: GlobalData.instance.userInfo?.employeeCode,
                      fullName: GlobalData.instance.userInfo?.fullName,
                      areaCode: state.customerAddress?.ward?.code,
                    ),
                  );
              return;
            }
            AppBottomSheet.showNormalBottomSheet(
              context,
              title: "Người thực hiện",
              height: MediaQuery.of(context).size.height * 0.95,
              child: ChooseStaffPage(
                aioUserSelectType: AioUserSelectType.performUser,
                provinceCode: state.customerType == AioCustomerType.customer
                    ? state.customerAddress?.ward?.code
                    : state.businessAddress?.ward?.code,
                onSelectStaff: (data) {
                  ref
                      .read(aioContractCreateProvider.notifier)
                      .selectPerformUser(
                        data: data,
                      );
                },
              ),
            );
          },
        ),
      );
    }
    return const SizedBox();
  }

  Widget _buildAssociation(AioContractCreateState state) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: CardCustomWidget(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        titleWidget: state.survey != null
            ? Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Row(
                          children: [
                            Text(
                              "Đợt tiếp xúc",
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                            Text(
                              " *",
                              style: UITextStyle.body1Regular.copyWith(
                                color: BaseColors.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          state.survey?.surveyContent ?? '',
                          style: UITextStyle.body1Regular,
                        ),
                        const SizedBox(height: 4),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: !state.disableInputForm,
                    child: InkWellWidget(
                      onTap: () {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .deleteSurvey();
                      },
                      child: MyAssets.icons.iconCloseCircle.svg(),
                    ),
                  ),
                ],
              )
            : Row(
                children: <Widget>[
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          "Đợt tiếp xúc",
                          style: UITextStyle.body1Regular,
                        ),
                        Text(
                          " *",
                          style: UITextStyle.body1Regular.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  MyAssets.icons.iconSearchS20.svg(),
                ],
              ),
        onPress: () {
          AppBottomSheet.showNormalBottomSheet(
            context,
            title: "Đợt tiếp xúc",
            height: MediaQuery.of(context).size.height * 0.95,
            child: ChooseAssociationPage(
              onSelect: (data) {
                ref.read(aioContractCreateProvider.notifier).selectSurvey(
                      data: data,
                    );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildAssociationCustomer(AioContractCreateState state) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: CardCustomWidget(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 16,
        ),
        titleWidget: state.surveyCustomer != null
            ? Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: <Widget>[
                        Row(
                          children: [
                            Text(
                              "Thông tin khách hàng tiếp xúc",
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                            Text(
                              " *",
                              style: UITextStyle.body1Regular.copyWith(
                                color: BaseColors.primary,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 4),
                        Text(
                          state.surveyCustomer?.customerName ?? '',
                          style: UITextStyle.body1Regular,
                        ),
                        const SizedBox(height: 4),
                        Text(
                          StringUtils.maskPhoneNumber(
                              state.surveyCustomer?.customerPhone ?? ''),
                          style: UITextStyle.body2Regular,
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: !state.disableInputForm,
                    child: InkWellWidget(
                      onTap: () {
                        ref
                            .read(aioContractCreateProvider.notifier)
                            .deleteSurveyCustomer();
                      },
                      child: MyAssets.icons.iconCloseCircle.svg(),
                    ),
                  ),
                ],
              )
            : Row(
                children: <Widget>[
                  Expanded(
                    child: Row(
                      children: [
                        Text(
                          "Thông tin khách hàng tiếp xúc",
                          style: UITextStyle.body1Regular,
                        ),
                        Text(
                          " *",
                          style: UITextStyle.body1Regular.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 8),
                  MyAssets.icons.iconSearchS20.svg(),
                ],
              ),
        onPress: () {
          AppBottomSheet.showNormalBottomSheet(
            context,
            title: "Thông tin khách hàng tiếp xúc",
            height: MediaQuery.of(context).size.height * 0.95,
            child: ChooseSurveyCustomerPage(
              surveyId: state.survey?.surveyId ?? 2001,
              onSelect: (data) {
                ref
                    .read(aioContractCreateProvider.notifier)
                    .selectSurveyCustomer(
                      data: data,
                    );
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildCollectInfo(AioContractCreateState state) {
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 8, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          CardCustomWidget(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            titleWidget: state.collectionInfo != null
                ? Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Text(
                              "Thông tin khảo sát",
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              state.collectionInfo?.customerName ?? '',
                              style: UITextStyle.body1Regular,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              StringUtils.maskPhoneNumber(
                                  state.collectionInfo?.customerPhone ?? ''),
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Visibility(
                        visible: !state.disableInputForm,
                        child: InkWellWidget(
                          onTap: () {
                            ref
                                .read(aioContractCreateProvider.notifier)
                                .deleteCollectionInfo();
                          },
                          child: MyAssets.icons.iconCloseCircle.svg(),
                        ),
                      ),
                    ],
                  )
                : Row(
                    children: <Widget>[
                      Expanded(
                        child: Text(
                          "Thông tin khảo sát",
                          style: UITextStyle.body1Regular,
                        ),
                      ),
                      const SizedBox(width: 8),
                      MyAssets.icons.iconSearchS20.svg(),
                    ],
                  ),
            onPress: () {
              AppBottomSheet.showNormalBottomSheet(
                context,
                title: "Thông tin khảo sát",
                height: MediaQuery.of(context).size.height * 0.95,
                child: ChooseCollectionInfoPage(
                  onSelect: (data) {
                    ref
                        .read(aioContractCreateProvider.notifier)
                        .selectCollectionInfo(
                          data: data,
                        );
                  },
                ),
              );
            },
          ),
          if (state.collectionInfo != null) ...[
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 4,
              ),
              child: Text("Mã YCKS: ${state.collectionInfo?.orderCode}"),
            ),
          ],
        ],
      ),
    );
  }

  Widget _displayText(type) {
    return Text(
      type.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _displayName(type) {
    return Text(
      type.name,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  void onSelectAddress({
    bool? isSetup,
    AddressEntity? address,
  }) async {
    var state = ref.watch(aioContractCreateProvider);
    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) {
          ref.read(aioContractCreateProvider.notifier).selectAddress(address);

          AioAreaEntity aioAreaDTO = AioAreaEntity(
            areaId: address.ward?.id,
            code: address.ward?.id == null ? address.ward?.code : null,
            provinceId: address.province?.id,
            configServiceCode: state.industry?.code,
            field: state.employmentField?.code,
            industryCode: state.industry?.code,
            text: "VCC",
            type: 2,
            contractType: int.parse(state.contractInfoType.keyToServer),
            sysUserId: GlobalData.instance.userInfo?.sysUserId,
          );

          ref.read(aioContractCreateProvider.notifier).getPackage(
                  param: AioPackageParamEntity(
                aioAreaDTO: aioAreaDTO,
              ));
        },
      ),
    );
  }

  void onSelectAddressOwner({
    AddressEntity? address,
  }) async {
    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) {
          ref
              .read(aioContractCreateProvider.notifier)
              .selectAddressOwner(address);
        },
      ),
    );
  }

  void onSelectAddressContact({
    AddressEntity? address,
  }) async {
    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) {
          ref
              .read(aioContractCreateProvider.notifier)
              .selectAddressContact(address);
        },
      ),
    );
  }

  void openDatetimePicker({AioDateType? type, DateTime? date}) {
    DateTime dateInit = DateTime.now();
    if (date != null) {
      dateInit = date;
    }
    DateTime minDateTime = dateInit.subtract(const Duration(days: 100 * 365));
    if (type == AioDateType.dateStartContract ||
        type == AioDateType.scheduleTime) {
      minDateTime = dateInit.subtract(const Duration(hours: 1));
    }
    if (type == AioDateType.scheduleTime) {
      CustomBottomPicker.dateTime(
        height: MediaQuery.of(context).size.height * 0.40,
        title: "Chọn thời gian",
        buttonText: "Xác nhận",
        initialDateTime: dateInit,
        minDateTime: minDateTime,
        titleStyle: UITextStyle.body1SemiBold,
        pickerTextStyle: TextStyle(
          fontSize: 18,
          color: BaseColors.backgroundBlack,
        ),
        dateOrder: DatePickerDateOrder.dmy,
        onSubmit: (date) {
          String err = ref
              .read(aioContractCreateProvider.notifier)
              .setUpTime(date, type: type);
          if (err != "") {
            AppDialog.showDialogCenter(
              context,
              message: err,
              status: DialogStatus.error,
            );
          }
        },
      ).show(context);
    } else {
      CustomBottomPicker.date(
        height: MediaQuery.of(context).size.height * 0.40,
        title: "Chọn thời gian",
        buttonText: "Xác nhận",
        initialDateTime: dateInit,
        minDateTime: minDateTime,
        titleStyle: UITextStyle.body1SemiBold,
        pickerTextStyle: TextStyle(
          fontSize: 18,
          color: BaseColors.backgroundBlack,
        ),
        dateOrder: DatePickerDateOrder.dmy,
        onSubmit: (date) {
          String err = ref
              .read(aioContractCreateProvider.notifier)
              .setUpTime(date, type: type);
          if (err != "") {
            AppDialog.showDialogCenter(
              context,
              message: err,
              status: DialogStatus.error,
            );
          }
        },
      ).show(context);
    }
  }

  void onTakePicture(int index) async {
    await MediaUtils.onTakeImage(
      context: context,
      onSubmitImage: (file) async {
        String base64Image = await fileToBase64(file);
        String fileName = '$index.jpg';
        ref.read(aioContractCreateProvider.notifier).uploadImage(AioImage(
              name: fileName,
              base64String: base64Image,
            ));
      },
    );
  }

  Future<String> fileToBase64(File file) async {
    Uint8List bytes = await file.readAsBytes();
    return base64Encode(bytes);
  }

  void checkStaff({
    required BuildContext context,
    String? phoneNumber,
  }) async {
    final checkStaff =
        await ref.read(aioContractCreateProvider.notifier).checkInternalStaff(
              phoneNumber: phoneNumber,
            );
    if (checkStaff != null) {
      if (!context.mounted) return;
      AppDialog.showDialogConfirm(
        context,
        title: "Xác nhận",
        message:
            "Số điện thoại của nhân viên nội bộ, bạn có chắc chắn hợp đồng hàng không?",
        buttonNameConfirm: "Tạo đơn",
        onConfirmAction: () {
          phoneNumberFocus.unfocus();
          getCustomerInfo360(
            phoneNumber: phoneNumber,
            isInternalStaff: true,
            context: context,
          );
          isStaff = true;
        },
        onCancelAction: () {
          isStaff = false;
          customerPhoneNumberController.text = "";
          phoneNumberFocus.unfocus();
        },
      );
    } else {
      if (!context.mounted) return;
      getCustomerInfo360(
        phoneNumber: phoneNumber,
        context: context,
      );
      phoneNumberFocus.unfocus();
    }
  }

  void getCustomerInfo360({
    String? phoneNumber,
    String? taxCode,
    bool? isInternalStaff,
    required BuildContext context,
  }) async {
    var state = ref.watch(aioContractCreateProvider);
    final customerInfo =
        await ref.read(aioContractCreateProvider.notifier).getCustomerInfo360(
              phoneNumber: phoneNumber,
              taxCode: taxCode,
              type: state.customerType == AioCustomerType.customer
                  ? UserType.personal
                  : UserType.company,
              isInternalStaff: isInternalStaff,
            );

    if ((customerInfo != null &&
            state.customerType == AioCustomerType.customer &&
            ValidateUtils.onValidatePhoneNullAble(customerInfo.phone ?? "") !=
                null) ||
        (customerInfo != null &&
            state.customerType == AioCustomerType.bussiness &&
            ValidateUtils.onValidateCompanyPhone(customerInfo.phone ?? "") !=
                null)) {
      if (!context.mounted) return;
      AppDialog.showDialogCenter(
        context,
        message:
            "Dữ liệu khách hàng chưa được chuẩn hóa. Vui lòng báo lỗi hệ thống để CSKH hỗ trợ",
        status: DialogStatus.error,
      );
      businessTaxNumberController.text = "";
      state.businessAddress = null;
      ref.read(aioContractCreateProvider.notifier).businessAddressSetNull();
      return;
    }

    if (AioCustomerType.bussiness == state.customerType) {
      businessPhoneNumberController.text = customerInfo?.phone ?? "";
    }
    socialController.text = customerInfo?.phone ?? "";
    contactPhoneController.text = customerInfo?.phone ?? "";
    if (customerInfo != null &&
        state.customerType == AioCustomerType.customer) {
      customerNameController.text = customerInfo.fullName ?? '';
    }
    if (customerInfo != null &&
        state.customerType == AioCustomerType.bussiness) {
      businessNameController.text = customerInfo.fullName ?? '';
    }
    if (customerInfo?.customerId != null) {
      ref.read(aioContractCreateProvider.notifier).showCus360();
    }
  }

  Widget _buildPackage({
    required AioPackageEntity package,
    required int index,
  }) {
    var state = ref.watch(aioContractCreateProvider);
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 14, 14, 14),
            child: Text(
              "${index + 1}.",
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            package.aioPackageName ?? '',
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: <Widget>[
                              Expanded(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Text(
                                      StringUtils.formatMoney(
                                          package.price ?? 0),
                                      style: UITextStyle.body2Medium.copyWith(
                                        color: BaseColors.primary,
                                      ),
                                    ),
                                    Text(
                                      '/gói',
                                      style:
                                          UITextStyle.caption1Medium.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              PlusAndMinusWidget(
                                quantity: package.quantity ?? 1,
                                onPlus: (value) {
                                  ref
                                      .read(aioContractCreateProvider.notifier)
                                      .changeQuantityPackage(
                                        index: index,
                                        quantity: value.toInt(),
                                      );
                                  changeMoney(state);
                                },
                                onMinus: (value) {
                                  if (value < 1) {
                                    return AppDialog.showDialogConfirm(
                                      context,
                                      title: "Xác nhận",
                                      message:
                                          'Bạn có chắc chắn muốn xoá gói "${package.aioPackageName}"?',
                                      buttonNameConfirm: "Xoá",
                                      onConfirmAction: () async {
                                        ref
                                            .read(aioContractCreateProvider
                                                .notifier)
                                            .removePackage(
                                              index: index,
                                              package: package,
                                            );
                                        changeMoney(state);
                                      },
                                      buttonNameCancel: "Hủy bỏ",
                                      onCancelAction: () {
                                        ref
                                            .read(aioContractCreateProvider
                                                .notifier)
                                            .changeQuantityPackage(
                                              index: index,
                                              quantity: 1,
                                            );
                                      },
                                    );
                                  } else {
                                    ref
                                        .read(
                                            aioContractCreateProvider.notifier)
                                        .changeQuantityPackage(
                                          index: index,
                                          quantity: value.toInt(),
                                        );
                                  }
                                  changeMoney(state);
                                },
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (package.serviceItem != null) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: BaseColors.backgroundGray,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            MyAssets.icons.iconRepairMaintain.svg(
                              colorFilter: ColorFilter.mode(
                                BaseColors.textSubtitle,
                                BlendMode.srcIn,
                              ),
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Expanded(
                              child: Text(
                                package.serviceItem?.displayName ?? '',
                                style: UITextStyle.body2Medium.copyWith(
                                  color: BaseColors.textLabel,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Row(
                          children: <Widget>[
                            Expanded(
                              child: Row(
                                children: [
                                  const SizedBox(
                                    width: 22,
                                  ),
                                  Text(
                                    StringUtils.formatMoney(
                                        package.serviceItem?.price ?? 0),
                                    style: UITextStyle.body2Medium.copyWith(
                                      color: BaseColors.primary,
                                    ),
                                  ),
                                  Text(
                                    '/gói',
                                    style: UITextStyle.body2Medium.copyWith(
                                      color: BaseColors.textSubtitle,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              'x${(package.quantity ?? 1) * (package.serviceItem?.amount ?? 0)}',
                              style: UITextStyle.body2Medium.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                          ],
                        ),
                        if (package.disableSelectService != true) ...[
                          Align(
                            alignment: Alignment.centerRight,
                            child: InkWell(
                              onTap: () {
                                context.push(
                                  RouterPaths.aioAddService,
                                  extra: AddServiceArguments(
                                    onSelected: (data) {
                                      ref
                                          .read(aioContractCreateProvider
                                              .notifier)
                                          .updatePackagesService(data);
                                    },
                                    packagesSelected: state.packages,
                                    itemPackagesSelected:
                                        state.packages?[index],
                                  ),
                                );
                              },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  MyAssets.icons.iconExchange.svg(),
                                  const SizedBox(
                                    width: 4,
                                  ),
                                  RichText(
                                    text: TextSpan(
                                      text: 'Thay đổi',
                                      style:
                                          UITextStyle.caption1SemiBold.copyWith(
                                        color: BaseColors.primary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                if (state.xcare == true &&
                    package.serviceItem == null &&
                    state.contractInfoType == AioContractInfoType.package &&
                    !(state.industry?.code == "NLMT" &&
                        state.service?.status == "2")) ...[
                  Align(
                    alignment: Alignment.centerRight,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        InkWell(
                          onTap: () {
                            context.push(
                              RouterPaths.aioAddService,
                              extra: AddServiceArguments(
                                onSelected: (data) {
                                  ref
                                      .read(aioContractCreateProvider.notifier)
                                      .updatePackagesService(data);
                                },
                                packagesSelected: state.packages,
                                itemPackagesSelected: state.packages?[index],
                              ),
                            );
                          },
                          child: RichText(
                            text: TextSpan(
                              text: 'Chọn dịch vụ HS',
                              style: UITextStyle.caption1SemiBold.copyWith(
                                color: BaseColors.primary,
                              ),
                            ),
                          ),
                        ),
                        MyAssets.icons.arrowRight.svg(
                          colorFilter: ColorFilter.mode(
                            BaseColors.primary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                Align(
                  alignment: Alignment.centerRight,
                  child: RichText(
                    text: TextSpan(
                      text: 'Thành tiền: ',
                      style: UITextStyle.caption1Medium.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: StringUtils.formatMoney(
                              (package.quantity ?? 1) * (package.price ?? 0) +
                                  (package.quantity ?? 0) *
                                      (package.serviceItem?.price ?? 0) *
                                      (package.serviceItem?.amount ?? 0)),
                          style: UITextStyle.caption1SemiBold.copyWith(
                            color: BaseColors.textLabel,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void getInvoice(
    AioContractCreateState state, {
    required BuildContext context,
  }) async {
    List<AioInvoiceItemEntity>? listInvoiceItem = await ref
        .read(aioContractCreateProvider.notifier)
        .getListInvoiceItem(
            listPackageId: (state.packages ?? [])
                .map((package) => package.packageDetailPriceId)
                .toList());

    List<BillDetailItemEntity>? listItem = [];
    state.packages?.forEach((package) {
      for (int i = 0; i < (listInvoiceItem ?? []).length; i++) {
        var billDetailItemEntity = listInvoiceItem![i].toBillDetailItemEntity();

        if (package.aioPackageId == billDetailItemEntity.packageId) {
          billDetailItemEntity.quantity =
              (billDetailItemEntity.quantity ?? 0) * (package.quantity ?? 0);
          billDetailItemEntity.amount = package.price ?? 0;
          billDetailItemEntity.amountBeforeTax =
              ((billDetailItemEntity.amount ?? 0) * 100) /
                  (100 + (billDetailItemEntity.taxPercent ?? 0));
          billDetailItemEntity.preTaxAmount =
              (billDetailItemEntity.amount ?? 0) -
                  (billDetailItemEntity.amountBeforeTax ?? 0);
          billDetailItemEntity.price =
              (billDetailItemEntity.amountBeforeTax ?? 0) /
                  (billDetailItemEntity.quantity ?? 1);

          listItem.add(billDetailItemEntity);
        }
      }
    });

    if (!context.mounted) return;
    context.push(
      RouterPaths.billUpdate,
      extra: BillUpdateArguments(
        isFromOrder: true,
        data: BillDetailEntity(
          totalMoney: state.totalMoney,
          address: state.customerAddress?.getFullAddress,
          email: state.customerType == AioCustomerType.customer
              ? customerEmailController.text
              : businessEmailController.text,
          description: noteController.text,
          customerName: customerNameController.text,
          items: listItem,
        ),
        onSaveData: (callBackData) {
          saveDataInvoice(callBackData);
        },
      ),
    );
  }

  void saveDataInvoice(callBackData) {
    List<AioInvoiceItemEntity> items = [];
    // Danh sách hàng hóa
    for (int i = 0; i < (callBackData.items ?? []).length; i++) {
      var itTmp = callBackData.items?[i];
      AioInvoiceItemEntity it = AioInvoiceItemEntity(
        goodsName: itTmp?.name ?? itTmp?.goodsName,
        goodsCode: itTmp?.goodsCode,
        goodsId: itTmp?.goodsId,
        quantity: itTmp?.quantity,
        amount: itTmp?.amount,
        amountBeforeTax: itTmp?.amountBeforeTax,
        preTaxAmount: itTmp?.preTaxAmount,
        price: itTmp?.price,
      );
      items.add(it);
    }
    // Danh sách ghi chú
    for (int i = 0; i < (callBackData.notes ?? []).length; i++) {
      var itTmp = callBackData.notes?[i];
      AioInvoiceItemEntity it = AioInvoiceItemEntity(
        goodsName: itTmp,
      );
      items.add(it);
    }
    // Danh sách coupon Todo: Confirm lại với Thúy để tạo được model mẫu
    // for (int i = 0; i < (callBackData.coupons ?? []).length; i++) {
    //   var itTmp = callBackData.coupons?[i];
    //   AioInvoiceItemEntity it = AioInvoiceItemEntity(
    //     goodsName: itTmp,
    //   );
    //   items.add(it);
    // }

    AioInvoiceEntity invoice = AioInvoiceEntity(
        address: callBackData.address,
        bankAccount: callBackData.bankAccount,
        bankName: callBackData.bankName,
        companyName: callBackData.companyName,
        customerName: callBackData.customerName,
        paymentMethod: callBackData.paymentMethodStr,
        email: callBackData.email,
        description: callBackData.description,
        taxCode: callBackData.taxCode,
        aioInvoiceItemDTOS: items);
    ref.read(aioContractCreateProvider.notifier).updateInvoice(invoice);
  }

  setDataFromStaff(AioContractCreateState state) {
    customerNameController.text = state.internalStaff?.fullName ?? "";
    customerPhoneNumberController.text = state.internalStaff?.phoneNumber ?? "";
    socialController.text = state.internalStaff?.phoneNumber ?? "";
    contactPhoneController.text = state.internalStaff?.phoneNumber ?? "";
  }

  getCusB2B() {
    getCustomerInfo360(
      taxCode: businessTaxNumberController.text,
      context: context,
    );
    businessTaxNumberFocusNode.unfocus();
  }

  changeTransportType(
    value, {
    required BuildContext context,
  }) async {
    var state = ref.watch(aioContractCreateProvider);
    if (state.loadStatusDeployContract == LoadStatus.loading) {
      return;
    }
    if (state.customerAddress == null && state.businessAddress == null) {
      AppDialog.showDialogCenter(
        context,
        message: "Vui lòng chọn địa chỉ",
        status: DialogStatus.error,
      );
      return;
    }
    AioResultInfoEntity? result = await ref
        .read(aioContractCreateProvider.notifier)
        .changeTransportType(value);
    if (result?.message != null && result?.message != "") {
      if (!context.mounted) return;
      AppDialog.showDialogCenter(
        context,
        message: result?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
      return;
    }
    if (value == AioTransportType.vtp) {
      collectedMoneyController.text = "0";
      receivablesMoneyController.text =
          StringUtils.formatNumber(state.totalMoney ?? 0);
    }
    if (value == AioTransportType.vcc &&
        state.contractInfoType == AioContractInfoType.commerce) {
      await ref.read(aioContractCreateProvider.notifier).xCareOff(false);
    }
  }

  getCodVtp(state) async {
    var state = ref.watch(aioContractCreateProvider);
    if (state.deploymentMethodContract == AioTransportType.vtp) {
      collectedMoneyController.text = "0";
      receivablesMoneyController.text =
          StringUtils.formatNumber(state.totalMoney ?? 0);
    }
  }

  changeMoney(state) async {
    var state = ref.watch(aioContractCreateProvider);
    int collectedMoney =
        int.parse(collectedMoneyController.text.replaceAll(".", ""));
    int money = (state.totalMoney ?? 0) - collectedMoney;
    receivablesMoneyController.text =
        StringUtils.formatNumber(money < 0 ? 0 : money);
  }

  getCusB2C() {
    if (customerPhoneNumberController.text.validatePhone()) {
      checkStaff(
        context: context,
        phoneNumber: customerPhoneNumberController.text,
      );
      customerPhoneFocusNode.unfocus();
    }
  }

  void getUrlToRequestSubmitMoney({
    int? contractId,
    required BuildContext context,
  }) async {
    var aioContractParamPayment = AioContractDigitalParam(
      sysUserRequest: SysUserRequest(
        sysUserId: GlobalData.instance.userInfo?.sysUserId,
        flag: 0,
        isTtqh: false,
        ft: false,
      ),
      contractId: contractId,
    );
    AioContractResponseEntity? response = await ref
        .read(aioContractCreateProvider.notifier)
        .getUrlToRequestSubmitMoney(aioContractParamPayment);
    if (!context.mounted) return;
    if (response?.resultInfo?.status == BaseConstant.ok) {
      context.push(
        RouterPaths.webView,
        extra: WebViewArguments(
            url: response?.requestUrl ?? "",
            title: "Viettel Paygate",
            backFunction: () {
              context.pop();
              context.pop();
              goToOrderContractMng();
            }),
      );
    } else {
      AppDialog.showDialogCenter(
        context,
        message: response?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
  }

  updatePackages(
    data, {
    required BuildContext context,
  }) async {
    var state = ref.watch(aioContractCreateProvider);
    // trọn gói, hình thức triển khai VCC tự thực hiện => không cho chọn dịch vụ HS chỉ chọn dịch vụ AIO
    if (state.contractInfoType == AioContractInfoType.package &&
        state.deploymentMethodContract == AioTransportType.vcc) {
      await ref.read(aioContractCreateProvider.notifier).changeXCare(false);
    } else if (state.contractInfoType == AioContractInfoType.commerce &&
        state.deploymentMethodContract == AioTransportType.vcc) {
      await ref.read(aioContractCreateProvider.notifier).changeXCare(true);
    }

    if (state.deploymentMethodContract == AioTransportType.vcc &&
        state.contractInfoType == AioContractInfoType.commerce) {
      await ref.read(aioContractCreateProvider.notifier).xCareOff(false);
    }

    if (state.xcare == true &&
        state.contractInfoType == AioContractInfoType.package) {
      var services = await ref
          .read(aioContractCreateProvider.notifier)
          .getServices(packageSelected: data);
      if ((services ?? []).isNotEmpty &&
          services?.first.displayCode == BaseConstant.notOK) {
        if (!context.mounted) return;
        AppDialog.showDialogCenter(
          context,
          message: services?.first.displayName ?? "Đã có lỗi xảy ra!",
          status: DialogStatus.error,
        );
        await ref.read(aioContractCreateProvider.notifier).updatePackages([]);
        return;
      }
      var packageNoService = [];
      var packageCodeNoService = "";
      for (int i = 0; i < data.length; i++) {
        var package = data[i];
        var servicePackages = services
            ?.where((it) => it.aioPackageId == package.aioPackageId)
            .toList();
        if ((servicePackages ?? []).length == 1) {
          data[i].serviceItem = servicePackages?[0];
          data[i].disableSelectService = true;
        } else if ((servicePackages ?? []).isEmpty) {
          packageNoService.add(package);
          data.removeAt(i);
          packageCodeNoService =
              "${StringUtils.truncate(package.aioPackageName, 30)}, ";
        }
      }
      if (packageCodeNoService.isNotEmpty) {
        if (!context.mounted) return;
        AppDialog.showDialogCenter(
          context,
          message: "Gói: $packageCodeNoService không có dịch vụ HS",
          status: DialogStatus.error,
        );
      }
    }

    if (state.deploymentMethodContract == AioTransportType.vcc) {
      var services = await ref
          .read(aioContractCreateProvider.notifier)
          .getServices(packageSelected: data);
      if (services != null &&
          services.isNotEmpty &&
          services.first.displayCode == BaseConstant.notOK) {
        await ref.read(aioContractCreateProvider.notifier).changeXCare(false);
        collectedMoneyController.text = "0";
        receivablesMoneyController.text =
            StringUtils.formatNumber(state.totalMoney ?? 0);
        if (state.contractInfoType == AioContractInfoType.package) {
          if (!context.mounted) return;
          AppDialog.showDialogCenter(
            context,
            message: services.first.displayName ?? "Đã có lỗi xảy ra!",
            status: DialogStatus.error,
          );
          await ref.read(aioContractCreateProvider.notifier).updatePackages([]);
          return;
        }
      }
      // Comment theo code nampv10 fix trên app cũ AIO
      // else {
      //   await ref.read(aioContractCreateProvider.notifier).changeXCare(true);
      // }
    }
    await ref.read(aioContractCreateProvider.notifier).updatePackages(data);
    changeMoney(state);
    getCodVtp(state);
  }

  void _unFocusAll() {
    customerNameFocusNode.unfocus();
    customerPhoneFocusNode.unfocus();
    positionFocusNode.unfocus();
    contactPhoneFocusNode.unfocus();
    companyTaxFocusNode.unfocus();
    businessEmailFocusNode.unfocus();
    customerEmailFocusNode.unfocus();
    noteFocusNode.unfocus();
    contentFocusNode.unfocus();
    businessTaxNumberFocusNode.unfocus();
    contractCodeFocusNode.unfocus();
    businessNameFocusNode.unfocus();
    businessPhoneNumberFocusNode.unfocus();
  }

  @override
  void dispose() {
    customerPhoneNumberController.dispose();
    socialController.dispose();
    customerNameController.dispose();
    contactPhoneController.dispose();
    companyTaxController.dispose();
    companyNameController.dispose();
    companyAddressController.dispose();
    customerEmailController.dispose();
    customerLevelController.dispose();
    customerFieldController.dispose();
    customerAreaController.dispose();
    businessTaxNumberController.dispose();
    businessNameController.dispose();
    businessAddressController.dispose();
    businessPhoneNumberController.dispose();
    businessContactPhoneNumberController.dispose();
    businessEmailController.dispose();
    businessEstablishController.dispose();
    noteController.dispose();
    contentController.dispose();
    percentController.dispose();
    contractCodeController.dispose();
    positionController.dispose();

    customerNameFocusNode.dispose();
    customerPhoneFocusNode.dispose();
    positionFocusNode.dispose();
    contactPhoneFocusNode.dispose();
    companyTaxFocusNode.dispose();
    businessEmailFocusNode.dispose();
    customerEmailFocusNode.dispose();
    noteFocusNode.dispose();
    contentFocusNode.dispose();
    businessTaxNumberFocusNode.dispose();
    contractCodeFocusNode.dispose();
    businessNameFocusNode.dispose();
    businessPhoneNumberFocusNode.dispose();

    super.dispose();
  }
}
