part of 'aio_contract_package_view_model.dart';

class AioContractPackageState extends Equatable {
  final LoadStatus loadStatus;
  final String? message;
  final List<AioContractDetailEntity>? listPackage;

  const AioContractPackageState({
    this.loadStatus = LoadStatus.initial,
    this.message,
    this.listPackage,
  });

  AioContractPackageState copyWith({
    LoadStatus? loadStatus,
    String? message,
    List<AioContractDetailEntity>? listPackage,
  }) {
    return AioContractPackageState(
      loadStatus: loadStatus ?? this.loadStatus,
      message: message ?? this.message,
      listPackage: listPackage ?? this.listPackage,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        message,
        listPackage,
      ];
}
