import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_contract_detail_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';

part 'aio_contract_package_state.dart';

final aioContractPackageProvider = StateNotifierProvider.autoDispose<
    AioContractPackageViewModel, AioContractPackageState>(
  (ref) => AioContractPackageViewModel(ref: ref),
);

class AioContractPackageViewModel
    extends StateNotifier<AioContractPackageState> {
  final Ref ref;

  AioContractPackageViewModel({
    required this.ref,
  }) : super(const AioContractPackageState());

  Future<void> getData(AioContractEntity param) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      final resultParam = await appLocator<AioOrderRepository>()
          .getListContractDetailByContractId(
              params: AioContractEntity(
                  contractId: param.contractId,
                  sysUserId: GlobalData.instance.userInfo?.sysUserId ??
                      GlobalData.instance.userInfo?.userId));

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            listPackage: data.data,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }
}
