import 'dart:convert';
import 'dart:io';
import 'dart:typed_data';

import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_area_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_contract_detail_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_request_cancel_body.dart';
import 'package:vcc/domain/entities/order/bill_detail_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_item_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_package_status_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/reason_query_type.dart';
import 'package:vcc/domain/params/aio_contract/aio_image.dart';
import 'package:vcc/domain/params/aio_contract/authentication_info.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';
import 'package:vcc/env/env_config.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/aio_change_payment_type/aio_change_payment_type_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/aio_contract/add_package/add_package_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/add_service/add_service_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_check_inventory/aio_check_inventory_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_request_cancel/aio_request_cancel_screen.dart';
import 'package:vcc/presentation/views/features/aio_contract/list_stock_good/list_stock_good_page.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_update/bill_update_page.dart';
import 'package:vcc/presentation/views/features/work/request_buy_product/create_request_buy_product/create_request_buy_product_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/image_base64_widget.dart';
import 'package:vcc/presentation/views/widgets/image_preview_popup.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/plus_and_minus_widget.dart';
import 'package:vcc/utils/media_utils.dart';
import 'package:vcc/utils/string_utils.dart';

import 'aio_contract_detail_performer_view_model.dart';

class AioContractDetailPerformerArguments {
  final String? data;
  final AioContractEntity aioContract;
  final Function()? backFunction;

  AioContractDetailPerformerArguments({
    this.data,
    this.backFunction,
    required this.aioContract,
  });
}

class AioContractDetailPerformerPage extends StatefulHookConsumerWidget {
  final AioContractDetailPerformerArguments arguments;

  const AioContractDetailPerformerPage({
    super.key,
    required this.arguments,
  });

  @override
  ConsumerState<AioContractDetailPerformerPage> createState() =>
      _AioContractDetailPageState();
}

class _AioContractDetailPageState
    extends ConsumerState<AioContractDetailPerformerPage>
    with TickerProviderStateMixin {
  late TextEditingController collectedMoneyController;

  @override
  void initState() {
    Future(() {
      ref.read(aioContractDetailPerformerProvider.notifier).getData(
            aioContract: widget.arguments.aioContract,
          );
    });

    collectedMoneyController = TextEditingController();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(aioContractDetailPerformerProvider);

    PreferredSizeWidget appbarWidget;

    appbarWidget = AppBarCustom(
      backFunction: () {
        context.pop();
      },
      title: "Thông tin hợp đồng dịch vụ",
      actionWidget: [
        InkWellWidget(
          onTap: () {},
        ),
      ],
    );

    return LayoutPage(
      appbar: appbarWidget,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildPage(state),
      ),
      bottomAction: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 16,
        ),
        decoration: BoxDecoration(
          boxShadow: AppBoxShadows.shadowNormal,
          color: BaseColors.backgroundWhite,
        ),
        child: SafeArea(
          child: state.loadStatus == LoadStatus.success
              ? Row(
                  children: [
                    if (state.contractInfo?.canOrderRequest == true) ...[
                      Expanded(
                        child: BaseButton(
                          text: "Đặt hàng",
                          onTap: () async {
                            List<AioInvoiceItemEntity>? requestData = await ref
                                .read(
                                    aioContractDetailPerformerProvider.notifier)
                                .dataOrderRequestForContract(
                                  param: AioContractEntity(
                                    sysUserId:
                                        GlobalData.instance.userInfo?.sysUserId,
                                    contractId: state.contractInfo?.contractId,
                                    contractCode:
                                        state.contractInfo?.contractCode,
                                  ),
                                );

                            var listItemSupply = (requestData ?? []).map((e) {
                              var rs = e.toSupplyRequestProductEntity();
                              rs.quantity = (e.amount ?? 0).toInt();
                              rs.quantityInContract = (e.amount ?? 0).toInt();
                              return rs;
                            }).toList();

                            if (!context.mounted) return;
                            if (listItemSupply.isNotEmpty) {
                              context.push(RouterPaths.createRequestBuyProduct,
                                  extra: CreateRequestBuyProductArguments(
                                      listSupply: listItemSupply,
                                      contractInfo: state.contractInfo,
                                      onSave: () {
                                        context.pop();
                                      }));
                            } else {
                              context.pop();
                            }
                          },
                          backgroundColor: Colors.white,
                          textColor: BaseColors.primary,
                          borderColor: BaseColors.primary,
                        ),
                      ),
                      const SizedBox(
                        width: 12,
                      ),
                    ],
                    Expanded(
                      child: BaseButton(
                        text: "Lưu",
                        onTap: () async {
                          editContract(
                            state,
                            context: context,
                          );
                        },
                      ),
                    ),
                  ],
                )
              : const Row(
                  children: [
                    Expanded(
                        child: SizedBox(
                      height: 1,
                    )),
                  ],
                ),
        ),
      ),
    );
  }

  Widget _buildPage(AioContractDetailPerformerState state) {
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      return SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildContractInfo(),
            _buildCustomerInfo(),
            _buildListUseSupply(),
            _buildLogInventoryContract(),
            _buildTeamContributeInfo(),
            _buildPaymentInfoInfo(),
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            // if (checkStatus() !=
            //     AioContractPackageStatusType.proposalToCancel) ...[
            Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              child: Column(
                children: [
                  if (checkStatus() ==
                      AioContractPackageStatusType.processing) ...[
                    if (state.contractInfo?.isInvoice == 1) ...[
                      BaseButton(
                        text: "Xem hóa đơn",
                        borderColor: CoreColors.neutral04,
                        backgroundColor: Colors.transparent,
                        textColor: BaseColors.textLabel,
                        onTap: () {},
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                    ],
                    // BaseButton(
                    //   text: "Thay đổi hình thức thanh toán",
                    //   borderColor: CoreColors.primary,
                    //   backgroundColor: BaseColors.backgroundGray,
                    //   textColor: BaseColors.primary,
                    //   onTap: () {
                    //     changePaymentType();
                    //   },
                    // ),
                    // const SizedBox(
                    //   height: 12,
                    // ),
                  ],
                  if (checkStatus() ==
                          AioContractPackageStatusType.unfulfilled ||
                      checkStatus() ==
                          AioContractPackageStatusType.processing ||
                      checkStatus() ==
                          AioContractPackageStatusType.waitingDistrict ||
                      checkStatus() ==
                          AioContractPackageStatusType.waitingCreateOrder ||
                      checkStatus() ==
                          AioContractPackageStatusType.waitingBranch) ...[
                    BaseButton(
                      text: "Đề xuất hủy",
                      borderColor: CoreColors.neutral04,
                      backgroundColor: Colors.transparent,
                      textColor: BaseColors.textLabel,
                      onTap: () {
                        context.push(
                          RouterPaths.aioRequestCancelPage,
                          extra: AioRequestCancelArguments(
                            reasonQueryType: ReasonQueryType.employee,
                            aioContract: widget.arguments.aioContract,
                            aioContractDetail: state.contractDetail ??
                                AioContractDetailEntity(),
                            backFunction: () {
                              context.pop();
                              widget.arguments.backFunction?.call();
                            },
                          ),
                        );
                      },
                    ),
                  ],
                  if (checkStatus() ==
                      AioContractPackageStatusType.proposalToCancel) ...[
                    BaseButton(
                      text: "Xóa đề xuất hủy",
                      borderColor: CoreColors.neutral04,
                      backgroundColor: Colors.transparent,
                      textColor: BaseColors.textLabel,
                      onTap: () {
                        deleteCancelOrder(
                          state,
                          context: context,
                        );
                      },
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                  ],
                ],
              ),
            ),
          ],
          // ],
        ),
      );
    }
  }

  Future<void> refreshData() async {}

  AioContractPackageStatusType checkStatus() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    if (state.contractInfo?.status != 0 &&
        (state.contractInfo?.status == 5 || state.contractInfo?.status == 6)) {
      switch (state.contractInfo?.status) {
        case 5:
          return AioContractPackageStatusType.proposedPause;
        case 6:
          return AioContractPackageStatusType.proposalToCancel;
      }
    } else {
      switch (state.contractInfo?.status) {
        case 1:
          return AioContractPackageStatusType.unfulfilled;
        case 2:
          return AioContractPackageStatusType.processing;
        case 3:
          return AioContractPackageStatusType.completed;
        case 5:
          return AioContractPackageStatusType.proposedPause;
        case 6:
          return AioContractPackageStatusType.proposalToCancel;
        case 7:
          return AioContractPackageStatusType.waitingCreateOrder;
        case 8:
          return AioContractPackageStatusType.preparingGoods;
        case 9:
          return AioContractPackageStatusType.waitingDistrict;
        case 10:
          return AioContractPackageStatusType.waitingBranch;
        default:
          return AioContractPackageStatusType.nan;
      }
    }
    return AioContractPackageStatusType.nan;
  }

  Widget checkIcon() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    if (state.contractDetail?.statusContract != 0 &&
        (state.contractDetail?.statusContract == 5 ||
            state.contractDetail?.statusContract == 6)) {
      switch (state.contractDetail?.statusContract) {
        case 5:
          return MyAssets.icons.cancelOrderActive.svg();
        case 6:
          return MyAssets.icons.cancelOrderActive.svg();
      }
    } else {
      switch (state.contractDetail?.status) {
        case 1:
          return MyAssets.icons.iconOrderWaitingConfirmS40.svg();
        case 2:
          return MyAssets.icons.iconProcessDeactiveS24.svg();
        default:
          return MyAssets.icons.iconOrderWaitingConfirmS40.svg();
      }
    }
    return MyAssets.icons.iconOrderWaitingConfirmS40.svg();
  }

  Widget _buildListUseSupply() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 12,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Danh sách gói bán hàng',
                    style: UITextStyle.body1SemiBold.copyWith(
                      color: BaseColors.textTitle,
                    ),
                  ),
                  if (state.contractInfo?.xcare != 1 &&
                      checkStatus() !=
                          AioContractPackageStatusType.completed) ...[
                    AppTextButton(
                      iconLeft: MyAssets.icons.iconAddCircle.svg(),
                      title: "Thêm gói",
                      onTap: () {
                        AioAreaEntity param = AioAreaEntity(
                          areaId: state.contractInfo?.customerDTO?.aioAreaId,
                          provinceId:
                              state.contractInfo?.customerDTO?.provinceId,
                          configServiceCode: state.contractInfo?.serviceCode,
                          industryCode: state.contractInfo?.industryCode,
                          text: "VCC",
                          type: 2,
                          field: state.contractInfo?.serviceCode == "PINNLMT"
                              ? null
                              : state.contractInfo?.field,
                          isInternal: state.contractInfo?.isInternal,
                          contractType: state.contractInfo?.type,
                          sysUserId: GlobalData.instance.userInfo?.sysUserId,
                        );

                        context.push(
                          RouterPaths.aioAddPackage,
                          extra: AddPackageArguments(
                            param: param,
                            onSelected: (data) {
                              ref
                                  .read(aioContractDetailPerformerProvider
                                      .notifier)
                                  .calculatorTotalMoney();
                            },
                            packagesSelected: state.packages,
                          ),
                        );
                      },
                    ),
                  ],
                ],
              ),
              const SizedBox(
                height: 8,
              ),
              DividerWidget(
                height: 1,
                color: BaseColors.backgroundGray,
              ),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: (state.packages ?? []).length,
                itemBuilder: (context, index) {
                  var item = state.packages![index];
                  return AbsorbPointer(
                    absorbing:
                        checkStatus() == AioContractPackageStatusType.completed,
                    child: _buildPackage(
                      package: item,
                      index: index,
                    ),
                  );
                },
              ),
              const SizedBox(
                height: 16,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Tổng: ",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  Text(
                    StringUtils.formatMoney(
                        state.contractInfo?.contractAmount ?? 0),
                    style: UITextStyle.body2SemiBold.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ],
              ),
              const SizedBox(
                height: 16,
              ),
              DividerWidget(
                height: 1,
                color: BaseColors.backgroundGray,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCustomerInfo() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    return Column(
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 12,
              ),
              Text(
                'Thông tin khách hàng',
                style: UITextStyle.body1SemiBold.copyWith(
                  color: BaseColors.textTitle,
                ),
              ),
              const SizedBox(
                height: 12,
              ),
              DividerWidget(
                height: 1,
                color: BaseColors.backgroundGray,
              ),
              const SizedBox(
                height: 8,
              ),
              buildRowData(
                  "Loại khách hàng",
                  state.contractInfo?.customerDTO?.type == 1
                      ? "KH cá nhân"
                      : "KH doanh nghiệp"),
              buildRowData("Đối tượng khách hàng",
                  state.contractInfo?.diplomaticCustomer),
              buildRowData("Email", state.contractInfo?.customerDTO?.email),
              buildRowData("Tên khách hàng", state.contractInfo?.customerName),
              buildRowData(
                  "Số điện thoại", state.contractInfo?.customerPhone ?? ""),
              buildRowData(
                  "Địa chỉ", state.contractInfo?.customerAddress ?? ""),
              buildRowData("Ngày sinh", state.contractInfo?.customerDTO?.birth),
              buildRowData(
                  "Giới tính",
                  state.contractInfo?.customerDTO?.gender == 1
                      ? "Nam"
                      : state.contractInfo?.customerDTO?.gender == 2
                          ? "Nữ"
                          : "Khác"),
              buildRowData(
                  "Ngành nghề kinh doanh", state.contractInfo?.employmentField),
              buildRowData("Khu vực địa lý", state.contractInfo?.geographical),
              buildRowData("Mã số thuế", state.contractInfo?.customerCode),
              buildRowData(
                  "Hạng khách hàng", state.contractInfo?.customerTypeName),
              const SizedBox(
                height: 16,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildContractInfo() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    return Column(
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 12,
              ),
              Text(
                "Thông tin hợp đồng",
                style: UITextStyle.body1SemiBold.copyWith(
                  color: BaseColors.textTitle,
                ),
              ),
              const SizedBox(
                height: 12,
              ),
              DividerWidget(
                height: 1,
                color: BaseColors.backgroundGray,
              ),
              const SizedBox(
                height: 12,
              ),
              buildRowData("Mã dịch vụ", state.contractInfo?.serviceCode),
              buildRowData(
                  "Loại hợp đồng",
                  state.contractInfo?.contractType == 2
                      ? "Thương mại"
                      : "Dự án/trọn gói"),
              buildRowData("Nguồn", state.contractInfo?.createdSource),
              buildRowData(
                  "Triển khai",
                  state.contractInfo?.deploymentMethodContract == 1
                      ? "Tự thực hiện"
                      : state.contractInfo?.deploymentMethodContract == 2
                          ? "VCC thực hiện"
                          : "VTPost thực hiện"),
              buildRowData("Thời gian tạo", state.contractInfo?.createdDate),
              if (state.contractInfo?.endDate != null) ...[
                buildRowData("Thời hoàn thành", state.contractInfo?.endDate),
              ],
              buildRowData("Trạng thái", checkStatus().display),
              const SizedBox(
                height: 16,
              ),
            ],
          ),
        ),
      ],
    );
  }

  String checkPayType(AioContractDetailPerformerState state) {
    String rs = "";
    if (state.contractInfo?.payType == 3 &&
        state.contractInfo?.contractType == 2) {
      rs = "COD";
    } else {
      rs = "TK chuyên thu CNCT";
      if (state.contractInfo?.payType == 1) {
        rs = "ViettelPay";
      }
      if (state.contractInfo?.payType == 5) {
        rs = "OnePay";
      }
    }
    return rs;
  }

  Widget _buildPaymentInfoInfo() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 16,
          ),
          child: Text(
            'Thông tin thanh toán',
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
        ),
        DividerWidget(
          height: 1,
          color: BaseColors.backgroundGray,
        ),
        const SizedBox(
          height: 16,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.start,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              buildRowDataPayment("Xuất hóa đơn",
                  state.contractInfo?.isBill == 0 ? "Không" : "Có"),
              buildRowDataPayment("Hình thức thanh toán", checkPayType(state)),
              const SizedBox(
                height: 8,
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  vertical: 8,
                  horizontal: 16,
                ),
                decoration: BoxDecoration(
                  border: Border.all(color: BaseColors.infoPressed),
                  color: BaseColors.infoSurface,
                  borderRadius: const BorderRadius.all(
                    Radius.circular(12),
                  ),
                ),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        vertical: 4,
                      ),
                      child: MyAssets.icons.infoCircle.svg(
                        width: 14,
                        height: 14,
                        colorFilter: ColorFilter.mode(
                          BaseColors.info,
                          BlendMode.srcIn,
                        ),
                      ),
                    ),
                    const SizedBox(
                      width: 12,
                    ),
                    Expanded(
                      child: Text(
                        "Để thay đổi phương thức thanh toán hoặc lựa chọn xuất hóa đơn. Vui lòng đến màn hình nghiệm thu của hợp đồng để thay đổi.",
                        style: UITextStyle.bodyText2.copyWith(
                          color: BaseColors.textTitle,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(
                height: 8,
              ),
              Text(
                "Ảnh thông tin hợp đồng: ",
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ],
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 8,
            horizontal: 16,
          ),
          child: SizedBox(
            height: 80,
            child: ListView.separated(
              itemCount: (state.listImage?.length ?? 0) + 1,
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.only(right: 16),
              separatorBuilder: (_, __) => const SizedBox(width: 8),
              itemBuilder: (context, index) {
                if (index == (state.listImage?.length ?? 0)) {
                  return InkWellWidget(
                    onTap: () {
                      onTakePicture(index);
                    },
                    child: MyAssets.icons.iconAddImageDashline.svg(),
                  );
                }
                return Stack(
                  children: [
                    Container(
                      height: 80,
                      width: 80,
                      padding: const EdgeInsets.only(
                        top: 4,
                        right: 4,
                      ),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: state.listImage![index].filePath != null
                            ? ImageWidget(
                          "${EnvConfig.baseAioImageUrl}${state.listImage![index]
                              .filePath}",
                          enableShowPreview: true,
                          size: const Size(76, 76),
                        )
                            : ImageBase64Widget(
                          base64Image:
                          state.listImage![index].base64String ?? "",
                        ),
                      ),
                    ),
                    Positioned(
                      top: 0,
                      right: 0,
                      child: Visibility(
                        visible: true,
                        child: InkWellWidget(
                          onTap: () {
                            ref
                                .read(
                                    aioContractDetailPerformerProvider.notifier)
                                .deleteImage(index);
                          },
                          child: MyAssets.icons.iconCloseRed.svg(),
                        ),
                      ),
                    ),
                  ],
                );
              },
            ),
          ),
        ),
        if (state.contractInfo?.status == 3) ...[
          const SizedBox(
            height: 8,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            child: Text(
              "Ảnh nghiệm thu hợp đồng: ",
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 8,
              horizontal: 16,
            ),
            child: SizedBox(
              height: 80,
              child: ListView.separated(
                itemCount: (state.listImageAcceptanceContract?.length ?? 0) + 1,
                scrollDirection: Axis.horizontal,
                padding: const EdgeInsets.only(right: 16),
                separatorBuilder: (_, __) => const SizedBox(width: 8),
                itemBuilder: (context, index) {
                  if (index ==
                      (state.listImageAcceptanceContract?.length ?? 0)) {
                    return InkWellWidget(
                      onTap: () {
                        onTakePictureAcceptance(index);
                      },
                      child: MyAssets.icons.iconAddImageDashline.svg(),
                    );
                  }
                  return Stack(
                    children: [
                      Container(
                        height: 80,
                        width: 80,
                        padding: const EdgeInsets.only(
                          top: 4,
                          right: 4,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: InkWell(
                            onTap: () {
                              showDialog(
                                context: context,
                                builder: (BuildContext context) {
                                  return ImagePreviewPopup(
                                    base64Image: state
                                        .listImageAcceptanceContract![index]
                                        .base64String,
                                    contextX: context,
                                    imageUrl: '',
                                  );
                                },
                              );
                            },
                            child: ImageBase64Widget(
                              base64Image: state
                                      .listImageAcceptanceContract![index]
                                      .base64String ??
                                  "",
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Visibility(
                          visible: true,
                          child: InkWellWidget(
                            onTap: () {
                              ref
                                  .read(aioContractDetailPerformerProvider
                                      .notifier)
                                  .deleteImageAcceptance(index);
                            },
                            child: MyAssets.icons.iconCloseRed.svg(),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ),
        ],
        const SizedBox(
          height: 8,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: DividerWidget(
            height: 1,
            color: BaseColors.backgroundGray,
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Thành tiền: ",
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              Text(
                StringUtils.formatMoney(
                    state.contractInfo?.contractAmount ?? 0),
                style: UITextStyle.body2SemiBold.copyWith(
                  color: BaseColors.primary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        if (state.contractInfo?.xcare == 1 &&
            state.contractInfo?.payType == 1) ...[
          _buildMoney(
            title: "Số tiền đã thu:",
            value: (state.contractInfo?.collectedMoney ?? 0),
          ),
          _buildMoney(
            title: "Số tiền còn lại:",
            value: (state.contractInfo?.contractAmount ?? 0) -
                (state.contractInfo?.collectedMoney ?? 0),
          ),
        ],
      ],
    );
  }

  Widget _buildMoney({
    required String title,
    required num value,
  }) {
    return Column(
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                title,
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              Text(
                StringUtils.formatMoney(value),
                style: UITextStyle.body2SemiBold.copyWith(
                  color: BaseColors.primary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 16,
        ),
      ],
    );
  }

  Widget _buildTeamContributeInfo() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    List<InternalStaffEntity> listUser =
        convertStringToUserList(state.contractInfo?.salesTogether ?? "");
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisAlignment: MainAxisAlignment.start,
            children: [
              Text(
                "Đội ngũ tham gia",
                style: UITextStyle.body1SemiBold,
              ),
              const SizedBox(height: 16),
              DividerWidget(
                height: 1,
                color: BaseColors.backgroundGray,
              ),
              const SizedBox(height: 16),
              ListView.builder(
                  itemCount: listUser.length,
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (context, index) {
                    var item = listUser[index];
                    return _buildItemUser(item, "Người bán hàng");
                  }),
              _buildItemUser(
                  InternalStaffEntity(
                    fullName: state.contractInfo?.performerName,
                    employeeCode: state.contractInfo?.performerCode,
                  ),
                  "Người thực hiện"),
              _buildItemUser(
                  InternalStaffEntity(
                    fullName: state.contractInfo?.referralCode,
                  ),
                  "Mã người giới thiệu"),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildItemUser(InternalStaffEntity item, String title) {
    // if (item.fullName == null) {
    //   return const SizedBox();
    // }
    return Container(
      decoration: BoxDecoration(
        border: Border.all(color: BaseColors.borderDefault),
        borderRadius: const BorderRadius.all(
          Radius.circular(10),
        ),
      ),
      padding: const EdgeInsets.fromLTRB(16, 7, 16, 11),
      margin: const EdgeInsets.fromLTRB(0, 0, 0, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          Text(
            title,
            style: UITextStyle.caption1Regular.copyWith(
              color: BaseColors.textSubtitle,
            ),
          ),
          const SizedBox(
            height: 4,
          ),
          Row(
            children: [
              Expanded(
                child: Text(
                  item.fullName ?? "Không có",
                  style: UITextStyle.body1Regular.copyWith(
                    color: item.fullName == null
                        ? BaseColors.textSubtitle
                        : BaseColors.textBody,
                  ),
                ),
              ),
              if (item.percentBonus != null) ...[
                Text(
                  "${item.percentBonus}%",
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.textBody,
                  ),
                ),
              ],
            ],
          ),
          if (item.employeeCode != null) ...[
            const SizedBox(
              height: 4,
            ),
            Text(
              item.employeeCode ?? "",
              style: UITextStyle.caption1Regular.copyWith(
                color: BaseColors.textSubtitle,
              ),
            ),
          ],
        ],
      ),
    );
  }

  // Widget _buildFee() {
  //   var state = ref.watch(aioContractDetailPerformerProvider);
  //   return Column(
  //     crossAxisAlignment: CrossAxisAlignment.start,
  //     children: [
  //       DividerWidget(
  //         height: 8,
  //         color: BaseColors.backgroundGray,
  //       ),
  //       Padding(
  //           padding: const EdgeInsets.symmetric(
  //             vertical: 16,
  //             horizontal: 16,
  //           ),
  //           child: Column(
  //             crossAxisAlignment: CrossAxisAlignment.start,
  //             children: [
  //               Text(
  //                 'Chi phí',
  //                 style: UITextStyle.body1SemiBold.copyWith(
  //                   color: BaseColors.textTitle,
  //                 ),
  //               ),
  //               const SizedBox(
  //                 height: 8,
  //               ),
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 children: [
  //                   Text(
  //                     "Chi phí CNKT: ",
  //                     style: UITextStyle.body2Regular.copyWith(
  //                       color: BaseColors.textSubtitle,
  //                     ),
  //                   ),
  //                   Text(
  //                     StringUtil.formatMoney(5000000),
  //                     style: UITextStyle.body2Medium.copyWith(
  //                       color: BaseColors.textLabel,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               const SizedBox(
  //                 height: 8,
  //               ),
  //               Row(
  //                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
  //                 children: [
  //                   Text(
  //                     "Chi phí không lương: ",
  //                     style: UITextStyle.body2Regular.copyWith(
  //                       color: BaseColors.textSubtitle,
  //                     ),
  //                   ),
  //                   Text(
  //                     StringUtil.formatMoney(5000000),
  //                     style: UITextStyle.body2Medium.copyWith(
  //                       color: BaseColors.textLabel,
  //                     ),
  //                   ),
  //                 ],
  //               ),
  //               const SizedBox(
  //                 height: 13,
  //               ),
  //               DividerWidget(
  //                 height: 1,
  //                 color: BaseColors.backgroundGray,
  //               ),
  //               const SizedBox(
  //                 height: 16,
  //               ),
  //               TextFieldWidget(
  //                 controller: collectedMoneyController,
  //                 labelText: "Chi phí bán hàng",
  //                 enabled: true,
  //                 onFocusChange: (isFocus) {
  //                   if (!isFocus) {}
  //                 },
  //                 onChanged: (value) {},
  //                 hintText: "1.000.000",
  //                 inputFormatters: [
  //                   ThousandsFormatter(allowFraction: true),
  //                 ],
  //               ),
  //               const SizedBox(
  //                 height: 4,
  //               ),
  //               Padding(
  //                 padding: const EdgeInsets.symmetric(
  //                   horizontal: 16,
  //                 ),
  //                 child: Text(
  //                   "1.5% doanh thu <= CPBH <= 7.0% doanh thu",
  //                   style: UITextStyle.body2Regular.copyWith(
  //                     color: BaseColors.textSubtitle,
  //                     fontSize: 13,
  //                   ),
  //                 ),
  //               ),
  //               const SizedBox(
  //                 height: 16,
  //               ),
  //               TextFieldWidget(
  //                 controller: collectedMoneyController,
  //                 labelText: "Chi phí nhân công lắp đặt, thuê ngoài",
  //                 enabled: true,
  //                 onFocusChange: (isFocus) {
  //                   if (!isFocus) {}
  //                 },
  //                 onChanged: (value) {},
  //                 hintText: "1.000.000",
  //                 inputFormatters: [
  //                   ThousandsFormatter(allowFraction: true),
  //                 ],
  //               ),
  //               const SizedBox(
  //                 height: 4,
  //               ),
  //               Padding(
  //                 padding: const EdgeInsets.symmetric(
  //                   horizontal: 16,
  //                 ),
  //                 child: Text(
  //                   "2.5% doanh thu <= CPNC <= 9.0% doanh thu",
  //                   style: UITextStyle.body2Regular.copyWith(
  //                     color: BaseColors.textSubtitle,
  //                     fontSize: 13,
  //                   ),
  //                 ),
  //               ),
  //               const SizedBox(
  //                 height: 16,
  //               ),
  //               TextFieldWidget(
  //                 controller: collectedMoneyController,
  //                 labelText: "Chi phí nhân công thuê ngoài trước VAT",
  //                 enabled: true,
  //                 onFocusChange: (isFocus) {
  //                   if (!isFocus) {}
  //                 },
  //                 onChanged: (value) {},
  //                 hintText: "1.000.000",
  //                 inputFormatters: [
  //                   ThousandsFormatter(allowFraction: true),
  //                 ],
  //               ),
  //               const SizedBox(
  //                 height: 4,
  //               ),
  //               const SizedBox(
  //                 height: 16,
  //               ),
  //               TextFieldWidget(
  //                 controller: collectedMoneyController,
  //                 labelText: "Chi phí vật tư lắp đặt",
  //                 enabled: true,
  //                 onFocusChange: (isFocus) {
  //                   if (!isFocus) {}
  //                 },
  //                 onChanged: (value) {},
  //                 hintText: "1.000.000",
  //                 inputFormatters: [
  //                   ThousandsFormatter(allowFraction: true),
  //                 ],
  //               ),
  //               const SizedBox(
  //                 height: 16,
  //               ),
  //               TextFieldWidget(
  //                 controller: collectedMoneyController,
  //                 labelText: "Chi phí vận chuyển trước VAT",
  //                 enabled: true,
  //                 onFocusChange: (isFocus) {
  //                   if (!isFocus) {}
  //                 },
  //                 onChanged: (value) {},
  //                 hintText: "1.000.000",
  //                 inputFormatters: [
  //                   ThousandsFormatter(allowFraction: true),
  //                 ],
  //               ),
  //               const SizedBox(
  //                 height: 16,
  //               ),
  //               TextFieldWidget(
  //                 controller: collectedMoneyController,
  //                 labelText: "Chi phí tiếp khách trước VAT",
  //                 enabled: true,
  //                 onFocusChange: (isFocus) {
  //                   if (!isFocus) {}
  //                 },
  //                 onChanged: (value) {},
  //                 hintText: "1.000.000",
  //                 inputFormatters: [
  //                   ThousandsFormatter(allowFraction: true),
  //                 ],
  //               ),
  //               const SizedBox(
  //                 height: 16,
  //               ),
  //             ],
  //           )),
  //     ],
  //   );
  // }

  changePaymentType() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Thay đổi hình thức thanh toán",
      height: MediaQuery.of(context).size.height * 0.7,
      child: AioChangePaymentTypeView(
          contractDetail: state.contractDetail,
          isViettelPay: state.isViettelPay,
          backFunction: () {
            context.pop();
          }),
    );
  }

  showUpdateReasonExpired({
    required BuildContext context,
    required AioContractDetailPerformerState? state,
  }) async {
    List<String>? reasons = await ref
        .read(aioContractDetailPerformerProvider.notifier)
        .getReasonExpired();

    if (!context.mounted) return;
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Chọn nguyên nhân quá hạn",
      height: MediaQuery.of(context).size.height * 0.7,
      child: Expanded(
        child: ListView.builder(
          shrinkWrap: true,
          physics: const AlwaysScrollableScrollPhysics(),
          itemCount: (reasons ?? []).length,
          itemBuilder: (context, index) {
            var item = reasons?[index];
            return InkWell(
              onTap: () {
                ref
                    .read(aioContractDetailPerformerProvider.notifier)
                    .selectReason(item ?? "");
                AppDialog.showDialogCenter(
                  context,
                  message: "Cập nhật nguyên nhân quá hạn thành công",
                  status: DialogStatus.success,
                );
                context.pop();
              },
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.start,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Expanded(
                          child: Text(
                            item ?? "",
                            style: UITextStyle.body1Regular.copyWith(
                              color:
                                  state?.contractDetail?.reasonOutOfDate == item
                                      ? BaseColors.primary
                                      : BaseColors.textBody,
                            ),
                          ),
                        ),
                        if (state?.contractDetail?.reasonOutOfDate == item) ...[
                          MyAssets.icons.iconCheckedS24.svg(),
                        ],
                      ],
                    ),
                    const SizedBox(
                      height: 16,
                    ),
                    DividerWidget(
                      height: 1,
                      color: BaseColors.backgroundGray,
                    ),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  deleteCancelOrder(
    AioContractDetailPerformerState state, {
    required BuildContext context,
  }) async {
    AioContractResponseEntity? rs = await ref
        .read(aioContractDetailPerformerProvider.notifier)
        .deleteRequestCancel(
          body: AioRequestCancelBody(
            aioCancelOrderId: state.contractInfo?.aioCancelOrderId,
            updatedUser: GlobalData.instance.userInfo?.sysUserId ??
                GlobalData.instance.userInfo?.userId,
          ),
        );

    if (!context.mounted) return;
    if (rs?.resultInfo?.status == BaseConstant.ok) {
      AppDialog.showDialogCenter(
        context,
        message: "Xóa đề xuất hủy thành công",
        status: DialogStatus.success,
      );
      widget.arguments.backFunction?.call();
      context.pop(true);
    } else {
      AppDialog.showDialogCenter(
        context,
        message: "Xóa đề xuất hủy thất bại",
        status: DialogStatus.error,
      );
    }
  }

  void getInvoice(
    AioContractDetailPerformerState state, {
    required BuildContext context,
  }) async {
    AioInvoiceEntity? aioInvoice = await ref
        .read(aioContractDetailPerformerProvider.notifier)
        .getDataForInvoice(
          contractId: state.contractDetail?.contractId,
        );

    List<AioInvoiceItemEntity>? listInvoiceItem =
        aioInvoice?.aioInvoiceItemDTOS ?? [];

    List<BillDetailItemEntity>? listItem = listInvoiceItem
        .map((it) => BillDetailItemEntity(
              taxPercent: it.taxPercent,
              name: it.goodsName,
              goodsId: it.goodsId,
              goodsCode: it.goodsCode,
              goodsName: it.goodsName,
              quantity: it.quantity,
              price: it.price,
              goodsUnitName: it.goodsUnitName,
              amountBeforeTax: it.amountBeforeTax,
              preTaxAmount: it.preTaxAmount,
              amount: it.amount,
              packageId: it.packageId,
            ))
        .toList();

    if (!context.mounted) return;
    context.push(
      RouterPaths.billUpdate,
      extra: BillUpdateArguments(
          isFromAioContract: true,
          data: BillDetailEntity(
            orderCode: state.contractDetail?.contractCode,
            totalMoney: state.contractDetail?.contractAmount,
            address: state.contractDetail?.customerAddress,
            customerName: state.contractDetail?.customerName,
            items: listItem,
          ),
          onSaveData: (billDetailEntity) {
            ref.read(aioContractDetailPerformerProvider.notifier).updateInvoice(
                  invoice: billDetailEntity.toAioInvoiceEntity(),
                  listItem: listItem,
                );
          }),
    );
  }

  void saveDataInvoice(callBackData) {
    List<AioInvoiceItemEntity> items = [];
    // Danh sách hàng hóa
    for (int i = 0; i < (callBackData.items ?? []).length; i++) {
      var itTmp = callBackData.items?[i];
      AioInvoiceItemEntity it = AioInvoiceItemEntity(
        goodsName: itTmp?.name ?? itTmp?.goodsName,
        goodsCode: itTmp?.goodsCode,
        goodsId: itTmp?.goodsId,
        quantity: itTmp?.quantity,
        amount: itTmp?.amount,
        amountBeforeTax: itTmp?.amountBeforeTax,
        preTaxAmount: itTmp?.preTaxAmount,
        price: itTmp?.price,
      );
      items.add(it);
    }
    // Danh sách ghi chú
    for (int i = 0; i < (callBackData.notes ?? []).length; i++) {
      var itTmp = callBackData.notes?[i];
      AioInvoiceItemEntity it = AioInvoiceItemEntity(
        goodsName: itTmp,
      );
      items.add(it);
    }
    // Danh sách coupon Todo: Confirm lại với Thúy để tạo được model mẫu
    // for (int i = 0; i < (callBackData.coupons ?? []).length; i++) {
    //   var itTmp = callBackData.coupons?[i];
    //   AioInvoiceItemEntity it = AioInvoiceItemEntity(
    //     goodsName: itTmp,
    //   );
    //   items.add(it);
    // }

    AioInvoiceEntity invoice = AioInvoiceEntity(
        address: callBackData.address,
        bankAccount: callBackData.bankAccount,
        bankName: callBackData.bankName,
        companyName: callBackData.companyName,
        customerName: callBackData.customerName,
        paymentMethod: callBackData.paymentMethodStr,
        email: callBackData.email,
        description: callBackData.description,
        taxCode: callBackData.taxCode,
        aioInvoiceItemDTOS: items);
    ref.read(aioContractDetailPerformerProvider.notifier).updateInvoice(
          invoice: invoice,
        );
  }

  @override
  void dispose() {
    collectedMoneyController.dispose();
    super.dispose();
  }

  selectSupply({
    required AioContractDetailPerformerState state,
    required List<int?> listGoodIds,
    bool? isSerial,
    bool? disableCheckbox,
    int? indexSupply,
  }) {
    context.push(
      RouterPaths.aioCheckInventory,
      extra: AioCheckInventoryArguments(
          contractId: state.contractDetail?.contractId,
          listGoodIds: listGoodIds,
          disableCheckbox: disableCheckbox,
          listPackageGoods: state.listPackageGoods,
          confirmSelectItem: (data) {
            if (isSerial == true) {
              ref
                  .read(aioContractDetailPerformerProvider.notifier)
                  .updateListSerial(data: data, indexSupply: indexSupply!);
            } else {
              ref
                  .read(aioContractDetailPerformerProvider.notifier)
                  .updateListPackageGoodSelected(data);
            }
          }),
    );
  }

  Widget _buildPackage({
    required AioPackageEntity package,
    required int index,
  }) {
    var state = ref.watch(aioContractDetailPerformerProvider);
    return Slidable(
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        extentRatio: 0.2,
        children: <Widget>[
          const SizedBox(
            width: 8,
          ),
          CustomSlidableAction(
            backgroundColor: BaseColors.primary,
            foregroundColor: Colors.yellow,
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                MyAssets.icons.iconWhiteTrash.svg(),
                const SizedBox(height: 8),
                Text(
                  "Xoá",
                  style: UITextStyle.body2SemiBold.copyWith(
                    color: BaseColors.backgroundWhite,
                  ),
                ),
              ],
            ),
            onPressed: (context) {
              confirmDelete(
                package: package,
                index: index,
              );
            },
          )
        ],
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.fromLTRB(0, 14, 14, 14),
            child: Text(
              "${index + 1}.",
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 16,
                ),
                Row(
                  children: <Widget>[
                    Expanded(
                      child: Column(
                        mainAxisSize: MainAxisSize.min,
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Text(
                            package.aioPackageName ?? '',
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: <Widget>[
                              Expanded(
                                child: Row(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: <Widget>[
                                    Text(
                                      StringUtils.formatMoney(
                                          package.price ?? 0),
                                      style: UITextStyle.body2Medium.copyWith(
                                        color: BaseColors.primary,
                                      ),
                                    ),
                                    Text(
                                      '/gói',
                                      style:
                                          UITextStyle.caption1Medium.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (package.status != 3) ...[
                                PlusAndMinusWidget(
                                  quantity: package.quantity ?? 1,
                                  onPlus: (value) {
                                    ref
                                        .read(aioContractDetailPerformerProvider
                                            .notifier)
                                        .changeQuantityPackage(
                                          index: index,
                                          quantity: value.toInt(),
                                        );
                                  },
                                  onMinus: (value) {
                                    if (value < 1) {
                                      return AppDialog.showDialogConfirm(
                                        context,
                                        title: "Xác nhận",
                                        message:
                                            'Bạn có chắc chắn muốn xoá gói "${package.aioPackageName}"?',
                                        buttonNameConfirm: "Xoá",
                                        onConfirmAction: () async {
                                          ref
                                              .read(
                                                  aioContractDetailPerformerProvider
                                                      .notifier)
                                              .removePackage(
                                                index: index,
                                                package: package,
                                              );
                                        },
                                        buttonNameCancel: "Hủy bỏ",
                                        onCancelAction: () {
                                          ref
                                              .read(
                                                  aioContractDetailPerformerProvider
                                                      .notifier)
                                              .changeQuantityPackage(
                                                index: index,
                                                quantity: 1,
                                              );
                                        },
                                      );
                                    } else {
                                      ref
                                          .read(
                                              aioContractDetailPerformerProvider
                                                  .notifier)
                                          .changeQuantityPackage(
                                            index: index,
                                            quantity: value.toInt(),
                                          );
                                    }
                                  },
                                ),
                              ],
                            ],
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                if (package.serviceItem != null) ...[
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: BaseColors.backgroundGray,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      children: [
                        Row(
                          children: [
                            MyAssets.icons.iconRepairMaintain.svg(
                              colorFilter: ColorFilter.mode(
                                BaseColors.textSubtitle,
                                BlendMode.srcIn,
                              ),
                            ),
                            const SizedBox(
                              width: 8,
                            ),
                            Expanded(
                              child: Text(
                                package.serviceItem?.displayName ?? '',
                                style: UITextStyle.body2Medium.copyWith(
                                  color: BaseColors.textLabel,
                                ),
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(
                          height: 6,
                        ),
                        Row(
                          children: <Widget>[
                            Expanded(
                              child: Row(
                                children: [
                                  const SizedBox(
                                    width: 22,
                                  ),
                                  Text(
                                    StringUtils.formatMoney(
                                        package.serviceItem?.price ?? 0),
                                    style: UITextStyle.body2Medium.copyWith(
                                      color: BaseColors.primary,
                                    ),
                                  ),
                                  Text(
                                    '/gói',
                                    style: UITextStyle.body2Medium.copyWith(
                                      color: BaseColors.textSubtitle,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Text(
                              'x${(package.quantity ?? 1) * (package.serviceItem?.amount ?? 0)}',
                              style: UITextStyle.body2Medium.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                          ],
                        ),
                        if (state.contractInfo?.xcare != 1) ...[
                          Align(
                            alignment: Alignment.centerRight,
                            child: InkWell(
                              onTap: () {
                                context.push(
                                  RouterPaths.aioAddService,
                                  extra: AddServiceArguments(
                                    onSelected: (data) {
                                      // ref
                                      //     .read(
                                      //     aioContractCreateProvider.notifier)
                                      //     .updatePackagesService(data);
                                      ref
                                          .read(
                                              aioContractDetailPerformerProvider
                                                  .notifier)
                                          .calculatorTotalMoney();
                                    },
                                    packagesSelected: state.packages,
                                    itemPackagesSelected:
                                        state.packages?[index],
                                  ),
                                );
                              },
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  MyAssets.icons.iconExchange.svg(),
                                  const SizedBox(
                                    width: 4,
                                  ),
                                  RichText(
                                    text: TextSpan(
                                      text: 'Thay đổi',
                                      style:
                                          UITextStyle.caption1SemiBold.copyWith(
                                        color: BaseColors.primary,
                                      ),
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ],
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                ],
                if (state.contractInfo?.xcare == 1 &&
                    package.serviceItem == null) ...[
                  Align(
                    alignment: Alignment.centerRight,
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        InkWell(
                          onTap: () {
                            context.push(
                              RouterPaths.aioAddService,
                              extra: AddServiceArguments(
                                onSelected: (data) {
                                  // ref
                                  //     .read(aioContractCreateProvider.notifier)
                                  //     .updatePackagesService(data);
                                  ref
                                      .read(aioContractDetailPerformerProvider
                                          .notifier)
                                      .calculatorTotalMoney();
                                },
                                packagesSelected: state.packages,
                                itemPackagesSelected: state.packages?[index],
                              ),
                            );
                          },
                          child: RichText(
                            text: TextSpan(
                              text: 'Chọn dịch vụ HS',
                              style: UITextStyle.caption1SemiBold.copyWith(
                                color: BaseColors.primary,
                              ),
                            ),
                          ),
                        ),
                        MyAssets.icons.arrowRight.svg(
                          colorFilter: ColorFilter.mode(
                            BaseColors.primary,
                            BlendMode.srcIn,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 16),
                ],
                Align(
                  alignment: Alignment.centerRight,
                  child: RichText(
                    text: TextSpan(
                      text: 'Thành tiền: ',
                      style: UITextStyle.caption1Medium.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                      children: <TextSpan>[
                        TextSpan(
                          text: StringUtils.formatMoney(
                              (package.quantity ?? 1) * (package.price ?? 0) +
                                  (package.quantity ?? 0) *
                                      (package.serviceItem?.price ?? 0) *
                                      (package.serviceItem?.amount ?? 0)),
                          style: UITextStyle.caption1SemiBold.copyWith(
                            color: BaseColors.textLabel,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(
                  height: 16,
                ),
                DividerWidget(
                  height: 1,
                  color: BaseColors.backgroundGray,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget buildRowData(String label, String? content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 128,
            child: Text(
              "$label: ",
              style: UITextStyle.body2Regular
                  .copyWith(color: BaseColors.textSubtitle),
            ),
          ),
          Expanded(
            child: Text(
              content ?? "Chưa cập nhật",
              style: UITextStyle.body2Regular.copyWith(
                color: content == null
                    ? BaseColors.textPlaceholder
                    : BaseColors.textTitle,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildRowDataPayment(String label, String? content) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 200,
            child: Text(
              "$label: ",
              style: UITextStyle.body2Regular
                  .copyWith(color: BaseColors.textSubtitle),
            ),
          ),
          Expanded(
            child: Text(
              textAlign: TextAlign.right,
              content ?? "Chưa cập nhật",
              style: UITextStyle.body2Medium.copyWith(
                color: content == null
                    ? BaseColors.textPlaceholder
                    : BaseColors.textTitle,
              ),
            ),
          ),
        ],
      ),
    );
  }

  List<InternalStaffEntity> convertStringToUserList(String inputString) {
    List<InternalStaffEntity> userList = [];
    List<String> parts = inputString.split(';');
    for (var part in parts) {
      List<String> userProperties = part.split('~');
      if (userProperties.length == 3) {
        String employeeCode = userProperties[0];
        String fullName = userProperties[1];
        int percent = int.parse(userProperties[2].replaceAll(".0", ""));
        userList.add(InternalStaffEntity(
          employeeCode: employeeCode,
          fullName: fullName,
          percentBonus: percent,
        ));
      }
    }

    return userList;
  }

  void onTakePicture(int index) async {
    await MediaUtils.onTakeImage(
      context: context,
      onSubmitImage: (file) async {
        String base64Image = await fileToBase64(file);
        String fileName = '$index.jpg';
        ref
            .read(aioContractDetailPerformerProvider.notifier)
            .uploadImage(AioImage(
              name: fileName,
              base64String: base64Image,
            ));
      },
    );
  }

  void onTakePictureAcceptance(int index) async {
    await MediaUtils.onTakeImage(
      context: context,
      onSubmitImage: (file) async {
        String base64Image = await fileToBase64(file);
        String fileName = '$index.jpg';
        ref
            .read(aioContractDetailPerformerProvider.notifier)
            .uploadImageAcceptance(AioImage(
              name: fileName,
              base64String: base64Image,
            ));
      },
    );
  }

  Future<String> fileToBase64(File file) async {
    Uint8List bytes = await file.readAsBytes();
    return base64Encode(bytes);
  }

  editContract(
    AioContractDetailPerformerState state, {
    required BuildContext context,
  }) async {
    var param = state.contractInfo;
    param?.listImage = state.listImage;
    if ((state.packages ?? []).isEmpty) {
      AppDialog.showDialogCenter(
        context,
        message: "Danh sách gói bán hàng không được để trống",
        status: DialogStatus.error,
      );
      return;
    }
    param?.detailDTOS =
        state.packages?.map((it) => it.toAioContractInfoItemEntity()).toList();
    param?.deleteDTOS = state.packagesDeleted
            ?.map((it) => it.toAioContractInfoItemEntity())
            .toList() ??
        [];
    param?.listExplanation = [];
    AioContractResponseEntity? result = await ref
        .read(aioContractDetailPerformerProvider.notifier)
        .editContractOfPerformer(param!);

    if (!context.mounted) return;
    if (result?.resultInfo?.status == BaseConstant.ok) {
      AppDialog.showDialogCenter(
        context,
        message: "Cập nhật thành công",
        status: DialogStatus.success,
      );
    } else {
      AppDialog.showDialogCenter(
        context,
        message: result?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
  }

  SysUserRequest getUserInfo() {
    return SysUserRequest(
      authenticationInfo: AuthenticationInfo(
        type: 0,
        username: GlobalData.instance.userInfo?.username,
      ),
      sysUserId: GlobalData.instance.userInfo?.sysUserId,
      departmentId: GlobalData.instance.userInfo?.sysGroupId,
      flag: 0,
      isTtqh: false,
      ft: false,
    );
  }

  void confirmDelete({
    required AioPackageEntity package,
    required int index,
  }) {
    if (package.status == 3) {
      AppDialog.showDialogCenter(
        context,
        message: "Không được xóa gói đã hoàn thành nghiệm thu!",
        status: DialogStatus.error,
      );
      return;
    }
    AppDialog.showDialogConfirm(
      context,
      title: "Xác nhận",
      message: 'Bạn có chắc chắn muốn xoá gói "${package.aioPackageName}"?',
      buttonNameConfirm: "Xoá",
      onConfirmAction: () async {
        ref.read(aioContractDetailPerformerProvider.notifier).removePackage(
              index: index,
              package: package,
            );
      },
      buttonNameCancel: "Hủy bỏ",
      onCancelAction: () {
        ref
            .read(aioContractDetailPerformerProvider.notifier)
            .changeQuantityPackage(
              index: index,
              quantity: 1,
            );
      },
    );
  }

  Widget _buildLogInventoryContract() {
    var state = ref.watch(aioContractDetailPerformerProvider);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 16,
          ),
          child: InkWell(
            onTap: () {
              context.push(
                RouterPaths.listStockGoodPage,
                extra: ListStockGoodArguments(
                  contractId: state.contractDetail?.contractId,
                ),
              );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    'Tồn kho',
                    style: UITextStyle.body1SemiBold,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      'Xem thông tin đảm bảo',
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                    ),
                    MyAssets.icons.iconArrowRightS18.svg(),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  String checkObjectCus(state) {
    if (state.contractInfo?.diplomaticCustomer == "normal") {
      return "Khách hàng thường";
    }
    if (state.contractInfo?.diplomaticCustomer == "communication") {
      return "Khách hàng ngoại giao";
    }
    return "";
  }
}
