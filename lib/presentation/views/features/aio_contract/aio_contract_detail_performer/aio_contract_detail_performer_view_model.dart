import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_detail_request.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_info_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_invoice_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_supply_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_contract_detail_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_request_cancel_body.dart';
import 'package:vcc/domain/entities/order/bill_detail_item_entity.dart';
import 'package:vcc/domain/entities/order/drop_down_list_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_confirm_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_agency/enum_invoice_type.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_digital_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_update_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_image.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';
import 'package:vcc/extensions/date_time_extensions.dart';

part 'aio_contract_detail_performer_state.dart';

final aioContractDetailPerformerProvider = StateNotifierProvider.autoDispose<
    AioContractDetailPerformerViewModel, AioContractDetailPerformerState>(
  (ref) => AioContractDetailPerformerViewModel(ref: ref),
);

class AioContractDetailPerformerViewModel
    extends StateNotifier<AioContractDetailPerformerState> {
  final Ref ref;

  AioContractDetailPerformerViewModel({
    required this.ref,
  }) : super(AioContractDetailPerformerState());

  Future<void> getData({
    AioContractEntity? aioContract,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<AioOrderRepository>().getFullContractInfo(
        param: AioContractDetailRequest(
          data: aioContract?.contractId,
          sysUserRequest: SysUserRequest(
            sysUserId: GlobalData.instance.userInfo?.sysUserId,
          ),
        ),
      );
      await result?.when(
        success: (data) async {
          var packages = (data?.detailDTOS ?? [])
              .map((it) => it.toAioPackageEntity())
              .toList();

          for (int i = 0; i < packages.length; i++) {
            AioPackageEntity package = packages[i];
            var listItemService = (data?.hsServiceList ?? [])
                .where((it) => it.aioPackageId == package.aioPackageId)
                .toList();
            if (listItemService.isNotEmpty) {
              packages[i].serviceItem = listItemService[0];
            }
          }
          var listImage =
              data?.listImage?.where((it) => it.filePath != null).toList();

          state = state.copyWith(
            loadStatus: LoadStatus.success,
            contractInfo: data,
            packages: packages,
            listImage: listImage,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<List<AioInvoiceItemEntity>?> dataOrderRequestForContract({
    required AioContractEntity param,
  }) async {
    try {
      state = state.copyWith(
        loadStatus: LoadStatus.loading,
      );
      final result =
          await appLocator<AioOrderRepository>().dataOrderRequestForContract(
        param: param,
      );

      List<AioInvoiceItemEntity>? rs = [];

      await result?.when(
        success: (data) async {
          if (data.data != null) {
            rs = data.data;
          }
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          rs = null;
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
        },
      );

      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  void changeIsViettelPay(AioConfirmType type) {
    state = state.copyWith(
      isViettelPay: type,
    );
  }

  Future<List<String>?> getReasonExpired() async {
    try {
      final result = await appLocator<AioOrderRepository>().getReasonExpired();
      List<String>? list = [];
      await result?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            list = data.data;
          }
        },
        error: (err) async {
          list = null;
        },
      );

      return list;
    } catch (error) {
      return null;
    }
  }

  Future<List<DropDownListEntity>?> getReasonReject() async {
    try {
      final result =
          await appLocator<AioOrderRepository>().getListAppParamByParType(
        params: DropDownListEntity(
          parType: "REASON_REJECT_CONTRACT",
        ),
      );
      List<DropDownListEntity>? list = [];
      await result?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            list = data.data;
          }
        },
        error: (err) async {
          list = null;
        },
      );

      return list;
    } catch (error) {
      return null;
    }
  }

  Future<AioContractResponseEntity?> deleteRequestCancel({
    required AioRequestCancelBody body,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      AioContractResponseEntity? rs;
      final result = await appLocator<AioOrderRepository>()
          .deleteCancelOrder(params: body);
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
          rs = data;
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );
      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  Future<AioContractResponseEntity?> startContractService() async {
    try {
      final result =
          await appLocator<AioOrderRepository>().startContractService(
              param: AioContractParam(
        sysUserRequest: SysUserRequest(
          sysUserId: GlobalData.instance.userInfo?.sysUserId ??
              GlobalData.instance.userInfo?.userId,
        ),
        aioContractDTO: AioContractEntity(
          contractId: state.contractDetail?.contractId,
          contractDetailId: state.contractDetail?.contractDetailId,
        ),
      ));
      AioContractResponseEntity? response;
      await result?.when(
        success: (data) async {
          response = data;
        },
        error: (err) async {
          response = null;
        },
      );

      return response;
    } catch (error) {
      return null;
    }
  }

  Future<AioContractResponseEntity?> editContractOfPerformer(
      AioContractInfoEntity params) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      for (var element in (params.listImage ?? [])) {
        element.type = "101";
      }
      final result =
          await appLocator<AioOrderRepository>().editContractOfPerformer(
              params: AioContractUpdateParam(
        data: params,
        sysUserRequest: SysUserRequest(
          sysUserId: GlobalData.instance.userInfo?.sysUserId,
          departmentId: GlobalData.instance.userInfo?.sysGroupId,
        ),
      ));
      AioContractResponseEntity? response;
      await result?.when(
        success: (data) async {
          response = data;
        },
        error: (err) async {
          response = null;
        },
      );
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return response;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  void changeIsBill(AioConfirmType type) {
    state = state.copyWith(
      isBillType: type,
    );
  }

  void updateListPackageGoodSelected(List<AioSupplyEntity>? data) {
    state = state.copyWith(
      listPackageGoodSelected: data,
    );
  }

  void updateListSerial({
    List<AioSupplyEntity>? data,
    required int indexSupply,
  }) {
    var listPackageGoods = state.listPackageGoods;
    listPackageGoods?[indexSupply].typeSerial =
        listPackageGoods[indexSupply].goodsIsSerial;
    listPackageGoods?[indexSupply].lstSerial = (data ?? []).map((it) {
      return it.serialNumber;
    }).toList();
    state = state.copyWith(
      listPackageGoods: listPackageGoods,
    );
  }

  void deleteSerial({
    required int indexSupply,
    required String serial,
  }) {
    var listPackageGoods = state.listPackageGoods;
    listPackageGoods?[indexSupply].lstSerial =
        (listPackageGoods[indexSupply].lstSerial ?? [])
            .where((it) => it != serial)
            .toList();
    state = state.copyWith(
      listPackageGoods: listPackageGoods,
    );
  }

  void selectReason(String reason) async {
    var contractDetail = state.contractDetail;
    contractDetail?.reasonOutOfDate = reason;
    state = state.copyWith(
      contractDetail: contractDetail,
    );
    await appLocator<AioOrderRepository>().updateContractHold(
      param: AioContractParam(
        aioContractDTO: AioContractEntity(
          contractId: state.contractDetail?.contractId,
          reasonOutOfDate: reason,
          subAction: 1,
        ),
      ),
    );
  }

  void selectReasonReject(DropDownListEntity? reason) async {}

  void uploadImage(AioImage image) async {
    state = state.copyWith(
      uploadImageStatus: LoadStatus.loading,
    );
    List<AioImage>? listImages = state.listImage ?? [];
    listImages.add(image);
    state = state.copyWith(
      listImage: listImages,
      uploadImageStatus: LoadStatus.success,
    );
  }

  void changeQuantityPackage({
    AioPackageEntity? package,
    int? index,
    int? quantity,
  }) async {
    var listPackages = state.packages ?? [];

    var package = listPackages[index ?? 0];
    if (quantity == 0) {
      return;
    }

    if ((quantity ?? 1) < (package.minNumber ?? 1)) {
      quantity = (package.minNumber ?? 1);
    }

    // if ((quantity ?? 1) > (package.maxNumber ?? 1)) {
    //   quantity = (package.maxNumber ?? 1);
    // }

    package.quantity = quantity;

    for (int i = 0; i < listPackages.length; i++) {
      final item = listPackages[i];
      if (item.aioPackageId == package.aioPackageId) {
        listPackages[i] = item.copyWith(
          quantity: quantity,
        );
        break;
      }
    }

    state = state.copyWith(
      packages: listPackages,
    );
    calculatorTotalMoney();
  }

  void removePackage({
    required int index,
    required AioPackageEntity package,
  }) {
    var listSelected = state.packages ?? [];
    var packages = state.packages ?? [];
    packages[index].isSelect = false;

    for (int i = 0; i < listSelected.length; i++) {
      final item = listSelected[i];
      if (item.aioPackageId == package.aioPackageId) {
        listSelected.remove(item);
        break;
      }
    }

    List<AioPackageEntity>? packagesDeleted = state.packagesDeleted ?? [];
    packagesDeleted.add(package);

    state = state.copyWith(
      packages: listSelected,
      packagesDeleted: packagesDeleted,
    );
    calculatorTotalMoney();
  }

  void calculatorTotalMoney() {
    num totalMoney = 0;
    if ((state.packages ?? []).isNotEmpty) {
      for (int i = 0; i < state.packages!.length; i++) {
        final item = state.packages![i];
        var money = (item.quantity ?? 1) * (item.price ?? 0) +
            (item.quantity ?? 1) *
                (item.serviceItem?.price ?? 0) *
                (item.serviceItem?.amount ?? 0);
        totalMoney = totalMoney + money;
      }

      var contractInfo = state.contractInfo;
      contractInfo?.contractAmount = totalMoney;
      state = state.copyWith(
        contractInfo: contractInfo,
      );
    } else {
      var contractInfo = state.contractInfo;
      contractInfo?.contractAmount = 0;
      state = state.copyWith(
        contractInfo: contractInfo,
      );
    }
  }

  void uploadImageAcceptance(AioImage image) async {
    state = state.copyWith(
      uploadImageAcceptanceStatus: LoadStatus.loading,
    );
    List<AioImage>? listImages = state.listImageAcceptanceContract ?? [];
    listImages.add(image);
    state = state.copyWith(
      listImageAcceptanceContract: listImages,
      uploadImageAcceptanceStatus: LoadStatus.success,
    );
  }

  void deleteImage(int index) async {
    List<AioImage> listImage = state.listImage ?? [];
    listImage.removeAt(index);

    state = state.copyWith(
      listImage: listImage,
    );
  }

  void deleteImageAcceptance(int index) async {
    List<AioImage> listImage = state.listImageAcceptanceContract ?? [];
    listImage.removeAt(index);

    state = state.copyWith(
      listImageAcceptanceContract: listImage,
    );
  }

  void selectCancelDate(DateTime date) async {
    var contractDetail = state.contractDetail;
    contractDetail?.cancelDate = date;
    state = state.copyWith(
      contractDetail: contractDetail,
    );
    await appLocator<AioOrderRepository>().updateContractHold(
      param: AioContractParam(
        aioContractDTO: AioContractEntity(
          contractId: state.contractDetail?.contractId,
          appointmentDate: date.display(
            format: DateTimeFormater.hotOrderTimeFormat,
          ),
          subAction: 2,
        ),
      ),
    );
  }

  Future<AioInvoiceResponseEntity?> saveInvoice(AioInvoiceEntity? param) async {
    AioInvoiceResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result =
        await appLocator<AioOrderRepository>().saveInvoice(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  Future<AioContractResponseEntity?> getUrlToRequestSubmitMoney(
      AioContractDigitalParam? param) async {
    AioContractResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result = await appLocator<AioOrderRepository>()
        .getUrlToRequestSubmitMoney(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  Future<AioContractResponseEntity?> updateStatusAndPerformerContract(
      AioContractEntity? param) async {
    AioContractResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result = await appLocator<AioOrderRepository>()
        .updateStatusAndPerformerContract(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  Future<AioInvoiceResponseEntity?> signDigitalContract(
      AioContractDigitalParam? param) async {
    AioInvoiceResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result = await appLocator<AioOrderRepository>()
        .signDigitalContract(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  void deletePerformer(int index) {
    var listUser = state.listPerformer!;
    listUser.removeAt(index);
    state = state.copyWith(
      listPerformer: listUser,
    );
  }

  void onChangePerformer(List<InternalStaffEntity> listUser) {
    List<InternalStaffEntity> listPerformer = [];
    listPerformer.add(InternalStaffEntity(
      sysUserId: GlobalData.instance.userInfo?.sysUserId ??
          GlobalData.instance.userInfo?.userId,
      employeeCode: GlobalData.instance.userInfo?.employeeCode ??
          GlobalData.instance.userInfo?.username,
      fullName: GlobalData.instance.userInfo?.fullName,
      percentBonus: 100,
    ));
    listPerformer.addAll(listUser);
    state = state.copyWith(
      listPerformer: listPerformer,
    );
  }

  void changePerformerCreated(int percent) {
    List<InternalStaffEntity> listPerformer = state.listPerformer ?? [];
    listPerformer[0] = InternalStaffEntity(
      sysUserId: GlobalData.instance.userInfo?.sysUserId ??
          GlobalData.instance.userInfo?.userId,
      employeeCode: GlobalData.instance.userInfo?.employeeCode ??
          GlobalData.instance.userInfo?.username,
      fullName: GlobalData.instance.userInfo?.fullName,
      percentBonus: percent,
    );
    state = state.copyWith(
      listPerformer: listPerformer,
    );
  }

  Future<List<AioInvoiceItemEntity>?> getListInvoiceItem({
    required List<int?> listPackageId,
  }) async {
    try {
      final result = await appLocator<AioOrderRepository>().getListInvoiceItem(
        listPackageId: listPackageId,
      );
      List<AioInvoiceItemEntity>? listInvoiceItem = [];
      await result?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            listInvoiceItem = data.data;
          }
        },
        error: (err) async {
          listInvoiceItem = null;
        },
      );

      return listInvoiceItem;
    } catch (error) {
      return null;
    }
  }

  Future<AioInvoiceEntity?> getDataForInvoice({
    int? contractId,
  }) async {
    try {
      final result = await appLocator<AioOrderRepository>().getDataForInvoice(
        param: AioContractEntity(
          contractId: contractId,
          invoiceType: EnumInvoiceType.hasInvoice.keyToServer,
        ),
      );
      AioInvoiceEntity? rs;
      await result?.when(
        success: (data) async {
          rs = data.data;
        },
        error: (err) async {
          rs = null;
        },
      );

      return rs;
    } catch (error) {
      return null;
    }
  }

  void changePerformerPercent({
    required int percent,
    required int index,
  }) {
    List<InternalStaffEntity> listPerformer = state.listPerformer ?? [];
    listPerformer[index].percentBonus = percent;
    state = state.copyWith(
      listPerformer: listPerformer,
    );
  }

  void updateInvoice({
    AioInvoiceEntity? invoice,
    List<BillDetailItemEntity>? listItem,
  }) async {
    state = state.copyWith(
      invoiceDto: invoice,
      listBillDetailItemEntity: listItem,
    );
  }
}
