import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_status_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/work_menu_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_contract_package/aio_contract_package_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/deBouncer.dart';
import 'package:vcc/utils/string_utils.dart';

import 'aio_contract_detail_performer/aio_contract_detail_performer_page.dart';
import 'aio_contract_view_model.dart';

class AioContractArguments {
  final String? data;

  AioContractArguments({
    this.data,
  });
}

class AioContractPage extends StatefulHookConsumerWidget {
  final AioContractArguments? arguments;

  const AioContractPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<AioContractPage> createState() => _AioContractPageState();
}

class _AioContractPageState extends ConsumerState<AioContractPage>
    with TickerProviderStateMixin {
  late TextEditingController searchController;
  late Debounce<String> deBouncer;

  @override
  void initState() {
    getData();
    searchController = TextEditingController();
    deBouncer = Debounce<String>(
      const Duration(milliseconds: 2000),
      (value) {
        getData(value: value);
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(aioContractProvider);

    PreferredSizeWidget appbarWidget;

    appbarWidget = AppBarCustom(
      title:
          "${widget.arguments?.data != WorkMenuType.contractSale.value ? "Đơn gói giá" : "Danh sách hợp đồng mới tạo"} (${(state.listAioContract ?? []).length} đơn)",
    );

    return LayoutPage(
      appbar: appbarWidget,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildPage(state),
      ),
    );
  }

  Widget _buildPage(AioContractState state) {
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      return Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 16),
            child: SearchTextFieldWidget(
              controller: searchController,
              hintText: 'Tìm kiếm',
              // onChanged: (value) {
              //   deBouncer.value = value;
              // },
              onSubmitted: (value) {
                getData(value: value);
              },
            ),
          ),
          DividerWidget(
            height: 8,
            color: BaseColors.backgroundGray,
          ),
          if ((state.listAioContract ?? []).isEmpty) ...[
            Expanded(
              child: EmptyListWidget(
                title: "Không có dữ liệu",
                onRefresh: refreshData,
              ),
            ),
          ],
          if ((state.listAioContract ?? []).isNotEmpty) ...[
            Expanded(
              child: RefreshIndicatorWidget(
                onRefresh: refreshData,
                child: ListView.builder(
                    shrinkWrap: true,
                    physics: const AlwaysScrollableScrollPhysics(),
                    itemCount: (state.listAioContract ?? []).length,
                    itemBuilder: (context, index) {
                      var item = state.listAioContract![index];
                      return InkWell(
                        onTap: () {
                          if (widget.arguments?.data ==
                              WorkMenuType.contractSale.value) {
                            context.push(
                              RouterPaths.aioContractDetailPerformer,
                              extra: AioContractDetailPerformerArguments(
                                aioContract: item,
                                backFunction: refreshData,
                              ),
                            );
                            return;
                          }
                          if (item.isPaying == 1) {
                            AppDialog.showDialogConfirm(
                              context,
                              title: "Chú ý",
                              message:
                                  "Hiện tại hợp đồng (${item.contractCode}) đang được thực hiện trên một giao dịch khác. Bạn có muốn tiếp tục không ?",
                              buttonNameConfirm: "Xác nhận",
                              onConfirmAction: () async {
                                context.push(
                                  RouterPaths.aioContractPackage,
                                  extra: AioContractPackageArguments(
                                    aioContractEntity: item,
                                    backFunction: refreshData,
                                  ),
                                );
                              },
                            );
                            return;
                          }
                          context.push(
                            RouterPaths.aioContractPackage,
                            extra: AioContractPackageArguments(
                              aioContractEntity: item,
                              backFunction: refreshData,
                            ),
                          );
                        },
                        child: Column(
                          children: [
                            Padding(
                              padding: const EdgeInsets.all(16),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    children: [
                                      Text(
                                        item.contractCode ?? "",
                                        style:
                                            UITextStyle.body2SemiBold.copyWith(
                                          color: BaseColors.textLabel,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 4,
                                      ),
                                      InkWell(
                                        onTap: () {
                                          AppUtils.copyToClipboard(
                                            item.contractCode ?? "",
                                          );
                                        },
                                        child:
                                            MyAssets.icons.copyClipBoard.svg(),
                                      ),
                                    ],
                                  ),
                                  Container(
                                    margin: const EdgeInsets.symmetric(
                                      vertical: 8,
                                    ),
                                    padding: const EdgeInsets.symmetric(
                                      horizontal: 12,
                                      vertical: 3,
                                    ),
                                    decoration: BoxDecoration(
                                      color: checkStatus(item.status).bgColor,
                                      borderRadius: BorderRadius.circular(360),
                                    ),
                                    child: Text(
                                      checkStatus(item.status).display,
                                      style: UITextStyle.body2Medium.copyWith(
                                        color: checkStatus(item.status).color,
                                      ),
                                    ),
                                  ),
                                  if (item.sellerName != null) ...[
                                    const SizedBox(
                                      height: 8,
                                    ),
                                    Text(
                                      'Tạo bởi: ${item.sellerName ?? ''}',
                                      style:
                                          UITextStyle.caption1Regular.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                  ],
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Text(
                                    'Ngày tạo: ${item.createdDate ?? ''}',
                                    style: UITextStyle.caption1Regular.copyWith(
                                      color: BaseColors.textSubtitle,
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  DividerWidget(
                                    height: 1,
                                    color: BaseColors.backgroundGray,
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        'Tên khách hàng: ',
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 8,
                                      ),
                                      Expanded(
                                        child: Text(
                                          item.customerName ?? "",
                                          textAlign: TextAlign.right,
                                          style:
                                              UITextStyle.body2Regular.copyWith(
                                            color: BaseColors.textLabel,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Số điện thoại: ',
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 8,
                                      ),
                                      Expanded(
                                        child: Text(
                                          textAlign: TextAlign.right,
                                          item.customerPhone ?? "",
                                          style:
                                              UITextStyle.body2Regular.copyWith(
                                            color: BaseColors.textLabel,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Text(
                                        'Địa chỉ: ',
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 8,
                                      ),
                                      Expanded(
                                        child: Text(
                                          textAlign: TextAlign.right,
                                          item.customerAddress ?? "",
                                          style:
                                              UITextStyle.body2Regular.copyWith(
                                            color: BaseColors.textLabel,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        'Nhân viên bán hàng: ',
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          item.sellerName ?? "",
                                          textAlign: TextAlign.right,
                                          style:
                                              UITextStyle.body2Regular.copyWith(
                                            color: BaseColors.textLabel,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Row(
                                    children: [
                                      Text(
                                        'Giá trị hợp đồng (VNĐ): ',
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                      Expanded(
                                        child: Text(
                                          StringUtils.formatMoney(
                                              item.contractAmount ?? 0),
                                          textAlign: TextAlign.right,
                                          style:
                                              UITextStyle.body2Regular.copyWith(
                                            color: BaseColors.textLabel,
                                          ),
                                        ),
                                      )
                                    ],
                                  ),
                                ],
                              ),
                            ),
                            DividerWidget(
                              height: 8,
                              color: BaseColors.backgroundGray,
                            ),
                          ],
                        ),
                      );
                    }),
              ),
            ),
          ],
        ],
      );
    }
  }

  Future<void> refreshData() async {
    var state = ref.watch(aioContractProvider);
    String? keySearch = state.keySearch;

    getData(value: keySearch);
  }

  getData({
    String? value,
  }) {
    if (widget.arguments?.data == WorkMenuType.contractSale.value) {
      Future(() {
        ref
            .read(aioContractProvider.notifier)
            .getDataPerformer(keySearch: value);
      });
    } else {
      Future(() {
        ref.read(aioContractProvider.notifier).getData(keySearch: value);
      });
    }
  }

  AioContractStatusType checkStatus(int? status) {
    if (status != null) {
      switch (status) {
        case 0:
          return AioContractStatusType.notPerformStaff;
        case 1:
          return AioContractStatusType.notDone;
        case 2:
          return AioContractStatusType.doing;
        case 3:
          return AioContractStatusType.completed;
        case 4:
          return AioContractStatusType.canceled;
        case 5:
          return AioContractStatusType.suggestSuspension;
        case 6:
          return AioContractStatusType.suggestCanceled;
        case 7:
          return AioContractStatusType.waitingCreateOrder;
        case 8:
          return AioContractStatusType.preparingGoods;
        case 9:
          return AioContractStatusType.waitingDistrict;
        case 10:
          return AioContractStatusType.waitingGoodsBranch;
        default:
          return AioContractStatusType.nan;
      }
    }
    return AioContractStatusType.nan;
  }

  @override
  void dispose() {
    searchController.dispose();
    super.dispose();
  }
}
