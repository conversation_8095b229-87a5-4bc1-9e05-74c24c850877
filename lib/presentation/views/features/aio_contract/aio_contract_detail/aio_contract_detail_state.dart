part of 'aio_contract_detail_view_model.dart';

//ignore: must_be_immutable
class AioContractDetailState extends Equatable {
  final LoadStatus loadStatus;
  final LoadStatus loadStatusEnd;
  final String? message;
  final List<AioContractDetailEntity>? listContractDetail;
  AioContractDetailEntity? contractDetail;
  final List<AioImage>? listImage;
  final List<AioPackageEntity>? listPackageGoods;
  final List<AioSupplyEntity>? listPackageGoodSelected;
  final AioConfirmType isBillType;
  final AioContractEntity? aioContract;
  final List<InternalStaffEntity>? listPerformer;
  AioConfirmType isViettelPay;
  final AioInvoiceEntity? invoiceDto;
  final List<BillDetailItemEntity>? listBillDetailItemEntity;
  final AioContractEntity? contractCost;
  int costCNKT;
  int costNoSalary;
  int tvCostCNKT;
  double percentCode;
  String? noteCostSale;
  String? noteCostInstallationWorker;

  AioContractDetailState({
    this.loadStatus = LoadStatus.initial,
    this.loadStatusEnd = LoadStatus.initial,
    this.message,
    this.listContractDetail,
    this.listPackageGoodSelected,
    this.contractDetail,
    this.listPackageGoods,
    this.isBillType = AioConfirmType.no,
    this.isViettelPay = AioConfirmType.yes,
    this.listImage,
    this.listPerformer,
    this.listBillDetailItemEntity,
    this.invoiceDto,
    this.aioContract,
    this.contractCost,
    this.costCNKT = 0,
    this.costNoSalary = 0,
    this.tvCostCNKT = 0,
    this.percentCode = 1,
    this.noteCostSale,
    this.noteCostInstallationWorker,
  });

  AioContractDetailState copyWith({
    LoadStatus? loadStatus,
    LoadStatus? loadStatusEnd,
    String? message,
    List<AioContractDetailEntity>? listContractDetail,
    List<AioSupplyEntity>? listPackageGoodSelected,
    AioContractDetailEntity? contractDetail,
    List<AioPackageEntity>? listPackageGoods,
    List<AioImage>? listImage,
    AioConfirmType? isBillType,
    AioConfirmType isViettelPay = AioConfirmType.no,
    List<InternalStaffEntity>? listPerformer,
    AioInvoiceEntity? invoiceDto,
    List<BillDetailItemEntity>? listBillDetailItemEntity,
    AioContractEntity? aioContract,
    AioContractEntity? contractCost,
    int? costCNKT,
    int? costNoSalary,
    int? tvCostCNKT,
    double? percentCode,
    String? noteCostSale,
    String? noteCostInstallationWorker,
  }) {
    return AioContractDetailState(
      loadStatus: loadStatus ?? this.loadStatus,
      loadStatusEnd: loadStatusEnd ?? this.loadStatusEnd,
      invoiceDto: invoiceDto ?? this.invoiceDto,
      message: message ?? this.message,
      listContractDetail: listContractDetail ?? this.listContractDetail,
      contractDetail: contractDetail ?? this.contractDetail,
      listPackageGoods: listPackageGoods ?? this.listPackageGoods,
      isBillType: isBillType ?? this.isBillType,
      isViettelPay: isViettelPay,
      listImage: listImage ?? this.listImage,
      listPerformer: listPerformer ?? this.listPerformer,
      listPackageGoodSelected:
          listPackageGoodSelected ?? this.listPackageGoodSelected,
      listBillDetailItemEntity:
          listBillDetailItemEntity ?? this.listBillDetailItemEntity,
      aioContract: aioContract ?? this.aioContract,
      contractCost: contractCost ?? this.contractCost,
      costCNKT: costCNKT ?? this.costCNKT,
      costNoSalary: costNoSalary ?? this.costNoSalary,
      tvCostCNKT: tvCostCNKT ?? this.tvCostCNKT,
      percentCode: percentCode ?? this.percentCode,
      noteCostSale: noteCostSale ?? this.noteCostSale,
      noteCostInstallationWorker:
          noteCostInstallationWorker ?? this.noteCostInstallationWorker,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        loadStatusEnd,
        message,
        listContractDetail,
        contractDetail,
        listPackageGoods,
        isBillType,
        isViettelPay,
        listImage,
        listPerformer,
        listPackageGoodSelected,
        invoiceDto,
        listBillDetailItemEntity,
        aioContract,
        contractCost,
        costCNKT,
        costNoSalary,
        tvCostCNKT,
        percentCode,
        noteCostSale,
        noteCostInstallationWorker,
      ];
}
