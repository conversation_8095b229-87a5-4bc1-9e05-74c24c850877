import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_invoice_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_supply_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_contract_detail_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_request_cancel_body.dart';
import 'package:vcc/domain/entities/order/bill_detail_item_entity.dart';
import 'package:vcc/domain/entities/order/drop_down_list_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_confirm_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_agency/enum_invoice_type.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_digital_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_param.dart';
import 'package:vcc/domain/params/aio_contract/aio_image.dart';
import 'package:vcc/domain/params/aio_contract/authentication_info.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';
import 'package:vcc/extensions/date_time_extensions.dart';
import 'package:vcc/presentation/base/app_dialog.dart';

part 'aio_contract_detail_state.dart';

final aioContractDetailProvider = StateNotifierProvider.autoDispose<
    AioContractDetailViewModel, AioContractDetailState>(
  (ref) => AioContractDetailViewModel(ref: ref),
);

class AioContractDetailViewModel extends StateNotifier<AioContractDetailState> {
  final Ref ref;

  AioContractDetailViewModel({
    required this.ref,
  }) : super(AioContractDetailState());

  Future<void> getData({
    required AioContractDetailEntity detail,
    AioContractEntity? aioContract,
    required BuildContext context,
  }) async {
    getListPackageGood(detail: detail);
    await getListContractSellTaskDetail(detail: detail);
    List<InternalStaffEntity> listPerformer = [];
    listPerformer.add(InternalStaffEntity(
      sysUserId: GlobalData.instance.userInfo?.sysUserId ??
          GlobalData.instance.userInfo?.userId,
      employeeCode: GlobalData.instance.userInfo?.employeeCode ??
          GlobalData.instance.userInfo?.username,
      fullName: GlobalData.instance.userInfo?.fullName,
      percentBonus: 100,
    ));
    await getContract(contractCode: aioContract?.contractCode);
    await getPackageDetail(
      contractDetailId: detail.contractDetailId,
      contractId: aioContract?.contractId,
    );

    state = state.copyWith(
      listPerformer: listPerformer,
      costCNKT: 0,
      costNoSalary: 0,
    );

    await getListAppParamSolarCare();

    if (!context.mounted) return;
    if (((state.contractDetail?.configServiceStatus == 2 ||
        (state.contractDetail?.configServiceStatus == 8 &&
            state.contractDetail?.isProvinceBought == 2) ||
        (state.contractDetail?.configServiceStatus == 8 &&
            state.contractDetail?.isProvinceBought == 1)))) {
      await calculatorCostTechnicalBranchNoti(context, detail);
    }
  }

  Future<void> getListAppParamSolarCare() async {
    var rsAppParamSolarCare =
        await appLocator<AioOrderRepository>().getListAppParamSolarCare(
      param: AioContractEntity(),
    );

    await rsAppParamSolarCare.when(
      success: (data) async {
        if (data?.resultInfo?.status == BaseConstant.ok &&
            (data?.data ?? []).isNotEmpty) {
          double saleOutMin = 0;
          double saleOutMax = 0;
          double performOutMin = 0;
          double performOutMax = 0;
          double performerInMin = 0;
          double performerInMax = 0;
          double costSaleMin = 0;
          double percentCode = 0;
          for (var item in data?.data ?? []) {
            switch (item.parType) {
              case "SALE_OUT_MIN":
                saleOutMin = item.valueOut;
                break;
              case "SALE_OUT_MAX":
                saleOutMax = item.valueOut;
                break;
              case "PERFORM_OUT_MIN":
                performOutMin = item.valueOut;
                break;
              case "PERFORM_OUT_MAX":
                performOutMax = item.valueOut;
                break;
              case "PERFORM_IN_MIN":
                performerInMin = item.valueOut;
                break;
              case "PERFORM_IN_MAX":
                performerInMax = item.valueOut;
                break;
              case "CPBH_MIN":
                costSaleMin = item.valueOut;
                break;
              case "TMNLMT_SALE_COMMISSION_RATE":
                percentCode = item.valueOut;
                break;
            }
          }
          var noteCostSale = "";
          var noteCostInstallationWorker = "";
          if (state.contractDetail?.configServiceStatus == 2 &&
              state.contractDetail?.isInternal == null) {
            noteCostSale =
                '${(saleOutMin * 100).toStringAsFixed(1)}% doanh thu <= CPBH <= ${(saleOutMax * 100).toStringAsFixed(1)}% doanh thu';
            noteCostInstallationWorker =
                '${(performOutMin * 100).toStringAsFixed(1)}% doanh thu <= CPNC <= ${(performOutMax * 100).toStringAsFixed(1)}% doanh thu';
            state = state.copyWith(
              noteCostSale: noteCostSale,
              noteCostInstallationWorker: noteCostInstallationWorker,
            );
          } else if (state.contractDetail?.configServiceStatus == 2 &&
              state.contractDetail?.isInternal == "1") {
            noteCostInstallationWorker =
                '${(performerInMin * 100).toStringAsFixed(1)}% doanh thu <= CPNC <= ${(performerInMax * 100).toStringAsFixed(1)}% doanh thu';
            state = state.copyWith(
              noteCostInstallationWorker: noteCostInstallationWorker,
            );
          } else if (state.contractDetail?.configServiceStatus == 8 &&
              state.contractDetail?.isInternal == "1") {
            noteCostSale =
                'Chi phí bán hàng >= ${(costSaleMin * 100).toStringAsFixed(1)}% doanh thu';
            state = state.copyWith(
              noteCostSale: noteCostSale,
            );
          }
          state = state.copyWith(
            percentCode: percentCode,
          );
        }
      },
      error: (err) {},
    );
  }

  calculatorCostTechnicalBranchNoti(
    BuildContext context,
    AioContractDetailEntity detail,
  ) async {
    AioContractResponseEntity? result = await ref
        .read(aioContractDetailProvider.notifier)
        .calculatorCostTechnicalBranch(
          contractDetail: detail,
        );

    if (result?.resultInfo?.status == "NOK") {
      if (!context.mounted) return;
      AppDialog.showDialogCenter(
        context,
        message: result?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
  }

  Future<void> getListPackageGood({
    required AioContractDetailEntity? detail,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      final resultParam =
          await appLocator<AioOrderRepository>().getListPackageGood(
              params: AioContractParam(
        aioContractDTO: AioContractEntity(
          contractId: detail?.contractId,
          contractDetailId: detail?.contractDetailId,
          packageDetailId: detail?.packageDetailId,
          sysUserId: GlobalData.instance.userInfo?.sysUserId ??
              GlobalData.instance.userInfo?.userId,
        ),
      ));

      await resultParam?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            state = state.copyWith(
              loadStatus: LoadStatus.success,
              listPackageGoods: data.data,
            );
          } else {
            state = state.copyWith(
              loadStatus: LoadStatus.failure,
            );
          }
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getListContractSellTaskDetail({
    AioContractDetailEntity? detail,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      final resultParam =
          await appLocator<AioOrderRepository>().getListContractSellTaskDetail(
              params: AioContractParam(
        aioContractDTO: AioContractEntity(
          contractId: detail?.contractId,
          contractDetailId: detail?.contractDetailId,
          packageDetailId: detail?.packageDetailId,
          sysUserId: GlobalData.instance.userInfo?.sysUserId ??
              GlobalData.instance.userInfo?.userId,
        ),
      ));

      await resultParam?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            if (data.data?[0].isBill == 1) {
              state = state.copyWith(
                isBillType: AioConfirmType.yes,
              );
            }
            state = state.copyWith(
              loadStatus: LoadStatus.success,
              contractDetail: data.data?[0],
            );
          } else {
            state = state.copyWith(
              loadStatus: LoadStatus.failure,
              message: data.resultInfo?.message,
            );
          }
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void changeIsViettelPay(AioConfirmType type) {
    state = state.copyWith(
      isViettelPay: type,
    );
  }

  Future<List<String>?> getReasonExpired() async {
    try {
      final result = await appLocator<AioOrderRepository>().getReasonExpired();
      List<String>? list = [];
      await result?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            list = data.data;
          }
        },
        error: (err) async {
          list = null;
        },
      );

      return list;
    } catch (error) {
      return null;
    }
  }

  Future<List<DropDownListEntity>?> getReasonReject() async {
    try {
      final result =
          await appLocator<AioOrderRepository>().getListAppParamByParType(
        params: DropDownListEntity(
          parType: "REASON_REJECT_CONTRACT",
        ),
      );
      List<DropDownListEntity>? list = [];
      await result?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            list = data.data;
          }
        },
        error: (err) async {
          list = null;
        },
      );

      return list;
    } catch (error) {
      return null;
    }
  }

  Future<AioContractResponseEntity?> deleteRequestCancel({
    required AioRequestCancelBody body,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      AioContractResponseEntity? rs;
      final result = await appLocator<AioOrderRepository>()
          .deleteCancelOrder(params: body);
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
          rs = data;
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );
      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  Future<AioContractResponseEntity?> startContractService() async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      final result =
          await appLocator<AioOrderRepository>().startContractService(
              param: AioContractParam(
        sysUserRequest: SysUserRequest(
          sysUserId: GlobalData.instance.userInfo?.sysUserId ??
              GlobalData.instance.userInfo?.userId,
        ),
        aioContractDTO: state.contractDetail?.toAioContractEntity(),
      ));
      AioContractResponseEntity? response;
      await result?.when(
        success: (data) async {
          response = data;
        },
        error: (err) async {
          response = null;
        },
      );

      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );

      getListPackageGood(detail: state.contractDetail);
      // getListContractSellTaskDetail(detail: state.contractDetail);
      await getContract(contractCode: state.aioContract?.contractCode);
      await getPackageDetail(
        contractDetailId: state.contractDetail?.contractDetailId,
        contractId: state.contractDetail?.contractId,
      );

      return response;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  void changeIsBill(AioConfirmType type) {
    state = state.copyWith(
      isBillType: type,
    );
  }

  void updateListPackageGoodSelected(List<AioSupplyEntity>? data) {
    state = state.copyWith(
      listPackageGoodSelected: data,
    );
  }

  void updateListSerial({
    List<AioSupplyEntity>? data,
    required int indexSupply,
  }) {
    var listPackageGoods = state.listPackageGoods;
    listPackageGoods?[indexSupply].typeSerial =
        listPackageGoods[indexSupply].goodsIsSerial;
    listPackageGoods?[indexSupply].lstSerial = (data ?? []).map((it) {
      return it.serialNumber;
    }).toList();
    state = state.copyWith(
      listPackageGoods: listPackageGoods,
    );
    calculatorCostTechnicalBranch();
  }

  void deleteSerial({
    required int indexSupply,
    required String serial,
  }) {
    var listPackageGoods = state.listPackageGoods;
    listPackageGoods?[indexSupply].lstSerial =
        (listPackageGoods[indexSupply].lstSerial ?? [])
            .where((it) => it != serial)
            .toList();
    state = state.copyWith(
      listPackageGoods: listPackageGoods,
    );
    calculatorCostTechnicalBranch();
  }

  void selectReason(String reason) async {
    var contractDetail = state.contractDetail;
    contractDetail?.reasonOutOfDate = reason;
    state = state.copyWith(
      contractDetail: contractDetail,
    );
    await appLocator<AioOrderRepository>().updateContractHold(
      param: AioContractParam(
        aioContractDTO: AioContractEntity(
          contractId: state.contractDetail?.contractId,
          reasonOutOfDate: reason,
          subAction: 1,
        ),
      ),
    );
    getListContractSellTaskDetail(detail: state.contractDetail);
  }

  void selectReasonReject(DropDownListEntity? reason) async {
    getListContractSellTaskDetail(detail: state.contractDetail);
  }

  void selectCancelDate(DateTime date) async {
    var contractDetail = state.contractDetail;
    contractDetail?.cancelDate = date;
    state = state.copyWith(
      contractDetail: contractDetail,
    );
    await appLocator<AioOrderRepository>().updateContractHold(
      param: AioContractParam(
        aioContractDTO: AioContractEntity(
          contractId: state.contractDetail?.contractId,
          appointmentDate: date.display(
            format: DateTimeFormater.hotOrderTimeFormat,
          ),
          subAction: 2,
        ),
      ),
    );
    getListPackageGood(detail: state.contractDetail);
    // getListContractSellTaskDetail(detail: state.contractDetail);
    await getContract(contractCode: state.aioContract?.contractCode);
    await getPackageDetail(
      contractDetailId: state.contractDetail?.contractDetailId,
      contractId: state.contractDetail?.contractId,
    );
  }

  Future<AioInvoiceResponseEntity?> saveInvoice(AioInvoiceEntity? param) async {
    AioInvoiceResponseEntity? response;
    // param?.aioOrderId = param.contractId ?? state.aioContract?.contractId;
    param?.contractId = param.contractId ?? state.aioContract?.contractId;
    param?.createdBy = GlobalData.instance.userInfo?.sysUserId;

    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result =
        await appLocator<AioOrderRepository>().saveInvoice(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  Future<AioContractResponseEntity?> endContractSell(
      AioContractDigitalParam param) async {
    AioContractResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result =
        await appLocator<AioOrderRepository>().endContractSell(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  Future<AioContractResponseEntity?> getUrlToRequestSubmitMoney(
      AioContractDigitalParam? param) async {
    AioContractResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result = await appLocator<AioOrderRepository>()
        .getUrlToRequestSubmitMoney(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  Future<AioContractResponseEntity?> updateStatusAndPerformerContract(
      AioContractEntity? param) async {
    AioContractResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result = await appLocator<AioOrderRepository>()
        .updateStatusAndPerformerContract(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  Future<AioContractResponseEntity?> calculatorCostTechnicalBranch({
    AioContractDetailEntity? contractDetail,
    AioContractParam? param,
  }) async {
    state = state.copyWith(
      loadStatusEnd: LoadStatus.loading,
    );

    AioContractResponseEntity? rs;
    var lstAIOContractMobileDTO = state.listPackageGoods;
    for (int i = 0; i < (lstAIOContractMobileDTO ?? []).length; i++) {
      if ((lstAIOContractMobileDTO?[i].lstSerial ?? []).isEmpty) {
        lstAIOContractMobileDTO?[i].lstSerial = [];
      }
      if ((lstAIOContractMobileDTO?[i].lstSerial ?? []).isEmpty) {
        lstAIOContractMobileDTO?[i].lstSerial = [];
      }
      lstAIOContractMobileDTO?[i].typeSerial =
          lstAIOContractMobileDTO[i].goodsIsSerial;
    }

    final resultParam =
        await appLocator<AioOrderRepository>().calculatorCostTechnicalBranch(
            param: AioContractParam(
      sysUserRequest: SysUserRequest(
        sysUserId: GlobalData.instance.userInfo?.sysUserId,
        departmentId: GlobalData.instance.userInfo?.sysGroupId,
        authenticationInfo: AuthenticationInfo(
          username: GlobalData.instance.userInfo?.username ??
              GlobalData.instance.userInfo?.employeeCode,
        ),
      ),
      aioContractDTO: AioContractEntity(
        contractId:
            contractDetail?.contractId ?? state.contractDetail?.contractId,
        contractDetailId: contractDetail?.contractDetailId ??
            state.contractDetail?.contractDetailId,
        packageDetailId: contractDetail?.packageDetailId ??
            state.contractDetail?.packageDetailId,
        configServiceStatus: contractDetail?.configServiceStatus ??
            state.contractDetail?.configServiceStatus,
        isProvinceBought: contractDetail?.isProvinceBought ??
            state.contractDetail?.isProvinceBought,
        sysUserId: GlobalData.instance.userInfo?.sysUserId ??
            GlobalData.instance.userInfo?.userId,
      ),
      lstAIOContractMobileDTO: lstAIOContractMobileDTO,
      listProductSelected: [],
    ));

    await resultParam.when(success: (data) async {
      rs = data;
      int costCNKT = 0;
      // int costNoSalary = 0;
      int tvCostCNKT = 0;

      // List<AioPackageEntity> mListCalculatorContractServiceDTOS = [];
      List<AioPackageEntity> arrGoodsDefault = [];
      for (var contract in state.listPackageGoodSelected ?? []) {
        if (contract.packageGoodsType == 1) {
          arrGoodsDefault.add(contract);
        } else if (contract.packageGoodsType == 2) {
          if (contract.quantityEnter > 0) {
            contract.quantity = contract.quantityEnter;
            arrGoodsDefault.add(contract);
          }
        }
      }

      if (data?.aioContractMobileDTO != null) {
        costCNKT =
            double.parse(data?.aioContractMobileDTO?.amountStr ?? "0").toInt();

        if (costCNKT < 0) {
          costCNKT = 0;
        }
        tvCostCNKT = costCNKT;
      }

      state = state.copyWith(
        contractCost: data?.aioContractMobileDTO,
        costCNKT: costCNKT,
        costNoSalary: costCNKT,
        tvCostCNKT: tvCostCNKT,
        loadStatusEnd: LoadStatus.success,
      );
    });
    return rs;
  }

  Future<AioInvoiceResponseEntity?> signDigitalContract(
      AioContractDigitalParam? param) async {
    AioInvoiceResponseEntity? response;
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    final result = await appLocator<AioOrderRepository>()
        .signDigitalContract(param: param);
    await result?.when(
      success: (data) async {
        response = data;
      },
      error: (err) async {
        response = null;
      },
    );

    state = state.copyWith(
      loadStatus: LoadStatus.success,
    );
    return response;
  }

  void deletePerformer(int index) {
    var listUser = state.listPerformer!;
    listUser.removeAt(index);
    state = state.copyWith(
      listPerformer: listUser,
    );
  }

  void onChangePerformer(List<InternalStaffEntity> listUser) {
    List<InternalStaffEntity> listPerformer = [];
    listPerformer.add(InternalStaffEntity(
      sysUserId: GlobalData.instance.userInfo?.sysUserId ??
          GlobalData.instance.userInfo?.userId,
      employeeCode: GlobalData.instance.userInfo?.employeeCode ??
          GlobalData.instance.userInfo?.username,
      fullName: GlobalData.instance.userInfo?.fullName,
      percentBonus: state.listPerformer?[0].percentBonus ?? 100,
    ));
    listPerformer.addAll(listUser);
    state = state.copyWith(
      listPerformer: listPerformer,
    );
  }

  void changePerformerCreated(int percent) {
    List<InternalStaffEntity> listPerformer = state.listPerformer ?? [];
    listPerformer[0] = InternalStaffEntity(
      sysUserId: GlobalData.instance.userInfo?.sysUserId ??
          GlobalData.instance.userInfo?.userId,
      employeeCode: GlobalData.instance.userInfo?.employeeCode ??
          GlobalData.instance.userInfo?.username,
      fullName: GlobalData.instance.userInfo?.fullName,
      percentBonus: percent,
    );
    state = state.copyWith(
      listPerformer: listPerformer,
    );
  }

  Future<List<AioInvoiceItemEntity>?> getListInvoiceItem({
    required List<int?> listPackageId,
  }) async {
    try {
      final result = await appLocator<AioOrderRepository>().getListInvoiceItem(
        listPackageId: listPackageId,
      );
      List<AioInvoiceItemEntity>? listInvoiceItem = [];
      await result?.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            listInvoiceItem = data.data;
          }
        },
        error: (err) async {
          listInvoiceItem = null;
        },
      );

      return listInvoiceItem;
    } catch (error) {
      return null;
    }
  }

  Future<AioInvoiceEntity?> getDataForInvoice({
    int? contractId,
  }) async {
    try {
      state = state.copyWith(
        loadStatus: LoadStatus.loading,
      );
      final result = await appLocator<AioOrderRepository>().getDataForInvoice(
        param: AioContractEntity(
          contractId: contractId,
          invoiceType: EnumInvoiceType.hasInvoice.keyToServer,
        ),
      );
      AioInvoiceEntity? rs;
      await result?.when(
        success: (data) async {
          rs = data.data;
        },
        error: (err) async {
          rs = null;
        },
      );

      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  Future<AioInvoiceEntity?> getDataForInvoiceForB2C({
    int? contractId,
  }) async {
    try {
      final result = await appLocator<AioOrderRepository>().getDataForInvoice(
        param: AioContractEntity(
          contractId: contractId,
          invoiceType: EnumInvoiceType.hasInvoice.keyToServer,
        ),
      );
      AioInvoiceEntity? rs;
      await result?.when(
        success: (data) async {
          rs = data.data;
        },
        error: (err) async {
          rs = null;
        },
      );
      return rs;
    } catch (error) {
      return null;
    }
  }

  void changePerformerPercent({
    required int percent,
    required int index,
  }) {
    List<InternalStaffEntity> listPerformer = state.listPerformer ?? [];
    listPerformer[index].percentBonus = percent;
    state = state.copyWith(
      listPerformer: listPerformer,
    );
  }

  void updateInvoice({
    AioInvoiceEntity? invoice,
    List<BillDetailItemEntity>? listItem,
  }) async {
    state = state.copyWith(
      invoiceDto: invoice,
      listBillDetailItemEntity: listItem,
    );
  }

  Future<void> getPackageDetail({
    int? contractId,
    int? contractDetailId,
  }) async {
    final resultParam = await appLocator<AioOrderRepository>()
        .getListContractDetailByContractId(
            params: AioContractEntity(
                contractId: contractId,
                sysUserId: GlobalData.instance.userInfo?.sysUserId ??
                    GlobalData.instance.userInfo?.userId));

    await resultParam?.when(
      success: (data) async {
        int? packageId = state.contractDetail?.packageId;
        var contractDetail = state.contractDetail;
        var contractDetailTmp = (data.data ?? [])
            .where((it) => it.contractDetailId == contractDetailId)
            .toList()
            .first;
        contractDetail?.packageId = packageId;
        contractDetail?.status = contractDetailTmp.status;
        contractDetail?.haveLastPackage = contractDetailTmp.haveLastPackage;

        state = state.copyWith(
          loadStatus: LoadStatus.success,
          contractDetail: contractDetail,
        );
      },
      error: (err) {},
    );
  }

  Future<void> getContract({String? contractCode}) async {
    final resultParam =
        await appLocator<AioOrderRepository>().findListTotalContract(
      params: AioContractEntity(
        detailFinished: 0,
        sysUserId: GlobalData.instance.userInfo?.sysUserId ??
            GlobalData.instance.userInfo?.userId,
        sysGroupId: GlobalData.instance.userInfo?.sysGroupId,
        keySearch: contractCode,
      ),
    );

    await resultParam?.when(
      success: (data) async {
        state = state.copyWith(
          loadStatus: LoadStatus.success,
          aioContract: data.data?.first,
        );
      },
      error: (err) {
        state = state.copyWith(
          loadStatus: LoadStatus.failure,
          message: err.message,
        );
      },
    );
  }

  updateCostNoSalary({
    required int cost,
  }) {
    state = state.copyWith(
      costNoSalary: cost,
    );
  }

  updateCostCNKT({
    required int costCNKT,
  }) {
    state = state.copyWith(
      costCNKT: costCNKT,
    );
  }

  Future<AioContractResponseEntity?> validOtpSignContract({
    AioContractEntity? body,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      AioContractResponseEntity? rs;
      final result = await appLocator<AioOrderRepository>()
          .validOtpSignContract(param: body);
      await result.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
          rs = data;
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );
      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }

  Future<AioContractResponseEntity?> sendSmsOtp({
    AioContractEntity? body,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );
    try {
      AioContractResponseEntity? rs;
      final result =
          await appLocator<AioOrderRepository>().sendSmsOtp(param: body);
      await result.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
          rs = data;
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );
      return rs;
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.success,
      );
      return null;
    }
  }
}
