import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pattern_formatter/pattern_formatter.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/body/sign_acceptance_body.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_contract_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract/aio_invoice_response_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_invoice_item_entity.dart';
import 'package:vcc/domain/entities/aio_contract_create/aio_package_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_contract_detail_entity.dart';
import 'package:vcc/domain/entities/aio_contract_detail/aio_request_cancel_body.dart';
import 'package:vcc/domain/entities/one_pay_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_entity.dart';
import 'package:vcc/domain/entities/order/bill_detail_item_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/order/drop_down_list_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_confirm_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_package_status_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/reason_query_type.dart';
import 'package:vcc/domain/enums/send_otp_type.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_digital_param.dart';
import 'package:vcc/domain/params/aio_contract/authentication_info.dart';
import 'package:vcc/domain/params/aio_contract/sys_user_request.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/aio_change_payment_type/aio_change_payment_type_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker.dart';
import 'package:vcc/presentation/views/bottom_sheet/verify_phone/verify_phone_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_add_seller/aio_add_seller_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_bill_detail/aio_bill_detail_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_check_inventory/aio_check_inventory_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/aio_request_cancel/aio_request_cancel_screen.dart';
import 'package:vcc/presentation/views/features/aio_contract/list_stock_good/list_stock_good_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/update_contract_real/update_contract_real_page.dart';
import 'package:vcc/presentation/views/features/aio_contract/widget/aio_supply_item_serial_widget.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment/one_pay_view.dart';
import 'package:vcc/presentation/views/features/order/detail_order/sign_contract_order/sign_contract_order_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/sign_contract_order/sign_contract_view_model.dart';
import 'package:vcc/presentation/views/widgets/pages/web_view_screen.dart';
import 'package:vcc/presentation/views/features/work/bill/bill_update/bill_update_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/plus_and_minus_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/normal_text_field.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/deBouncer.dart';
import 'package:vcc/utils/string_utils.dart';

import 'aio_contract_detail_view_model.dart';

class AioContractDetailArguments {
  final String? data;
  final AioContractEntity aioContract;
  final AioContractDetailEntity aioContractDetail;
  final Function(bool? toParent)? backFunction;

  AioContractDetailArguments({
    this.data,
    this.backFunction,
    required this.aioContractDetail,
    required this.aioContract,
  });
}

class AioContractDetailPage extends StatefulHookConsumerWidget {
  final AioContractDetailArguments arguments;

  const AioContractDetailPage({
    super.key,
    required this.arguments,
  });

  @override
  ConsumerState<AioContractDetailPage> createState() =>
      _AioContractDetailPageState();
}

class _AioContractDetailPageState extends ConsumerState<AioContractDetailPage>
    with TickerProviderStateMixin {
  late TextEditingController collectedMoneyController;
  late TextEditingController costSaleController;
  late TextEditingController costInstallationWorkerController;
  late TextEditingController costMarketingController;
  late TextEditingController costLaborController;
  late TextEditingController costSuppliesSubController;
  late TextEditingController costShippingController;
  late TextEditingController costReceptionController;
  late Debounce<String> deBouncerPercent;

  @override
  void initState() {
    Future(() {
      ref.read(aioContractDetailProvider.notifier).getData(
            detail: widget.arguments.aioContractDetail,
            aioContract: widget.arguments.aioContract,
            context: context,
          );
    });
    collectedMoneyController = TextEditingController();
    costSaleController = TextEditingController();
    costMarketingController = TextEditingController();
    costInstallationWorkerController = TextEditingController();
    costLaborController = TextEditingController();
    costSuppliesSubController = TextEditingController();
    costShippingController = TextEditingController();
    costReceptionController = TextEditingController();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(aioContractDetailProvider);
    PreferredSizeWidget appbarWidget;

    appbarWidget = AppBarCustom(
      backFunction: () {
        widget.arguments.backFunction?.call(false);
        context.pop();
      },
      title: widget.arguments.aioContractDetail.packageName ?? "",
      actionWidget: [
        InkWellWidget(
          onTap: () {},
        ),
      ],
    );

    return LayoutPage(
      appbar: appbarWidget,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildPage(state),
      ),
      bottomAction: Container(
        padding: const EdgeInsets.symmetric(
          vertical: 8,
          horizontal: 16,
        ),
        decoration: BoxDecoration(
          boxShadow: AppBoxShadows.shadowNormal,
          color: BaseColors.backgroundWhite,
        ),
        child: SafeArea(
          child: checkStatus() != AioContractPackageStatusType.nan &&
                  state.loadStatus == LoadStatus.success
              ? Row(
                  children: [
                    if (checkStatus() ==
                        AioContractPackageStatusType.processing) ...[
                      Expanded(
                        child: BaseButton(
                          isLoading: state.loadStatusEnd == LoadStatus.loading,
                          text: "Kết thúc",
                          onTap: () {
                            finishContract(
                              state,
                              context: context,
                            );
                          },
                        ),
                      ),
                    ],
                    if (checkStatus() !=
                            AioContractPackageStatusType.processing &&
                        checkStatus() ==
                            AioContractPackageStatusType.proposalToCancel) ...[
                      Expanded(
                        child: BaseButton(
                          text: "Xóa đề xuất hủy",
                          backgroundColor: Colors.white,
                          textColor: BaseColors.primary,
                          borderColor: BaseColors.primary,
                          onTap: () {
                            deleteCancelOrder(
                              state,
                              context: context,
                            );
                          },
                        ),
                      ),
                    ],
                    if (checkStatus() !=
                            AioContractPackageStatusType.processing &&
                        checkStatus() !=
                            AioContractPackageStatusType.proposalToCancel) ...[
                      Expanded(
                        child: BaseButton(
                          text: "Từ chối",
                          onTap: () {
                            showReasonReject(
                              state: state,
                              context: context,
                            );
                          },
                          backgroundColor: Colors.white,
                          textColor: BaseColors.primary,
                          borderColor: BaseColors.primary,
                        ),
                      ),
                      const SizedBox(
                        width: 12,
                      ),
                      Expanded(
                        child: BaseButton(
                          text: "Bắt đầu",
                          onTap: () async {
                            startContractPackage(
                              state,
                              context: context,
                            );
                          },
                        ),
                      ),
                    ],
                  ],
                )
              : const Row(
                  children: [
                    Expanded(
                        child: SizedBox(
                      height: 1,
                    )),
                  ],
                ),
        ),
      ),
    );
  }

  startContractPackage(
    state, {
    required BuildContext context,
  }) async {
    AioContractResponseEntity? reasons = await ref
        .read(aioContractDetailProvider.notifier)
        .startContractService();
    if (!context.mounted) return;
    if (reasons?.resultInfo?.status == BaseConstant.notOK) {
      AppDialog.showDialogCenter(
        context,
        message: reasons?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    } else {
      // Success, Link to work page
      // widget.arguments.backFunction?.call(true);
      AppDialog.showDialogCenter(
        context,
        message: "Bắt đầu hợp đồng thành công",
        status: DialogStatus.success,
      );
    }
  }

  Widget _buildPage(AioContractDetailState state) {
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else {
      return SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        checkStatus().display,
                        style: UITextStyle.body2SemiBold.copyWith(
                          color: checkStatus().color ?? BaseColors.textLabel,
                        ),
                      ),
                      const SizedBox(
                        height: 6,
                      ),
                      Text(
                        state.contractDetail?.isMoney == 1
                            ? "Đã thu tiền"
                            : "Chưa thu tiền",
                        style: UITextStyle.body2Regular.copyWith(
                          color: state.contractDetail?.isMoney == 1
                              ? BaseColors.success
                              : BaseColors.textLabel,
                        ),
                      ),
                    ],
                  ),
                  checkIcon(),
                ],
              ),
            ),
            _buildContractInfo(),
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 12,
                  ),
                  Text(
                    'Thông tin liên hệ',
                    style: UITextStyle.body1SemiBold.copyWith(
                      color: BaseColors.textTitle,
                    ),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.start,
                    children: [
                      Center(
                        child: MyAssets.icons.iconUserS16.svg(),
                      ),
                      const SizedBox(
                        width: 6,
                      ),
                      Expanded(
                        child: Text(
                          widget.arguments.aioContract.customerName ?? "",
                          style: UITextStyle.body1Medium.copyWith(
                            color: BaseColors.textLabel,
                          ),
                        ),
                      ),
                    ],
                  ),
                  Padding(
                    padding: const EdgeInsets.only(left: 20),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.arguments.aioContract.customerPhone ?? "",
                          style: UITextStyle.body1Medium.copyWith(
                            color: BaseColors.textTitle,
                          ),
                        ),
                        Text(
                          widget.arguments.aioContract.customerAddress ?? "",
                          style: UITextStyle.body1Regular.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                ],
              ),
            ),
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 12,
                  ),
                  Text(
                    'Thông tin gói',
                    style: UITextStyle.body1SemiBold.copyWith(
                      color: BaseColors.textTitle,
                    ),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  DividerWidget(
                    height: 1,
                    color: BaseColors.backgroundGray,
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Text(
                    state.contractDetail?.packageName ?? "",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  const SizedBox(
                    height: 12,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Số lượng gói: ",
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.textSubtitle,
                        ),
                      ),
                      Text(
                        "x${state.contractDetail?.quantity ?? 1}",
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                ],
              ),
            ),
            _buildListUseSupply(),
            _buildListSelectedSupply(),
            if (checkStatus() == AioContractPackageStatusType.processing) ...[
              _buildLogInventoryContract(),
              _buildFee(),
              _buildTeamContributeInfo(),
              _buildPaymentInfoInfo(),
            ],
            DividerWidget(
              height: 8,
              color: BaseColors.backgroundGray,
            ),
            if (checkStatus() !=
                AioContractPackageStatusType.proposalToCancel) ...[
              Container(
                decoration: BoxDecoration(
                  color: BaseColors.backgroundGray,
                ),
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
                child: Column(
                  children: [
                    if (checkStatus() ==
                        AioContractPackageStatusType.processing) ...[
                      BaseButton(
                        text: "Thay đổi hình thức thanh toán",
                        borderColor: CoreColors.primary,
                        backgroundColor: BaseColors.backgroundGray,
                        textColor: BaseColors.primary,
                        onTap: () {
                          changePaymentType();
                        },
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                      BaseButton(
                        text: "Cập nhật triển khai thực tế",
                        borderColor: CoreColors.primary,
                        backgroundColor: BaseColors.backgroundGray,
                        textColor: BaseColors.primary,
                        onTap: () {
                          updateDeployment();
                        },
                      ),
                      const SizedBox(
                        height: 12,
                      ),
                    ],
                    if (checkStatus() !=
                        AioContractPackageStatusType.proposalToCancel)
                      ...[],
                    BaseButton(
                      text: "Cập nhật nguyên nhân quá hạn",
                      borderColor: CoreColors.neutral04,
                      backgroundColor: Colors.transparent,
                      textColor: BaseColors.textLabel,
                      onTap: () {
                        showUpdateReasonExpired(
                          context: context,
                          state: state,
                        );
                      },
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    BaseButton(
                      text: "Đề xuất tạm dừng",
                      borderColor: CoreColors.neutral04,
                      backgroundColor: Colors.transparent,
                      textColor: BaseColors.textLabel,
                      onTap: () {
                        openCancelDatePicker();
                      },
                    ),
                    const SizedBox(
                      height: 12,
                    ),
                    BaseButton(
                      text: "Đề xuất hủy",
                      borderColor: CoreColors.neutral04,
                      backgroundColor: Colors.transparent,
                      textColor: BaseColors.textLabel,
                      onTap: () {
                        context.push(
                          RouterPaths.aioRequestCancelPage,
                          extra: AioRequestCancelArguments(
                            reasonQueryType: ReasonQueryType.employee,
                            aioContract: widget.arguments.aioContract,
                            aioContractDetail: state.contractDetail ??
                                AioContractDetailEntity(),
                            backFunction: () {
                              context.pop();
                              widget.arguments.backFunction?.call(true);
                            },
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ],
        ),
      );
    }
  }

  Future<void> refreshData() async {}

  AioContractPackageStatusType checkStatus() {
    var state = ref.watch(aioContractDetailProvider);
    // if (widget.arguments.aioContractDetail.statusContract != 0 &&
    //     (widget.arguments.aioContractDetail.statusContract == 5 ||
    //         widget.arguments.aioContractDetail.statusContract == 6)) {
    if (state.aioContract?.status != 0 &&
        (state.aioContract?.status == 5 || state.aioContract?.status == 6)) {
      switch (state.aioContract?.status) {
        case 5:
          return AioContractPackageStatusType.proposedPause;
        case 6:
          return AioContractPackageStatusType.proposalToCancel;
      }
    } else {
      switch (state.contractDetail?.status) {
        case 1:
          return AioContractPackageStatusType.unfulfilled;
        case 2:
          return AioContractPackageStatusType.processing;
        case 5:
          return AioContractPackageStatusType.proposedPause;
        case 6:
          return AioContractPackageStatusType.proposalToCancel;
        case 7:
          return AioContractPackageStatusType.waitingCreateOrder;
        case 8:
          return AioContractPackageStatusType.preparingGoods;
        case 9:
          return AioContractPackageStatusType.waitingDistrict;
        case 10:
          return AioContractPackageStatusType.waitingBranch;
        default:
          return AioContractPackageStatusType.nan;
      }
    }
    return AioContractPackageStatusType.nan;
  }

  Widget checkIcon() {
    var state = ref.watch(aioContractDetailProvider);
    if (state.aioContract?.status != 0 &&
        (state.aioContract?.status == 5 || state.aioContract?.status == 6)) {
      switch (state.aioContract?.status) {
        case 5:
          return MyAssets.icons.cancelOrderActive.svg();
        case 6:
          return MyAssets.icons.cancelOrderActive.svg();
      }
    } else {
      switch (state.contractDetail?.status) {
        case 1:
          return MyAssets.icons.iconOrderWaitingConfirmS40.svg();
        case 2:
          return MyAssets.icons.iconProcessingServiceOrder.svg(height: 33);
        default:
          return MyAssets.icons.iconOrderWaitingConfirmS40.svg();
      }
    }
    return MyAssets.icons.iconOrderWaitingConfirmS40.svg();
  }

  Widget _displayName(type) {
    return Text(
      type.name,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _buildListUseSupply() {
    var state = ref.watch(aioContractDetailProvider);
    if ((state.listPackageGoods ?? []).isEmpty) {
      return const SizedBox();
    }
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 12,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Vật tư đặt hàng',
                    style: UITextStyle.body1SemiBold.copyWith(
                      color: BaseColors.textTitle,
                    ),
                  ),
                  if (checkStatus() ==
                          AioContractPackageStatusType.unfulfilled &&
                      state.listPackageGoods?[0].goodsIsSerial != 1) ...[
                    InkWell(
                      onTap: () {
                        selectSupply(
                          disableCheckbox: true,
                          state: state,
                          listGoodIds: (state.listPackageGoods ?? [])
                              .map((it) => it.goodsId)
                              .toList(),
                        );
                      },
                      child: Text(
                        'KIỂM TRA TỒN KHO',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.primary,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
              const SizedBox(
                height: 12,
              ),
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: (state.listPackageGoods ?? []).length,
                itemBuilder: (context, index) {
                  var item = state.listPackageGoods![index];
                  return Column(
                    children: [
                      DividerWidget(
                        height: 1,
                        color: BaseColors.backgroundGray,
                      ),
                      const SizedBox(
                        height: 16,
                      ),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          MyAssets.icons.iconSupply.svg(),
                          const SizedBox(
                            width: 8,
                          ),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(item.goodsName ?? ""),
                                const SizedBox(
                                  height: 8,
                                ),
                                Row(
                                  children: [
                                    Expanded(
                                      child: Text(
                                        "Mã sản phẩm",
                                        style: UITextStyle.caption1Regular
                                            .copyWith(
                                          color: BaseColors.textLabel,
                                        ),
                                      ),
                                    ),
                                    Text(
                                      "${item.goodsCode}",
                                      style:
                                          UITextStyle.caption1Regular.copyWith(
                                        color: BaseColors.textLabel,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(
                                  height: 8,
                                ),
                                if (item.goodsIsSerial != 1) ...[
                                  _buildItemQuantity(item.quantity),
                                  Text(
                                    "Vật tư không có serial",
                                    style: UITextStyle.caption1Regular.copyWith(
                                      color: BaseColors.textLabel,
                                    ),
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                ],
                                if (item.goodsIsSerial == 1) ...[
                                  _buildItemQuantity(item.quantity),
                                  Text(
                                    "Vật tư có serial",
                                    style: UITextStyle.caption1Regular.copyWith(
                                      color: BaseColors.textLabel,
                                    ),
                                  ),
                                  if ((item.lstSerial ?? []).isEmpty &&
                                      checkStatus() ==
                                          AioContractPackageStatusType
                                              .processing) ...[
                                    const SizedBox(
                                      height: 8,
                                    ),
                                    InkWell(
                                      onTap: () {
                                        selectSupply(
                                          state: state,
                                          listGoodIds: [item.goodsId],
                                          isSerial: true,
                                          indexSupply: index,
                                        );
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 10,
                                        ),
                                        decoration: BoxDecoration(
                                          color: BaseColors.backgroundGray1,
                                          borderRadius:
                                              BorderRadius.circular(12),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              "Chọn serial",
                                              style: UITextStyle.body2Regular
                                                  .copyWith(
                                                color: BaseColors.textSubtitle,
                                              ),
                                            ),
                                            MyAssets.icons.arrowRight.svg()
                                          ],
                                        ),
                                      ),
                                    ),
                                    const SizedBox(
                                      height: 8,
                                    ),
                                  ],
                                  if ((item.lstSerial ?? []).isNotEmpty) ...[
                                    const SizedBox(
                                      height: 8,
                                    ),
                                    InkWell(
                                      onTap: () {
                                        selectSupply(
                                          state: state,
                                          listGoodIds: [item.goodsId],
                                          isSerial: true,
                                          indexSupply: index,
                                        );
                                      },
                                      child: Container(
                                        padding: const EdgeInsets.symmetric(
                                          horizontal: 12,
                                          vertical: 10,
                                        ),
                                        decoration: BoxDecoration(
                                          color: BaseColors.backgroundGray1,
                                          borderRadius: const BorderRadius.only(
                                            topRight: Radius.circular(12),
                                            topLeft: Radius.circular(12),
                                          ),
                                        ),
                                        child: Row(
                                          mainAxisAlignment:
                                              MainAxisAlignment.spaceBetween,
                                          children: [
                                            Text(
                                              "Chọn serial",
                                              style: UITextStyle.body2Regular
                                                  .copyWith(
                                                color: BaseColors.textSubtitle,
                                              ),
                                            ),
                                            MyAssets.icons.arrowRight.svg()
                                          ],
                                        ),
                                      ),
                                    ),
                                    ListView.builder(
                                      shrinkWrap: true,
                                      physics:
                                          const NeverScrollableScrollPhysics(),
                                      itemCount: (item.lstSerial ?? []).length,
                                      itemBuilder: (context, indexSerial) {
                                        String? serial =
                                            item.lstSerial![indexSerial];
                                        return AioSupplyItemSerialWidget(
                                          serial: serial ?? "",
                                          onDelete: () {
                                            ref
                                                .read(aioContractDetailProvider
                                                    .notifier)
                                                .deleteSerial(
                                                  indexSupply: index,
                                                  serial: serial ?? "",
                                                );
                                          },
                                        );
                                      },
                                    ),
                                    const SizedBox(
                                      height: 8,
                                    ),
                                  ],
                                ],
                              ],
                            ),
                          )
                        ],
                      ),
                    ],
                  );
                },
              ),
              const SizedBox(
                height: 16,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildListSelectedSupply() {
    var state = ref.watch(aioContractDetailProvider);
    if (checkStatus() != AioContractPackageStatusType.unfulfilled &&
        (state.listPackageGoods ?? []).isNotEmpty &&
        (state.listPackageGoods ?? []).any((item) => item.goodsIsSerial != 1)) {
      return Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DividerWidget(
            height: 8,
            color: BaseColors.backgroundGray,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(
                  height: 12,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Vật tư sử dụng',
                      style: UITextStyle.body1SemiBold.copyWith(
                        color: BaseColors.textTitle,
                      ),
                    ),
                    InkWell(
                      onTap: () {
                        selectSupply(
                          state: state,
                          listGoodIds: (state.listPackageGoods ?? [])
                              .map((it) => it.goodsId)
                              .toList(),
                          isSerial: false,
                        );
                      },
                      child: Text(
                        'CHỌN VẬT TƯ',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.primary,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
                ListView.builder(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  itemCount: (state.listPackageGoodSelected ?? []).length,
                  itemBuilder: (context, index) {
                    var item = state.listPackageGoodSelected![index];
                    return Column(
                      children: [
                        DividerWidget(
                          height: 1,
                          color: BaseColors.backgroundGray,
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                        Row(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            MyAssets.icons.iconSupply.svg(),
                            const SizedBox(
                              width: 8,
                            ),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(item.goodsName ?? ""),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  Row(
                                    children: [
                                      Expanded(
                                        child: Text(
                                          "Mã sản phẩm",
                                          style: UITextStyle.caption1Regular
                                              .copyWith(
                                            color: BaseColors.textLabel,
                                          ),
                                        ),
                                      ),
                                      Text(
                                        "${item.goodsCode}",
                                        style: UITextStyle.caption1Regular
                                            .copyWith(
                                          color: BaseColors.textLabel,
                                        ),
                                      ),
                                    ],
                                  ),
                                  const SizedBox(
                                    height: 8,
                                  ),
                                  if (item.serialNumber == null) ...[
                                    Row(
                                      children: [
                                        Expanded(
                                          child: Row(
                                            children: [
                                              Text(
                                                StringUtils.formatMoney(
                                                    item.applyPrice ?? 0),
                                                style: UITextStyle.body2SemiBold
                                                    .copyWith(
                                                  color: BaseColors.primary,
                                                ),
                                              ),
                                              Text(
                                                "/${item.goodsUnitName}",
                                                style: UITextStyle.body2Regular
                                                    .copyWith(
                                                  color:
                                                      BaseColors.textSubtitle,
                                                ),
                                              ),
                                            ],
                                          ),
                                        ),
                                        Text(
                                          "x${item.quantity}",
                                          style:
                                              UITextStyle.body2Regular.copyWith(
                                            color: BaseColors.textLabel,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 8,
                                    ),
                                    Text(
                                      "Vật tư không có serial",
                                      style:
                                          UITextStyle.caption1Regular.copyWith(
                                        color: BaseColors.textLabel,
                                      ),
                                    ),
                                  ],
                                  if (item.serialNumber != null) ...[
                                    const SizedBox(
                                      height: 8,
                                    ),
                                    Row(
                                      mainAxisAlignment:
                                          MainAxisAlignment.spaceBetween,
                                      children: [
                                        Text(
                                          "Vật tư có serial: ",
                                          style: UITextStyle.caption1Regular
                                              .copyWith(
                                            color: BaseColors.textLabel,
                                          ),
                                        ),
                                        Expanded(
                                          child: Text(
                                            item.serialNumber ?? "",
                                            style: UITextStyle.caption1Regular
                                                .copyWith(
                                              color: BaseColors.textTitle,
                                            ),
                                            textAlign: TextAlign.right,
                                          ),
                                        ),
                                      ],
                                    ),
                                    const SizedBox(
                                      height: 8,
                                    ),
                                  ],
                                ],
                              ),
                            )
                          ],
                        ),
                        const SizedBox(
                          height: 16,
                        ),
                      ],
                    );
                  },
                ),
              ],
            ),
          ),
        ],
      );
    }
    return const SizedBox();
  }

  Widget _buildContractInfo() {
    var state = ref.watch(aioContractDetailProvider);
    return Column(
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(
                height: 12,
              ),
              Row(children: [
                Text(
                  widget.arguments.aioContract.contractCode ?? "",
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textTitle,
                  ),
                ),
                const SizedBox(
                  width: 8,
                ),
                InkWell(
                    onTap: () {
                      AppUtils.copyToClipboard(
                          widget.arguments.aioContract.contractCode ?? "");
                    },
                    child: MyAssets.icons.copyClipBoard.svg()),
              ]),
              const SizedBox(
                height: 12,
              ),
              DividerWidget(
                height: 1,
                color: BaseColors.backgroundGray,
              ),
              const SizedBox(
                height: 12,
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Ngày bắt đầu: ",
                    style: UITextStyle.body2Regular.copyWith(
                      color: BaseColors.textSubtitle,
                    ),
                  ),
                  Text(
                    state.contractDetail?.dateStartContract ?? "",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                ],
              ),
              if (checkStatus() == AioContractPackageStatusType.processing) ...[
                const SizedBox(
                  height: 12,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Phương thức thanh toán: ",
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                    ),
                    Text(
                      state.contractDetail?.payType == 1
                          ? "ViettelPay"
                          : state.contractDetail?.payType == 5
                              ? "OnePayCollect"
                              : "TK chuyên thu CNCT",
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 12,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Trạng thái phê duyệt HTTT: ",
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                    ),
                    Text(
                      (state.contractDetail?.approvedPay ?? 1) == 1
                          ? "Đã phê duyệt"
                          : "Chờ phê duyệt",
                      style: UITextStyle.body2Medium.copyWith(
                        color: (state.contractDetail?.approvedPay ?? 1) == 1
                            ? BaseColors.success
                            : BaseColors.warning,
                      ),
                    ),
                  ],
                ),
              ],
              /*   if (false) ...[
                const SizedBox(
                  height: 12,
                ),
                Text(
                  "Ảnh biên bản nghiệm thu: ",
                  style: UITextStyle.body2Regular.copyWith(
                    color: BaseColors.textSubtitle,
                  ),
                ),
                if ((state.listImage ?? []).isNotEmpty) ...[
                  const SizedBox(
                    height: 12,
                  ),
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 8,
                      horizontal: 16,
                    ),
                    child: SizedBox(
                      height: 80,
                      child: ListView.separated(
                        itemCount: (state.listImage?.length ?? 0),
                        scrollDirection: Axis.horizontal,
                        padding: const EdgeInsets.only(right: 16),
                        separatorBuilder: (_, __) => const SizedBox(width: 8),
                        itemBuilder: (context, index) {
                          return Stack(
                            children: [
                              Container(
                                height: 80,
                                width: 80,
                                padding: const EdgeInsets.only(
                                  top: 4,
                                  right: 4,
                                ),
                                decoration: BoxDecoration(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: ClipRRect(
                                  borderRadius: BorderRadius.circular(8),
                                  child: ImageBase64Widget(
                                    base64Image:
                                        state.listImage![index].base64String ??
                                            "",
                                  ),
                                ),
                              ),
                            ],
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ],*/
              const SizedBox(
                height: 16,
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildPaymentInfoInfo() {
    var state = ref.watch(aioContractDetailProvider);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 16,
          ),
          child: Text(
            'Thông tin thanh toán',
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 8,
            horizontal: 16,
          ),
          child: Text(
            'Xuất hóa đơn',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 8,
            horizontal: 16,
          ),
          child: Row(
            children: <Widget>[
              Expanded(
                child: RadioWidget<AioConfirmType>(
                  value: AioConfirmType.yes,
                  groupValue: state.isBillType,
                  onChanged: (value) {
                    ref
                        .read(aioContractDetailProvider.notifier)
                        .changeIsBill(value);
                  },
                  displayWidget: (context, item) {
                    return _displayName(item);
                  },
                ),
              ),
              Expanded(
                child: RadioWidget<AioConfirmType>(
                  value: AioConfirmType.no,
                  groupValue: state.isBillType,
                  onChanged: (value) {
                    ref
                        .read(aioContractDetailProvider.notifier)
                        .changeIsBill(value);
                  },
                  displayWidget: (context, item) {
                    return _displayName(item);
                  },
                ),
              ),
            ],
          ),
        ),
        if (state.isBillType == AioConfirmType.yes) ...[
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 8,
            ),
            child: Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  InkWell(
                    onTap: () {
                      getInvoice(
                        state,
                        context: context,
                      );
                    },
                    child: RichText(
                      text: TextSpan(
                        text: 'Thông tin xuất hóa đơn',
                        style: UITextStyle.caption1SemiBold.copyWith(
                          color: BaseColors.primary,
                        ),
                      ),
                    ),
                  ),
                  MyAssets.icons.arrowRight.svg(
                    colorFilter: ColorFilter.mode(
                      BaseColors.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
        const SizedBox(
          height: 8,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: DividerWidget(
            height: 1,
            color: BaseColors.backgroundGray,
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Số tiền tạm ứng ${state.contractDetail?.amountPay != null ? (state.contractDetail?.amountPay ?? 0) * 100 ~/ (state.contractDetail?.contractAmount ?? 1) : ""} ${state.contractDetail?.amountPay != null ? "%" : ""}:",
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textSubtitle,
                ),
              ),
              Text(
                StringUtils.formatMoney(state.contractDetail?.amountPay ?? 0),
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                "Thành tiền dự kiến: ",
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              Text(
                StringUtils.formatMoney(
                    state.contractDetail?.amountDetail ?? 0),
                style: UITextStyle.body2SemiBold.copyWith(
                  color: BaseColors.primary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(
          height: 16,
        ),
      ],
    );
  }

  Widget _buildLogInventoryContract() {
    var state = ref.watch(aioContractDetailProvider);
    return Column(
      mainAxisAlignment: MainAxisAlignment.start,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 16,
          ),
          child: InkWell(
            onTap: () {
              context.push(
                RouterPaths.listStockGoodPage,
                extra: ListStockGoodArguments(
                  contractId: state.contractDetail?.contractId,
                ),
              );
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Expanded(
                  child: Text(
                    'Tồn kho',
                    style: UITextStyle.body1SemiBold,
                  ),
                ),
                Row(
                  children: [
                    Text(
                      'Xem thông tin đảm bảo',
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                    ),
                    MyAssets.icons.iconArrowRightS18.svg(),
                  ],
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildTeamContributeInfo() {
    return Column(
      children: [
        DividerWidget(
          height: 8,
          color: BaseColors.backgroundGray,
        ),
        Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: <Widget>[
              Text(
                "Người thực hiện",
                style: UITextStyle.body1SemiBold,
              ),
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.fromLTRB(12, 12, 12, 4),
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: BaseColors.borderDefault,
                  ),
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: <Widget>[
                    ListView.separated(
                      itemCount: ref
                              .watch(aioContractDetailProvider)
                              .listPerformer
                              ?.length ??
                          0,
                      shrinkWrap: true,
                      padding: EdgeInsets.zero,
                      physics: const NeverScrollableScrollPhysics(),
                      separatorBuilder: (_, __) {
                        return const Padding(
                          padding: EdgeInsets.symmetric(vertical: 8),
                          child: DividerWidget(),
                        );
                      },
                      itemBuilder: (context, index) {
                        final item = ref
                            .watch(aioContractDetailProvider)
                            .listPerformer![index];
                        var percentController = TextEditingController(
                          text: "${item.percentBonus ?? ""}",
                        );

                        if (index == 0) {
                          return InkWellWidget(
                            onTap: () async {
                              final value =
                                  await AppBottomSheet.showNormalBottomSheet(
                                context,
                                title: "Thay đổi tỷ lệ",
                                isFlexible: true,
                                child: EditQuantityView(
                                  label: "Tỷ lệ",
                                  quantity: percentController.text.isNotEmpty
                                      ? int.parse(percentController.text)
                                      : 0,
                                ),
                              );

                              item.percentBonus = int.tryParse(value) ?? 0;
                              ref
                                  .read(aioContractDetailProvider.notifier)
                                  .changePerformerCreated(
                                      int.tryParse(value) ?? 0);
                            },
                            child: Row(
                              children: <Widget>[
                                Expanded(
                                  child: Column(
                                    mainAxisSize: MainAxisSize.min,
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: <Widget>[
                                      Text(
                                        item.fullName ?? '',
                                        style: UITextStyle.body1Medium.copyWith(
                                          color: BaseColors.textTitle,
                                        ),
                                      ),
                                      const SizedBox(height: 2),
                                      Text(
                                        item.username ??
                                            item.employeeCode ??
                                            '',
                                        style: UITextStyle.caption1Regular
                                            .copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                                SizedBox(
                                  width: 42,
                                  child: NormalTextField(
                                    controller: percentController,
                                    showClearIcon: false,
                                    height: 30,
                                    readOnly: true,
                                    textInputAction: TextInputAction.done,
                                    keyboardType: TextInputType.number,
                                    borderColor: Colors.transparent,
                                    borderRadius: BorderRadius.zero,
                                    textAlign: TextAlign.end,
                                    suffixText: "%",
                                    maxLength: 3,
                                    contentPadding:
                                        const EdgeInsets.only(top: 2),
                                  ),
                                ),
                                InkWellWidget(
                                  onTap: () async {},
                                  child: Padding(
                                    padding: const EdgeInsets.all(2),
                                    child: MyAssets.icons.iconEditS12.svg(
                                      height: 14,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                          );
                        }
                        return Row(
                          children: <Widget>[
                            InkWellWidget(
                              onTap: () {
                                ref
                                    .read(aioContractDetailProvider.notifier)
                                    .deletePerformer(index);
                              },
                              child: MyAssets.icons.iconMinusRounedRed.svg(),
                            ),
                            const SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  Text(
                                    item.fullName ?? '',
                                    style: UITextStyle.body1Regular,
                                  ),
                                  const SizedBox(height: 2),
                                  Text(
                                    item.username ?? '',
                                    style: UITextStyle.caption1Regular.copyWith(
                                      color: BaseColors.textSubtitle,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            SizedBox(
                              width: 38,
                              child: NormalTextField(
                                controller: percentController,
                                showClearIcon: false,
                                height: 30,
                                textInputAction: TextInputAction.done,
                                keyboardType: TextInputType.number,
                                borderColor: Colors.transparent,
                                borderRadius: BorderRadius.zero,
                                suffixText: "%",
                                maxLength: 3,
                                readOnly: true,
                                textAlign: TextAlign.end,
                                contentPadding: const EdgeInsets.only(top: 2),
                                onChanged: (value) {
                                  // item.percentBonus = int.tryParse(value) ?? 0;
                                  // ref
                                  //     .read(aioContractDetailProvider.notifier)
                                  //     .changePerformerPercent(
                                  //       index: index,
                                  //       percent: int.tryParse(value) ?? 0,
                                  //     );
                                },
                              ),
                            ),
                            InkWellWidget(
                              onTap: () async {
                                final result =
                                    await AppBottomSheet.showNormalBottomSheet(
                                  context,
                                  title: "Thay đổi tỷ lệ",
                                  isFlexible: true,
                                  child: EditQuantityView(
                                    label: "Tỷ lệ",
                                    quantity: percentController.text.isNotEmpty
                                        ? int.parse(percentController.text)
                                        : 0,
                                  ),
                                );
                                percentController.text = result;
                                item.percentBonus = int.tryParse(result) ?? 0;
                                ref
                                    .read(aioContractDetailProvider.notifier)
                                    .changePerformerPercent(
                                      index: index,
                                      percent: int.tryParse(result) ?? 0,
                                    );
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(2),
                                child: MyAssets.icons.iconEditS12.svg(
                                  height: 14,
                                ),
                              ),
                            ),
                          ],
                        );
                      },
                    ),
                    const SizedBox(height: 8),
                    const DividerWidget(),
                    const SizedBox(height: 4),
                    AppTextButton(
                      iconLeft: MyAssets.icons.iconAddCircle.svg(),
                      title: "Thêm người thực hiện",
                      onTap: () {
                        context.push(
                          RouterPaths.aioAddSeller,
                          extra: AioAddSellerArguments(
                            sellersSelected: ref
                                .watch(aioContractDetailProvider)
                                .listPerformer,
                            onSelected: (data) {
                              var dataProcess = data
                                  .where((it) =>
                                      it.sysUserId !=
                                      GlobalData.instance.userInfo?.sysUserId)
                                  .toList();
                              ref
                                  .read(aioContractDetailProvider.notifier)
                                  .onChangePerformer(dataProcess);
                            },
                          ),
                        );
                      },
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  calculatorCostTechnicalBranch({
    required BuildContext context,
  }) async {
    AioContractResponseEntity? result = await ref
        .read(aioContractDetailProvider.notifier)
        .calculatorCostTechnicalBranch(
          contractDetail: widget.arguments.aioContractDetail,
        );

    if (!context.mounted) return;
    if (result?.resultInfo?.status == BaseConstant.notOK) {
      AppDialog.showDialogCenter(
        context,
        message: result?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
  }

  _buildFee() {
    var state = ref.watch(aioContractDetailProvider);
    var dto = state.contractDetail;
    bool isShowCostInstallationWorker = false;
    // bool isShowCostMarketing = false;
    bool isShowNoteCostInstallationWorker = false;
    bool isShowCostSale = false;
    bool isShowNoteCostSale = false;
    bool isShowCostLabor = false;
    bool isShowCostNoSalary = false;
    bool isShowCostSuppliesSub = false;
    bool isShowCostShipping = false;
    bool isShowCostReception = false;
    bool isEnableCostSale = true;
    bool isEnableCostReception = true;

    if (dto?.configServiceStatus == 2 && dto?.isInternal == null) {
      isShowCostInstallationWorker = true;
      isShowNoteCostInstallationWorker = true;
      isShowCostSale = true;
      isShowNoteCostSale = true;
      isShowCostLabor = true;
      isShowCostNoSalary = true;
      isShowCostSuppliesSub = true;
      isShowCostShipping = true;
      isShowCostReception = true;
    } else if (dto?.configServiceStatus == 2 && dto?.isInternal == '1') {
      isShowCostInstallationWorker = true;
      isShowNoteCostInstallationWorker = true;

      isShowNoteCostSale = true;
      isShowCostLabor = true;
      isShowCostNoSalary = true;
      isShowCostSuppliesSub = true;
      isShowCostShipping = true;
    } else if (dto?.configServiceStatus == 8 && dto?.isProvinceBought == 2) {
      isShowCostReception = true;
      isShowCostSale = true;
      // costSale.value = (percentCode * costCNKT.value).toInt();
      // costReception.value = (costCNKT.value - costSale.value);
      // edtCostReception.text = costReception.value.toString();
      // edtCostSale.text = costSale.value.toString();
      costSaleController.text =
          StringUtils.formatNumber(state.percentCode * state.costCNKT);
      costReceptionController.text = StringUtils.formatNumber(
          state.costCNKT - (state.percentCode * state.costCNKT));
      isEnableCostSale = false;
      isEnableCostReception = false;
    } else if (dto?.configServiceStatus == 8 && dto?.isProvinceBought == 1) {
      isShowCostSale = true;
      isShowNoteCostSale = true;
      isShowCostNoSalary = false;
      isShowCostShipping = false;
      isShowCostReception = false;

      // costSale.value = costCNKT.value;
      // edtCostSale.text = costCNKT.value.toString();
      costSaleController.text = StringUtils.formatNumber(state.costCNKT);
      isEnableCostSale = false;
    }

    if ((dto?.configServiceStatus == 2 ||
        (dto?.configServiceStatus == 8 && dto?.isProvinceBought == 2) ||
        (dto?.configServiceStatus == 8 && dto?.isProvinceBought == 1))) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          DividerWidget(
            height: 8,
            color: BaseColors.backgroundGray,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              vertical: 16,
              horizontal: 16,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Chi phí',
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textTitle,
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      "Chi phí CNKT: ",
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                    ),
                    Text(
                      StringUtils.formatMoney(state.costCNKT),
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ],
                ),
                const SizedBox(
                  height: 16,
                ),
                if (isShowCostSale) ...[
                  TextFieldWidget(
                    controller: costSaleController,
                    labelText: "Chi phí bán hàng",
                    enabled: isEnableCostSale,
                    onFocusChange: (isFocus) {
                      if (!isFocus) {}
                    },
                    onChanged: (value) {
                      // calculateFee(state);
                      setValueCostNoSalary(state, type: "costSaleController");
                    },
                    hintText: "",
                    inputFormatters: [
                      ThousandsFormatter(allowFraction: true),
                    ],
                    keyboardType: TextInputType.number,
                  ),
                  if (isShowNoteCostSale) ...[
                    const SizedBox(
                      height: 4,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                      ),
                      child: Text(
                        // "1.5% doanh thu <= CPBH <= 7.0% doanh thu",
                        state.noteCostSale ?? "",
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.primary,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                  const SizedBox(
                    height: 16,
                  ),
                ],
                /*if (isShowCostMarketing) ...[
                  TextFieldWidget(
                    controller: costMarketingController,
                    labelText: "Chi phí Marketing trước VAT",
                    enabled: true,
                    onFocusChange: (isFocus) {
                      if (!isFocus) {}
                    },
                    onChanged: (value) {
                      // calculateFee(state);
                    },
                    hintText: "",
                    inputFormatters: [
                      ThousandsFormatter(allowFraction: true),
                    ],
                    keyboardType: TextInputType.number,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                ],*/
                if (isShowCostInstallationWorker) ...[
                  TextFieldWidget(
                    controller: costInstallationWorkerController,
                    labelText: "Chi phí nhân công lắp đặt, thuê ngoài",
                    enabled: true,
                    onFocusChange: (isFocus) {
                      if (!isFocus) {}
                    },
                    onChanged: (value) {
                      // calculateFee(state);
                      setValueCostNoSalary(state,
                          type: "costInstallationWorkerController");
                    },
                    inputFormatters: [
                      ThousandsFormatter(allowFraction: true),
                    ],
                    keyboardType: TextInputType.number,
                  ),
                  if (isShowNoteCostInstallationWorker) ...[
                    const SizedBox(
                      height: 4,
                    ),
                    Padding(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                      ),
                      child: Text(
                        // "1.5% doanh thu <= CPNC <= 9.0% doanh thu",
                        state.noteCostInstallationWorker ?? "",
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.primary,
                          fontSize: 13,
                        ),
                      ),
                    ),
                  ],
                  const SizedBox(
                    height: 16,
                  ),
                ],
                if (isShowCostLabor) ...[
                  TextFieldWidget(
                    controller: costLaborController,
                    labelText: "Chi phí nhân công thuê ngoài trước VAT",
                    enabled: true,
                    onFocusChange: (isFocus) {
                      if (!isFocus) {}
                    },
                    onChanged: (value) {},
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      ThousandsFormatter(allowFraction: true),
                    ],
                  ),
                  const SizedBox(
                    height: 4,
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                ],
                if (isShowCostNoSalary) ...[
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        "Chi phí không lương: ",
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.textSubtitle,
                        ),
                      ),
                      Text(
                        StringUtils.formatMoney(state.costNoSalary),
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 13,
                  ),
                  DividerWidget(
                    height: 1,
                    color: BaseColors.backgroundGray,
                  ),
                ],
                if (isShowCostSuppliesSub) ...[
                  TextFieldWidget(
                    controller: costSuppliesSubController,
                    labelText: "Chi phí vật tư phụ lắp đặt",
                    enabled: true,
                    onFocusChange: (isFocus) {
                      if (!isFocus) {}
                    },
                    onChanged: (value) {},
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      ThousandsFormatter(allowFraction: true),
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                ],
                if (isShowCostShipping) ...[
                  TextFieldWidget(
                    controller: costShippingController,
                    labelText: "Chi phí vận chuyển trước VAT",
                    enabled: true,
                    onFocusChange: (isFocus) {
                      if (!isFocus) {}
                    },
                    onChanged: (value) {},
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      ThousandsFormatter(allowFraction: true),
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                ],
                if (isShowCostReception) ...[
                  TextFieldWidget(
                    controller: costReceptionController,
                    labelText: "Chi phí tiếp khách trước VAT",
                    enabled: isEnableCostReception,
                    onFocusChange: (isFocus) {
                      if (!isFocus) {}
                    },
                    onChanged: (value) {},
                    keyboardType: TextInputType.number,
                    inputFormatters: [
                      ThousandsFormatter(allowFraction: true),
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                ],
              ],
            ),
          ),
        ],
      );
    }
    return const SizedBox();
  }

  changePaymentType() {
    var state = ref.watch(aioContractDetailProvider);
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Thay đổi hình thức thanh toán",
      height: MediaQuery.of(context).size.height * 0.7,
      child: AioChangePaymentTypeView(
          contractDetail: state.contractDetail,
          isViettelPay: state.isViettelPay,
          backFunction: () {
            context.pop();
          }),
    );
  }

  updateDeployment() {
    var state = ref.watch(aioContractDetailProvider);
    context.push(
      RouterPaths.updateContractRealPage,
      extra: UpdateContractRealArguments(
        contractDetail: state.contractDetail,
      ),
    );
  }

  showUpdateReasonExpired({
    required BuildContext context,
    required AioContractDetailState? state,
  }) async {
    List<String>? reasons =
        await ref.read(aioContractDetailProvider.notifier).getReasonExpired();

    if (!context.mounted) return;
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Chọn nguyên nhân quá hạn",
      height: MediaQuery.of(context).size.height * 0.7,
      child: Expanded(
          child: ListView.builder(
        shrinkWrap: true,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: (reasons ?? []).length,
        itemBuilder: (context, index) {
          var item = reasons?[index];
          return InkWell(
            onTap: () {
              ref
                  .read(aioContractDetailProvider.notifier)
                  .selectReason(item ?? "");
              AppDialog.showDialogCenter(
                context,
                message: "Cập nhật nguyên nhân quá hạn thành công",
                status: DialogStatus.success,
              );
              context.pop();
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          item ?? "",
                          style: UITextStyle.body1Regular.copyWith(
                            color:
                                state?.contractDetail?.reasonOutOfDate == item
                                    ? BaseColors.primary
                                    : BaseColors.textBody,
                          ),
                        ),
                      ),
                      if (state?.contractDetail?.reasonOutOfDate == item) ...[
                        MyAssets.icons.iconCheckedS24.svg(),
                      ],
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  DividerWidget(
                    height: 1,
                    color: BaseColors.backgroundGray,
                  ),
                ],
              ),
            ),
          );
        },
      )),
    );
  }

  deleteCancelOrder(
    AioContractDetailState state, {
    required BuildContext context,
  }) async {
    AioContractResponseEntity? rs =
        await ref.read(aioContractDetailProvider.notifier).deleteRequestCancel(
              body: AioRequestCancelBody(
                aioCancelOrderId: state.contractDetail?.aioCancelOrderId,
                updatedUser: GlobalData.instance.userInfo?.sysUserId ??
                    GlobalData.instance.userInfo?.userId,
              ),
            );

    if (!context.mounted) return;
    if (rs?.resultInfo?.status == BaseConstant.ok) {
      AppDialog.showDialogCenter(
        context,
        message: "Xóa đề xuất hủy thành công",
        status: DialogStatus.success,
      );
      widget.arguments.backFunction?.call(true);
      context.pop(true);
    } else {
      AppDialog.showDialogCenter(
        context,
        message: "Xóa đề xuất hủy thất bại",
        status: DialogStatus.error,
      );
    }
  }

  showReasonReject({
    required BuildContext context,
    required AioContractDetailState? state,
  }) async {
    List<DropDownListEntity>? reasons =
        await ref.read(aioContractDetailProvider.notifier).getReasonReject();

    if (!context.mounted) return;
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Chọn lý do từ chối",
      height: MediaQuery.of(context).size.height * 0.7,
      child: Expanded(
          child: ListView.builder(
        shrinkWrap: true,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: (reasons ?? []).length,
        itemBuilder: (context, index) {
          var item = reasons?[index];
          return InkWell(
            onTap: () {
              ref
                  .read(aioContractDetailProvider.notifier)
                  .selectReasonReject(item);
              AppDialog.showDialogConfirm(
                context,
                title: "Xác nhận",
                message:
                    "Bạn có chắc chắn muốn từ chối hợp đồng, lý do ${item?.name}?",
                buttonNameConfirm: "Từ chối",
                onConfirmAction: () async {
                  AioContractResponseEntity? result = await ref
                      .read(aioContractDetailProvider.notifier)
                      .updateStatusAndPerformerContract(AioContractEntity(
                        contractId: state?.contractDetail?.contractId,
                        contractCode: state?.contractDetail?.contractCode,
                        performerId: state?.contractDetail?.performerId,
                        sysUserId: widget.arguments.aioContract.sysUserId ??
                            GlobalData.instance.userInfo?.sysUserId,
                        subAction: 0,
                        done: 0,
                        reasonRejectContract: item?.name,
                        notDone: 0,
                      ));

                  if (!context.mounted) return;
                  if (result?.resultInfo?.status == BaseConstant.ok) {
                    AppDialog.showDialogCenter(
                      context,
                      message: "Từ chối hợp đồng thành công",
                      status: DialogStatus.success,
                    );
                    context.pop();
                    context.pop();
                  } else {
                    AppDialog.showDialogCenter(
                      context,
                      message:
                          result?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
                      status: DialogStatus.error,
                    );
                  }
                  context.pop();
                },
              );
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    height: 16,
                  ),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Expanded(
                        child: Text(
                          item?.name ?? "",
                          style: UITextStyle.body1Regular.copyWith(
                            color: BaseColors.textBody,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(
                    height: 16,
                  ),
                  DividerWidget(
                    height: 1,
                    color: BaseColors.backgroundGray,
                  ),
                ],
              ),
            ),
          );
        },
      )),
    );
  }

  calculatorCostTechnicalBranchForEndCelling() async {
    int costCNKT = 0;
    int costNoSalary = 0;
    // int tvCostCNKT = 0;

    var state = ref.watch(aioContractDetailProvider);

    List<AioPackageEntity> mListCalculatorContractServiceDTOS = [];
    List<AioPackageEntity> arrGoodsDefault = [];
    for (var contract in state.listPackageGoodSelected ?? []) {
      if (contract.packageGoodsType == 1) {
        arrGoodsDefault.add(contract);
      } else if (contract.packageGoodsType == 2) {
        if (contract.quantityEnter > 0) {
          contract.quantity = contract.quantityEnter;
          arrGoodsDefault.add(contract);
        }
      }
    }
    mListCalculatorContractServiceDTOS.addAll(arrGoodsDefault);

    if (state.contractCost != null) {
      costCNKT = int.parse(state.contractCost?.amountStr ?? "0");

      if (costCNKT < 0) {
        costCNKT = 0;
      }
      costNoSalary = costCNKT -
          ((costSaleController.text.toString().trim().isEmpty)
              ? 0
              : int.parse(costSaleController.text.toString())) -
          ((costInstallationWorkerController.text.toString().trim().isEmpty)
              ? 0
              : int.parse(costInstallationWorkerController.text.toString()));
      // tvCostCNKT = costCNKT;

      ref
          .read(aioContractDetailProvider.notifier)
          .updateCostNoSalary(cost: costNoSalary);
      ref
          .read(aioContractDetailProvider.notifier)
          .updateCostCNKT(costCNKT: costCNKT);
    }
  }

  bool validateFee(AioContractDetailState state) {
    int? costReception = costReceptionController.text.toString().trim().isEmpty
        ? 0
        : int.parse(
            costReceptionController.text.toString().replaceAll(".", ""));
    int? costShipping = costShippingController.text.toString().trim().isEmpty
        ? 0
        : int.parse(costShippingController.text.toString().replaceAll(".", ""));

    int? costSuppliesSub =
        costSuppliesSubController.text.toString().trim().isEmpty
            ? 0
            : int.parse(
                costSuppliesSubController.text.toString().replaceAll(".", ""));

    int? costNoSalary = state.costNoSalary;
    // Không lương = vật tư phụ lắp đặt + vận chuyển trước VAT + tiếp khách trước VAT
    if (state.contractDetail?.configServiceStatus == 2 &&
        state.contractDetail?.isInternal == null) {
      if (costNoSalary != (costSuppliesSub + costShipping + costReception)) {
        AppDialog.showDialogCenter(
          context,
          message:
              "Tổng CP vật tư phụ lắp đặt, CP vận chuyển và CP tiếp khách phải bằng CP không lương",
          status: DialogStatus.error,
        );
        return false;
      }
    }
    return true;
  }

  void finishContract(
    AioContractDetailState state, {
    required BuildContext context,
  }) async {
    for (int i = 0; i < (state.listPackageGoods ?? []).length; i++) {
      var itemIndex = state.listPackageGoods?[i];
      double totalQuantity = 0;
      if (itemIndex?.typeSerial == 1) {
        totalQuantity = (itemIndex?.lstSerial ?? []).length.toDouble();
      } else {
        var suppliesSelected = state.listPackageGoodSelected;
        suppliesSelected?.forEach((element) {
          if (element.goodsCode == itemIndex?.goodsCode) {
            totalQuantity += element.quantity ?? 1;
          }
        });
      }

      if ((itemIndex?.quantity ?? 0) != totalQuantity) {
        String condition = "lớn";
        if ((itemIndex?.quantity ?? 0) > totalQuantity) {
          condition = "nhỏ";
        }
        AppDialog.showDialogCenter(
          context,
          message:
              "Mã sản phẩm ${itemIndex?.goodsCode} bạn chọn đang $condition hơn số lượng vật tư trong gói (SL yêu cầu: ${itemIndex?.quantity})",
          status: DialogStatus.error,
        );
        return;
      }
    }

    // Luồng NLMT
    if ((state.contractDetail?.configServiceStatus == 2 ||
        (state.contractDetail?.configServiceStatus == 8 &&
            state.contractDetail?.isProvinceBought == 2) ||
        (state.contractDetail?.configServiceStatus == 8 &&
            state.contractDetail?.isProvinceBought == 1))) {
      if (validateFee(state) == false) {
        return;
      }
      // calculatorCostTechnicalBranchForEndCelling();
    }

    // Luồng khác NLMT
    int totalPercentPerform = 0;
    state.listPerformer?.forEach((it) {
      totalPercentPerform += it.percentBonus ?? 0;
    });
    if (totalPercentPerform != 100) {
      AppDialog.showDialogCenter(
        context,
        message: "Tổng tỷ lệ chia lương phải bằng \n 100%",
        status: DialogStatus.error,
      );
      return;
    }
    // if ((state.listPackageGoodSelected ?? []).isEmpty) {
    // AppDialog.showDialogCenter(
    //   context,
    //   message: "Bạn chưa chọn vật tư",
    //   status: DialogStatus.error,
    // );
    // return;
    // }
    if (state.contractDetail?.haveLastPackage == 1) {
      if (state.isBillType == AioConfirmType.yes &&
          (state.invoiceDto?.email == null ||
              state.invoiceDto?.email?.trim() == "")) {
        AppDialog.showDialogCenter(
          context,
          message: "Bạn chưa nhập địa chỉ email cho hóa đơn",
          status: DialogStatus.error,
        );
      } else {
        context.push(
          RouterPaths.aioBillDetail,
          extra: AioBillDetailArguments(
              previewData: BillDetailEntity(
                orderCode: state.contractDetail?.contractCode,
                totalMoney: state.contractDetail?.contractAmount,
                address: state.contractDetail?.customerAddress,
                customerName: state.contractDetail?.customerName,
                items: state.listBillDetailItemEntity,
              ),
              isBillType: state.isBillType,
              listPerformer: state.listPerformer,
              confirmFunction: () async {
                AioInvoiceEntity? invoiceDto;
                if (state.invoiceDto == null) {
                  invoiceDto = await getDefaultInvoice(state);
                }
                invoiceDto?.invoiceType =
                    state.isBillType == AioConfirmType.yes ? 1 : 0;
                AioInvoiceResponseEntity? result = await ref
                    .read(aioContractDetailProvider.notifier)
                    .saveInvoice(state.invoiceDto ?? invoiceDto);
                if (!context.mounted) return;
                if (result?.resultInfo?.status == BaseConstant.ok) {
                  signContractOrder(
                    state,
                    context: context,
                  );
                } else {
                  AppDialog.showDialogCenter(
                    context,
                    message: result?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
                    status: DialogStatus.error,
                  );
                }
              }),
        );
      }
    } else {
      endContract(
        getAioContractParam(),
        context: context,
      );
    }
  }

  Future<AioInvoiceEntity> getDefaultInvoice(
      AioContractDetailState state) async {
    AioInvoiceEntity? aioInvoice =
        await ref.read(aioContractDetailProvider.notifier).getDataForInvoice(
              contractId: state.contractDetail?.contractId,
            );

    List<AioInvoiceItemEntity>? listInvoiceItem =
        aioInvoice?.aioInvoiceItemDTOS ?? [];

    return AioInvoiceEntity(
      contractId: state.aioContract?.contractId,
      aioOrderId: state.aioContract?.contractId,
      customerName: "Người mua không lấy hoá đơn",
      address: "Khách lẻ",
      paymentMethod: aioInvoice?.paymentMethod,
      createdBy: GlobalData.instance.userInfo?.sysUserId,
      customerType: state.aioContract?.customerTypeId,
      invoiceType: aioInvoice?.invoiceType,
      contractCode: state.aioContract?.contractCode,
      aioInvoiceItemDTOS: listInvoiceItem,
    );
  }

  Future<AioContractResponseEntity?> endContract(
    AioContractDigitalParam aioContractParam, {
    bool? isPin,
    bool? disableSuccess,
    required BuildContext context,
  }) async {
    AioContractResponseEntity? result = await ref
        .read(aioContractDetailProvider.notifier)
        .endContractSell(getAioContractParam());
    if (result?.resultInfo?.status == BaseConstant.ok) {
      if (disableSuccess != true) {
        if (!context.mounted) return null;
        AppDialog.showDialogCenter(
          context,
          message: "Kết thúc thành công",
          status: DialogStatus.success,
        );
        context.pop();
        context.pop();
        context.pop();
      }
      // if (isPin == true) {
      //   context.pop();
      //   widget.arguments.backFunction?.call(true);
    } else {
      if (!context.mounted) return null;
      AppDialog.showDialogCenter(
        context,
        message: result?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
    return result;
  }

  AioContractDigitalParam getAioContractParam() {
    var state = ref.watch(aioContractDetailProvider);
    var aioContract = state.contractDetail;
    aioContract?.payType ??= widget.arguments.aioContract.payType;
    aioContract?.quantity ??= state.listPackageGoods?.first.quantity;
    aioContract?.packageId ??= state.contractDetail?.packageId;
    aioContract?.amount = aioContract.amount ?? aioContract.amountDetail;
    aioContract?.price = aioContract.amount ?? aioContract.amountDetail;
    aioContract?.checkBill = aioContract.amount ?? aioContract.amountDetail;

    // Luồng NLMT
    if ((state.contractDetail?.configServiceStatus == 2 ||
        (state.contractDetail?.configServiceStatus == 8 &&
            state.contractDetail?.isProvinceBought == 2) ||
        (state.contractDetail?.configServiceStatus == 8 &&
            state.contractDetail?.isProvinceBought == 1))) {
      int? costSale = costSaleController.text.toString().trim().isEmpty
          ? 0
          : int.parse(costSaleController.text.toString().replaceAll(".", ""));
      int? costInstallationWorker =
          costInstallationWorkerController.text.toString().trim().isEmpty
              ? 0
              : int.parse(costInstallationWorkerController.text
                  .toString()
                  .replaceAll(".", ""));
      int? marketingCosts =
          costMarketingController.text.toString().trim().isEmpty
              ? 0
              : int.parse(
                  costMarketingController.text.toString().replaceAll(".", ""));
      int? costReception =
          costReceptionController.text.toString().trim().isEmpty
              ? 0
              : int.parse(
                  costReceptionController.text.toString().replaceAll(".", ""));
      int? costShipping = costShippingController.text.toString().trim().isEmpty
          ? 0
          : int.parse(
              costShippingController.text.toString().replaceAll(".", ""));
      int? costLabor = costLaborController.text.toString().trim().isEmpty
          ? 0
          : int.parse(costLaborController.text.toString().replaceAll(".", ""));
      int? costSuppliesSub = costSuppliesSubController.text
              .toString()
              .trim()
              .isEmpty
          ? 0
          : int.parse(
              costSuppliesSubController.text.toString().replaceAll(".", ""));
      // int? costNoSalary = state.costNoSalary;

      if (marketingCosts > 0) {
        aioContract?.marketingCosts = marketingCosts;
      }
      if (costReception > 0) {
        aioContract?.guestCosts = costReception;
      }
      if (costShipping > 0) {
        aioContract?.shippingUnloadingCosts = costShipping;
      }
      if (costSale > 0) {
        aioContract?.saleCosts = costSale;
      }
      if (costInstallationWorker > 0) {
        aioContract?.performerCosts = costInstallationWorker;
      }
      if (costSuppliesSub > 0) {
        aioContract?.accessoriesCosts = costSuppliesSub;
      }
      aioContract?.outsourcingCosts = costLabor;
      aioContract?.branchCosts = state.costCNKT;
    }

    String performTogether = "";
    state.listPerformer?.forEach((performer) {
      performTogether +=
          "${performer.username ?? performer.employeeCode}~${performer.fullName}~${performer.percentBonus};";
    });

    state.listPackageGoods?.forEach((it) {
      if ((it.lstSerial ?? []).isEmpty) {
        it.lstSerial = [];
        it.typeSerial = it.goodsIsSerial == 1 ? 1 : 0;
      }
      it.lstSerialText = [];
    });
    return AioContractDigitalParam(
      sysUserRequest: getUserInfo(),
      contractId: widget.arguments.aioContract.contractId,
      aioContractDTO: aioContract,
      lstAIOContractMobileDTO: state.listPackageGoods ?? [],
      listProductSelected: state.listPackageGoodSelected ?? [],
      performTogether: performTogether,
    );
  }

  void signContractOrder(
    AioContractDetailState state, {
    required BuildContext context,
  }) async {
    var aioContractParam = getAioContractParam();
    context.push(
      RouterPaths.signContractOrder,
      extra: SignContractOrderArguments(
        isFromAioContract: true,
        orderCode: "",
        orderEntity: DetailOrderEntity(),
        aioContractParam: aioContractParam,
        onCompleted: (SignAcceptanceBody signData) async {
          // List<Uint8List?>? byteImgCustomerSign = [
          //   base64Decode(signData.customerSignature ?? "")
          // ];
          // List<Uint8List?>? byteImgEmployeeSign = [
          //   base64Decode(signData.staffSignature ?? "")
          // ];
          aioContractParam.byteImgCustomerSignStr = signData.customerSignature;
          aioContractParam.byteImgEmployeeSignStr = signData.staffSignature;
          aioContractParam.typeAction = 1;
          if (state.contractDetail?.contractType == 2) {
            bool disableSuccess = false;
            if (state.contractDetail?.payType == 5 ||
                state.contractDetail?.payType == 1) {
              disableSuccess = true;
            }
            var rs = await endContract(
              aioContractParam,
              disableSuccess: disableSuccess,
              context: context,
            );

            if (!context.mounted) return;
            if (state.contractDetail?.payType == 1) {
              if (rs?.resultInfo?.status == BaseConstant.ok) {
                getUrlToRequestSubmitMoney(
                  1,
                  context: context,
                );
              }
            }
            if (state.contractDetail?.payType == 5) {
              if (rs?.resultInfo?.status == BaseConstant.ok) {
                getUrlToRequestSubmitMoney(
                  5,
                  context: context,
                );
              }
            }
            if (!context.mounted) return;
            context.pop();
            widget.arguments.backFunction?.call(true);
          } else {
            sendOtp(state);
            AppBottomSheet.showNormalBottomSheet(
              context,
              title: "Xác thực nghiệm thu",
              isFlexible: true,
              child: VerifyPhoneView(
                phoneNumber: state.contractDetail?.customerPhone ?? "",
                orderCode: state.contractDetail?.code,
                sendOtpType: SendOtpType.completeOrder,
                aioContract: AioContractEntity(
                  contractId: state.contractDetail?.contractId,
                  contractType: state.contractDetail?.contractType,
                ),
                onCompleted: (String otp) async {
                  // Kết thúc đơn
                  ref
                      .read(signContractProvider.notifier)
                      .setLoadStatus(LoadStatus.loading);

                  AioContractResponseEntity? result = await ref
                      .read(aioContractDetailProvider.notifier)
                      .validOtpSignContract(
                        body: AioContractEntity(
                          contractId: state.contractDetail?.contractId,
                          otp: otp,
                          contractType: state.contractDetail?.contractType,
                        ),
                      );

                  ref
                      .read(signContractProvider.notifier)
                      .setLoadStatus(LoadStatus.success);

                  if (result?.validOtp == true) {
                    AioInvoiceResponseEntity? resultSign = await ref
                        .read(aioContractDetailProvider.notifier)
                        .signDigitalContract(aioContractParam);
                    if (resultSign?.pathPdf != null) {
                      bool disableSuccess = false;
                      if (state.contractDetail?.payType == 5 ||
                          state.contractDetail?.payType == 1) {
                        disableSuccess = true;
                      }
                      if (!context.mounted) return;
                      var rs = await endContract(
                        context: context,
                        aioContractParam,
                        isPin: true,
                        disableSuccess:
                            state.contractDetail?.haveLastPackage == 1
                                ? disableSuccess
                                : false,
                      );
                      if (state.contractDetail?.haveLastPackage == 1) {
                        if (!context.mounted) return;
                        if (state.contractDetail?.payType == 1) {
                          if (rs?.resultInfo?.status == BaseConstant.ok) {
                            getUrlToRequestSubmitMoney(
                              1,
                              context: context,
                            );
                          }
                        }
                        if (state.contractDetail?.payType == 5) {
                          if (rs?.resultInfo?.status == BaseConstant.ok) {
                            getUrlToRequestSubmitMoney(
                              5,
                              context: context,
                            );
                          }
                        }
                      }
                    } else {
                      if (!context.mounted) return;
                      AppDialog.showDialogCenter(
                        context,
                        message: result?.message != BaseConstant.notOK
                            ? (result?.message ?? "Đã có lỗi xảy ra!")
                            : "Đã có lỗi xảy ra!",
                        status: DialogStatus.error,
                      );
                    }
                  } else {
                    if (!context.mounted) return;
                    AppDialog.showDialogCenter(
                      context,
                      message:
                          result?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
                      status: DialogStatus.error,
                    );
                  }
                },
                onResentOtp: () {
                  sendOtp(state);
                },
              ),
            );
          }
        },
      ),
    );
  }

  sendOtp(state) {
    ref.read(aioContractDetailProvider.notifier).sendSmsOtp(
          body: AioContractEntity(
            amount: state.contractDetail?.amount,
            contractId: state.contractDetail?.contractId,
            customerReviews: "5",
            contractType: state.contractDetail?.contractType,
            customerReviewsContents: "",
          ),
        );
  }

  void getUrlToRequestSubmitMoney(
    int payType, {
    required BuildContext context,
  }) async {
    var state = ref.watch(aioContractDetailProvider);
    var aioContractParamPayment = AioContractDigitalParam(
      sysUserRequest: SysUserRequest(
        sysUserId: GlobalData.instance.userInfo?.sysUserId,
        flag: 0,
        isTtqh: false,
        ft: false,
      ),
      contractId: widget.arguments.aioContract.contractId,
    );
    AioContractResponseEntity? response = await ref
        .read(aioContractDetailProvider.notifier)
        .getUrlToRequestSubmitMoney(aioContractParamPayment);
    if (response?.resultInfo?.status == BaseConstant.ok) {
      if (!context.mounted) return;
      context.pop();
      if (payType == 1) {
        context.push(
          RouterPaths.webView,
          extra: WebViewArguments(
            url: response?.requestUrl ?? "",
            title: "Viettel Paygate",
          ),
        );
      }
      if (payType == 5) {
        var lstData = response?.requestUrl?.split("~");
        String? qr = lstData?[0];
        String? bankName = lstData?[1];
        String? accountNumber = lstData?[2];
        String? accountName = lstData?[3];
        String? amount = lstData?[4];
        // String? type = lstData?[5];

        context.push(
          RouterPaths.onePayView,
          extra: OnePayArguments(
            fromAioContract: true,
            backFunction: () {
              context.pop();
              context.pop();
            },
            onePayInfo: OnePayEntity(
              orderCode: state.contractDetail?.code,
              bankName: bankName,
              accountNumber: accountNumber,
              amount: double.parse(amount ?? "0").toInt(),
              qr: qr,
              accountName: accountName,
            ),
          ),
        );
      }
    } else {
      if (!context.mounted) return;
      AppDialog.showDialogCenter(
        context,
        message: response?.resultInfo?.message ?? "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
  }

  void openCancelDatePicker({DateTime? date}) {
    DateTime dateInit = DateTime.now();
    if (date != null) {
      dateInit = date;
    }
    CustomBottomPicker.dateTime(
      height: MediaQuery.of(context).size.height * 0.40,
      title: "Đề xuất tạm dừng",
      buttonText: "Hẹn lịch",
      initialDateTime: dateInit,
      minDateTime: dateInit,
      titleStyle: UITextStyle.body1SemiBold,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      use24hFormat: true,
      dateOrder: DatePickerDateOrder.dmy,
      onSubmit: (date) {
        ref.read(aioContractDetailProvider.notifier).selectCancelDate(date);
        AppDialog.showDialogCenter(
          context,
          message: "Đề xuất tạm dừng thành công",
          status: DialogStatus.success,
        );
      },
    ).show(context);
  }

  void getInvoice(
    AioContractDetailState state, {
    required BuildContext context,
  }) async {
    AioInvoiceEntity? aioInvoice =
        await ref.read(aioContractDetailProvider.notifier).getDataForInvoice(
              contractId: state.contractDetail?.contractId,
            );

    List<AioInvoiceItemEntity>? listInvoiceItem =
        aioInvoice?.aioInvoiceItemDTOS ?? [];

    List<BillDetailItemEntity>? listItem = listInvoiceItem
        .map((it) => BillDetailItemEntity(
              taxPercent: it.taxPercent,
              name: it.goodsName,
              goodsId: it.goodsId,
              goodsCode: it.goodsCode,
              goodsName: it.goodsName,
              quantity: it.quantity,
              price: it.price,
              goodsUnitName: it.goodsUnitName,
              amountBeforeTax: it.amountBeforeTax,
              preTaxAmount: it.preTaxAmount,
              amount: it.amount,
              packageId: it.packageId,
            ))
        .toList();

    if (!context.mounted) return;
    context.push(
      RouterPaths.billUpdate,
      extra: BillUpdateArguments(
          isFromAioContract: true,
          data: BillDetailEntity(
            orderCode: state.contractDetail?.contractCode,
            totalMoney: aioInvoice?.totalAmount,
            address: state.contractDetail?.customerAddress ??
                state.invoiceDto?.address,
            customerName: state.contractDetail?.customerName ??
                state.invoiceDto?.customerName,
            email: state.invoiceDto?.email,
            phoneNumber: state.contractDetail?.customerPhone ??
                state.invoiceDto?.customerPhone,
            items: listItem,
            taxCode: state.invoiceDto?.taxCode ?? aioInvoice?.taxCode,
            invoiceType: state.invoiceDto?.invoiceType,
          ),
          onSaveData: (billDetailEntity) {
            ref.read(aioContractDetailProvider.notifier).updateInvoice(
                  invoice: billDetailEntity.toAioInvoiceEntity(),
                  listItem: listItem,
                );
          }),
    );
  }

  void saveDataInvoice(callBackData) {
    List<AioInvoiceItemEntity> items = [];
    // Danh sách hàng hóa
    for (int i = 0; i < (callBackData.items ?? []).length; i++) {
      var itTmp = callBackData.items?[i];
      AioInvoiceItemEntity it = AioInvoiceItemEntity(
        goodsName: itTmp?.name ?? itTmp?.goodsName,
        goodsCode: itTmp?.goodsCode,
        goodsId: itTmp?.goodsId,
        quantity: itTmp?.quantity,
        amount: itTmp?.amount,
        amountBeforeTax: itTmp?.amountBeforeTax,
        preTaxAmount: itTmp?.preTaxAmount,
        price: itTmp?.price,
      );
      items.add(it);
    }
    // Danh sách ghi chú
    for (int i = 0; i < (callBackData.notes ?? []).length; i++) {
      var itTmp = callBackData.notes?[i];
      AioInvoiceItemEntity it = AioInvoiceItemEntity(
        goodsName: itTmp,
      );
      items.add(it);
    }
    // Danh sách coupon Todo: Confirm lại với Thúy để tạo được model mẫu
    // for (int i = 0; i < (callBackData.coupons ?? []).length; i++) {
    //   var itTmp = callBackData.coupons?[i];
    //   AioInvoiceItemEntity it = AioInvoiceItemEntity(
    //     goodsName: itTmp,
    //   );
    //   items.add(it);
    // }

    AioInvoiceEntity invoice = AioInvoiceEntity(
      address: callBackData.address,
      bankAccount: callBackData.bankAccount,
      bankName: callBackData.bankName,
      companyName: callBackData.companyName,
      customerName: callBackData.customerName,
      paymentMethod: callBackData.paymentMethodStr,
      email: callBackData.email,
      description: callBackData.description,
      taxCode: callBackData.taxCode,
      aioInvoiceItemDTOS: items,
    );

    ref.read(aioContractDetailProvider.notifier).updateInvoice(
          invoice: invoice,
        );
  }

  @override
  void dispose() {
    collectedMoneyController.dispose();
    costSaleController.dispose();
    costInstallationWorkerController.dispose();
    costMarketingController.dispose();
    costReceptionController.dispose();
    costShippingController.dispose();
    costLaborController.dispose();
    costSuppliesSubController.dispose();
    costShippingController.dispose();
    costReceptionController.dispose();
    super.dispose();
  }

  selectSupply({
    required AioContractDetailState state,
    required List<int?> listGoodIds,
    bool? isSerial,
    bool? disableCheckbox,
    int? indexSupply,
  }) {
    context.push(
      RouterPaths.aioCheckInventory,
      extra: AioCheckInventoryArguments(
          data:
              state.contractDetail?.code ?? state.contractDetail?.contractCode,
          contractId: state.contractDetail?.contractId,
          listGoodIds: listGoodIds,
          disableCheckbox: disableCheckbox,
          isSerial: isSerial,
          listPackageGoods: state.listPackageGoods,
          indexSupply: indexSupply,
          confirmSelectItem: (data) {
            if (isSerial == true) {
              ref
                  .read(aioContractDetailProvider.notifier)
                  .updateListSerial(data: data, indexSupply: indexSupply!);
            } else {
              ref
                  .read(aioContractDetailProvider.notifier)
                  .updateListPackageGoodSelected(data);
            }
          }),
    );
  }

  SysUserRequest getUserInfo() {
    return SysUserRequest(
      authenticationInfo: AuthenticationInfo(
        type: 0,
        username: GlobalData.instance.userInfo?.username,
      ),
      sysUserId: GlobalData.instance.userInfo?.sysUserId,
      departmentId: GlobalData.instance.userInfo?.sysGroupId,
      flag: 0,
      isTtqh: false,
      ft: false,
    );
  }

  // calculateFee(AioContractDetailState state) async {
  //   int? costSale = costSaleController.text.toString().trim().isEmpty
  //       ? 0
  //       : int.parse(costSaleController.text.toString().replaceAll(".", ""));
  //   int? costInstallationWorker =
  //       costInstallationWorkerController.text.toString().trim().isEmpty
  //           ? 0
  //           : int.parse(costInstallationWorkerController.text
  //               .toString()
  //               .replaceAll(".", ""));
  //   int? costMarketing = costMarketingController.text.toString().trim().isEmpty
  //       ? 0
  //       : int.parse(costMarketingController.text.toString().replaceAll(".", ""));
  //   int? costReception = costReceptionController.text.toString().trim().isEmpty
  //       ? 0
  //       : int.parse(costReceptionController.text.toString().replaceAll(".", ""));
  //   int? costShipping = costShippingController.text.toString().trim().isEmpty
  //       ? 0
  //       : int.parse(costShippingController.text.toString().replaceAll(".", ""));
  //   int? costLabor = costLaborController.text.toString().trim().isEmpty
  //       ? 0
  //       : int.parse(costLaborController.text.toString().replaceAll(".", ""));
  //   int? costSuppliesSub = costSuppliesSubController.text.toString().trim().isEmpty
  //       ? 0
  //       : int.parse(costSuppliesSubController.text.toString().replaceAll(".", ""));;
  //   int? costNoSalary = state.costNoSalary;
  //
  //   // Bán hàng + lắp đặt, thuê ngoài  <= cnct
  //   if (state.costCNKT < (costInstallationWorker + costSale)) {
  //     costSaleController.text = "";
  //     costInstallationWorkerController.text = "";
  //     AppDialog.showDialogCenter(
  //       context,
  //       message: "Tổng CP vật tư phụ lắp đặt, CP vận chuyển và CP tiếp khách phải bẳng CP không lương",
  //       status: DialogStatus.error,
  //     );
  //     costNoSalary = state.costCNKT;
  //   } else {
  //     costNoSalary = state.costCNKT - (costInstallationWorker + costSale);
  //   }
  //   // Lắp đặt, thuê ngoài >= Thuê ngoài trước VAT
  //   if (costInstallationWorker < costLabor) {
  //
  //   }
  //   costNoSalary = state.costCNKT - (costInstallationWorker + costSale);
  //   await ref
  //       .read(aioContractDetailProvider.notifier)
  //       .updateCostNoSalary(cost: costNoSalary);
  //   return true;
  // }

  setValueCostNoSalary(AioContractDetailState state, {String? type}) {
    var contractServiceDto = state.contractDetail;
    int? costSale = costSaleController.text.toString().trim().isEmpty
        ? 0
        : int.parse(costSaleController.text.toString().replaceAll(".", ""));
    int? costInstallationWorker =
        costInstallationWorkerController.text.toString().trim().isEmpty
            ? 0
            : int.parse(costInstallationWorkerController.text
                .toString()
                .replaceAll(".", ""));
    // int? costMarketing = costMarketingController.text.toString().trim().isEmpty
    //     ? 0
    //     : int.parse(
    //         costMarketingController.text.toString().replaceAll(".", ""));
    // int? costReception = costReceptionController.text.toString().trim().isEmpty
    //     ? 0
    //     : int.parse(
    //         costReceptionController.text.toString().replaceAll(".", ""));
    // int? costShipping = costShippingController.text.toString().trim().isEmpty
    //     ? 0
    //     : int.parse(costShippingController.text.toString().replaceAll(".", ""));
    // int? costLabor = costLaborController.text.toString().trim().isEmpty
    //     ? 0
    //     : int.parse(costLaborController.text.toString().replaceAll(".", ""));
    // int? costSuppliesSub =
    //     costSuppliesSubController.text.toString().trim().isEmpty
    //         ? 0
    //         : int.parse(
    //             costSuppliesSubController.text.toString().replaceAll(".", ""));

    int? costNoSalary = state.costNoSalary;

    if (contractServiceDto?.configServiceStatus == 2 &&
        contractServiceDto?.isInternal == null) {
      if (state.costCNKT >= (costSale + costInstallationWorker)) {
        costNoSalary = state.costCNKT - costSale - costInstallationWorker;
      } else {
        AppDialog.showDialogCenter(
          context,
          message:
              "Tổng chi phí bán hàng và chi phí nhân công lắp đặt thuê ngoài phải nhỏ hơn hoặc bằng chi phí CNKT",
          status: DialogStatus.error,
        );
        if (type == "costSaleController") {
          costSaleController.text = "";
        }
        if (type == "costInstallationWorkerController") {
          costInstallationWorkerController.text = "";
        }
      }
      ref
          .read(aioContractDetailProvider.notifier)
          .updateCostNoSalary(cost: costNoSalary);
    } else if (contractServiceDto?.configServiceStatus == 2 &&
        contractServiceDto?.isInternal == '1') {
      if (state.costCNKT >= costInstallationWorker) {
        costNoSalary = state.costCNKT - costInstallationWorker;
      } else {
        AppDialog.showDialogCenter(
          context,
          message:
              "Chi phí nhân công lắp đặt thuê ngoài phải nhỏ hơn hoặc bằng chi phí CNKT",
          status: DialogStatus.error,
        );
        costInstallationWorkerController.text = "";
        costNoSalary = state.costCNKT;
      }
      ref
          .read(aioContractDetailProvider.notifier)
          .updateCostNoSalary(cost: costNoSalary);
    } else if (contractServiceDto?.configServiceStatus == 8 &&
        contractServiceDto?.isInternal == '1') {
      if (state.costCNKT >= costInstallationWorker) {
        costNoSalary = state.costCNKT - costSale;
      } else {
        AppDialog.showDialogCenter(
          context,
          message: "Chi phí bán hàng phải nhỏ hơn hoặc bằng chi phí CNKT",
          status: DialogStatus.error,
        );
        costSaleController.text = "";

        costNoSalary = state.costCNKT;
      }
      ref
          .read(aioContractDetailProvider.notifier)
          .updateCostNoSalary(cost: costNoSalary);
    }
  }

  _buildItemQuantity(quantity) {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: Row(
                children: [
                  Text(
                    "Số lượng",
                    style: UITextStyle.caption1Regular.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              "x$quantity",
              style: UITextStyle.body2Regular.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ],
        ),
        const SizedBox(
          height: 8,
        ),
      ],
    );
  }
}
