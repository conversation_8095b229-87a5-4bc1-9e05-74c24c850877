part of 'list_order_view_model.dart';

//ignore: must_be_immutable
class AllOrderState extends Equatable {
  final LoadStatus loadStatus;
  final LoadStatus countStatus;
  final String? message;
  final List<OrderInfoShortEntity>? shortOrders;
  final bool isSearching;
  final CountOrderEntity? countOrderEntity;
  final OrderType? orderType;
  final String? keySearch;
  final String? status;

  String? startTimeStr;
  String? endTimeStr;
  DateTime? startDate;
  DateTime? endDate;
  List<String>? participantTypes;
  final int? totalItems;
  final int currentPage;
  CodeEntity? province;
  List<CodeEntity>? districts;
  List<String>? ordersType;
  List<OrderTypeFilter>? orderTypesDefault;

  AllOrderState({
    this.loadStatus = LoadStatus.initial,
    this.countStatus = LoadStatus.initial,
    this.message,
    this.shortOrders,
    this.isSearching = false,
    this.countOrderEntity,
    this.startTimeStr,
    this.participantTypes,
    this.endTimeStr,
    this.currentPage = 0,
    this.totalItems,
    this.province,
    this.districts,
    this.orderType,
    this.startDate,
    this.endDate,
    this.ordersType,
    this.keySearch,
    this.status,
    this.orderTypesDefault,
  });

  AllOrderState copyWith({
    LoadStatus? loadStatus,
    LoadStatus? countStatus,
    String? message,
    List<OrderInfoShortEntity>? shortOrders,
    bool? isSearching,
    CountOrderEntity? countOrderEntity,
    String? startTime,
    String? endTime,
    List<String>? participantTypes,
    int? totalItems,
    int? currentPage,
    CodeEntity? province,
    List<CodeEntity>? districts,
    OrderType? orderType,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? ordersType,
    String? keySearch,
    String? status,
    List<OrderTypeFilter>? orderTypesDefault,
  }) {
    return AllOrderState(
      loadStatus: loadStatus ?? this.loadStatus,
      countStatus: countStatus ?? this.countStatus,
      message: message ?? this.message,
      shortOrders: shortOrders ?? this.shortOrders,
      isSearching: isSearching ?? this.isSearching,
      countOrderEntity: countOrderEntity ?? this.countOrderEntity,
      startTimeStr: startTime ?? startTimeStr,
      endTimeStr: endTime ?? endTimeStr,
      participantTypes: participantTypes ?? this.participantTypes,
      currentPage: currentPage ?? this.currentPage,
      totalItems: totalItems ?? this.totalItems,
      districts: districts ?? this.districts,
      province: province ?? this.province,
      orderType: orderType ?? this.orderType,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      ordersType: ordersType ?? this.ordersType,
      keySearch: keySearch ?? this.keySearch,
      status: status ?? this.status,
      orderTypesDefault: orderTypesDefault ?? this.orderTypesDefault,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        countStatus,
        message,
        shortOrders,
        isSearching,
        countOrderEntity,
        currentPage,
        totalItems,
        province,
        districts,
        orderType,
        startTimeStr,
        endTimeStr,
        startDate,
        endDate,
        ordersType,
        keySearch,
        status,
        orderTypesDefault,
      ];
}
