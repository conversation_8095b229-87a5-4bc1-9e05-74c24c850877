import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:intl/intl.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/entities/order/order_info_short_entity.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/process_kpi_status.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_installation/order_installation_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_shipping_detail/order_shipping_detail_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/order_single_service_page.dart';
import 'package:vcc/presentation/views/widgets/build_info_row_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/utils/app_utils.dart';

class OrderItem extends StatelessWidget {
  final OrderInfoShortEntity item;
  final Function? callBackOnBack;

  const OrderItem({
    super.key,
    required this.item,
    this.callBackOnBack,
  });

  @override
  Widget build(BuildContext context) {
    var inputFormat = DateFormat(DateTimeFormater.dateTimeSecond);
    String hourFrom = '';
    String month = '';
    String day = '';

    if ((item.startTime ?? "").isNotEmpty) {
      String scheduleDate;

      if ((item.scheduleTime ?? '').isNotEmpty) {
        scheduleDate = item.scheduleTime!;
        hourFrom = DateFormat(DateTimeFormater.hourMinute)
            .format(inputFormat.parse(scheduleDate));
      } else {
        if (item.isHotOrder ?? false) {
          scheduleDate = item.endTime!;
          hourFrom = DateFormat(DateTimeFormater.hourMinute)
              .format(inputFormat.parse(scheduleDate));
        } else {
          scheduleDate = item.startTime!;

          if ((item.endTime ?? '').isNotEmpty) {
            hourFrom = item.getTimeRange;
          } else {
            hourFrom = DateFormat(DateTimeFormater.hourMinute)
                .format(inputFormat.parse(scheduleDate));
          }
        }
      }

      day = DateFormat('dd').format(inputFormat.parse(scheduleDate));

      month = DateFormat(
        DateTimeFormater.onlyMonth,
        BaseConstant.localeVi,
      ).format(inputFormat.parse(scheduleDate));
    }

    String statusName = "";

    if (item.orderType == OrderType.smart.keyToServer) {
      if (item.status == OrderStatus.receptionWaiting) {
        statusName = "Chờ tiếp nhận";
      } else {
        statusName = item.status?.label ?? '';
      }
    } else {
      statusName = item.status?.label ?? '';
    }

    return GestureDetector(
      onTap: () async {
        final orderType = OrderTypeExtension.fromString(
          item.orderType,
        );

        if (orderType == OrderType.setup) {
          await context.push(
            RouterPaths.orderInstallationDetail,
            extra: OrderInstallationArguments(
              orderCode: item.orderCode ?? "",
            ),
          );

          callBackOnBack?.call();
        } else if (orderType == OrderType.smart) {
          await context.push(
            RouterPaths.orderShippingDetail,
            extra: OrderShippingDetailArguments(
              orderCode: item.orderCode ?? "",
            ),
          );

          callBackOnBack?.call();
        } else {
          await context.push(
            RouterPaths.orderSingleServiceDetail,
            extra: OrderSingleServiceArguments(
              orderCode: item.orderCode ?? "",
            ),
          );

          callBackOnBack?.call();
        }
      },
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          color: BaseColors.backgroundWhite,
        ),
        margin: const EdgeInsets.only(top: 8),
        padding: const EdgeInsets.only(
          top: 12,
          left: 12,
          right: 16,
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                left: 4,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: <Widget>[
                  Row(
                    children: [
                      Flexible(
                        child: Text(
                          item.orderCode ?? '',
                          overflow: TextOverflow.ellipsis,
                          maxLines: 3,
                          style: UITextStyle.body2SemiBold,
                        ),
                      ),
                      const SizedBox(width: 4),
                      InkWellWidget(
                        onTap: () {
                          AppUtils.copyToClipboard(item.orderCode ?? '');
                        },
                        child: MyAssets.icons.copyClipBoard.svg(),
                      ),
                    ],
                  ),
                  Row(
                    children: [
                      Container(
                        margin: const EdgeInsets.symmetric(
                          vertical: 8,
                        ),
                        padding: const EdgeInsets.symmetric(
                          horizontal: 12,
                          vertical: 3,
                        ),
                        decoration: BoxDecoration(
                          color: item.status?.surfaceColor,
                          borderRadius: BorderRadius.circular(360),
                        ),
                        child: Text(
                          statusName,
                          style: UITextStyle.body2Medium.copyWith(
                            color: item.status?.color,
                          ),
                        ),
                      ),
                      if (item.processKPIStatus != null) ...[
                        const Spacer(),
                        item.processKPIStatus == ProcessKpiStatus.deadline
                            ? Row(
                                mainAxisSize: MainAxisSize.min,
                                children: <Widget>[
                                  MyAssets.icons.iconWarningOrangeS16.svg(),
                                  const SizedBox(width: 4),
                                  Text(
                                    item.processKPIStatus?.title ?? '',
                                    style: UITextStyle.caption1Medium.copyWith(
                                      color: BaseColors.warningHover,
                                    ),
                                  ),
                                ],
                              )
                            : item.processKPIStatus == ProcessKpiStatus.expired
                                ? Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: <Widget>[
                                      MyAssets.icons.iconWarningRedS16.svg(),
                                      const SizedBox(width: 4),
                                      Text(
                                        item.processKPIStatus?.title ?? '',
                                        style:
                                            UITextStyle.caption1Medium.copyWith(
                                          color: BaseColors.error,
                                        ),
                                      ),
                                    ],
                                  )
                                : const SizedBox(),
                      ],
                    ],
                  ),
                  Text(
                    'Quản lý đơn: ${item.performer ?? ""}',
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                    style: UITextStyle.caption1Medium.copyWith(
                      color: BaseColors.textSubtitle,
                    ),
                  ),
                ],
              ),
            ),
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 4,
              ),
              child: Row(
                children: [
                  Stack(
                    children: [
                      Padding(
                        padding: const EdgeInsets.only(
                          left: 4,
                          top: 8,
                        ),
                        child: Container(
                          width: 92,
                          decoration: BoxDecoration(
                            color: BaseColors.backgroundGray,
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: BaseColors.borderDivider,
                            ),
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 8,
                            vertical: 30,
                          ),
                          child: Center(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.center,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Visibility(
                                  visible: (item.startTime ?? '').isNotEmpty,
                                  child: Text(
                                    day,
                                    style: UITextStyle.body1SemiBold.copyWith(
                                      color: BaseColors.textLabel,
                                    ),
                                  ),
                                ),
                                Text(
                                  month,
                                  style: UITextStyle.body2SemiBold.copyWith(
                                    color: BaseColors.textLabel,
                                  ),
                                ),
                                Padding(
                                  padding:
                                      const EdgeInsets.symmetric(vertical: 6.0),
                                  child: DividerWidget(
                                    height: 1,
                                    color: BaseColors.borderDefault,
                                  ),
                                ),
                                Text(
                                  hourFrom,
                                  style: UITextStyle.body2SemiBold.copyWith(
                                    color: BaseColors.textLabel,
                                  ),
                                  maxLines: 3,
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                      Visibility(
                        visible: item.isHotOrder ?? false,
                        child: Positioned(
                          top: 0,
                          left: 0,
                          child: MyAssets.icons.iconHotS24.svg(),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        BuildInfoRowWidget(
                          icon:
                              UserTypeExtension.fromString(item.customerType) ==
                                      UserType.personal
                                  ? MyAssets.icons.contactCustomer.svg()
                                  : MyAssets.icons.iconBuilding.svg(),
                          text: item.customerName ?? '',
                        ),
                        BuildInfoRowWidget(
                          icon: MyAssets.icons.iconBookS16.svg(),
                          text: item.orderName ?? '',
                        ),
                        BuildInfoRowWidget(
                          icon: MyAssets.icons.location.svg(),
                          text: item.customerAddress ?? '',
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
          ],
        ),
      ),
    );
  }
}
