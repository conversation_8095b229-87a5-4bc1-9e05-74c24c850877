import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/list_order_body.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/order/count_order_entity.dart';
import 'package:vcc/domain/entities/order/order_info_short_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/order_type_filter.dart';
import 'package:vcc/utils/log_utils.dart';

part 'list_order_state.dart';

final allOrderProvider =
    StateNotifierProvider.autoDispose<OrderViewModel, AllOrderState>(
        (ref) => OrderViewModel(ref: ref));

class OrderViewModel extends StateNotifier<AllOrderState> {
  final Ref ref;

  OrderViewModel({
    required this.ref,
  }) : super(AllOrderState());

  Future<void> getOrders({
    ListOrderBody? body,
    bool isReloadCount = true,
  }) async {
    if (body?.province == null) {
      state.province = null;
    }
    if (body?.districts == null) {
      state.districts = null;
    }
    if (body?.participantTypes == null) {
      state.participantTypes = null;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      startTime: body?.startTime,
      endTime: body?.endTime,
      participantTypes: body?.participantTypes,
      province: body?.province,
      districts: body?.districts,
      ordersType: body?.ordersType,
      keySearch: body?.keyword,
      status: body?.status,
    );

    try {
      if (isReloadCount) {
        getCountOrders();
      }

      final result = await appLocator<OrderRepository>().getShortOrders(
        body: ListOrderBody(
          keyword: state.keySearch,
          page: 0,
          pageSize: BaseConstant.defaultLimitSize,
          startTime: state.startTimeStr,
          endTime: state.endTimeStr,
          participantTypes: state.participantTypes,
          ordersType: (state.ordersType ?? []).isEmpty
              ? (state.orderTypesDefault ?? [])
                  .map((e) => e.keyToServer)
                  .toList()
              : state.ordersType,
          province: state.province,
          districts: state.districts,
          status: state.status,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            shortOrders: data.data,
            totalItems: data.total ?? 0,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> updateOrders() async {
    try {
      getCountOrders();

      final result = await appLocator<OrderRepository>().getShortOrders(
        body: ListOrderBody(
          keyword: state.keySearch,
          page: state.currentPage,
          pageSize: BaseConstant.defaultLimitSize,
          startTime: state.startTimeStr,
          endTime: state.endTimeStr,
          participantTypes: state.participantTypes,
          ordersType: (state.ordersType ?? []).isEmpty
              ? (state.orderTypesDefault ?? [])
                  .map((e) => e.keyToServer)
                  .toList()
              : state.ordersType,
          province: state.province,
          districts: state.districts,
          status: state.status,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            shortOrders: data.data,
            totalItems: data.total ?? 0,
          );
        },
        error: (err) {},
      );
    } catch (error) {
      LogUtils.d('error: $error');
    }
  }

  Future<void> getCountOrders() async {
    state = state.copyWith(
      countStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().getCountOrder(
        body: ListOrderBody(
          keyword: state.keySearch,
          page: state.currentPage,
          pageSize: BaseConstant.defaultLimitSize,
          startTime: state.startTimeStr,
          endTime: state.endTimeStr,
          participantTypes: state.participantTypes,
          ordersType: (state.ordersType ?? []).isEmpty
              ? (state.orderTypesDefault ?? [])
                  .map((e) => e.keyToServer)
                  .toList()
              : state.ordersType,
          province: state.province,
          districts: state.districts,
          status: state.status,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            countOrderEntity: data,
            countStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            countStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        countStatus: LoadStatus.failure,
      );
    }
  }

  void changeSearchStatus(bool status) {
    state = state.copyWith(
      isSearching: status,
    );
  }

  Future<void> fetchNextData() async {
    if (state.shortOrders!.length >= (state.totalItems ?? 0)) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final result = await appLocator<OrderRepository>().getShortOrders(
        body: ListOrderBody(
          keyword: state.keySearch,
          page: state.currentPage + 1,
          pageSize: BaseConstant.defaultLimitSize,
          startTime: state.startTimeStr,
          endTime: state.endTimeStr,
          participantTypes: state.participantTypes,
          ordersType: (state.ordersType ?? []).isEmpty
              ? (state.orderTypesDefault ?? [])
                  .map((e) => e.keyToServer)
                  .toList()
              : state.ordersType,
          province: state.province,
          districts: state.districts,
          status: state.status,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            shortOrders: state.shortOrders! + (data.data ?? []),
            currentPage: state.currentPage + 1,
            loadStatus: LoadStatus.success,
          );
        },
        error: (e) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void setOrderType(List<OrderType>? orderType) {
    List<OrderTypeFilter> orderTypes = [];

    orderType?.forEach(
      (element) {
        OrderTypeFilter? orderTypeFilter;

        if (element == OrderType.partnerResolveProblem ||
            element == OrderType.partnerOperate ||
            element == OrderType.partnerIsInstallingOrder ||
            element == OrderType.partnerNotWarrantyService ||
            element == OrderType.partnerWarrantyService ||
            element == OrderType.partnerMaintenance) {
          orderTypeFilter = OrderTypeFilter.partners;
        } else if (element == OrderType.salePointSingle ||
            element == OrderType.salePointCombo ||
            element == OrderType.salePoint) {
          orderTypeFilter = OrderTypeFilter.salePoints;
        } else {
          orderTypeFilter = OrderTypeFilter.values
              .where((e) => e.keyToServer == element.keyToServer)
              .firstOrNull;
        }

        if (orderTypeFilter != null) {
          if (orderTypes.isNotEmpty) {
            if (!orderTypes.contains(orderTypeFilter)) {
              orderTypes.add(
                orderTypeFilter,
              );
            }
          } else {
            orderTypes.add(
              orderTypeFilter,
            );
          }
        }
      },
    );

    state = state.copyWith(
      orderType: (orderType?.length ?? 0) == 1 ? orderType?.first : null,
      orderTypesDefault: orderTypes,
    );
  }

  void changeDate(
    DateTime? startTime,
    DateTime? endTime,
  ) {
    if (startTime == null) {
      state.startDate = null;
      state = state.copyWith(
        startDate: null,
      );
    }

    if (endTime == null) {
      state.endDate = null;
      state = state.copyWith(
        endDate: null,
      );
    }

    state = state.copyWith(
      startDate: startTime,
      endDate: endTime,
    );
  }

  Future<bool> checkShowWarningOrder() async {
    try {
      final result =
          await appLocator<OrderRepository>().checkShowWarningOrder();

      bool isShow = false;

      await result?.when(
        success: (data) async {
          isShow = data.isShowAlert ?? false;
          state = state.copyWith(
            message: data.message,
          );
        },
      );

      return isShow;
    } catch (error) {
      return false;
    }
  }
}
