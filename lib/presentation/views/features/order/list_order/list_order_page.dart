import 'package:base_ui/base_ui.dart';
import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/theme/theme.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/body/list_order_body.dart';
import 'package:vcc/domain/entities/order_status_ui_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_order/filter_order_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/list_order/list_order_view_model.dart';
import 'package:vcc/presentation/views/features/order/list_order/widgets/order_item.dart';
import 'package:vcc/presentation/views/features/order/list_order/widgets/order_status_single_widget.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/debouncer.dart';

class ListOrderArguments {
  final List<OrderType>? orderTypes;

  ListOrderArguments({
    this.orderTypes,
  });
}

class ListOrderPage extends StatefulHookConsumerWidget {
  const ListOrderPage({
    super.key,
    this.arguments,
  });

  final ListOrderArguments? arguments;

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _ListOrderPage();
}

class _ListOrderPage extends ConsumerState<ListOrderPage> {
  late ScrollController scrollController;
  late ScrollController statusScrollController;
  late TextEditingController searchController;
  int position = 0;
  List<OrderStatusEntity> listOrderStatus = [];
  late Debounce<String> deBouncer;

  @override
  void initState() {
    Future(() async {
      ref.read(allOrderProvider.notifier).setOrderType(
            widget.arguments?.orderTypes,
          );

      await ref.read(allOrderProvider.notifier).getOrders();
    });

    scrollController = ScrollController();
    searchController = TextEditingController();
    scrollController.addListener(_scrollListener);
    deBouncer = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref.read(allOrderProvider.notifier).getOrders(
              body: ListOrderBody(
                status: listOrderStatus[position].orderStatus.keyToServer,
                keyword: value,
              ),
            );
      },
    );
    super.initState();
  }

  @override
  void dispose() {
    scrollController.dispose();
    super.dispose();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(allOrderProvider.notifier).fetchNextData();
    }
  }

  Future<void> refreshData() async {
    ref.read(allOrderProvider.notifier).getOrders(
          body: ListOrderBody(
            status: listOrderStatus[position].orderStatus.keyToServer,
            keyword: searchController.text,
          ),
        );
  }

  @override
  Widget build(BuildContext context) {
    ref.listen<AllOrderState>(
      allOrderProvider,
      (previous, current) {
        if (previous?.countStatus != current.countStatus) {
          if (current.countStatus == LoadStatus.success) {
            mapDataToOrderStatus();
          }
        }
      },
    );

    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Danh sách đơn hàng",
      ),
      body: SafeArea(
        child: Column(
          children: <Widget>[
            _buildListStatus(),
            _buildSearchZone(),
            Expanded(
              child: _buildOrder(),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrder() {
    var orderState = ref.watch(allOrderProvider);
    if (orderState.loadStatus == LoadStatus.loading) {
      return const LoadingIndicatorWidget();
    }
    if (orderState.loadStatus == LoadStatus.failure) {
      return Container(
        color: BaseColors.backgroundGray,
        child: EmptyListWidget(
          title: "Không có dữ liệu",
          onRefresh: refreshData,
        ),
      );
    }
    if ((orderState.shortOrders ?? []).isEmpty) {
      return Container(
        color: BaseColors.backgroundGray,
        child: EmptyListWidget(
          title: "Không có dữ liệu",
          onRefresh: refreshData,
        ),
      );
    } else {
      return Container(
        color: BaseColors.backgroundGray,
        child: RefreshIndicatorWidget(
          onRefresh: refreshData,
          child: ListView.builder(
            controller: scrollController,
            physics: const AlwaysScrollableScrollPhysics(),
            itemCount: orderState.shortOrders?.length ?? 0,
            itemBuilder: (context, index) {
              return OrderItem(
                item: orderState.shortOrders![index],
                callBackOnBack: () {
                  ref.read(allOrderProvider.notifier).updateOrders();
                },
              );
            },
          ),
        ),
      );
    }
  }

  Widget _buildSearchZone() {
    var state = ref.watch(allOrderProvider);

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
        vertical: 8,
      ),
      child: Row(
        children: [
          Expanded(
            child: SearchTextFieldWidget(
              controller: searchController,
              hintText: 'Mã đơn hàng, tên khách hàng',
              onChanged: (value) {
                deBouncer.value = value;
              },
            ),
          ),
          const SizedBox(width: 16),
          InkWellWidget(
            onTap: () {
              FocusScope.of(context).unfocus();
              var orderState = ref.watch(allOrderProvider);

              AppBottomSheet.showNormalBottomSheet(
                context,
                title: "Bộ lọc",
                height: MediaQuery.of(context).size.height * 0.8,
                child: FilterOrderViewSheet(
                  startDateStr: orderState.startTimeStr,
                  endDateStr: orderState.endTimeStr,
                  orderUserType: orderState.participantTypes,
                  province: orderState.province,
                  districts: orderState.districts,
                  startDate: orderState.startDate,
                  endDate: orderState.endDate,
                  ordersType: orderState.ordersType,
                  showOrderType:
                      state.orderType == OrderType.smart ? false : true,
                  onApply: (FilterArgumentCallback value) {
                    ref.read(allOrderProvider.notifier).getOrders(
                          body: ListOrderBody(
                            ordersType: (value.ordersType ?? [])
                                .map((e) => e.keyToServer)
                                .toList(),
                            status: listOrderStatus[position]
                                .orderStatus
                                .keyToServer,
                            keyword: searchController.text.isEmpty
                                ? null
                                : searchController.text,
                            participantTypes: value.participantTypes,
                            startTime: value.startTime?.toIso8601String(),
                            endTime: value.endTime?.toIso8601String(),
                            province: value.province,
                            districts: value.districts,
                          ),
                        );
                    ref.read(allOrderProvider.notifier).changeDate(
                          value.startTime,
                          value.endTime,
                        );
                  },
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                children: [
                  MyAssets.icons.filter.svg(),
                  Text(
                    'Lọc',
                    style: UITextStyle.caption1Medium.copyWith(
                      color: BaseColors.textSubtitle,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildListStatus() {
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        physics: const BouncingScrollPhysics(),
        itemCount: listOrderStatus.length,
        itemBuilder: (context, index) {
          final item = listOrderStatus[index];

          return OrderStatusSingleWidget(
            icon: item.isSelected ? item.icon : item.iconDeActive,
            nameStatus: item.statusName,
            count: item.countOrder,
            onTap: () async {
              position = index;
              ref.read(allOrderProvider.notifier).getOrders(
                    isReloadCount: false,
                    body: ListOrderBody(
                      status: listOrderStatus[position].orderStatus.keyToServer,
                      keyword: searchController.text,
                    ),
                  );
              for (var element in listOrderStatus) {
                element.isSelected = false;
              }
              item.isSelected = true;

              if (!(widget.arguments?.orderTypes ?? [])
                  .contains(OrderType.smart)) {
                if (item.orderStatus == OrderStatus.processWaiting ||
                    item.orderStatus == OrderStatus.processing) {
                  final result = await ref
                      .read(allOrderProvider.notifier)
                      .checkShowWarningOrder();

                  if (result) {
                    if (!context.mounted) return;
                    AppDialog.showDialogConfirm(
                      context,
                      message: ref.watch(allOrderProvider).message,
                      buttonNameConfirm: "Thực hiện",
                      onConfirmAction: () {
                        context.push(
                          RouterPaths.warningOrder,
                        );
                      },
                    );
                  }
                }
              }
            },
          );
        },
      ),
    );
  }

  void mapDataToOrderStatus() {
    var countInfo = ref.watch(allOrderProvider).countOrderEntity;

    listOrderStatus = [
      OrderStatusEntity(
        icon: MyAssets.icons.totalOrderActive.svg(),
        iconDeActive: MyAssets.icons.totalOrderDeactive.svg(),
        statusName: 'Tất cả',
        orderStatus: OrderStatus.all,
        isSelected: position == 0,
        countOrder: countInfo?.totalOrder ?? 0,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.draftOrderActive.svg(),
        iconDeActive: MyAssets.icons.draftOrderActive.svg(),
        statusName: "Đã đăng ký",
        orderStatus: OrderStatus.registered,
        countOrder: countInfo?.registeredCount ?? 0,
        isSelected: position == 1,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.waitOrderActive.svg(),
        iconDeActive: MyAssets.icons.waitOrderDeactive.svg(),
        statusName: 'Xác nhận đơn hàng',
        orderStatus: OrderStatus.confirmWaiting,
        countOrder: countInfo?.confirmWaitingCount ?? 0,
        isSelected: position == 2,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.confirmOrderActive.svg(),
        iconDeActive: MyAssets.icons.confirmOrderDeactive.svg(),
        statusName: "Đảm bảo hàng hóa",
        orderStatus: OrderStatus.goodWarrant,
        countOrder: countInfo?.goodWarrantCount ?? 0,
        isSelected: position == 3,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.findingPartnerActive.svg(),
        iconDeActive: MyAssets.icons.findingPartnerDeactive.svg(),
        statusName: "Yêu cầu điều phối",
        orderStatus: OrderStatus.receptionWaiting,
        countOrder: countInfo?.receptionWaitingCount ?? 0,
        isSelected: position == 4,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.waitingProcessActive.svg(),
        iconDeActive: MyAssets.icons.waitingProcessDeactive.svg(),
        statusName: 'Chờ thực hiện',
        orderStatus: OrderStatus.processWaiting,
        countOrder: countInfo?.processWaitingCount ?? 0,
        isSelected: position == 5,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.deliveriesActive.svg(),
        iconDeActive: MyAssets.icons.deliveriesDeactive.svg(),
        statusName: 'Đang thực hiện ',
        orderStatus: OrderStatus.processing,
        countOrder: countInfo?.processingCount ?? 0,
        isSelected: position == 6,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.completeDeliveriesActive.svg(),
        iconDeActive: MyAssets.icons.completeDeliveriesDeactive.svg(),
        statusName: 'Đã thực hiện',
        orderStatus: OrderStatus.processed,
        countOrder: countInfo?.processedCount ?? 0,
        isSelected: position == 7,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.completeActive.svg(),
        iconDeActive: MyAssets.icons.completeDeactive.svg(),
        statusName: 'Hoàn thành',
        orderStatus: OrderStatus.complete,
        isSelected: position == 8,
      ),
      OrderStatusEntity(
        icon: MyAssets.icons.cancelActive.svg(),
        iconDeActive: MyAssets.icons.cancelDeactive.svg(),
        statusName: 'Hủy',
        orderStatus: OrderStatus.cancel,
        isSelected: position == 9,
      ),
    ];
  }
}
