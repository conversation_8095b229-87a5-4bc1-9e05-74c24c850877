import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/entities/association/association_entity.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/entities/draft_order_entity.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/service_customer_info/service_customer_info_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/service_order_info/service_order_info_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/service_payment_info/service_payment_info_page.dart';
import 'package:vcc/presentation/views/widgets/dash_painter_horizontal.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';

import 'create_service_order_view_model.dart';

class CreateServiceOrderArguments {
  final ServiceType? serviceType;
  final ServiceInfoEntity? serviceYearInfo;
  final SupplyEntity? supplyInfo;
  final List<ServiceInfoEntity>? listSingleService;
  final DraftOrderEntity? draftOrder;
  final bool? isFromDraftIcon;
  final bool? isCreateSpeedOrder;
  final ServiceType? speedOrderType; //when select speed order: DV or VT
  final CollectionInfoEntity? collectionInfo; //TT.TTKH
  final AssociationEntity? associationInfo; //YCTX

  CreateServiceOrderArguments({
    this.serviceType,
    this.serviceYearInfo,
    this.listSingleService,
    this.draftOrder,
    this.isFromDraftIcon,
    this.isCreateSpeedOrder,
    this.collectionInfo,
    this.associationInfo,
    this.supplyInfo,
    this.speedOrderType,
  });
}

class CreateServiceOrderPage extends StatefulHookConsumerWidget {
  final CreateServiceOrderArguments? arguments;

  const CreateServiceOrderPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<CreateServiceOrderPage> createState() =>
      _CreateServiceOrderPageState();
}

class _CreateServiceOrderPageState
    extends ConsumerState<CreateServiceOrderPage> {
  late PageController pageController;

  @override
  void initState() {
    pageController = PageController();
    String? wardCode;
    if (widget.arguments?.collectionInfo != null) {
      wardCode = widget.arguments?.collectionInfo?.ward?.code;
    } else if (widget.arguments?.associationInfo != null) {
      wardCode = widget.arguments?.associationInfo?.ward?.code;
    } else {
      wardCode = GlobalData.instance.addressDefault?.ward?.code;
    }

    Future(() {
      ref.read(createServiceOrderProvider.notifier).initData(
            associationInfo: widget.arguments?.associationInfo,
            collectionInfo: widget.arguments?.collectionInfo,
            serviceType: widget.arguments?.serviceType,
            serviceYearInfo: widget.arguments?.serviceYearInfo,
            singleServices: widget.arguments?.listSingleService,
            isCreateSpeedOrder: widget.arguments?.isCreateSpeedOrder,
            supplies: widget.arguments?.supplyInfo != null
                ? [widget.arguments!.supplyInfo!]
                : null,
            speedOrderType: widget.arguments?.speedOrderType,
          );
      ref.read(createServiceOrderProvider.notifier).checkSpeedOrder(
            orderType: widget.arguments?.serviceType?.keyToCreateOrder,
            wardCode: wardCode,
          );
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var createState = ref.watch(createServiceOrderProvider);

    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      body: Column(
        children: <Widget>[
          Container(
            color: BaseColors.primary,
            padding: const EdgeInsets.fromLTRB(16, 0, 16, 8),
            child: SafeArea(
              top: true,
              bottom: false,
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  InkWellWidget(
                    onTap: () {
                      context.pop();
                    },
                    child: Padding(
                      padding: const EdgeInsets.all(8),
                      child: MyAssets.icons.iconCloseS24.svg(),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: <Widget>[
                        buildStep(
                          stepNumber: 1,
                          label: 'Thông tin\nchung',
                          isActive: createState.pageIndex >= 1,
                          onTap: () {
                            if (createState.pageIndex >= 1) {
                              pageController.jumpToPage(0);
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .changePageIndex(1);
                            }
                          },
                        ),
                        buildDashedLine(),
                        buildStep(
                          stepNumber: 2,
                          label: 'Thông tin\ndịch vụ & vật tư',
                          isActive: createState.pageIndex >= 2,
                          onTap: () {
                            if (createState.pageIndex >= 2) {
                              pageController.jumpToPage(1);
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .changePageIndex(2);
                            }
                          },
                        ),
                        buildDashedLine(),
                        buildStep(
                          stepNumber: 3,
                          label: 'Hóa đơn &\nThanh toán',
                          isActive: createState.pageIndex == 3,
                          onTap: () {
                            if (createState.pageIndex >= 3) {
                              pageController.jumpToPage(2);
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .changePageIndex(3);
                            }
                          },
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(width: 40),
                ],
              ),
            ),
          ),
          Expanded(
            child: Container(
              color: BaseColors.primary,
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.only(top: 16),
                decoration: BoxDecoration(
                  color: BaseColors.backgroundWhite,
                  borderRadius: const BorderRadius.only(
                    topLeft: Radius.circular(12),
                    topRight: Radius.circular(12),
                  ),
                ),
                child: createState.loadStatus == LoadStatus.success
                    ? PageView(
                        controller: pageController,
                        physics: const NeverScrollableScrollPhysics(),
                        children: <Widget>[
                          ServiceCustomerInfoPage(
                            collectionInfo: widget.arguments?.collectionInfo,
                            associationInfo: widget.arguments?.associationInfo,
                            onNextPageCallBack: () {
                              pageController.jumpToPage(1);
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .changePageIndex(2);
                            },
                          ),
                          ServiceOrderInfoPage(
                            onNextPageCallBack: () {
                              pageController.jumpToPage(2);
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .changePageIndex(3);
                            },
                          ),
                          const ServicePaymentInfoPage(),
                        ],
                      )
                    : const Center(
                        child: LoadingIndicatorWidget(),
                      ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget buildStep({
    required int stepNumber,
    required String label,
    bool? isActive,
    Function? onTap,
  }) {
    return InkWell(
      onTap: () {
        onTap?.call();
      },
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Container(
            height: 32,
            width: 32,
            decoration: BoxDecoration(
              color: (isActive ?? false)
                  ? BaseColors.backgroundWhite
                  : BaseColors.primary,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: BaseColors.backgroundWhite,
              ),
            ),
            child: Center(
              child: Text(
                "$stepNumber",
                style: UITextStyle.body2Medium.copyWith(
                  color: (isActive ?? false)
                      ? BaseColors.primary
                      : BaseColors.backgroundWhite,
                ),
              ),
            ),
          ),
          const SizedBox(height: 8.0),
          Text(
            label,
            textAlign: TextAlign.center,
            style: UITextStyle.caption1Medium.copyWith(
              color: BaseColors.backgroundWhite,
            ),
          ),
        ],
      ),
    );
  }

  Widget buildDashedLine() {
    return Expanded(
      child: SizedBox(
        height: 32,
        child: Center(
          child: SizedBox(
            height: 2,
            width: double.infinity,
            child: CustomPaint(
              painter: DashPainterHorizontal(
                color: Colors.white,
                dashWidth: 2,
                dashSpace: 3,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    pageController.dispose();
    super.dispose();
  }
}
