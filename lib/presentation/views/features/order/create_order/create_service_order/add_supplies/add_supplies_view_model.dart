import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/supplies_type.dart';
import 'package:vcc/domain/enums/tax_type.dart';
import 'package:vcc/utils/log_utils.dart';

part 'add_supplies_state.dart';

final addSuppliesProvider =
    StateNotifierProvider.autoDispose<AddSuppliesViewModel, AddSuppliesState>(
  (ref) => AddSuppliesViewModel(ref: ref),
);

class AddSuppliesViewModel extends StateNotifier<AddSuppliesState> {
  final Ref ref;

  AddSuppliesViewModel({
    required this.ref,
  }) : super(const AddSuppliesState());

  void initData({
    List<SupplyEntity>? supplies,
  }) {
    state = state.copyWith(
      listSupplySelected: supplies,
    );
    ref.read(addSuppliesProvider.notifier).searchOem();
  }

  void changeSuppliesType(SuppliesType type) {
    state = state.copyWith(
      suppliesType: type,
    );
  }

  void changeTaxType(TaxType type) {
    if (!state.canChangeTax) return;

    final supply = state.normalSupply;
    supply?.vat = type.keyToServer;

    state = state.copyWith(
      taxType: type,
      normalSupply: supply,
    );
  }

  void selectSupply(SupplyEntity supply) {
    List<SupplyEntity> listSupplies = state.listOemSelected ?? [];

    if (listSupplies.isEmpty) {
      supply.isSelected = true;
      if (supply.quantity == 0) {
        supply.quantity = 1;
      }
      listSupplies.add(supply);
    } else {
      final checkExist =
          listSupplies.where((e) => e.code == supply.code).toList();

      if (checkExist.isNotEmpty) {
        supply.isSelected = false;
        listSupplies.remove(checkExist.first);
      } else {
        supply.isSelected = true;
        if (supply.quantity == 0) {
          supply.quantity = 1;
        }
        listSupplies.add(supply);
      }
    }

    state = state.copyWith(
      listOemSelected: listSupplies,
    );
    calculateMoney();
  }

  void selectSupplyNormal(SupplyEntity supply) {
    TaxType? tax;
    bool canChangeTax = true;

    if (supply.vat != null) {
      tax = TaxType.values.firstWhere(
        (element) {
          canChangeTax = false;
          return element.keyToServer == supply.vat;
        },
        orElse: () => TaxType.ten,
      );
    } else {
      supply.vat = TaxType.ten.keyToServer;
    }

    if (supply.price != null) {
      state = state.copyWith(
        enableInputPrice: false,
      );
    }

    state = state.copyWith(
      canChangeTax: canChangeTax,
      taxType: tax ?? TaxType.ten,
      normalSupply: supply,
    );
  }

  void changeChangeQuantity(num quantityOrder, int index) {
    var oemSupplies = state.oemSupplies;
    oemSupplies![index].quantity = quantityOrder;

    state = state.copyWith(
      oemSupplies: oemSupplies,
    );
    calculateMoney();
  }

  Future<void> searchOem({
    String? keySearch,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final resultParam = await appLocator<OrderRepository>().searchSupply(
        keyword: keySearch,
        supplyTypes: SuppliesType.oem.keyToServer,
      );

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            oemSupplies: data.data,
            totalOem: data.total ?? 0,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> fetchNextOemSupply() async {
    if (state.oemSupplies!.length >= state.totalOem) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final resultParam = await appLocator<OrderRepository>().searchSupply(
        keyword: state.keySearch,
        supplyTypes: SuppliesType.oem.keyToServer,
        page: state.currentPageOem + 1,
      );

      await resultParam?.when(
        success: (data) async {
          calculateMoney();
          state = state.copyWith(
            currentPageOem: state.currentPageOem + 1,
            oemSupplies: state.oemSupplies! + (data.data ?? []),
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) {
          LogUtils.d(err.message);
        },
      );
    } catch (error) {
      LogUtils.d("Lỗi lấy về danh sách vật tư");
    }
  }

  void calculateMoney() {
    num totalMoney = 0;
    if ((state.listOemSelected ?? []).isNotEmpty) {
      for (int i = 0; i < state.listOemSelected!.length; i++) {
        var money = (state.listOemSelected![i].quantity) *
            (state.listOemSelected![i].price ?? 0);

        totalMoney = totalMoney + money;
      }
    }

    state = state.copyWith(
      totalMoneyOem: totalMoney.toInt(),
    );
  }

  void changeQuantity(String quantity) {
    final supply = state.normalSupply;
    late double? number;

    if (quantity.isNotEmpty) {
      String quantityStr = quantity.replaceAll(('.'), '');
      quantityStr = quantityStr.replaceAll(",", '.');

      number = double.parse(quantityStr);

      if (supply?.maxQuantity != null) {
        if (number > (supply!.maxQuantity!)) {
          state = state.copyWith(
            errorQuantityMessage:
                "Số lượng tối đa không qua kho cho phép ${supply.maxQuantity}.",
          );
          return;
        }
      }

      supply?.quantityInput = number;
    }

    state = state.copyWith(
      normalSupply: supply,
      errorQuantityMessage: "",
    );

    calculatorPrice();
  }

  void changeOriginalPrice(String price) {
    final supply = state.normalSupply;
    final data = price.isEmpty ? "0" : price.replaceAll(('.'), '');
    supply?.originalPrice = double.parse(data);

    state = state.copyWith(
      normalSupply: supply,
    );
  }

  void changePrice(String price) {
    final supply = state.normalSupply;
    final data = price.isEmpty ? "0" : price.replaceAll(('.'), '');
    supply?.price = int.parse(data);

    state = state.copyWith(
      normalSupply: supply,
    );

    calculatorPrice();
  }

  void calculatorPrice() {
    final supply = state.normalSupply;
    final price = supply?.price ?? 0;
    final quantity = supply?.quantityInput ?? 0;
    final total = price * quantity;

    state = state.copyWith(
      totalMoney: total.toDouble(),
    );
  }

  bool validateForm({
    String? quantity,
    String? price,
  }) {
    bool isCheck = true;

    if ((quantity ?? "").isEmpty) {
      state = state.copyWith(
        errorQuantityMessage: "Vui lòng nhập số lượng",
      );
      isCheck = false;
    }

    if ((price ?? "").isEmpty) {
      state = state.copyWith(
        errorPriceMessage: "Vui lòng nhập giá bán",
      );
      isCheck = false;
    }

    if (isCheck) {
      state = state.copyWith(
        errorQuantityMessage: "",
        errorPriceMessage: "",
      );
    }

    return isCheck;
  }
}
