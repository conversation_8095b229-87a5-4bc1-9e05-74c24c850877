import 'package:base_ui/base_ui.dart';
import 'package:base_ui/theme/colors.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pattern_formatter/pattern_formatter.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/supplies_type.dart';
import 'package:vcc/domain/enums/tax_type.dart';
import 'package:vcc/domain/enums/warehouse_supply_type.dart';
import 'package:vcc/extensions/formatter.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/widgets/oem_supply_item_widget.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/normal_text_field.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/string_utils.dart';

import 'add_supplies_view_model.dart';

class AddSuppliesArguments {
  final List<SupplyEntity>? listSupplySelected;
  final DetailOrderEntity? orderInfo;

  AddSuppliesArguments({
    this.listSupplySelected,
    this.orderInfo,
  });
}

class AddSuppliesPage extends StatefulHookConsumerWidget {
  final AddSuppliesArguments? arguments;

  const AddSuppliesPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<AddSuppliesPage> createState() => _AddSuppliesPageState();
}

class _AddSuppliesPageState extends ConsumerState<AddSuppliesPage> {
  late TextEditingController quantityController;
  late TextEditingController priceController;
  late TextEditingController originalPriceController;

  late TextEditingController oemSearchController;
  late ScrollController oemController;

  @override
  void initState() {
    quantityController = TextEditingController();
    priceController = TextEditingController();
    originalPriceController = TextEditingController();
    oemSearchController = TextEditingController();
    oemController = ScrollController();
    oemController.addListener(_scrollListener);
    Future(() {
      ref.read(addSuppliesProvider.notifier).initData(
            supplies: widget.arguments?.listSupplySelected,
          );
    });

    super.initState();
  }

  void _scrollListener() {
    final maxScroll = oemController.position.maxScrollExtent;
    final currentScroll = oemController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(addSuppliesProvider.notifier).fetchNextOemSupply();
    }
  }

  @override
  void dispose() {
    quantityController.dispose();
    priceController.dispose();
    originalPriceController.dispose();
    oemSearchController.dispose();
    oemController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(addSuppliesProvider);

    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Thêm vật tư",
      ),
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildPage(state),
      ),
      bottomAction: state.suppliesType == SuppliesType.normal
          ? _buildBottomBar(
              price: StringUtils.formatFlexibleDouble(state.totalMoney),
              enableButton: state.normalSupply != null,
              onTap: () {
                final validate =
                    ref.read(addSuppliesProvider.notifier).validateForm(
                          quantity: quantityController.text,
                          price: priceController.text,
                        );

                if (validate) {
                  List<SupplyEntity> listData = [];
                  String quantityStr =
                      quantityController.text.replaceAll(('.'), '');
                  quantityStr = quantityStr.replaceAll(",", '.');

                  SupplyEntity supply = SupplyEntity(
                    name: state.normalSupply?.name,
                    code: state.normalSupply?.code,
                    unit: state.normalSupply?.unit,
                    thumbnailUrl: state.normalSupply?.thumbnailUrl,
                    supplyType: state.normalSupply?.supplyType,
                    vat: state.normalSupply?.vat,
                    price: state.normalSupply?.price,
                    originalPrice: state.normalSupply?.originalPrice,
                    outsourcePrice: state.normalSupply?.outsourcePrice,
                    wmsType: state.normalSupply?.getWmsType ==
                            WarehouseSupplyType.both
                        ? (double.parse(quantityStr) >
                                (state.normalSupply?.quantity ?? 0))
                            ? WarehouseSupplyType.wms.keyToServer
                            : WarehouseSupplyType.notWms.keyToServer
                        : state.normalSupply?.wmsType,
                    wmsTypeDisplay: state.normalSupply?.getWmsType ==
                            WarehouseSupplyType.both
                        ? (double.parse(quantityStr) >
                                (state.normalSupply?.quantity ?? 0))
                            ? WarehouseSupplyType.wms.keyToServer
                            : WarehouseSupplyType.notWms.keyToServer
                        : state.normalSupply?.wmsType,
                    maxQuantity: state.normalSupply?.maxQuantity,
                    percent: state.normalSupply?.percent,
                    isSerial: state.normalSupply?.isSerial,
                    serials: state.normalSupply?.serials,
                    serial: state.normalSupply?.serial,
                    quantity: state.normalSupply?.quantityInput ?? 1,
                    maxExportQuantity: state.normalSupply?.quantity,
                    isSelected: state.normalSupply?.isSelected ?? false,
                    isRequired: state.normalSupply?.isRequired,
                    defaultSupply: state.normalSupply?.defaultSupply,
                    minItem: state.normalSupply?.minItem,
                    maxItem: state.normalSupply?.maxItem,
                    step: state.normalSupply?.step,
                    valueType: state.normalSupply?.valueType,
                  );

                  listData.add(supply);
                  context.pop(listData);
                }
              },
            )
          : _buildBottomBar(
              price: StringUtils.formatMoney(state.totalMoneyOem ?? 0),
              enableButton: state.listOemSelected?.isNotEmpty ?? false,
              onTap: () {
                if ((state.listOemSelected ?? []).isEmpty) {
                  return;
                }

                context.pop(state.listOemSelected ?? []);
              },
            ),
    );
  }

  Widget _buildPage(AddSuppliesState state) {
    String supplyType = state.normalSupply?.getWmsName ?? "";

    if (state.normalSupply?.getWmsType == WarehouseSupplyType.both) {
      if (quantityController.text.isNotEmpty) {
        String quantityStr = quantityController.text.replaceAll(('.'), '');
        quantityStr = quantityStr.replaceAll(",", '.');

        if (double.parse(quantityStr) > (state.normalSupply?.quantity ?? 0)) {
          supplyType = WarehouseSupplyType.wms.display;
        } else {
          supplyType = WarehouseSupplyType.notWms.display;
        }
      }
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (!(widget.arguments?.orderInfo?.isOrderPartner ?? false)) ...[
          Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Phân loại kinh doanh ',
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
                const SizedBox(height: 12),
                Row(
                  children: <Widget>[
                    Expanded(
                      child: RadioWidget<SuppliesType>(
                        value: SuppliesType.normal,
                        groupValue: state.suppliesType,
                        onChanged: (value) {
                          ref
                              .read(addSuppliesProvider.notifier)
                              .changeSuppliesType(value);
                        },
                        displayWidget: (context, item) {
                          return _displayTypeText(item);
                        },
                      ),
                    ),
                    Expanded(
                      child: RadioWidget<SuppliesType>(
                        value: SuppliesType.oem,
                        groupValue: state.suppliesType,
                        onChanged: (value) {
                          ref
                              .read(addSuppliesProvider.notifier)
                              .changeSuppliesType(value);
                        },
                        displayWidget: (context, item) {
                          return _displayTypeText(item);
                        },
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          DividerWidget(
            height: 4,
            color: BaseColors.backgroundGray,
          )
        ],
        if (state.suppliesType == SuppliesType.normal ||
            (widget.arguments?.orderInfo?.isOrderPartner ?? false)) ...[
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  DropdownWidget(
                    labelText: "Vật tư",
                    isRequired: true,
                    content: state.normalSupply?.name ?? "",
                    suffix: MyAssets.icons.search.svg(),
                    validator: (value) {
                      if ((value ?? "").isEmpty) {
                        return "Vui lòng chọn vật tư";
                      }
                      return null;
                    },
                    onTap: () {
                      searchSupplies();
                    },
                  ),
                  if (state.normalSupply != null) ...[
                    const SizedBox(height: 4),
                    Padding(
                      padding: const EdgeInsets.only(left: 16),
                      child: Text(
                        "Loại vật tư: $supplyType",
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.textBody,
                        ),
                      ),
                    ),
                  ],
                  const SizedBox(height: 16),
                  Row(
                    children: [
                      Text(
                        'Số lượng & Giá',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                      Text(
                        " *",
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  Container(
                    decoration: BoxDecoration(
                      border: Border.all(
                        color: BaseColors.borderDefault,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Container(
                          padding: const EdgeInsets.only(left: 16),
                          decoration: BoxDecoration(
                            color: BaseColors.backgroundWhite,
                            borderRadius: const BorderRadius.only(
                              topLeft: Radius.circular(12),
                              topRight: Radius.circular(12),
                            ),
                          ),
                          child: Row(
                            children: [
                              Text(
                                "Số lượng",
                                style: UITextStyle.body1Regular.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                              Expanded(
                                child: NormalTextField(
                                  controller: quantityController,
                                  borderColor: Colors.white,
                                  textAlign: TextAlign.end,
                                  showClearIcon: false,
                                  keyboardType:
                                      const TextInputType.numberWithOptions(
                                    decimal: true,
                                  ),
                                  inputFormatters: [
                                    DecimalInputFormatter(),
                                  ],
                                  hintText: "0",
                                  onChanged: (val) {
                                    ref
                                        .read(addSuppliesProvider.notifier)
                                        .changeQuantity(val);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                        DividerWidget(
                          height: 1,
                          color: BaseColors.borderDefault,
                        ),
                        if (widget.arguments?.orderInfo?.isOrderPartner ??
                            false) ...[
                          Padding(
                            padding: const EdgeInsets.only(left: 16),
                            child: Row(
                              children: [
                                Text(
                                  "Giá vốn (không có VAT) ",
                                  style: UITextStyle.body1Regular.copyWith(
                                    color: BaseColors.textSubtitle,
                                  ),
                                ),
                                Expanded(
                                  child: NormalTextField(
                                    controller: originalPriceController,
                                    borderColor: Colors.white,
                                    textAlign: TextAlign.end,
                                    showClearIcon: false,
                                    keyboardType: TextInputType.number,
                                    inputFormatters: [
                                      ThousandsFormatter(allowFraction: true),
                                    ],
                                    hintText: "0đ",
                                    onChanged: (val) {
                                      ref
                                          .read(addSuppliesProvider.notifier)
                                          .changeOriginalPrice(val);
                                    },
                                  ),
                                ),
                              ],
                            ),
                          ),
                          DividerWidget(
                            height: 1,
                            color: BaseColors.borderDefault,
                          ),
                        ],
                        Container(
                          padding: const EdgeInsets.only(left: 16),
                          decoration: BoxDecoration(
                            color: state.enableInputPrice
                                ? BaseColors.backgroundWhite
                                : BaseColors.backgroundGray,
                            borderRadius: const BorderRadius.only(
                              bottomLeft: Radius.circular(12),
                              bottomRight: Radius.circular(12),
                            ),
                          ),
                          child: Row(
                            children: [
                              Text(
                                "Giá bán (VNĐ)",
                                style: UITextStyle.body1Regular.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                              Expanded(
                                child: NormalTextField(
                                  controller: priceController,
                                  textAlign: TextAlign.end,
                                  showClearIcon: false,
                                  enabled: state.enableInputPrice,
                                  hintText: "0đ",
                                  textInputAction: TextInputAction.done,
                                  keyboardType: TextInputType.number,
                                  borderRadius: const BorderRadius.only(
                                    bottomRight: Radius.circular(12),
                                  ),
                                  borderColor: state.enableInputPrice
                                      ? BaseColors.backgroundWhite
                                      : BaseColors.backgroundGray,
                                  inputFormatters: [
                                    ThousandsFormatter(allowFraction: true),
                                  ],
                                  onChanged: (val) {
                                    ref
                                        .read(addSuppliesProvider.notifier)
                                        .changePrice(val);
                                  },
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  Visibility(
                    visible: (state.errorQuantityMessage ?? "").isNotEmpty,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.only(
                        top: 8,
                        right: 8,
                      ),
                      child: Text(
                        state.errorQuantityMessage ?? "",
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.error,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ),
                  Visibility(
                    visible: (state.errorPriceMessage ?? "").isNotEmpty,
                    child: Container(
                      width: double.infinity,
                      padding: const EdgeInsets.only(
                        top: 8,
                        right: 8,
                      ),
                      child: Text(
                        state.errorPriceMessage ?? "",
                        style: UITextStyle.body2Regular.copyWith(
                          color: BaseColors.error,
                        ),
                        textAlign: TextAlign.end,
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  Row(
                    children: [
                      Text(
                        'Thuế',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                      Text(
                        " *",
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.primary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 12),
                  // RadioWidget<TaxType>(
                  //   value: TaxType.zero,
                  //   groupValue: state.taxType,
                  //   onChanged: (value) {
                  //     ref
                  //         .read(addSuppliesProvider.notifier)
                  //         .changeTaxType(value);
                  //   },
                  //   displayWidget: (context, item) {
                  //     return _displayTaxTypeText(item);
                  //   },
                  // ),
                  // const SizedBox(height: 16),
                  // RadioWidget<TaxType>(
                  //   value: TaxType.five,
                  //   groupValue: state.taxType,
                  //   onChanged: (value) {
                  //     ref
                  //         .read(addSuppliesProvider.notifier)
                  //         .changeTaxType(value);
                  //   },
                  //   displayWidget: (context, item) {
                  //     return _displayTaxTypeText(item);
                  //   },
                  // ),
                  // const SizedBox(height: 16),
                  RadioWidget<TaxType>(
                    value: TaxType.eight,
                    groupValue: state.taxType,
                    onChanged: (value) {
                      ref
                          .read(addSuppliesProvider.notifier)
                          .changeTaxType(value);
                    },
                    displayWidget: (context, item) {
                      return _displayTaxTypeText(
                        item,
                        isSelected: state.taxType == TaxType.eight,
                      );
                    },
                  ),
                  const SizedBox(height: 16),
                  RadioWidget<TaxType>(
                    value: TaxType.ten,
                    groupValue: state.taxType,
                    onChanged: (value) {
                      ref
                          .read(addSuppliesProvider.notifier)
                          .changeTaxType(value);
                    },
                    displayWidget: (context, item) {
                      return _displayTaxTypeText(
                        item,
                        isSelected: state.taxType == TaxType.ten,
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
        if (state.suppliesType == SuppliesType.oem) ...[
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            child: SearchTextFieldWidget(
              controller: oemSearchController,
              hintText: "Tên/Mã vật tư",
              onChanged: (value) {
                ref.read(addSuppliesProvider.notifier).searchOem(
                      keySearch: value,
                    );
              },
            ),
          ),
          Expanded(
            child: _buildListSupplyOem(),
          ),
        ],
      ],
    );
  }

  Widget _buildListSupplyOem() {
    var state = ref.watch(addSuppliesProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      if ((state.oemSupplies ?? []).isEmpty) {
        return EmptyListWidget(
          title: 'Không có dữ liệu',
          onRefresh: refreshData,
        );
      }

      return ListView.separated(
        controller: oemController,
        shrinkWrap: true,
        itemCount: state.oemSupplies?.length ?? 0,
        padding: const EdgeInsets.only(top: 4),
        separatorBuilder: (_, __) => DividerWidget(
          height: 4,
          color: BaseColors.backgroundGray,
        ),
        itemBuilder: (context, index) {
          var item = (state.oemSupplies ?? [])[index];
          return OemSupplyItemWidget(
            item: item,
            onSelect: () {
              ref.read(addSuppliesProvider.notifier).selectSupply(item);
            },
            changeQuantity: (quantity) {
              ref
                  .read(addSuppliesProvider.notifier)
                  .changeChangeQuantity(quantity, index);
            },
          );
        },
      );
    }
  }

  Widget _buildBottomBar({
    required String price,
    bool? enableButton,
    Function? onTap,
  }) {
    return SafeArea(
      top: false,
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          border: Border(
            top: BorderSide(
              width: 4,
              color: BaseColors.backgroundGray,
            ),
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text("Tổng tiền tạm tính"),
                  Text(
                    price,
                    style: UITextStyle.body2SemiBold.copyWith(
                      color: BaseColors.primary,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(
              width: 4,
            ),
            Expanded(
              child: BaseButton(
                text: "Xác nhận",
                isEnable: enableButton ?? false,
                onTap: () {
                  onTap?.call();
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  void searchSupplies() async {
    final supply = await context.push(
      RouterPaths.searchSupplies,
    );

    if (supply is SupplyEntity) {
      ref.read(addSuppliesProvider.notifier).selectSupplyNormal(supply);

      if (supply.price != null) {
        priceController.text = StringUtils.displayMoney(supply.price ?? 0);
      }

      quantityController.text = "";
      originalPriceController.text = "";
    }
  }

  Widget _displayTypeText(SuppliesType type) {
    return Text(
      type.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _displayTaxTypeText(
    TaxType type, {
    bool isSelected = false,
  }) {
    late Color textColor;

    if (ref.watch(addSuppliesProvider).canChangeTax) {
      textColor = BaseColors.textBody;
    } else {
      if (isSelected) {
        textColor = BaseColors.textBody;
      } else {
        textColor = BaseColors.textSubtitle;
      }
    }

    return Text(
      type.display,
      style: UITextStyle.body1Regular.copyWith(
        color: textColor,
      ),
    );
  }

  Future<void> refreshData() async {
    ref.read(addSuppliesProvider.notifier).searchOem();
  }
}
