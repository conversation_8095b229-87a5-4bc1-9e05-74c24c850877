import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/payment_type.dart';
import 'package:vcc/domain/enums/product_type.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/enums/worker_type.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_staff/choose_staff_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/add_seller/add_seller_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/create_service_order_view_model.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/select_voucher/select_voucher_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/order_success/order_success_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment_provider/payment_provider_page.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/card_custom_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/hide_button.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/normal_text_field.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class ServicePaymentInfoPage extends StatefulHookConsumerWidget {
  const ServicePaymentInfoPage({
    super.key,
  });

  @override
  ConsumerState<ServicePaymentInfoPage> createState() =>
      _ServicePaymentInfoPageState();
}

class _ServicePaymentInfoPageState extends ConsumerState<ServicePaymentInfoPage>
    with AutomaticKeepAliveClientMixin {
  late TextEditingController orderTaxCompany;
  late TextEditingController companyName;
  late TextEditingController emailController;

  late TextEditingController orderUserNameController;
  late TextEditingController orderPhoneNumberController;
  late TextEditingController orderPaymentTypeController;
  late TextEditingController orderBankNameController;
  late TextEditingController orderBankNumberController;
  late TextEditingController orderAddressController;
  late TextEditingController orderNoteController;
  final billFormKey = GlobalKey<FormState>();
  CustomerInfoParam? userInfo;

  @override
  void initState() {
    orderTaxCompany = TextEditingController();
    companyName = TextEditingController();
    emailController = TextEditingController();
    orderUserNameController = TextEditingController();
    orderPhoneNumberController = TextEditingController();
    orderPaymentTypeController = TextEditingController(
      text: "CK/TM",
    );
    orderBankNameController = TextEditingController();
    orderBankNumberController = TextEditingController();
    orderNoteController = TextEditingController();
    orderAddressController = TextEditingController();
    Future(() {
      userInfo =
          ref.watch(createServiceOrderProvider).serviceOrderInfo?.userInfo;
      ref.read(createServiceOrderProvider.notifier).getVouchers();
      ref.read(createServiceOrderProvider.notifier).getListPaymentMethod();
    });
    super.initState();
  }

  @override
  void dispose() {
    orderTaxCompany.dispose();
    companyName.dispose();
    emailController.dispose();
    orderUserNameController.dispose();
    orderPhoneNumberController.dispose();
    orderPaymentTypeController.dispose();
    orderBankNameController.dispose();
    orderBankNumberController.dispose();
    orderNoteController.dispose();
    orderAddressController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  void getPriceOrder() {
    ref.read(createServiceOrderProvider.notifier).calculatorPriceOrder(
          phoneNumber: userInfo?.phoneNumber,
        );
  }

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var state = ref.watch(createServiceOrderProvider);

    return Stack(
      key: const ValueKey("service_payment_info_page"),
      children: [
        Scaffold(
          backgroundColor: BaseColors.backgroundWhite,
          bottomNavigationBar: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            decoration: BoxDecoration(
              color: BaseColors.backgroundWhite,
              boxShadow: AppBoxShadows.shadowNormal,
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 6,
                    ),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            "Tổng tiền đơn hàng",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                        ),
                        Text(
                          StringUtils.formatMoney(
                              state.priceOrder?.totalOrder ?? 0),
                          style: UITextStyle.body1SemiBold.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  BaseButton(
                    text: "Tạo đơn hàng",
                    onTap: onCreateOrder,
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
          body: SingleChildScrollView(
            child: InkWell(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: Container(
                color: BaseColors.backgroundWhite,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    _buildOrderInfo(),
                    if (state.speedOrderType != ServiceType.salePoint) ...[
                      DividerWidget(
                        height: 8,
                        color: BaseColors.backgroundGray,
                      ),
                      _buildBillInfo(),
                      DividerWidget(
                        height: 8,
                        color: BaseColors.backgroundGray,
                      ),
                      _buildTeamContributeInfo(),
                    ]
                  ],
                ),
              ),
            ),
          ),
        ),
        if (state.createOrderStatus == LoadStatus.loading)
          const Center(
            child: LoadingIndicatorWidget(),
          ),
      ],
    );
  }

  Widget _buildOrderInfo() {
    var state = ref.watch(createServiceOrderProvider);
    String message = state.hasVoucher
        ? "Khách hàng có mã giảm giá, bạn có thể áp dụng mã giảm giá cho khách hàng"
        : "Hiện khách hàng chưa được áp dụng chương trình giảm giá nào";

    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            "Thông tin thanh toán",
            style: UITextStyle.body1SemiBold,
          ),
          const SizedBox(height: 16),
          CardCustomWidget(
            titleWidget: state.paymentSelected != null
                ? Row(
                    children: [
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.symmetric(
                            vertical: 8,
                          ),
                          child: Text(
                            state.paymentSelected?.name ?? "",
                            style: BaseStyle.labelMedium,
                          ),
                        ),
                      ),
                      const SizedBox(width: 12),
                      (state.paymentSelected?.iconUrl ?? "").isNotEmpty
                          ? Image.network(
                              state.paymentSelected?.iconUrl ?? "",
                              height: 48,
                              width: 48,
                            )
                          : const SizedBox(
                              height: 48,
                            ),
                    ],
                  )
                : RichText(
                    text: TextSpan(
                      text: "Phương thức thanh toán",
                      style: UITextStyle.body1Regular,
                      children: <TextSpan>[
                        TextSpan(
                          text: " *",
                          style: UITextStyle.body1Regular.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
            suffix: MyAssets.icons.arrowRight.svg(),
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            onPress: () {
              context.push(
                RouterPaths.choosePaymentProvider,
                extra: PaymentProviderArguments(
                  customerType: userInfo?.customerType,
                  productType: state.speedOrderType == ServiceType.salePoint
                      ? ProductType.salePoint
                      : state.serviceType == ServiceType.supply
                          ? ProductType.supply
                          : ProductType.service,
                  paymentType: PaymentType.after,
                  paymentSelected: state.paymentSelected,
                  onChoose: (value) {
                    ref
                        .read(createServiceOrderProvider.notifier)
                        .choosePaymentProvider(value);
                  },
                ),
              );
            },
          ),
          const SizedBox(height: 16),
          Visibility(
            visible: state.serviceType == ServiceType.single,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Visibility(
                  visible: state.serviceType == ServiceType.single,
                  child: Text(
                    "Mã giảm giá",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                ),
                const SizedBox(height: 8),
                CardCustomWidget(
                  titleWidget: state.voucherSelected != null
                      ? Row(
                          children: [
                            Expanded(
                              child: Text(
                                state.voucherSelected?.code ?? "",
                                style: BaseStyle.labelMedium,
                              ),
                            ),
                            if ((state.priceOrder?.discountAmount ?? 0) > 0)
                              Row(
                                children: [
                                  MyAssets.icons.iconCouponFrameLeft.svg(),
                                  Container(
                                    height: 24,
                                    decoration: BoxDecoration(
                                      border: Border.symmetric(
                                        horizontal: BorderSide(
                                          color: BaseColors.primary,
                                          width: 1,
                                        ),
                                      ),
                                    ),
                                    child: Center(
                                      child: Text(
                                        "-đ${(state.priceOrder?.discountAmount ?? 0) ~/ 1000}K",
                                        style: UITextStyle.caption2Regular
                                            .copyWith(
                                          color: BaseColors.primary,
                                        ),
                                      ),
                                    ),
                                  ),
                                  MyAssets.icons.iconCouponFrameRight.svg(),
                                ],
                              ),
                          ],
                        )
                      : Text(
                          "Chọn mã giảm giá",
                          style: UITextStyle.body1Regular,
                        ),
                  titleStyle: UITextStyle.body1Regular.copyWith(
                    color: BaseColors.textPlaceholder,
                  ),
                  suffix: MyAssets.icons.arrowRight.svg(),
                  backgroundColor: state.hasVoucher
                      ? BaseColors.backgroundWhite
                      : BaseColors.backgroundGray,
                  onPress: () {
                    if (!state.hasVoucher) return;

                    context.push(
                      RouterPaths.selectVoucher,
                      extra: SelectVoucherArguments(
                        userPhoneNumber: userInfo?.phoneNumber ??
                            GlobalData.instance.userInfo?.username ??
                            "",
                        voucherSelected: state.voucherSelected,
                        promotionPrice: state.priceOrder?.discountAmount ?? 0,
                        provinceCode:
                            state.serviceOrderInfo?.shippingInfo?.provinceCode,
                        districtCode:
                            state.serviceOrderInfo?.shippingInfo?.districtCode,
                        wardCode:
                            state.serviceOrderInfo?.shippingInfo?.wardCode,
                        onCalculatePrice: (code) async {
                          return await ref
                              .read(createServiceOrderProvider.notifier)
                              .getPromotionPrice(
                                couponCode: code,
                                phoneNumber: userInfo?.phoneNumber,
                                startTime: state
                                    .serviceOrderInfo?.shippingInfo?.startTime,
                                districtCode: state.serviceOrderInfo
                                    ?.shippingInfo?.districtCode,
                                provinceCode: state.serviceOrderInfo
                                    ?.shippingInfo?.provinceCode,
                                services: state.serviceOrderInfo?.servicesInfo,
                              );
                        },
                        onSelectVoucher: (voucher) {
                          ref
                              .read(createServiceOrderProvider.notifier)
                              .selectVoucher(voucher);
                          getPriceOrder();
                        },
                        services: (state.serviceOrderInfo?.servicesInfo ?? [])
                            .map((e) {
                          return ProductsInfoParam(
                            code: e.code,
                            quantity: e.quantity,
                            // price: e.price,
                          );
                        }).toList(),
                      ),
                    );
                  },
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    top: 4,
                    left: 8,
                    bottom: 4,
                  ),
                  child: Text(
                    message,
                    style: UITextStyle.body2Regular.copyWith(
                      color: state.hasVoucher
                          ? BaseColors.success
                          : BaseColors.error,
                    ),
                  ),
                ),
              ],
            ),
          ),
          _buildPaymentInfo(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildPaymentInfo() {
    var state = ref.watch(createServiceOrderProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 12,
          ),
          child: Text(
            "Chi tiết thanh toán",
            style: UITextStyle.body2SemiBold.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
        ),
        const DividerWidget(),
        _buildItemPriceInfo(
          title: "Tổng dịch vụ",
          value: StringUtils.formatMoney(state.priceOrder?.servicesAmount ?? 0),
        ),
        _buildItemPriceInfo(
          title: "Tổng vật tư",
          value: StringUtils.formatMoney(state.priceOrder?.suppliesAmount ?? 0),
        ),
        _buildItemPriceInfo(
          title: "Tổng đơn",
          value: StringUtils.formatMoney(state.priceOrder?.totalAmount ?? 0),
        ),
        _buildItemPriceInfo(
          title: "Phí vận chuyển",
          value: StringUtils.formatMoney(state.priceOrder?.shippingAmount ?? 0),
        ),
        if ((state.priceOrder?.discountAmount ?? 0) > 0) ...[
          _buildItemPriceInfo(
            title: "Voucher/khuyến mại:",
            value:
                "-${StringUtils.formatMoney(state.priceOrder?.discountAmount ?? 0)}",
          ),
        ],
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 6,
          ),
          child: Row(
            children: <Widget>[
              Expanded(
                child: Text(
                  "Thành tiền:",
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                StringUtils.formatMoney(state.priceOrder?.totalOrder ?? 0),
                style: UITextStyle.body1SemiBold.copyWith(
                  color: BaseColors.primary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBillInfo() {
    var state = ref.watch(createServiceOrderProvider);
    bool enableUserName = true;

    if (userInfo?.customerType == UserType.personal.keyToServer) {
      enableUserName = false;
      orderUserNameController.text = userInfo?.fullName ?? '';
      orderPhoneNumberController.text = userInfo?.phoneNumber ?? '';
    } else {
      orderPhoneNumberController.text = userInfo?.phoneNumber ?? '';
      orderUserNameController.text = userInfo?.contactName ?? '';
      orderTaxCompany.text = userInfo?.customerId ?? '';
      companyName.text = userInfo?.fullName ?? '';
    }

    orderAddressController.text = userInfo?.address ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 8, 8),
          child: Row(
            children: <Widget>[
              Expanded(
                child: Text(
                  "Hóa đơn",
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textTitle,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              InkWellWidget(
                onTap: () {
                  ref
                      .read(createServiceOrderProvider.notifier)
                      .changeGetBillStatus();
                },
                child: Row(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: state.isGetBill
                          ? MyAssets.icons.iconChecked.svg()
                          : MyAssets.icons.iconCheckbox.svg(),
                    ),
                    Text(
                      "Lấy hóa đơn",
                      style: UITextStyle.body2Regular,
                    ),
                    const SizedBox(width: 8),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (state.isGetBill) ...[
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: Form(
              key: billFormKey,
              autovalidateMode: AutovalidateMode.always,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  TextFieldWidget(
                    controller: orderTaxCompany,
                    labelText: "Mã số thuế doanh nghiệp",
                    enabled: false,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: companyName,
                    labelText: "Tên doanh nghiệp",
                    enabled: false,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: emailController,
                    labelText: "Email",
                    isRequired: true,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidateEmail(value);
                    },
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderUserNameController,
                    labelText: "Họ và tên",
                    textInputAction: TextInputAction.done,
                    textCapitalization: TextCapitalization.words,
                    enabled: enableUserName,
                    isRequired:
                        userInfo?.customerType == UserType.company.keyToServer,
                    validator: (value) {
                      return ValidateUtils.onValidateUserName(value);
                    },
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderPhoneNumberController,
                    labelText: "Số điện thoại",
                    enabled: false,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderPaymentTypeController,
                    labelText: "Hình thức thanh toán",
                    enabled: false,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderBankNameController,
                    labelText: "Ngân hàng",
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderBankNumberController,
                    labelText: "Số tài khoản",
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget.area(
                    controller: orderAddressController,
                    hintText: "Địa chỉ",
                    enabled: false,
                    maxLines: 2,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget.area(
                    controller: orderNoteController,
                    maxLines: 4,
                    maxLength: 2000,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(
                          Patterns.note,
                          multiLine: true,
                        ),
                      ),
                    ],
                    hintText: "Ghi chú",
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTeamContributeInfo() {
    var state = ref.watch(createServiceOrderProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            "Đội ngũ tham gia",
            style: UITextStyle.body1SemiBold,
          ),
          const SizedBox(height: 16),
          Text(
            "Người bán và tỉ lệ % hoa hồng",
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.fromLTRB(12, 12, 12, 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: BaseColors.borderDefault,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ListView.separated(
                  itemCount:
                      ref.watch(createServiceOrderProvider).sellers?.length ??
                          0,
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(),
                  separatorBuilder: (_, __) {
                    return const Padding(
                      padding: EdgeInsets.symmetric(vertical: 8),
                      child: DividerWidget(),
                    );
                  },
                  itemBuilder: (context, index) {
                    final item =
                        ref.watch(createServiceOrderProvider).sellers![index];
                    TextEditingController percentController =
                        TextEditingController(
                            text: "${item.percentBonus ?? 0}");
                    if (index == 0) {
                      return Row(
                        children: <Widget>[
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Row(
                                  children: [
                                    Text(
                                      item.fullName ?? '',
                                      style: UITextStyle.body1Medium.copyWith(
                                        color: BaseColors.textTitle,
                                      ),
                                    ),
                                    Text(
                                      ' (Người tạo)',
                                      style:
                                          UITextStyle.caption1Medium.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  item.username ?? '',
                                  style: UITextStyle.caption1Regular.copyWith(
                                    color: BaseColors.textSubtitle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 42,
                            child: NormalTextField(
                              controller: percentController,
                              showClearIcon: false,
                              height: 30,
                              textInputAction: TextInputAction.done,
                              borderColor: Colors.transparent,
                              borderRadius: BorderRadius.zero,
                              textAlign: TextAlign.end,
                              suffixText: "%",
                              maxLength: 3,
                              keyboardType: TextInputType.number,
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'[0-9]'),
                                ),
                              ],
                              contentPadding: const EdgeInsets.only(top: 2),
                              onChanged: (value) {
                                item.percentBonus = int.tryParse(value) ?? 0;
                              },
                            ),
                          ),
                          HideButton(
                            child: InkWellWidget(
                              onTap: () {},
                              child: Padding(
                                padding: const EdgeInsets.all(2),
                                child: MyAssets.icons.iconEditS12.svg(),
                              ),
                            ),
                          ),
                        ],
                      );
                    }
                    return Row(
                      children: <Widget>[
                        InkWellWidget(
                          onTap: () {
                            ref
                                .read(createServiceOrderProvider.notifier)
                                .deleteSeller(index);
                          },
                          child: MyAssets.icons.iconMinusRounedRed.svg(),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Text(
                                item.fullName ?? '',
                                style: UITextStyle.body1Regular,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                item.username ?? '',
                                style: UITextStyle.caption1Regular.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          width: 38,
                          child: NormalTextField(
                            controller: percentController,
                            showClearIcon: false,
                            height: 30,
                            textInputAction: TextInputAction.done,
                            borderColor: Colors.transparent,
                            borderRadius: BorderRadius.zero,
                            keyboardType: TextInputType.number,
                            suffixText: "%",
                            maxLength: 3,
                            textAlign: TextAlign.end,
                            contentPadding: const EdgeInsets.only(top: 2),
                            onChanged: (value) {
                              item.percentBonus = int.tryParse(value) ?? 0;
                            },
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'[0-9]'),
                              ),
                            ],
                          ),
                        ),
                        HideButton(
                          child: InkWellWidget(
                            onTap: () {},
                            child: Padding(
                              padding: const EdgeInsets.all(2),
                              child: MyAssets.icons.iconEditS12.svg(),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 8),
                const DividerWidget(),
                const SizedBox(height: 4),
                AppTextButton(
                  iconLeft: MyAssets.icons.iconAddCircle.svg(),
                  title: "Thêm người bán",
                  onTap: () {
                    context.push(
                      RouterPaths.addSeller,
                      extra: AddSellerArguments(
                        sellersSelected:
                            ref.watch(createServiceOrderProvider).sellers,
                        onSelected: (data) {
                          ref
                              .read(createServiceOrderProvider.notifier)
                              .onChangeSellers(data);
                        },
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: CardCustomWidget(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              titleWidget: state.staffPropose != null
                  ? Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Text(
                                "Người giới thiệu",
                                style: UITextStyle.caption1Regular.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                state.staffPropose?.fullName ?? '',
                                style: UITextStyle.body1Regular,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                state.staffPropose?.username ?? '',
                                style: UITextStyle.caption1Regular.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                              const SizedBox(height: 4),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: !state.disableInputForm,
                          child: InkWellWidget(
                            onTap: () {
                              ref
                                  .read(createServiceOrderProvider.notifier)
                                  .deleteStaffPropose();
                            },
                            child: MyAssets.icons.iconCloseCircle.svg(),
                          ),
                        ),
                      ],
                    )
                  : Row(
                      children: <Widget>[
                        MyAssets.icons.iconSearchS20.svg(),
                        const SizedBox(width: 8),
                        Text(
                          "Người giới thiệu",
                          style: UITextStyle.body1Regular,
                        ),
                      ],
                    ),
              onPress: () {
                AppBottomSheet.showNormalBottomSheet(
                  context,
                  title: "Người giới thiệu",
                  height: MediaQuery.of(context).size.height * 0.95,
                  child: ChooseStaffPage(
                    onSelectStaff: (staff) {
                      ref.read(createServiceOrderProvider.notifier).selectStaff(
                            staff: staff,
                            isUserPropose: true,
                          );
                    },
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          CardCustomWidget(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            titleWidget: state.staffImplementation != null
                ? Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Text(
                              "Nhân sự triển khai",
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              state.staffImplementation?.fullName ?? '',
                              style: UITextStyle.body1Regular,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              state.staffImplementation?.username ?? '',
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                            const SizedBox(height: 4),
                          ],
                        ),
                      ),
                      InkWellWidget(
                        onTap: () {
                          ref
                              .read(createServiceOrderProvider.notifier)
                              .deleteStaffImplementation();
                        },
                        child: MyAssets.icons.iconCloseCircle.svg(),
                      ),
                    ],
                  )
                : Row(
                    children: <Widget>[
                      MyAssets.icons.iconSearchS20.svg(),
                      const SizedBox(width: 8),
                      Text(
                        "Nhân sự triển khai",
                        style: UITextStyle.body1Regular,
                      ),
                    ],
                  ),
            onPress: () {
              String? schedule =
                  state.serviceOrderInfo?.shippingInfo?.startTime;
              AppBottomSheet.showNormalBottomSheet(
                context,
                title: "Nhân sự triển khai",
                height: MediaQuery.of(context).size.height * 0.95,
                child: ChooseStaffPage(
                  orderType: OrderTypeExtension.fromString(
                      state.serviceOrderInfo?.orderType),
                  workerType: WorkerType.distributionCreate,
                  provinceCode:
                      state.serviceOrderInfo?.shippingInfo?.provinceCode,
                  wardCode:
                      state.serviceOrderInfo?.shippingInfo?.wardCode,
                  onSelectStaff: (staff) {
                    ref.read(createServiceOrderProvider.notifier).selectStaff(
                          staff: staff,
                        );
                  },
                  scheduleTime: schedule != null
                      ? DateTime.parse(schedule)
                      : DateTime.now(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildItemPriceInfo({
    required String title,
    String? value,
    bool visible = true,
  }) {
    return Visibility(
      visible: visible,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 6,
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Text(
                "$title:",
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              value ?? '',
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void onCreateOrder() async {
    var state = ref.watch(createServiceOrderProvider);

    if (state.paymentSelected == null) {
      AppDialog.showDialogCenter(
        context,
        message: "Vui lòng chọn phương thức thanh toán",
        status: DialogStatus.error,
      );
      return;
    }

    if (state.isGetBill) {
      final form = billFormKey.currentState;
      if (!form!.validate()) {
        ref.read(createServiceOrderProvider.notifier).reloadBillForm();
        return;
      }
    }

    int totalPercent = 0;
    for (int i = 0; i < state.sellers!.length; i++) {
      if ((state.sellers![i].percentBonus ?? 0) == 0) {
        AppDialog.showDialogCenter(
          context,
          message: "Vui lòng nhập % hoa hồng cho người bán",
          status: DialogStatus.error,
        );
        return;
      }
      totalPercent = totalPercent + (state.sellers![i].percentBonus ?? 0);
    }

    if (totalPercent != 100) {
      AppDialog.showDialogCenter(
        context,
        message: "Tổng % hoa hồng của người bán phải bằng 100%",
        status: DialogStatus.error,
      );
      return;
    }

    String paymentType = state.paymentSelected?.code ?? 'ONE_PAY_COLLECT';

    PaymentInfoParam payment = PaymentInfoParam(
      paymentMethod: paymentType,
      isGetBill: state.isGetBill,
      coupon: state.voucherSelected?.code,
      paymentType: PaymentType.after.keyToServer,
      billInfo: state.isGetBill
          ? userInfo?.customerType == UserType.personal.keyToServer
              ? BillInfoParam(
                  email: emailController.text,
                  fullName: orderUserNameController.text,
                  phoneNumber: orderPhoneNumberController.text,
                  paymentType: orderPaymentTypeController.text,
                  bankName: orderBankNameController.text,
                  accountNumber: orderBankNumberController.text,
                  addressDetail: userInfo?.addressDetail,
                  wardCode: userInfo?.wardCode,
                  districtCode: userInfo?.districtCode,
                  provinceCode: userInfo?.provinceCode,
                  address: userInfo?.address,
                )
              : BillInfoParam(
                  taxCode: orderTaxCompany.text,
                  businessName: companyName.text,
                  email: emailController.text,
                  fullName: orderUserNameController.text,
                  phoneNumber: orderPhoneNumberController.text,
                  paymentType: orderPaymentTypeController.text,
                  bankName: orderBankNameController.text,
                  accountNumber: orderBankNumberController.text,
                  addressDetail: userInfo?.addressDetail,
                  wardCode: userInfo?.wardCode,
                  districtCode: userInfo?.districtCode,
                  provinceCode: userInfo?.provinceCode,
                  address: userInfo?.address,
                )
          : null,
      multiplePaymentInfo: state.paymentSelected?.multiplePaymentInfo,
    );

    var processUserInfo = ProcessUserInfoParam(
      togetherUserInfo: state.sellers!
          .map(
            (e) => StaffInfoParam(
              userCode: e.username,
              roseRate: e.percentBonus,
            ),
          )
          .toList(),
      inviteUserCode: state.staffPropose?.username,
      processUserCode: state.staffImplementation?.username,
    );

    OrderParam param = state.serviceOrderInfo!.copyWith(
      paymentInfo: payment,
      processUserInfo: processUserInfo,
      associationCode: state.associationInfo?.code,
    );

    if (state.serviceType == ServiceType.combo) {
      param = param.copyWith(
        comboCode: "COMBO",
      );
    }

    if (state.serviceType == ServiceType.salePointCombo ||
        state.serviceType == ServiceType.salePoint) {
      param = param.copyWith(
        comboCode: state.comboCode,
      );
      param.processUserInfo?.inviteUserCode = null;
    }

    if (state.serviceType == ServiceType.package) {
      param = param.copyWith(
        packageCode: state.serviceOrderInfo?.packageCode ?? '',
      );
    }

    if (param.isSpeedService ?? false) {
      final shippingInfo = param.shippingInfo;
      shippingInfo?.startTime = DateTime.now().toIso8601String();
      shippingInfo?.endTime =
          DateTime.now().add(const Duration(hours: 3)).toIso8601String();

      param = param.copyWith(
        shippingInfo: shippingInfo,
      );
    }

    createOrder(
      param,
      context: context,
    );
  }

  void createOrder(
    OrderParam param, {
    required BuildContext context,
  }) async {
    final result =
        await ref.read(createServiceOrderProvider.notifier).createOrder(
              param,
            );

    if (!context.mounted) return;
    if (result != null) {
      context.push(
        RouterPaths.orderSuccess,
        extra: OrderSuccessArguments(
          orderInfo: result,
          orderType: OrderType.service,
        ),
      );
    } else {
      AppDialog.showDialogCenter(
        context,
        message: ref.watch(createServiceOrderProvider).message ??
            "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
  }
}
