import 'package:equatable/equatable.dart';
import 'package:flutter/foundation.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/schedule_worker_body.dart';
import 'package:vcc/domain/entities/service/date_status_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/log_utils.dart';

part 'select_date_service_state.dart';

final selectDateServiceProvider = StateNotifierProvider.autoDispose<
    SelectDateServiceViewModel, SelectDateServiceState>(
  (ref) => SelectDateServiceViewModel(ref: ref),
);

class SelectDateServiceViewModel extends StateNotifier<SelectDateServiceState> {
  final Ref ref;

  SelectDateServiceViewModel({
    required this.ref,
  }) : super(const SelectDateServiceState());

  Future<void> initData({
    String? provinceCode,
    String? districtCode,
    String? wardCode,
    bool? isCheckSchedule,
    DateStatusEntity? dateSelected,
  }) async {
    late DateTime startDate;
    late DateTime dateTimeSelected;

    if (dateSelected != null) {
      final dateSelect = dateSelected.getDate;
      final currentDate = DateTime.now();

      if (dateSelect.month == currentDate.month) {
        startDate = DateTime.now();
      } else {
        startDate = DateTime(dateSelect.year, dateSelect.month, 1);
      }

      dateTimeSelected = DateTime(
        dateSelect.year,
        dateSelect.month,
        dateSelect.day,
      );
    } else {
      startDate = DateTime.now();
    }

    final daysOfMonth = AppUtils.getDayInMonth(
      dateTime: startDate,
    );

    state = state.copyWith(
      daysOfMonth: daysOfMonth,
      dateViewer: startDate,
      provinceCode: provinceCode,
      districtCode: districtCode,
      wardCode: wardCode,
      dateTimeSelected:
          dateSelected != null ? dateTimeSelected : daysOfMonth[0],
      isCheckSchedule: isCheckSchedule,
      dateSelected: dateSelected,
    );

    if (isCheckSchedule ?? false) {
      await getDateStatus(
        dateTime: dateSelected != null ? dateTimeSelected : daysOfMonth[0],
      );
    } else {
      state = state.copyWith(
        dateStatus: dateSelected ??
            DateStatusEntity(
              date: DateTime.now().toString(),
            ),
      );
    }
  }

  Future<void> getDateStatus({
    DateTime? dateTime,
  }) async {
    try {
      final result = await appLocator<OrderRepository>().checkScheduleWorker(
        body: ScheduleWorkerBody(
          provinceCode: state.provinceCode,
          districtCode: state.districtCode,
          wardCode: state.wardCode,
          startTime: dateTime?.toIso8601String(),
        ),
      );

      result.when(
        success: (data) {
          if (data.isNotEmpty) {
            bool? checkMorningTime = false;
            bool? checkAfternoonTime = false;

            for (int i = 0; i < data.length; i++) {
              if (data[i].session == "MORNING") {
                checkMorningTime = data[i].isAvailable;
              }

              if (data[i].session == "EVENING") {
                checkAfternoonTime = data[i].isAvailable;
              }
            }

            final dateStatus = DateStatusEntity(
              date: dateTime?.toString(),
              morningTime: checkMorningTime,
              afternoonTime: checkAfternoonTime,
            );

            if (state.dateSelected != null) {
              final date = DateTime(
                state.dateSelected!.getDate.year,
                state.dateSelected!.getDate.month,
                state.dateSelected!.getDate.day,
              );

              if (date == dateTime) {
                dateStatus.selectMorning =
                    state.dateSelected?.selectMorning ?? false;
                dateStatus.selectAfternoon =
                    state.dateSelected?.selectAfternoon ?? false;
              }
            }

            state = state.copyWith(
              dateStatus: dateStatus,
            );
          }
        },
        error: (e) {
          state = state.copyWith(
            message: "$e",
            dateStatus: DateStatusEntity(
              date: dateTime?.toString(),
            ),
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        message: "$e",
        dateStatus: DateStatusEntity(
          date: dateTime?.toString(),
        ),
      );
    }
  }

  void selectDate(DateTime dateSelect) async {
    state = state.copyWith(
      dateTimeSelected: dateSelect,
    );

    if (state.isCheckSchedule ?? false) {
      await getDateStatus(dateTime: dateSelect);
    } else {
      var dateStatus = DateStatusEntity(
        date: dateSelect.toString(),
      );

      if (state.dateSelected != null) {
        final date = DateTime(
          state.dateSelected!.getDate.year,
          state.dateSelected!.getDate.month,
          state.dateSelected!.getDate.day,
        );

        if (date == dateSelect) {
          dateStatus.selectMorning = state.dateSelected?.selectMorning ?? false;
          dateStatus.selectAfternoon =
              state.dateSelected?.selectAfternoon ?? false;
        }
      }

      state = state.copyWith(
        dateStatus: dateStatus,
      );
    }
  }

  void changeMonth(bool isPlus) {
    try {
      final dateTimeNow = DateTime.now();

      if (isPlus) {
        final endDay = state.daysOfMonth?.last ?? DateTime.now();
        final newDay = endDay.add(const Duration(days: 1));

        final daysOfMonth = AppUtils.getDayInMonth(
          dateTime: newDay,
        );

        state = state.copyWith(
          dateViewer: newDay,
          daysOfMonth: daysOfMonth,
        );
      } else {
        final firstDay = state.daysOfMonth?.first ?? DateTime.now();
        DateTime newDay = firstDay.subtract(const Duration(days: 1));

        if (newDay.month == dateTimeNow.month) {
          newDay = dateTimeNow;
        } else {
          newDay = DateTime(newDay.year, newDay.month, 1);
        }

        final daysOfMonth = AppUtils.getDayInMonth(
          dateTime: newDay,
        );

        state = state.copyWith(
          dateViewer: newDay,
          daysOfMonth: daysOfMonth,
        );
      }
    } catch (e) {
      LogUtils.e("$e");
    }
  }

  void selectTime({
    bool isMorning = true,
  }) {
    state = state.copyWith(
      loadDataStatus: LoadStatus.loading,
    );

    try {
      final temp = state.dateStatus ?? DateStatusEntity();
      temp.date = state.dateTimeSelected?.toString();

      if (isMorning) {
        temp.selectMorning = true;
        temp.selectAfternoon = false;
      } else {
        temp.selectMorning = false;
        temp.selectAfternoon = true;
      }

      state = state.copyWith(
        dateStatus: temp,
        loadDataStatus: LoadStatus.success,
      );
    } catch (e) {
      debugPrint("$e");
      state = state.copyWith(
        loadDataStatus: LoadStatus.failure,
      );
    }
  }
}
