import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/service/date_status_entity.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/widgets/status_worker_card.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/widgets/time_card.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';

import 'select_date_service_view_model.dart';

class SelectDateServiceView extends StatefulHookConsumerWidget {
  final String? provinceCode;
  final String? districtCode;
  final String? wardCode;
  final String? content;
  final Function(DateStatusEntity date)? onSelectDateCallback;
  final bool? isCheckSchedule;
  final DateStatusEntity? dateSelected;

  const SelectDateServiceView({
    super.key,
    this.districtCode,
    this.provinceCode,
    this.wardCode,
    this.content,
    this.onSelectDateCallback,
    this.isCheckSchedule,
    this.dateSelected,
  });

  @override
  ConsumerState<SelectDateServiceView> createState() =>
      _SelectDateServiceViewState();
}

class _SelectDateServiceViewState extends ConsumerState<SelectDateServiceView>
    with WidgetsBindingObserver {
  final _scrollController = ScrollController();

  @override
  void initState() {
    WidgetsBinding.instance.addObserver(this);
    Future(
      () async {
        await ref.read(selectDateServiceProvider.notifier).initData(
              provinceCode: widget.provinceCode,
              districtCode: widget.districtCode,
              wardCode: widget.wardCode,
              isCheckSchedule: widget.isCheckSchedule,
              dateSelected: widget.dateSelected,
            );

        WidgetsBinding.instance.addPostFrameCallback(
          (_) {
            if (widget.dateSelected != null) {
              const double itemWidth = 64;
              var dateTimeSelected = DateTime(
                widget.dateSelected!.getDate.year,
                widget.dateSelected!.getDate.month,
                widget.dateSelected!.getDate.day,
              );

              int index =
                  ref.read(selectDateServiceProvider).daysOfMonth!.indexWhere(
                        (element) => element == dateTimeSelected,
                      );

              final double offset = index * itemWidth;
              animateToOffset(offset);
            }
          },
        );
      },
    );

    super.initState();
  }

  void animateToOffset(double offset) {
    _scrollController.animateTo(
      offset,
      duration: const Duration(milliseconds: 500),
      curve: Curves.easeInOut,
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(selectDateServiceProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: <Widget>[
              Expanded(
                child: Text(
                  "Tháng ${state.dateViewer?.month ?? ''}, ${state.dateViewer?.year ?? ''}",
                  style: UITextStyle.body1SemiBold,
                ),
              ),
              _buildChooseMonthWidget(
                icon: MyAssets.icons.iconArrowLeftS18.svg(),
                onTap: () {
                  ref
                      .read(selectDateServiceProvider.notifier)
                      .changeMonth(false);
                  animateToOffset(0);
                },
              ),
              const SizedBox(width: 10),
              _buildChooseMonthWidget(
                icon: MyAssets.icons.iconArrowRightS18.svg(),
                onTap: () {
                  ref
                      .read(selectDateServiceProvider.notifier)
                      .changeMonth(true);
                  animateToOffset(0);
                },
              ),
            ],
          ),
        ),
        SizedBox(
          height: 64,
          child: ListView.separated(
            controller: _scrollController,
            itemCount: state.daysOfMonth?.length ?? 0,
            scrollDirection: Axis.horizontal,
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            separatorBuilder: (_, __) => const SizedBox(width: 12),
            itemBuilder: (context, index) {
              final item = state.daysOfMonth![index];
              return TimeCard(
                dateTime: item,
                isSelected: item == state.dateTimeSelected,
                onTap: () {
                  ref.read(selectDateServiceProvider.notifier).selectDate(item);
                },
              );
            },
          ),
        ),
        Container(
          height: 1,
          width: double.infinity,
          color: BaseColors.borderDivider,
          margin: const EdgeInsets.all(16),
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
          ),
          child: Text(
            widget.content ?? "Vui lòng chọn khung giờ có thợ sẵn",
            style: UITextStyle.body2Regular.copyWith(
              color: BaseColors.textBody,
            ),
          ),
        ),
        const SizedBox(height: 8),
        StatusWorkerCard(
          title: "Sáng (08:00 - 12:00)",
          isEnable: state.dateStatus?.enableMorningTime ?? true,
          isSelected: state.dateStatus?.selectMorning ?? false,
          onTap: () {
            ref
                .read(selectDateServiceProvider.notifier)
                .selectTime(isMorning: true);
          },
        ),
        const SizedBox(height: 16),
        StatusWorkerCard(
          title: "Chiều (12:00 - 17:30)",
          isEnable: state.dateStatus?.enableAfternoonTime ?? true,
          isSelected: state.dateStatus?.selectAfternoon ?? false,
          onTap: () {
            ref
                .read(selectDateServiceProvider.notifier)
                .selectTime(isMorning: false);
          },
        ),
        const SizedBox(height: 16),
        SafeArea(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            child: BaseButton(
              text: "Xác nhận",
              isEnable: !(state.dateStatus?.selectAfternoon == null &&
                  state.dateStatus?.selectMorning == null),
              onTap: () {
                Navigator.of(context).pop();
                widget.onSelectDateCallback?.call(state.dateStatus!);
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildChooseMonthWidget({
    Function? onTap,
    required Widget icon,
  }) {
    return InkWellWidget(
      onTap: () {
        onTap?.call();
      },
      child: Container(
        height: 32,
        width: 32,
        padding: const EdgeInsets.all(4),
        decoration: BoxDecoration(
          color: BaseColors.backgroundGray,
          borderRadius: BorderRadius.circular(8),
        ),
        child: icon,
      ),
    );
  }
}
