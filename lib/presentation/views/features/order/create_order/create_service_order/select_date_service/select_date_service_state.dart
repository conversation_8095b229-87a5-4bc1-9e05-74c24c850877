part of 'select_date_service_view_model.dart';

class SelectDateServiceState extends Equatable {
  final LoadStatus? loadDataStatus;
  final String? message;
  final List<DateTime>? daysOfMonth;
  final DateTime? dateTimeSelected;
  final DateTime? dateViewer;
  final DateStatusEntity? dateStatus;
  final DateStatusEntity? dateSelected;
  final String? provinceCode;
  final String? districtCode;
  final String? wardCode;
  final bool? isCheckSchedule;

  const SelectDateServiceState({
    this.loadDataStatus,
    this.message,
    this.dateTimeSelected,
    this.dateViewer,
    this.dateStatus,
    this.dateSelected,
    this.daysOfMonth,
    this.provinceCode,
    this.districtCode,
    this.wardCode,
    this.isCheckSchedule,
  });

  @override
  List<Object?> get props => [
        loadDataStatus,
        message,
        dateTimeSelected,
        dateViewer,
        daysOfMonth,
        dateStatus,
        provinceCode,
        districtCode,
        wardCode,
        isCheckSchedule,
        dateSelected,
      ];

  SelectDateServiceState copyWith({
    LoadStatus? loadDataStatus,
    String? message,
    List<DateTime>? daysOfMonth,
    DateTime? dateTimeSelected,
    DateTime? dateViewer,
    DateStatusEntity? dateStatus,
    DateStatusEntity? dateSelected,
    String? provinceCode,
    String? districtCode,
    String? wardCode,
    bool? isCheckSchedule,
  }) {
    return SelectDateServiceState(
      loadDataStatus: loadDataStatus ?? this.loadDataStatus,
      message: message ?? this.message,
      daysOfMonth: daysOfMonth ?? this.daysOfMonth,
      dateTimeSelected: dateTimeSelected ?? this.dateTimeSelected,
      dateViewer: dateViewer ?? this.dateViewer,
      dateStatus: dateStatus ?? this.dateStatus,
      dateSelected: dateSelected ?? this.dateSelected,
      provinceCode: provinceCode ?? this.provinceCode,
      wardCode: wardCode ?? this.wardCode,
      districtCode: districtCode ?? this.districtCode,
      isCheckSchedule: isCheckSchedule ?? this.isCheckSchedule,
    );
  }
}
