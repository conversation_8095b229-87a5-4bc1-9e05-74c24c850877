import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/payment_repository.dart';
import 'package:vcc/domain/body/check_role_user_body.dart';
import 'package:vcc/domain/body/voucher_body.dart';
import 'package:vcc/domain/entities/association/association_entity.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/entities/order/order_entity.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/payment_type.dart';
import 'package:vcc/domain/enums/product_type.dart';
import 'package:vcc/domain/enums/service_type.dart';
import 'package:vcc/domain/enums/warehouse_supply_type.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:rxdart/subjects.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/data/repositories/user_profile_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/items_amount_body.dart';
import 'package:vcc/domain/entities/address_info.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/customer_info_entity.dart';
import 'package:vcc/domain/entities/service/date_status_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/enums/gender_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/params/price_order_param.dart';
import 'package:vcc/domain/entities/payment_entity.dart';
import 'package:vcc/domain/entities/price_order_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/entities/voucher_entity.dart';
import 'package:vcc/domain/responses/order/commission_confirm_response.dart';
import 'package:vcc/domain/responses/order/order_response.dart';
import 'package:vcc/utils/log_utils.dart';

part 'create_service_order_state.dart';

final createServiceOrderProvider = StateNotifierProvider.autoDispose<
    CreateServiceOrderViewModel, CreateServiceOrderState>(
  (ref) => CreateServiceOrderViewModel(ref: ref),
);

class CreateServiceOrderViewModel
    extends StateNotifier<CreateServiceOrderState> {
  final Ref ref;

  CreateServiceOrderViewModel({
    required this.ref,
  }) : super(CreateServiceOrderState());

  final alertController = PublishSubject<List<dynamic>>();

  void initData({
    ServiceInfoEntity? serviceYearInfo,
    List<ServiceInfoEntity>? singleServices,
    List<SupplyEntity>? supplies,
    ServiceType? serviceType,
    ServiceType? speedOrderType,
    bool? isCreateSpeedOrder,
    CollectionInfoEntity? collectionInfo,
    AssociationEntity? associationInfo,
  }) {
    InternalStaffEntity? staffPropose;

    if (GlobalData.instance.userInfo?.canViewGroup ?? false) {
      staffPropose = InternalStaffEntity(
        fullName: GlobalData.instance.userInfo?.fullName ?? '',
        phoneNumber: GlobalData.instance.userInfo?.phoneNumber ?? '',
        username: GlobalData.instance.userInfo?.username ?? '',
      );
    }

    state = state.copyWith(
      collectionInfo: collectionInfo,
      associationInfo: associationInfo,
      staffPropose: staffPropose,
      loadStatus: LoadStatus.loading,
    );

    int price = 0;
    if (serviceYearInfo != null) {
      price = serviceYearInfo.price ?? 0;
    } else if (supplies != null) {
      for (var element in supplies) {
        price = (price + (element.price ?? 0) * (element.quantity)).toInt();
      }
    } else {
      singleServices?.forEach((element) {
        price = (price + (element.price ?? 0) * (element.quantity)).toInt();
      });
    }

    final currentUser = InternalStaffEntity(
      fullName: GlobalData.instance.userInfo?.fullName ?? '',
      phoneNumber: GlobalData.instance.userInfo?.phoneNumber ?? '',
      username: GlobalData.instance.userInfo?.username ?? '',
      percentBonus: 100,
    );

    state = state.copyWith(
      totalTemp: price,
      services: singleServices,
      serviceYearInfo: serviceYearInfo,
      supplies: supplies,
      userType: serviceType == ServiceType.combo
          ? UserType.company
          : UserType.personal,
      serviceType: serviceType,
      sellers: [
        currentUser,
      ],
      isCreateSpeedOrder: isCreateSpeedOrder,
      speedOrderType: speedOrderType,
      loadStatus: LoadStatus.success,
    );
  }

  void changePageIndex(int index) {
    state = state.copyWith(
      pageIndex: index,
    );
  }

  void saveOrderInfo(OrderParam productOrderInfo) {
    var orderInfo = state.serviceOrderInfo ?? OrderParam();

    orderInfo = orderInfo.copyWith(
      orderType: productOrderInfo.orderType ?? orderInfo.orderType,
      userInfo: productOrderInfo.userInfo ?? orderInfo.userInfo,
      shippingInfo: productOrderInfo.shippingInfo ?? orderInfo.shippingInfo,
      servicesInfo: productOrderInfo.servicesInfo ?? orderInfo.servicesInfo,
      paymentInfo: productOrderInfo.paymentInfo ?? orderInfo.paymentInfo,
      processUserInfo:
          productOrderInfo.processUserInfo ?? orderInfo.processUserInfo,
      packageCode: productOrderInfo.packageCode ?? orderInfo.packageCode,
      comboCode: productOrderInfo.comboCode ?? orderInfo.comboCode,
      productsInfo: productOrderInfo.productsInfo ?? orderInfo.productsInfo,
      suppliesInfo: productOrderInfo.suppliesInfo ?? orderInfo.suppliesInfo,
      isSpeedService:
          productOrderInfo.isSpeedService ?? orderInfo.isSpeedService,
    );

    state = state.copyWith(
      serviceOrderInfo: orderInfo,
    );
  }

  //page 1
  void fillDataFromCollectionInfo(
    CollectionInfoEntity item,
  ) {
    final userType = UserTypeExtension.fromString(item.customerType);
    GenderType? gender = GenderTypeExtension.fromString(item.gender);

    AddressInfo address = AddressInfo(
      addressDetail: item.addressDetail,
      province: item.province,
      district: item.district,
      ward: item.ward,
    );

    final user = InternalStaffEntity(
      fullName: GlobalData.instance.userInfo?.fullName ?? '',
      phoneNumber: GlobalData.instance.userInfo?.phoneNumber ?? '',
      username: GlobalData.instance.userInfo?.username ?? '',
    );

    if (userType == UserType.personal) {
      state = state.copyWith(
        customerAddress: address,
        customerSetupAddress: address,
      );
    } else {
      state = state.copyWith(
        companyAddress: address,
        companySetupAddress: address,
      );
    }

    state = state.copyWith(
      userType: userType,
      genderType: gender,
      staffPropose: user,
      disableInputForm: true,
    );

    checkSpeedOrder();
  }

  void fillDataFromUserInfo() {
    final data = GlobalData.instance;

    late UserType userType;
    userType = UserType.personal;
    GenderType? gender = GenderTypeExtension.fromString(data.userInfo?.gender);
    final user = InternalStaffEntity(
      fullName: data.userInfo?.fullName,
      phoneNumber: data.userInfo?.phoneNumber,
      username: data.userInfo?.username ?? '',
    );
    state = state.copyWith(
      userType: userType,
      genderType: gender,
      staffPropose: user,
    );

    checkSpeedOrder();
  }

  void fillDataFromAssociation(
    AssociationEntity associationInfo,
  ) {
    late UserType userType;
    if (associationInfo.customerType == UserType.personal) {
      userType = UserType.personal;
    } else {
      userType = UserType.company;
    }
    GenderType? gender = GenderTypeExtension.fromString(associationInfo.gender);

    AddressInfo address = AddressInfo(
      addressDetail: associationInfo.detailAddress,
      province: associationInfo.province,
      district: associationInfo.district,
      ward: associationInfo.ward,
    );

    final user = InternalStaffEntity(
      fullName: associationInfo.creatorInfo?.fullName,
      phoneNumber: associationInfo.creatorInfo?.phoneNumber,
      username: associationInfo.creatorInfo?.username ?? '',
    );

    if (userType == UserType.personal) {
      getCustomerInfo360(
        phoneNumber: associationInfo.customerPhone,
      );
      state = state.copyWith(
        customerAddress: address,
        customerSetupAddress: address,
      );
    } else {
      getCompanyInfo360(
        taxCode: associationInfo.companyTaxCode,
      );
      state = state.copyWith(
        companyAddress: address,
        companySetupAddress: address,
      );
    }

    state = state.copyWith(
      userType: userType,
      genderType: gender,
      staffPropose: user,
      disableInputForm: true,
    );

    checkSpeedOrder();
  }

  void changeTotalAmount(int price) {
    state = state.copyWith(
      totalTemp: price,
    );
  }

  void changeYearService({
    ServiceInfoEntity? serviceYearInfo,
  }) {
    state = state.copyWith(
      serviceYearInfo: serviceYearInfo,
      services: [],
      supplies: [],
    );

    checkSpeedOrder();
  }

  void changeUserType(UserType type) {
    if (state.serviceType == ServiceType.combo) {
      return;
    }

    state = state.copyWith(
      userType: type,
    );

    if (state.speedOrderType != null) {
      state.serviceType = null;
      state = state.copyWith(
        serviceType: null,
      );
    }
  }

  void changeGenderType(GenderType type) {
    state = state.copyWith(
      genderType: type,
    );
  }

  void reloadPage() {
    state = state.copyWith();
  }

  void setUpTime(DateStatusEntity dateSelect) {
    if (state.userType == UserType.personal) {
      state = state.copyWith(
        userTimeSetup: dateSelect,
      );
    } else {
      state = state.copyWith(
        companyTimeSetup: dateSelect,
      );
    }
  }

  void changeSetupSpeedService(bool isSetup) {
    if (isSetup) {
      final date = DateStatusEntity(
        selectSpeedTime: true,
        date: DateTime.now().toString(),
      );
      setUpTime(date);
    } else {
      final date = DateStatusEntity(
        selectSpeedTime: false,
        date: null,
        selectAfternoon: null,
        selectMorning: null,
      );
      setUpTime(date);
    }

    state = state.copyWith(
      isSetupSpeedService: isSetup,
    );
  }

  void selectAddress(
    AddressInfo address, {
    bool isSetup = false,
  }) {
    if (state.userType == UserType.personal) {
      if (isSetup) {
        state = state.copyWith(
          customerSetupAddress: address,
        );
      } else {
        state = state.copyWith(
          customerAddress: address,
          customerSetupAddress: address,
        );
      }
    } else {
      if (isSetup) {
        state = state.copyWith(
          companySetupAddress: address,
        );
      } else {
        state = state.copyWith(
          companyAddress: address,
          companySetupAddress: address,
        );
      }
    }

    checkSpeedOrder();
  }

  Future<CustomerInfoEntity?> getCustomerInfo360({
    String? phoneNumber,
    bool? isInternalStaff,
    bool checkPrice = false,
    bool? isCompanyType,
  }) async {
    state = state.copyWith(
      loadCustomerStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getCustomerInfo(
        customerType: UserType.personal,
        phoneNumber: phoneNumber,
      );

      CustomerInfoEntity? customerInfo;

      await result?.when(
        success: (data) async {
          if (data.phone != null) {
            customerInfo = data;
            AddressInfo? address;
            if (data.address != null) {
              address = AddressInfo(
                addressDetail: data.addressDetail,
                address: data.address,
                province: CodeEntity(
                  name: data.provinceName,
                  code: data.provinceCode,
                ),
                district: CodeEntity(
                  name: data.districtName,
                  code: data.districtCode,
                ),
                ward: CodeEntity(
                  name: data.wardName,
                  code: data.wardCode,
                ),
              );
            }

            state = state.copyWith(
              turnOnEditUserInfo: false,
              customerAddress: address,
              customerInfo: data,
              isInternalStaff: isInternalStaff,
              loadCustomerStatus: LoadStatus.success,
            );

            if (isCompanyType ?? false) return;

            if (checkPrice) {
              final price = await getPriceOrder(
                phoneNumber: phoneNumber,
                provinceCode: address?.province?.code,
                districtCode: address?.district?.code,
              );

              if (price != state.totalTemp) {
                alertController.sink.add(
                  [address!, price, false],
                );
              } else {
                state = state.copyWith(
                  customerSetupAddress: address,
                );
              }
            } else {
              if (address?.province?.code !=
                      GlobalData.instance.addressDefault?.province?.code ||
                  address?.district?.code !=
                      GlobalData.instance.addressDefault?.district?.code) {
                alertController.sink.add(
                  [
                    address!,
                    state.totalTemp,
                    true,
                  ],
                );
              } else {
                state = state.copyWith(
                  customerSetupAddress: address,
                );

                checkSpeedOrder();
              }
            }
          } else {
            state = state.copyWith(
              loadCustomerStatus: LoadStatus.failure,
              isInternalStaff: isInternalStaff,
              turnOnEditUserInfo: true,
            );
          }
        },
        error: (err) async {
          state = state.copyWith(
            loadCustomerStatus: LoadStatus.failure,
            isInternalStaff: isInternalStaff,
            message: err.message,
            turnOnEditUserInfo: true,
          );
        },
      );

      return customerInfo;
    } catch (error) {
      state = state.copyWith(
        loadCustomerStatus: LoadStatus.failure,
        isInternalStaff: isInternalStaff,
        turnOnEditUserInfo: true,
      );
    }
    return null;
  }

  Future<CustomerInfoEntity?> getCompanyInfo360({
    String? taxCode,
  }) async {
    state = state.copyWith(
      loadCustomerStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getCustomerInfo(
        customerType: UserType.company,
        taxCode: taxCode,
      );

      CustomerInfoEntity? companyInfo;

      await result?.when(
        success: (data) async {
          if (data.fullName != null) {
            companyInfo = data;
            AddressInfo? address;
            if (data.address != null) {
              address = AddressInfo(
                addressDetail: data.addressDetail,
                address: data.address,
                province: CodeEntity(
                  name: data.provinceName,
                  code: data.provinceCode,
                ),
                district: CodeEntity(
                  name: data.districtName,
                  code: data.districtCode,
                ),
                ward: CodeEntity(
                  name: data.wardName,
                  code: data.wardCode,
                ),
              );
            }

            state = state.copyWith(
              turnOnEditCompanyInfo: false,
              companyAddress: address,
              companySetupAddress: address,
              companyInfo: data,
              loadCustomerStatus: LoadStatus.success,
            );

            final price = await getPriceOrder(
              provinceCode: address?.province?.code,
              districtCode: address?.district?.code,
            );

            if (price != state.totalTemp) {
              alertController.sink.add(
                [address!, price],
              );
            } else {
              if (address?.province?.code !=
                      GlobalData.instance.addressDefault?.province?.code ||
                  address?.district?.code !=
                      GlobalData.instance.addressDefault?.district?.code) {
                alertController.sink.add(
                  [
                    address!,
                    state.totalTemp,
                    true,
                  ],
                );
              } else {
                state = state.copyWith(
                  companySetupAddress: address,
                );

                checkSpeedOrder();
              }
            }
          } else {
            state = state.copyWith(
              loadCustomerStatus: LoadStatus.failure,
              turnOnEditCompanyInfo: true,
            );
          }
        },
        error: (err) async {
          state = state.copyWith(
            loadCustomerStatus: LoadStatus.failure,
            message: err.message,
            turnOnEditCompanyInfo: true,
          );
        },
      );

      return companyInfo;
    } catch (error) {
      state = state.copyWith(
        loadCustomerStatus: LoadStatus.failure,
        turnOnEditCompanyInfo: true,
      );
    }
    return null;
  }

  void enableEditUserForm() {
    state = state.copyWith(
      turnOnEditUserInfo: true,
    );
  }

  void enableEditCompanyForm() {
    state = state.copyWith(
      turnOnEditCompanyInfo: true,
    );
  }

  void onValidateUserForm() {
    state = state.copyWith(
      autoValidateUserForm: AutovalidateMode.always,
    );
  }

  void onValidateCompanyForm() {
    state = state.copyWith(
      autoValidateCompanyForm: AutovalidateMode.always,
    );
  }

  void selectServiceType(ServiceType type) {
    state.userTimeSetup = null;
    state.companyTimeSetup = null;

    state = state.copyWith(
      serviceType: type,
      isSetupSpeedService: false,
      userTimeSetup: null,
      companyTimeSetup: null,
    );

    checkSpeedOrder();
  }

  void resetInfo() {
    state.paymentSelected = null;
    state.voucherSelected = null;
    state.serviceYearInfo = null;

    state.serviceOrderInfo?.servicesInfo = null;
    state.serviceOrderInfo?.suppliesInfo = null;
    state.serviceOrderInfo?.packageCode = null;
    state.serviceOrderInfo?.comboCode = null;

    state = state.copyWith(
      services: [],
      supplies: [],
      serviceYearInfo: null,
      isGetBill: false,
      paymentSelected: null,
      voucherSelected: null,
    );
  }

  Future<bool?> checkInternalStaff({
    String? phoneNumber,
  }) async {
    state = state.copyWith(
      checkStaffStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListStaff(
        phoneNumber: phoneNumber,
        userType: "INTERNAL",
      );
      bool checkStaff = false;

      await result.when(
        success: (data) async {
          if ((data.data ?? []).isNotEmpty) {
            checkStaff = true;
          }
          state = state.copyWith(
            checkStaffStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          checkStaff = false;
          state = state.copyWith(
            checkStaffStatus: LoadStatus.failure,
          );
        },
      );
      return checkStaff;
    } catch (error) {
      state = state.copyWith(
        checkStaffStatus: LoadStatus.failure,
      );
      return false;
    }
  }

  Future<int?> getPriceOrder({
    String? phoneNumber,
    DateTime? startTime,
    String? provinceCode,
    String? districtCode,
  }) async {
    try {
      List<ServiceInfoEntity> services = [];
      if (state.serviceYearInfo != null) {
        services.add(state.serviceYearInfo!);
      } else {
        services = state.services ?? [];
      }

      final servicesInfo = services.map((e) {
        return ProductsInfoParam(
          code: e.code,
          quantity: e.quantity,
        );
      }).toList();

      String? proCode = provinceCode;
      String? disCode = districtCode;
      DateTime? date;

      if (state.userType == UserType.personal) {
        final userAddress = state.customerSetupAddress;
        provinceCode = proCode ?? userAddress?.province?.code;
        districtCode = disCode ?? userAddress?.district?.code;
        date = startTime ?? state.userTimeSetup?.getDate ?? DateTime.now();
      } else {
        final companyAddress = state.companySetupAddress;
        provinceCode = proCode ?? companyAddress?.province?.code;
        districtCode = disCode ?? companyAddress?.district?.code;
        date = startTime ?? state.companyTimeSetup?.getDate ?? DateTime.now();
      }

      final result = await appLocator<OrderRepository>().getPriceOrder(
        param: PriceOrderParam(
          phoneNumber: phoneNumber ?? GlobalData.instance.userInfo?.phoneNumber,
          startTime: date.toIso8601String(),
          districtCode: districtCode,
          provinceCode: provinceCode,
          servicesInfo: servicesInfo,
          suppliesInfo: state.supplies,
          packageCode: state.serviceYearInfo?.code,
          orderType: state.serviceType == ServiceType.salePoint
              ? OrderType.salePoint.keyToServer
              : state.serviceType == ServiceType.salePointCombo
                  ? OrderType.salePointCombo.keyToServer
                  : null,
          comboCode: state.comboCode,
        ),
      );

      int? price;
      await result?.when(
        success: (data) async {
          price = data.servicesAmount ?? 0;
        },
      );

      return price;
    } catch (error) {
      return null;
    }
  }

  void updateListService({
    AddressInfo? address,
    DateTime? startTime,
  }) async {
    try {
      List<ServiceInfoEntity> services = [];
      List<ProductsInfoParam> servicesConvert = [];

      if (state.serviceYearInfo != null) {
        services.add(state.serviceYearInfo!);
      } else {
        services = state.services ?? [];
      }

      servicesConvert = services.map((e) {
        return ProductsInfoParam(
          code: e.code,
          quantity: e.quantity,
          // price: e.price,
        );
      }).toList();

      String? provinceCode;
      String? districtCode;
      String? wardCode;
      DateTime? date;

      if (state.userType == UserType.personal) {
        final userAddress = state.customerSetupAddress;
        provinceCode = address?.province?.code ?? userAddress?.province?.code;
        districtCode = address?.district?.code ?? userAddress?.district?.code;
        wardCode = address?.ward?.code ?? userAddress?.ward?.code;
        date = startTime ?? state.userTimeSetup?.getDate ?? DateTime.now();
      } else {
        final companyAddress = state.companySetupAddress;

        provinceCode =
            address?.province?.code ?? companyAddress?.province?.code;
        districtCode =
            address?.district?.code ?? companyAddress?.district?.code;
        wardCode = address?.ward?.code ?? companyAddress?.ward?.code;
        date = startTime ?? state.companyTimeSetup?.getDate ?? DateTime.now();
      }

      final result = await appLocator<OrderRepository>().getItemUpdated(
        body: ItemsAmountBody(
          provinceCode: provinceCode,
          districtCode: districtCode,
          wardCode: wardCode,
          startTime: date,
          servicesInfo: servicesConvert,
        ),
      );

      await result?.when(
        success: (data) async {
          if (state.serviceYearInfo != null) {
            state = state.copyWith(
              serviceYearInfo: data.servicesInfo?.first,
            );
          } else {
            state = state.copyWith(
              services: data.servicesInfo,
            );
          }
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: "Đã có lỗi xảy ra",
      );
    }
  }

  //page 2
  void setupService() async {
    try {
      OrderParam? orderInfo = state.serviceOrderInfo;
      List<ServiceInfoEntity> services = [];

      if (state.serviceType == ServiceType.package) {
        if ((state.services ?? []).isNotEmpty) {
          return;
        }

        List<SupplyEntity> supplies = [];

        final config = await appLocator<OrderRepository>().getPackageConfig(
          code: orderInfo?.packageCode ?? '',
          startTime: orderInfo?.shippingInfo?.startTime ?? '',
          provinceCode: orderInfo?.userInfo?.provinceCode ?? '',
          districtCode: orderInfo?.userInfo?.districtCode ?? '',
        );

        await config?.when(
          success: (data) async {
            final listService = data.services ?? [];
            for (var element in listService) {
              if (element.quantity < (element.minItem ?? 0)) {
                element.quantity = element.minItem!;
              }
            }

            services.addAll(listService);
            if (orderInfo?.isSpeedService ?? false) {
              final isExist = services.where((element) {
                return element.code == 'HOT_SERVICE';
              }).toList();

              if (isExist.isEmpty) {
                services.add(
                  ServiceInfoEntity(
                    code: 'HOT_SERVICE',
                    name: 'Phụ phí cung cấp dịch vụ nóng gấp trong 3 giờ',
                    price: 100000,
                    quantity: 1,
                    canEditService: false,
                  ),
                );
              }
            }

            if (data.supplies != null) {
              final listSupply = data.supplies ?? [];
              for (var element in listSupply) {
                if (element.quantity < (element.minItem ?? 0)) {
                  element.quantity = element.minItem!;
                }
              }
              supplies = listSupply;
            }

            state = state.copyWith(
              services: services,
              supplies: supplies,
            );
          },
          error: (err) async {
            state = state.copyWith(
              message: err.message,
            );
          },
        );
      } else {
        services.addAll(orderInfo?.servicesInfo ?? []);
        if (orderInfo?.isSpeedService ?? false) {
          final isExist = services.where((element) {
            return element.code == 'HOT_SERVICE';
          }).toList();

          if (isExist.isEmpty) {
            services.add(
              ServiceInfoEntity(
                code: 'HOT_SERVICE',
                name: 'Phụ phí cung cấp dịch vụ nóng gấp trong 3 giờ',
                price: 100000,
                quantity: 1,
                canEditService: false,
              ),
            );
          }
        }

        state = state.copyWith(
          services: services,
          supplies: state.supplies ?? [],
        );
      }

      calculatorServiceAndSupply();
    } catch (error) {
      state = state.copyWith(
        message: 'Có lỗi xảy ra',
      );
    }
  }

  void changeQuantity({
    required int indexItem,
    required num quantity,
  }) {
    final listService = state.services!;

    listService[indexItem].quantity = quantity;
    removeBaseOnQuantity(
      quantityOrder: quantity,
      servicesTmp: listService[indexItem],
    );

    state = state.copyWith(
      services: listService,
    );

    calculatorServiceAndSupply();
  }

  void removeBaseOnQuantity({
    required num quantityOrder,
    ServiceInfoEntity? servicesTmp,
  }) {
    final listFilter = state.services ?? [];
    if (listFilter.isEmpty) return;
    if (quantityOrder == 0 && (servicesTmp?.code ?? '').isNotEmpty) {
      final data = listFilter
          .where((element) => element.code == servicesTmp!.code)
          .toList();

      if (data.isNotEmpty) {
        listFilter.remove(data[0]);
      }

      state = state.copyWith(
        services: listFilter,
      );

      calculatorServiceAndSupply();
    }
  }

  void changeService(List<ServiceInfoEntity> services) {
    state = state.copyWith(
      services: services,
    );

    final orderInfo = OrderParam(
      servicesInfo: state.services ?? [],
      suppliesInfo: state.supplies ?? [],
    );

    saveOrderInfo(orderInfo);
    calculatorServiceAndSupply();
  }

  void getServiceInfo({
    required String code,
  }) async {
    try {
      final config =
          await appLocator<OrderRepository>().getDetailSuppliesComboInfo(
        comboCode: code,
      );
      await config?.when(
        success: (data) async {
          state = state.copyWith(
            comboCode: code,
          );
          changeSuppliesSalePoint(
            data.supplies ?? [],
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: 'Có lỗi xảy ra',
      );
    }
  }

  void changeSuppliesSalePoint(List<SupplyEntity> supplies) {
    for (var element in supplies) {
      element.isRequired = true;
    }
    state = state.copyWith(
      supplies: supplies,
    );

    calculatorServiceAndSupply();
  }

  void changeSupplies(List<SupplyEntity> supplies) {
    List<SupplyEntity> listSupply = state.supplies ?? [];

    for (var element in supplies) {
      final data = listSupply.where((e) => e.code == element.code).toList();

      if (data.isNotEmpty) {
        data[0].quantity = data[0].quantity + element.quantity;

        if (data[0].getWmsType == WarehouseSupplyType.both) {
          data[0].wmsTypeDisplay = (data[0].maxExportQuantity != null)
              ? data[0].quantity > (data[0].maxExportQuantity ?? 0)
                  ? WarehouseSupplyType.wms.keyToServer
                  : WarehouseSupplyType.notWms.keyToServer
              : data[0].wmsTypeDisplay;
        }
      } else {
        listSupply.add(element);
      }
    }

    state = state.copyWith(
      supplies: listSupply,
    );

    calculatorServiceAndSupply();
  }

  void changeSupplyQuantity({
    required int indexItem,
    required num quantity,
  }) {
    final listSupply = state.supplies!;
    final item = listSupply[indexItem];

    item.quantity = quantity;

    if (item.getWmsType == WarehouseSupplyType.both) {
      item.wmsTypeDisplay = (item.maxExportQuantity != null)
          ? quantity > (item.maxExportQuantity ?? 0)
              ? WarehouseSupplyType.wms.keyToServer
              : WarehouseSupplyType.notWms.keyToServer
          : item.wmsTypeDisplay;
    }

    if (quantity == 0 && (item.code ?? '').isNotEmpty) {
      final data =
          listSupply.where((element) => element.code == item.code).toList();

      if (data.isNotEmpty) {
        listSupply.remove(data[0]);
      }
    }

    state = state.copyWith(
      supplies: listSupply,
    );

    calculatorServiceAndSupply();
  }

  //page 3

  void changeGetBillStatus() {
    final currentState = state.isGetBill;

    state = state.copyWith(
      isGetBill: !currentState,
    );
  }

  void getListPaymentMethod() async {
    try {
      final result = await appLocator<PaymentRepository>().getPaymentMethod(
        productType: state.speedOrderType == ServiceType.salePoint
            ? ProductType.salePoint
            : ProductType.service,
        paymentType: PaymentType.after,
      );

      await result?.when(
        success: (data) async {
          List<PaymentEntity> listPayment = data.data ?? [];

          for (int i = 0; i < listPayment.length; i++) {
            final payment = listPayment[i];

            if (payment.isDefault == true) {
              state = state.copyWith(
                paymentSelected: payment,
              );
              break;
            }
          }
        },
        error: (e) async {
          state = state.copyWith(
            message: e.toString(),
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        message: e.toString(),
      );
    }
  }

  void choosePaymentProvider(PaymentEntity type) {
    state = state.copyWith(
      paymentSelected: type,
    );
  }

  void calculatorServiceAndSupply() {
    int total = 0;

    for (int i = 0; i < (state.services?.length ?? 0); i++) {
      final service = state.services![i];
      total = (total + service.quantity * (service.price ?? 0)).toInt();
    }

    for (int i = 0; i < (state.supplies?.length ?? 0); i++) {
      final supply = state.supplies![i];
      total = (total + supply.quantity * (supply.price ?? 0)).toInt();
    }

    state = state.copyWith(
      totalAmount: total,
    );
  }

  void getVouchers() async {
    try {
      String? provinceCode =
          state.serviceOrderInfo?.shippingInfo?.provinceCode ??
              GlobalData.instance.addressDefault?.province?.code;
      String? districtCode =
          state.serviceOrderInfo?.shippingInfo?.districtCode ??
              GlobalData.instance.addressDefault?.district?.code;
      String? wardCode = state.serviceOrderInfo?.shippingInfo?.wardCode ??
          GlobalData.instance.addressDefault?.ward?.code;

      final result = await appLocator<OrderRepository>().getMyCoupon(
        body: VoucherBody(
          phoneNumber: GlobalData.instance.userInfo?.phoneNumber ?? '',
          servicesInfo: (state.serviceOrderInfo?.servicesInfo ?? []).map((e) {
            return ProductsInfoParam(
              code: e.code,
              quantity: e.quantity,
            );
          }).toList(),
          startTime: state.serviceOrderInfo?.shippingInfo?.startTime,
          provinceCode: provinceCode,
          districtCode: districtCode,
          wardCode: wardCode,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            hasVoucher: (data.data ?? []).isNotEmpty,
          );
        },
        error: (err) async {
          state = state.copyWith(
            hasVoucher: false,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        hasVoucher: false,
      );
    }
  }

  Future calculatorPriceOrder({
    String? phoneNumber,
  }) async {
    state = state.copyWith(
      calculatorPriceStatus: LoadStatus.loading,
    );

    try {
      String? startTime = state.serviceOrderInfo?.shippingInfo?.startTime;
      String? provinceCode = state.serviceOrderInfo?.shippingInfo?.provinceCode;
      String? districtCode = state.serviceOrderInfo?.shippingInfo?.districtCode;
      final services = state.serviceOrderInfo?.servicesInfo;

      final servicesInfo = services?.map((e) {
        return ProductsInfoParam(
          code: e.code,
          quantity: e.quantity,
          price: e.price,
          outsourcePrice: e.outsourcePrice,
          name: e.name,
        );
      }).toList();

      final result = await appLocator<OrderRepository>().getPriceOrder(
        param: PriceOrderParam(
          phoneNumber: phoneNumber,
          startTime: startTime,
          districtCode: districtCode,
          provinceCode: provinceCode,
          servicesInfo: servicesInfo,
          suppliesInfo: state.supplies,
          couponCode: state.voucherSelected?.code,
          packageCode: state.serviceYearInfo?.code,
          orderType: state.serviceType == ServiceType.salePoint
              ? OrderType.salePoint.keyToServer
              : state.serviceType == ServiceType.salePointCombo
                  ? OrderType.salePointCombo.keyToServer
                  : null,
          comboCode: state.comboCode,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            priceOrder: data,
            calculatorPriceStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            priceOrder: null,
            calculatorPriceStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        priceOrder: null,
        calculatorPriceStatus: LoadStatus.failure,
      );
    }
  }

  Future getPromotionPrice({
    String? couponCode,
    String? phoneNumber,
    String? startTime,
    String? provinceCode,
    String? districtCode,
    List<ServiceInfoEntity>? services,
  }) async {
    try {
      final servicesInfo = services?.map((e) {
        return ProductsInfoParam(
          code: e.code,
          quantity: e.quantity,
        );
      }).toList();

      final result = await appLocator<OrderRepository>().getPriceOrder(
        param: PriceOrderParam(
          phoneNumber: phoneNumber,
          startTime: startTime,
          districtCode: districtCode,
          provinceCode: provinceCode,
          servicesInfo: servicesInfo,
          suppliesInfo: state.supplies,
          couponCode: couponCode,
          packageCode: state.serviceYearInfo?.code,
          orderType: state.serviceType == ServiceType.salePoint
              ? OrderType.salePoint.keyToServer
              : state.serviceType == ServiceType.salePointCombo
                  ? OrderType.salePointCombo.keyToServer
                  : null,
          comboCode: state.comboCode,
        ),
      );

      int? price;
      await result?.when(
        success: (data) async {
          price = data.discountAmount ?? 0;
        },
      );

      return price;
    } catch (error) {
      return null;
    }
  }

  void selectStaff({
    required InternalStaffEntity staff,
    bool isUserPropose = false,
  }) {
    if (isUserPropose) {
      state = state.copyWith(
        staffPropose: staff,
      );
    } else {
      state = state.copyWith(
        staffImplementation: staff,
      );
    }
  }

  void deleteStaffPropose() {
    state.staffPropose = null;
    state = state.copyWith(
      staffPropose: null,
    );
  }

  void deleteStaffImplementation() {
    state.staffImplementation = null;
    state = state.copyWith(
      staffImplementation: null,
    );
  }

  Future<OrderResponse?> createOrder(OrderParam param) async {
    state = state.copyWith(
      createOrderStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().createServiceOrder(
        param: param,
      );

      OrderResponse? orderResponse;
      await result?.when(
        success: (data) async {
          orderResponse = data;
          state = state.copyWith(
            createOrderStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            createOrderStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );

      return orderResponse;
    } catch (error) {
      state = state.copyWith(
        createOrderStatus: LoadStatus.failure,
      );
      return null;
    }
  }

  void onChangeSellers(List<InternalStaffEntity> listUser) {
    state = state.copyWith(
      sellers: listUser,
    );
  }

  void deleteSeller(int index) {
    var listUser = state.sellers!;

    listUser.removeAt(index);

    state = state.copyWith(
      sellers: listUser,
    );
  }

  void selectVoucher(VoucherEntity? voucher) {
    if (voucher == null) {
      state.voucherSelected = null;
      state = state.copyWith(
        voucherSelected: null,
      );
    } else {
      state = state.copyWith(
        voucherSelected: voucher,
      );
    }
  }

  void reloadBillForm() {
    state = state.copyWith();
  }

  Future<bool?> checkRoleFT3({
    String? phoneNumber,
  }) async {
    try {
      final result = await appLocator<UserProfileRepository>().checkRoleUser(
        body: CheckRoleUserBody(
          userName: phoneNumber,
          titles: ['FT3'],
        ),
      );

      bool? checkResult;

      await result?.when(
        success: (data) async {
          checkResult = data.isValid;
        },
        error: (err) async {
          checkResult = false;
        },
      );
      return checkResult;
    } catch (error) {
      return null;
    }
  }

  Future<bool?> checkExistOrder({
    required String customerId,
  }) async {
    try {
      final result = await appLocator<OrderRepository>().checkExistOrder(
        customerId: customerId,
      );

      bool? checkResult;

      await result?.when(
        success: (data) async {
          checkResult = data.isDuplicated ?? false;

          state = state.copyWith(
            listOrderExist: data.orders,
          );
        },
        error: (err) async {
          checkResult = false;
        },
      );
      return checkResult;
    } catch (error) {
      return null;
    }
  }

  Future checkSpeedOrder({
    String? orderType,
    String? wardCode,
  }) async {
    if (state.serviceType != ServiceType.single &&
        state.serviceType != ServiceType.package) {
      return;
    }

    try {
      DateTime timeTemp = DateTime.now();
      String? wardCodeTemp;

      if (state.userType == UserType.personal) {
        wardCodeTemp = state.customerSetupAddress?.ward?.code;
      } else {
        wardCodeTemp = state.companySetupAddress?.ward?.code;
      }

      if ((wardCodeTemp ?? '').isEmpty) {
        wardCodeTemp = wardCode ?? '';
      }

      final result = await appLocator<OrderRepository>().checkHotOrder(
        orderType: orderType ?? state.serviceType?.keyToCreateOrder ?? '',
        wardCode: wardCodeTemp ?? '',
        startTime: timeTemp.toIso8601String(),
        packageCode: state.serviceYearInfo?.code,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            enableSpeedOrder: data.isAvailable ?? false,
          );
        },
      );
    } catch (error) {
      LogUtils.d('error: $error');
    }
  }

  Future<CommissionConfirmResponse?> checkCommissionConfirm({
    required String phoneNumber,
  }) async {
    state = state.copyWith(
      checkStaffStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().checkCommissionConfirm(
        customerPhone: phoneNumber,
      );

      CommissionConfirmResponse? response;

      await result?.when(
        success: (data) async {
          response = data;
          state = state.copyWith(
            checkStaffStatus: LoadStatus.success,
          );
        },
        error: (e) async {
          state = state.copyWith(
            message: e.toString(),
            checkStaffStatus: LoadStatus.failure,
          );
        },
      );

      return response;
    } catch (e) {
      state = state.copyWith(
        message: e.toString(),
        checkStaffStatus: LoadStatus.failure,
      );
      return null;
    }
  }

  @override
  void dispose() {
    alertController.close();
    super.dispose();
  }
}
