import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/entities/supply_filter_entity.dart';
import 'package:vcc/domain/enums/inventory_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/supplies_type.dart';
import 'package:vcc/domain/enums/warehouse_supply_type.dart';

part 'search_supplies_state.dart';

final searchSuppliesProvider = StateNotifierProvider.autoDispose<
    SearchSuppliesViewModel, SearchSuppliesState>(
  (ref) => SearchSuppliesViewModel(ref: ref),
);

class SearchSuppliesViewModel extends StateNotifier<SearchSuppliesState> {
  final Ref ref;

  SearchSuppliesViewModel({
    required this.ref,
  }) : super(SearchSuppliesState());

  Future<void> getData({
    String? keySearch,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
      keySearch: keySearch,
    );

    try {
      final resultParam = await appLocator<OrderRepository>().searchSupply(
        page: state.currentPage,
        pageSize: BaseConstant.defaultLimitSize,
        keyword: state.keySearch,
        inventoryTypes: state.supplyFilter?.inventoryType?.keyToServer,
        wmsTypes: state.supplyFilter?.warehouseSupplyType?.keyToServer,
        supplyTypes: SuppliesType.normal.keyToServer,
      );

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            totalResult: data.total,
            listSupplies: data.data,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> fetchNextData() async {
    if (state.listSupplies!.length >= (state.totalResult ?? 0)) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final resultParam = await appLocator<OrderRepository>().searchSupply(
        page: state.currentPage + 1,
        pageSize: BaseConstant.defaultLimitSize,
        keyword: state.keySearch,
        inventoryTypes: state.supplyFilter?.inventoryType?.keyToServer,
        wmsTypes: state.supplyFilter?.warehouseSupplyType?.keyToServer,
        supplyTypes: SuppliesType.normal.keyToServer,
      );

      await resultParam?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            currentPage: state.currentPage + 1,
            listSupplies: state.listSupplies! + (data.data ?? []),
          );
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void filterData({
    SupplyFilterEntity? supplyFilter,
  }) {
    late SupplyFilterEntity filter;

    if (supplyFilter == null) {
      filter = SupplyFilterEntity(
        inventoryType: null,
        warehouseSupplyType: null,
      );
    } else {
      filter = supplyFilter;
    }
    state = state.copyWith(
      supplyFilter: filter,
    );

    getData();
  }
}
