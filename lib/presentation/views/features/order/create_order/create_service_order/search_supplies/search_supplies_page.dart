import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_supply/filter_supply_view.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_search.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/utils/debouncer.dart';
import 'package:vcc/utils/string_utils.dart';

import 'search_supplies_view_model.dart';

class SearchSuppliesArguments {}

class SearchSuppliesPage extends StatefulHookConsumerWidget {
  final SearchSuppliesArguments? arguments;

  const SearchSuppliesPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<SearchSuppliesPage> createState() => _SearchSuppliesPageState();
}

class _SearchSuppliesPageState extends ConsumerState<SearchSuppliesPage>
    with TickerProviderStateMixin {
  late Debounce<String> debouncer;
  late ScrollController scrollController;

  @override
  void initState() {
    scrollController = ScrollController();
    scrollController.addListener(_scrollListener);
    debouncer = Debounce<String>(const Duration(milliseconds: 500), (value) {
      ref.read(searchSuppliesProvider.notifier).getData(
            keySearch: value,
          );
    });

    Future(() {
      ref.read(searchSuppliesProvider.notifier).getData();
    });
    super.initState();
  }

  void _scrollListener() {
    final maxScroll = scrollController.position.maxScrollExtent;
    final currentScroll = scrollController.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(searchSuppliesProvider.notifier).fetchNextData();
    }
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(searchSuppliesProvider);

    PreferredSizeWidget appbarWidget;

    appbarWidget = AppBarSearch(
      actionWidget: [
        Padding(
          padding: const EdgeInsets.only(
            right: 16,
          ),
          child: InkWellWidget(
            onTap: () {
              AppBottomSheet.showNormalBottomSheet(
                context,
                title: "Bộ lọc",
                isFlexible: true,
                child: FilterSupplyView(
                  chooseFilter: state.supplyFilter,
                  onApply: (value) {
                    ref.read(searchSuppliesProvider.notifier).filterData(
                          supplyFilter: value,
                        );
                  },
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.only(
                top: 8,
                bottom: 8,
              ),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: <Widget>[
                  (state.supplyFilter?.inventoryType != null ||
                          state.supplyFilter?.warehouseSupplyType != null)
                      ? MyAssets.icons.iconFilterWhiteS24.svg()
                      : MyAssets.icons.iconFilterBorderWhiteS24.svg(),
                  Text(
                    "Lọc",
                    style: UITextStyle.caption1Medium.copyWith(
                      color: BaseColors.backgroundWhite,
                    ),
                  ),
                ],
              ),
            ),
          ),
        )
      ],
      backFunction: () {
        context.pop(true);
      },
      onChanged: (value) {
        debouncer.value = value;
      },
    );

    return LayoutPage(
      appbar: appbarWidget,
      body: GestureDetector(
        onTap: () {
          FocusScope.of(context).unfocus();
        },
        child: _buildPage(state),
      ),
    );
  }

  Widget _buildPage(SearchSuppliesState state) {
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      var state = ref.watch(searchSuppliesProvider);

      if ((state.listSupplies ?? []).isEmpty) {
        return EmptyListWidget(
          title: "Không tìm thấy vật tư nào",
          onRefresh: refreshData,
        );
      }

      return ListView.builder(
        controller: scrollController,
        physics: const AlwaysScrollableScrollPhysics(),
        itemCount: state.listSupplies?.length ?? 0,
        itemBuilder: (context, index) {
          var item = state.listSupplies![index];
          return InkWellWidget(
            onTap: () {
              context.pop(item);
            },
            child: Column(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item.name ?? "",
                        style: UITextStyle.body2Medium,
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      Row(
                        children: [
                          Text(
                            "Mã: ",
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                          Text(
                            item.code ?? "",
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textTitle,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      Row(
                        children: [
                          Text(
                            "Loại vật tư: ",
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                          Text(
                            item.getWmsName,
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textTitle,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(
                        height: 4,
                      ),
                      Row(
                        children: [
                          Text(
                            "Giá: ",
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textSubtitle,
                            ),
                          ),
                          Text(
                            item.price != null
                                ? StringUtils.formatMoney(item.price ?? 0)
                                : "Linh động",
                            style: UITextStyle.caption1Regular.copyWith(
                              color: BaseColors.textTitle,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
                DividerWidget(
                  height: 4,
                  color: BaseColors.backgroundGray,
                ),
              ],
            ),
          );
        },
      );
    }
  }

  Future<void> refreshData() async {
    ref.read(searchSuppliesProvider.notifier).getData();
  }
}
