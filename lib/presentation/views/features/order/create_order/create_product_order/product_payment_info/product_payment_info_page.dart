import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/payment_type.dart';
import 'package:vcc/domain/enums/product_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/enums/worker_type.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_staff/choose_staff_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_product_order/create_product_order_view_model.dart';
import 'package:vcc/presentation/views/features/order/create_order/order_success/order_success_page.dart';
import 'package:vcc/presentation/views/features/order/create_order/payment_provider/payment_provider_page.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/card_custom_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/hide_button.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/normal_text_field.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class ProductPaymentInfoPage extends StatefulHookConsumerWidget {
  const ProductPaymentInfoPage({
    super.key,
  });

  @override
  ConsumerState<ProductPaymentInfoPage> createState() =>
      _ProductPaymentInfoPageState();
}

class _ProductPaymentInfoPageState extends ConsumerState<ProductPaymentInfoPage>
    with AutomaticKeepAliveClientMixin {
  late TextEditingController orderTaxCompany;
  late TextEditingController companyName;
  late TextEditingController emailController;

  late TextEditingController orderUserNameController;
  late TextEditingController orderPhoneNumberController;
  late TextEditingController orderPaymentTypeController;
  late TextEditingController orderBankNameController;
  late TextEditingController orderBankNumberController;
  late TextEditingController orderAddressController;
  late TextEditingController orderNoteController;

  final billFormKey = GlobalKey<FormState>();

  CustomerInfoParam? userInfo;

  @override
  void initState() {
    orderTaxCompany = TextEditingController();
    companyName = TextEditingController();
    emailController = TextEditingController();
    orderUserNameController = TextEditingController();
    orderPhoneNumberController = TextEditingController();
    orderPaymentTypeController = TextEditingController(
      text: "CK/TM",
    );
    orderBankNameController = TextEditingController();
    orderBankNumberController = TextEditingController();
    orderNoteController = TextEditingController();
    orderAddressController = TextEditingController();

    Future(() {
      userInfo =
          ref.watch(createProductOrderProvider).productOrderInfo?.userInfo;
      ref.read(createProductOrderProvider.notifier).calculatorPriceOrder(
            phoneNumber: userInfo?.phoneNumber,
          );
      ref.read(createProductOrderProvider.notifier).getListPaymentMethod(
            paymentType: PaymentType.after,
            productType: ProductType.product,
          );
    });
    super.initState();
  }

  @override
  void dispose() {
    orderTaxCompany.dispose();
    companyName.dispose();
    emailController.dispose();
    orderUserNameController.dispose();
    orderPhoneNumberController.dispose();
    orderPaymentTypeController.dispose();
    orderBankNameController.dispose();
    orderBankNumberController.dispose();
    orderNoteController.dispose();
    orderAddressController.dispose();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var state = ref.watch(createProductOrderProvider);

    return Stack(
      key: const ValueKey("ProductPaymentInfoPage"),
      children: [
        Scaffold(
          backgroundColor: BaseColors.backgroundWhite,
          bottomNavigationBar: Container(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            decoration: BoxDecoration(
              color: BaseColors.backgroundWhite,
              boxShadow: AppBoxShadows.shadowNormal,
            ),
            child: SafeArea(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: 6,
                    ),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            "Tổng tiền đơn hàng",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                        ),
                        Text(
                          StringUtils.formatMoney(
                            state.priceOrder?.totalOrder ?? 0,
                          ),
                          style: UITextStyle.body1SemiBold.copyWith(
                            color: BaseColors.primary,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 8),
                  BaseButton(
                    text: "Tạo đơn hàng",
                    onTap: () {
                      onCreateOrder(context);
                    },
                  ),
                  const SizedBox(height: 8),
                ],
              ),
            ),
          ),
          body: SingleChildScrollView(
            child: InkWell(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () {
                FocusScope.of(context).unfocus();
              },
              child: Container(
                color: BaseColors.backgroundWhite,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    _buildOrderInfo(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    _buildBillInfo(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    _buildTeamContributeInfo(),
                  ],
                ),
              ),
            ),
          ),
        ),
        if (state.createOrderStatus == LoadStatus.loading)
          const Center(
            child: LoadingIndicatorWidget(),
          ),
      ],
    );
  }

  Widget _buildOrderInfo() {
    var state = ref.watch(createProductOrderProvider);
    return Padding(
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            "Thông tin thanh toán",
            style: UITextStyle.body1SemiBold,
          ),
          const SizedBox(height: 16),
          RichText(
            text: TextSpan(
              text: "Hình thức thanh toán",
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
              children: <TextSpan>[
                TextSpan(
                  text: " *",
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.primary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: InkWell(
                  onTap: () {
                    ref
                        .read(createProductOrderProvider.notifier)
                        .choosePaymentType(
                          PaymentType.before,
                          ProductType.product,
                        );

                    ref
                        .read(createProductOrderProvider.notifier)
                        .getListPaymentMethod(
                          paymentType: PaymentType.before,
                          productType: ProductType.product,
                        );
                  },
                  child: Row(
                    children: [
                      state.paymentType == PaymentType.before
                          ? MyAssets.icons.iconRadioButtonEnable.svg()
                          : MyAssets.icons.iconRadioButton.svg(),
                      const SizedBox(width: 4),
                      const Text(
                        "Thanh toán trước",
                        style: BaseStyle.bodyMedium,
                      )
                    ],
                  ),
                ),
              ),
              Expanded(
                child: InkWell(
                  onTap: () {
                    ref
                        .read(createProductOrderProvider.notifier)
                        .choosePaymentType(
                            PaymentType.after, ProductType.product);
                    ref
                        .read(createProductOrderProvider.notifier)
                        .getListPaymentMethod(
                          paymentType: PaymentType.after,
                          productType: ProductType.product,
                        );
                  },
                  child: Row(
                    children: [
                      state.paymentType == PaymentType.after
                          ? MyAssets.icons.iconRadioButtonEnable.svg()
                          : MyAssets.icons.iconRadioButton.svg(),
                      const SizedBox(width: 4),
                      const Text(
                        "Thanh toán sau",
                        style: BaseStyle.bodyMedium,
                      )
                    ],
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 24),
          RichText(
            text: TextSpan(
              text: "Phương thức thanh toán",
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
              children: <TextSpan>[
                TextSpan(
                  text: " *",
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.primary,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 8),
          CardCustomWidget(
            titleWidget: Text(
              state.paymentSelected?.name ?? "",
              style: BaseStyle.bodyMedium,
            ),
            suffix: MyAssets.icons.arrowRight.svg(),
            onPress: () {
              context.push(
                RouterPaths.choosePaymentProvider,
                extra: PaymentProviderArguments(
                  productType: ProductType.product,
                  paymentType: state.paymentType,
                  paymentSelected: state.paymentSelected,
                  onChoose: (value) {
                    ref
                        .read(createProductOrderProvider.notifier)
                        .choosePaymentProvider(value);
                  },
                ),
              );
            },
          ),
          const SizedBox(height: 24),
          Text(
            "Mã giảm giá",
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 8),
          CardCustomWidget(
            titleWidget: Text(
              "Không có mã giảm giá khả dụng",
              style: UITextStyle.body1Regular,
            ),
            titleStyle: UITextStyle.body1Regular.copyWith(
              color: BaseColors.textPlaceholder,
            ),
            backgroundColor: BaseColors.backgroundGray1,
            // suffix: MyAssets.icons.arrowRight.svg(),
          ),
          const SizedBox(height: 16),
          _buildPaymentInfo(),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildPaymentInfo() {
    var state = ref.watch(createProductOrderProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 12,
          ),
          child: Text(
            "Chi tiết thanh toán",
            style: UITextStyle.body2SemiBold.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
        ),
        const DividerWidget(),
        _buildItemPriceInfo(
          title: "Tổng hàng hóa",
          value: StringUtils.formatMoney(state.priceOrder?.productsAmount ?? 0),
        ),
        _buildItemPriceInfo(
          title: "Tổng dịch vụ",
          value: StringUtils.formatMoney(state.priceOrder?.servicesAmount ?? 0),
        ),
        _buildItemPriceInfo(
          title: "Tổng đơn",
          value: StringUtils.formatMoney(state.priceOrder?.totalAmount ?? 0),
        ),
        _buildItemPriceInfo(
          title: "Phí vận chuyển",
          value: StringUtils.formatMoney(state.priceOrder?.shippingAmount ?? 0),
        ),
        if ((state.priceOrder?.discountAmount ?? 0) > 0) ...[
          _buildItemPriceInfo(
            title: "Voucher/khuyến mại:",
            value:
                "-${StringUtils.formatMoney(state.priceOrder?.discountAmount ?? 0)}",
          ),
        ],
        ListView.builder(
          itemCount: state.priceOrder?.promotions?.length ?? 0,
          padding: EdgeInsets.zero,
          physics: const NeverScrollableScrollPhysics(),
          shrinkWrap: true,
          itemBuilder: (context, index) {
            final item = state.priceOrder?.promotions?[index];

            return _buildItemPriceInfo(
              title: "CTKM: ${item?.name ?? ''}",
              value: StringUtils.formatMoney(
                item?.discount ?? 0,
              ),
            );
          },
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: 6,
          ),
          child: Row(
            children: <Widget>[
              Expanded(
                child: Text(
                  "Thành tiền:",
                  style: UITextStyle.body2Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Text(
                StringUtils.formatMoney(state.priceOrder?.totalOrder ?? 0),
                style: UITextStyle.body1SemiBold.copyWith(
                  color: BaseColors.primary,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildBillInfo() {
    var state = ref.watch(createProductOrderProvider);
    bool enableUserName = true;

    if (userInfo?.customerType == UserType.personal.keyToServer) {
      enableUserName = false;
      orderUserNameController.text = userInfo?.fullName ?? '';
      orderPhoneNumberController.text = userInfo?.phoneNumber ?? '';
    } else {
      orderPhoneNumberController.text = userInfo?.phoneNumber ?? '';
      orderTaxCompany.text = userInfo?.customerId ?? '';
      companyName.text = userInfo?.fullName ?? '';
    }

    orderAddressController.text = userInfo?.address ?? '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Padding(
          padding: const EdgeInsets.fromLTRB(16, 8, 8, 8),
          child: Row(
            children: <Widget>[
              Expanded(
                child: Text(
                  "Hóa đơn",
                  style: UITextStyle.body1SemiBold.copyWith(
                    color: BaseColors.textTitle,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              InkWellWidget(
                onTap: () {
                  ref
                      .read(createProductOrderProvider.notifier)
                      .changeGetBillStatus();
                },
                child: Row(
                  children: <Widget>[
                    Padding(
                      padding: const EdgeInsets.all(8),
                      child: state.isGetBill
                          ? MyAssets.icons.iconChecked.svg()
                          : MyAssets.icons.iconCheckbox.svg(),
                    ),
                    Text(
                      "Lấy hóa đơn",
                      style: UITextStyle.body2Regular,
                    ),
                    const SizedBox(width: 8),
                  ],
                ),
              ),
            ],
          ),
        ),
        if (state.isGetBill) ...[
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
            ),
            child: Form(
              key: billFormKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  TextFieldWidget(
                    controller: orderTaxCompany,
                    labelText: "Mã số thuế doanh nghiệp",
                    enabled: false,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: companyName,
                    labelText: "Tên doanh nghiệp",
                    enabled: false,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: emailController,
                    labelText: "Email",
                    isRequired: true,
                    textInputAction: TextInputAction.done,
                    validator: (value) {
                      return ValidateUtils.onValidateEmail(value);
                    },
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderUserNameController,
                    labelText: "Họ và tên",
                    textInputAction: TextInputAction.done,
                    textCapitalization: TextCapitalization.words,
                    enabled: enableUserName,
                    isRequired:
                        userInfo?.customerType == UserType.company.keyToServer,
                    validator: (value) {
                      return ValidateUtils.onValidateUserName(value);
                    },
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderPhoneNumberController,
                    labelText: "Số điện thoại",
                    enabled: false,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderPaymentTypeController,
                    labelText: "Hình thức thanh toán",
                    enabled: false,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderBankNameController,
                    labelText: "Ngân hàng",
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget(
                    controller: orderBankNumberController,
                    labelText: "Số tài khoản",
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget.area(
                    controller: orderAddressController,
                    hintText: "Địa chỉ",
                    enabled: false,
                    maxLines: 2,
                  ),
                  const SizedBox(height: 20),
                  TextFieldWidget.area(
                    controller: orderNoteController,
                    maxLines: 4,
                    maxLength: 2000,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(
                        RegExp(
                          Patterns.note,
                          multiLine: true,
                        ),
                      ),
                    ],
                    hintText: "Ghi chú",
                  ),
                  const SizedBox(height: 16),
                ],
              ),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildTeamContributeInfo() {
    var state = ref.watch(createProductOrderProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          Text(
            "Đội ngũ tham gia",
            style: UITextStyle.body1SemiBold,
          ),
          const SizedBox(height: 16),
          Text(
            "Người bán và tỉ lệ % hoa hồng",
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          Container(
            padding: const EdgeInsets.fromLTRB(12, 12, 12, 4),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: BaseColors.borderDefault,
              ),
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                ListView.separated(
                  itemCount:
                      ref.watch(createProductOrderProvider).sellers?.length ??
                          0,
                  shrinkWrap: true,
                  padding: EdgeInsets.zero,
                  physics: const NeverScrollableScrollPhysics(),
                  separatorBuilder: (_, __) {
                    return const Padding(
                      padding: EdgeInsets.symmetric(vertical: 8),
                      child: DividerWidget(),
                    );
                  },
                  itemBuilder: (context, index) {
                    final item =
                        ref.watch(createProductOrderProvider).sellers![index];
                    var percentController = TextEditingController(
                      text: "${item.percentBonus ?? 0}",
                    );

                    if (index == 0) {
                      return Row(
                        children: <Widget>[
                          Expanded(
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: <Widget>[
                                Row(
                                  children: [
                                    Text(
                                      item.fullName ?? '',
                                      style: UITextStyle.body1Medium.copyWith(
                                        color: BaseColors.textTitle,
                                      ),
                                    ),
                                    Text(
                                      ' (Người tạo)',
                                      style:
                                          UITextStyle.caption1Medium.copyWith(
                                        color: BaseColors.textSubtitle,
                                      ),
                                    ),
                                  ],
                                ),
                                const SizedBox(height: 2),
                                Text(
                                  item.username ?? '',
                                  style: UITextStyle.caption1Regular.copyWith(
                                    color: BaseColors.textSubtitle,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(
                            width: 42,
                            child: NormalTextField(
                              controller: percentController,
                              showClearIcon: false,
                              height: 30,
                              textInputAction: TextInputAction.done,
                              keyboardType: TextInputType.number,
                              borderColor: Colors.transparent,
                              borderRadius: BorderRadius.zero,
                              textAlign: TextAlign.end,
                              suffixText: "%",
                              maxLength: 3,
                              contentPadding: const EdgeInsets.only(top: 2),
                              inputFormatters: [
                                FilteringTextInputFormatter.allow(
                                  RegExp(r'[0-9]'),
                                ),
                              ],
                              onChanged: (value) {
                                item.percentBonus = int.tryParse(value) ?? 0;
                              },
                            ),
                          ),
                          HideButton(
                            child: InkWellWidget(
                              onTap: () {},
                              child: Padding(
                                padding: const EdgeInsets.all(2),
                                child: MyAssets.icons.iconEditS12.svg(),
                              ),
                            ),
                          ),
                        ],
                      );
                    }
                    return Row(
                      children: <Widget>[
                        InkWellWidget(
                          onTap: () {
                            ref
                                .read(createProductOrderProvider.notifier)
                                .deleteSeller(index);
                          },
                          child: MyAssets.icons.iconMinusRounedRed.svg(),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Text(
                                item.fullName ?? '',
                                style: UITextStyle.body1Regular,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                item.username ?? '',
                                style: UITextStyle.caption1Regular.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                            ],
                          ),
                        ),
                        SizedBox(
                          width: 38,
                          child: NormalTextField(
                            controller: percentController,
                            showClearIcon: false,
                            height: 30,
                            textInputAction: TextInputAction.done,
                            keyboardType: TextInputType.number,
                            borderColor: Colors.transparent,
                            borderRadius: BorderRadius.zero,
                            suffixText: "%",
                            maxLength: 3,
                            textAlign: TextAlign.end,
                            contentPadding: const EdgeInsets.only(top: 2),
                            inputFormatters: [
                              FilteringTextInputFormatter.allow(
                                RegExp(r'[0-9]'),
                              ),
                            ],
                            onChanged: (value) {
                              item.percentBonus = int.tryParse(value) ?? 0;
                            },
                          ),
                        ),
                        HideButton(
                          child: InkWellWidget(
                            onTap: () {},
                            child: Padding(
                              padding: const EdgeInsets.all(2),
                              child: MyAssets.icons.iconEditS12.svg(),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
                const SizedBox(height: 8),
              ],
            ),
          ),
          const SizedBox(height: 16),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: CardCustomWidget(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              titleWidget: state.staffPropose != null
                  ? Row(
                      children: [
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              Text(
                                "Người giới thiệu",
                                style: UITextStyle.caption1Regular.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                state.staffPropose?.fullName ?? '',
                                style: UITextStyle.body1Regular,
                              ),
                              const SizedBox(height: 2),
                              Text(
                                state.staffPropose?.username ?? '',
                                style: UITextStyle.caption1Regular.copyWith(
                                  color: BaseColors.textSubtitle,
                                ),
                              ),
                              const SizedBox(height: 4),
                            ],
                          ),
                        ),
                        Visibility(
                          visible: !state.disableInputForm,
                          child: InkWellWidget(
                            onTap: () {
                              ref
                                  .read(createProductOrderProvider.notifier)
                                  .deleteStaffPropose();
                            },
                            child: MyAssets.icons.iconCloseCircle.svg(),
                          ),
                        ),
                      ],
                    )
                  : Row(
                      children: <Widget>[
                        MyAssets.icons.iconSearchS20.svg(),
                        const SizedBox(width: 8),
                        Text(
                          "Người giới thiệu",
                          style: UITextStyle.body1Regular,
                        ),
                      ],
                    ),
              onPress: () {
                AppBottomSheet.showNormalBottomSheet(
                  context,
                  title: "Người giới thiệu",
                  height: MediaQuery.of(context).size.height * 0.95,
                  child: ChooseStaffPage(
                    onSelectStaff: (staff) {
                      ref.read(createProductOrderProvider.notifier).selectStaff(
                            staff: staff,
                            isUserPropose: true,
                          );
                    },
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          CardCustomWidget(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 16,
            ),
            titleWidget: state.staffImplementation != null
                ? Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: <Widget>[
                            Text(
                              "Nhân sự triển khai",
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              state.staffImplementation?.fullName ?? '',
                              style: UITextStyle.body1Regular,
                            ),
                            const SizedBox(height: 2),
                            Text(
                              state.staffImplementation?.username ?? '',
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                            const SizedBox(height: 4),
                          ],
                        ),
                      ),
                      InkWellWidget(
                        onTap: () {
                          ref
                              .read(createProductOrderProvider.notifier)
                              .deleteStaffImplementation();
                        },
                        child: MyAssets.icons.iconCloseCircle.svg(),
                      ),
                    ],
                  )
                : Row(
                    children: <Widget>[
                      MyAssets.icons.iconSearchS20.svg(),
                      const SizedBox(width: 8),
                      Text(
                        "Nhân sự triển khai",
                        style: UITextStyle.body1Regular,
                      ),
                    ],
                  ),
            onPress: () {
              String? schedule =
                  state.productOrderInfo?.shippingInfo?.startTime;
              AppBottomSheet.showNormalBottomSheet(
                context,
                title: "Nhân sự triển khai",
                height: MediaQuery.of(context).size.height * 0.95,
                child: ChooseStaffPage(
                  orderType: OrderType.smart,
                  workerType: WorkerType.delivery,
                  provinceCode:
                      state.productOrderInfo?.shippingInfo?.provinceCode,
                  wardCode:
                      state.productOrderInfo?.shippingInfo?.wardCode,
                  onSelectStaff: (staff) {
                    ref.read(createProductOrderProvider.notifier).selectStaff(
                          staff: staff,
                        );
                  },
                  scheduleTime: schedule != null
                      ? DateTime.parse(schedule)
                      : DateTime.now(),
                ),
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildItemPriceInfo({
    required String title,
    String? value,
    bool visible = true,
  }) {
    return Visibility(
      visible: visible,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: 6,
        ),
        child: Row(
          children: <Widget>[
            Expanded(
              child: Text(
                "$title:",
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
            ),
            const SizedBox(width: 8),
            Text(
              value ?? '',
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void onCreateOrder(BuildContext context) async {
    var state = ref.watch(createProductOrderProvider);

    if (state.isGetBill) {
      final form = billFormKey.currentState;
      if (!form!.validate()) {
        ref.read(createProductOrderProvider.notifier).reloadBillForm();
        return;
      }
    }

    int totalPercent = 0;
    for (int i = 0; i < state.sellers!.length; i++) {
      if ((state.sellers![i].percentBonus ?? 0) == 0) {
        AppDialog.showDialogCenter(
          context,
          message: "Vui lòng nhập % hoa hồng cho người bán",
          status: DialogStatus.error,
        );
        return;
      }
      totalPercent = totalPercent + (state.sellers![i].percentBonus ?? 0);
    }

    if (totalPercent != 100) {
      AppDialog.showDialogCenter(
        context,
        message: "Tổng % hoa hồng của người bán phải bằng 100%",
        status: DialogStatus.error,
      );
      return;
    }

    String paymentMethod = state.paymentSelected?.code ?? "";

    PaymentInfoParam payment = PaymentInfoParam(
      paymentMethod: paymentMethod,
      paymentType: state.paymentType.keyToServer,
      isGetBill: state.isGetBill,
      billInfo: state.isGetBill
          ? userInfo?.customerType == UserType.personal.keyToServer
              ? BillInfoParam(
                  email: emailController.text,
                  fullName: orderUserNameController.text,
                  phoneNumber: orderPhoneNumberController.text,
                  paymentType: orderPaymentTypeController.text,
                  bankName: orderBankNameController.text,
                  accountNumber: orderBankNumberController.text,
                  addressDetail: userInfo?.addressDetail,
                  wardCode: userInfo?.wardCode,
                  districtCode: userInfo?.districtCode,
                  provinceCode: userInfo?.provinceCode,
                  address: userInfo?.address,
                )
              : BillInfoParam(
                  taxCode: orderTaxCompany.text,
                  businessName: companyName.text,
                  email: emailController.text,
                  fullName: orderUserNameController.text,
                  phoneNumber: orderPhoneNumberController.text,
                  paymentType: orderPaymentTypeController.text,
                  bankName: orderBankNameController.text,
                  accountNumber: orderBankNumberController.text,
                  addressDetail: userInfo?.addressDetail,
                  wardCode: userInfo?.wardCode,
                  districtCode: userInfo?.districtCode,
                  provinceCode: userInfo?.provinceCode,
                  address: userInfo?.address,
                )
          : null,
    );

    var processUserInfo = ProcessUserInfoParam(
      togetherUserInfo: state.sellers!
          .map(
            (e) => StaffInfoParam(
              userCode: e.username,
              roseRate: e.percentBonus,
            ),
          )
          .toList(),
      inviteUserCode: state.staffPropose?.username,
      processUserCode: state.staffImplementation?.username,
    );

    OrderParam param =
        ref.watch(createProductOrderProvider).productOrderInfo!.copyWith(
              paymentInfo: payment,
              processUserInfo: processUserInfo,
              associationCode: state.associationInfo?.code,
            );

    final result =
        await ref.read(createProductOrderProvider.notifier).createOrder(param);

    if (!context.mounted) return;
    if (result ?? false) {
      context.push(
        RouterPaths.orderSuccess,
        extra: OrderSuccessArguments(
          orderInfo: ref.watch(createProductOrderProvider).orderResponse!,
          paymentType: ref.watch(createProductOrderProvider).paymentType,
          paymentProvider: paymentMethod,
          onePayInfo: ref.watch(createProductOrderProvider).onePayInfo,
          viettelPayInfo: ref.watch(createProductOrderProvider).viettelPayInfo,
          orderType: OrderType.smart,
        ),
      );
    } else {
      AppDialog.showDialogCenter(
        context,
        message: ref.watch(createProductOrderProvider).message ??
            "Đã có lỗi xảy ra!",
        status: DialogStatus.error,
      );
    }
  }
}
