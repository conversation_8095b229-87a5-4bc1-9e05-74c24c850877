import 'dart:async';

import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/entities/address_info.dart';
import 'package:vcc/domain/entities/association/association_entity.dart';
import 'package:vcc/domain/entities/collection_info_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/entities/order/user_rate_entity.dart';
import 'package:vcc/domain/enums/gender_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/product_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_product_order/create_product_order_view_model.dart';
import 'package:vcc/presentation/views/features/order/create_order/create_service_order/select_date_service/select_date_service_view.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/cus360_widget.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/input_formatter_utils.dart';
import 'package:vcc/utils/log_utils.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/utils/validate_utils.dart';

class ProductCustomerInfoPage extends StatefulHookConsumerWidget {
  final CollectionInfoEntity? collectionInfo;
  final AssociationEntity? associationInfo;
  final Function(OrderParam productOrderInfo)? onNextPageCallBack;

  const ProductCustomerInfoPage({
    super.key,
    this.onNextPageCallBack,
    this.collectionInfo,
    this.associationInfo,
  });

  @override
  ConsumerState<ProductCustomerInfoPage> createState() =>
      _ProductCustomerInfoPageState();
}

class _ProductCustomerInfoPageState
    extends ConsumerState<ProductCustomerInfoPage>
    with AutomaticKeepAliveClientMixin {
  late TextEditingController userPhoneNumber;
  late TextEditingController userName;

  late TextEditingController userPhoneNumberSetup;
  late TextEditingController userNameSetup;

  late TextEditingController taxCompany;
  late TextEditingController companyPhoneNumber;
  late TextEditingController companyName;
  late TextEditingController companyUserName;
  late TextEditingController companyUserPhoneNumber;

  late TextEditingController companyUserNameSetup;
  late TextEditingController companyUserPhoneNumberSetup;

  late FocusNode userPhoneFocusNode;
  late FocusNode taxFocusNode;
  late FocusNode phoneNumberCompanyFocusNode;

  late TextEditingController noteController;

  final formUserKey = GlobalKey<FormState>();
  final formCompanyKey = GlobalKey<FormState>();

  final formUserSetupKey = GlobalKey<FormState>();
  final formCompanySetupKey = GlobalKey<FormState>();

  late StreamSubscription _showAlertSubscription;

  @override
  void initState() {
    userPhoneNumber = TextEditingController();
    userName = TextEditingController();

    userPhoneNumberSetup = TextEditingController();
    userNameSetup = TextEditingController();

    taxCompany = TextEditingController();
    companyName = TextEditingController();
    companyPhoneNumber = TextEditingController();
    companyUserName = TextEditingController();
    companyUserPhoneNumber = TextEditingController();

    companyUserNameSetup = TextEditingController();
    companyUserPhoneNumberSetup = TextEditingController();

    noteController = TextEditingController();

    userPhoneFocusNode = FocusNode();
    taxFocusNode = FocusNode();
    phoneNumberCompanyFocusNode = FocusNode();

    Future(() {
      if (ref.watch(createProductOrderProvider).draftOrder?.id != null) {
        initDataFromDraftOrder();
      }

      if (widget.collectionInfo != null) {
        ref
            .read(createProductOrderProvider.notifier)
            .fillDataFromCollectionInfo(widget.collectionInfo!);

        var state = ref.watch(createProductOrderProvider);
        var item = widget.collectionInfo!;

        if (state.userType == UserType.personal) {
          userName.text = item.customerName ?? "";
          userPhoneNumber.text = item.customerPhone ?? "";

          userNameSetup.text = item.customerName ?? "";
          userPhoneNumberSetup.text = item.customerPhone ?? "";
        } else {
          taxCompany.text = item.companyTaxCode ?? "";
          companyName.text = item.companyName ?? "";
          companyPhoneNumber.text = item.companyPhoneNumber ?? "";

          companyUserName.text = item.customerName ?? "";
          companyUserPhoneNumber.text = item.customerPhone ?? "";
          companyUserNameSetup.text = item.customerName ?? "";
          companyUserPhoneNumberSetup.text = item.customerPhone ?? "";
        }
      }

      if (widget.associationInfo != null) {
        ref.read(createProductOrderProvider.notifier).fillDataFromAssociation(
              widget.associationInfo!,
            );

        var state = ref.watch(createProductOrderProvider);
        var item = widget.associationInfo!;

        if (state.userType == UserType.personal) {
          userName.text = item.customerName ?? "";
          userPhoneNumber.text = item.customerPhone ?? "";

          userNameSetup.text = item.customerName ?? "";
          userPhoneNumberSetup.text = item.customerPhone ?? "";
        } else {
          taxCompany.text = item.companyTaxCode ?? "";
          companyName.text = item.companyName ?? "";
          companyPhoneNumber.text = item.companyPhone ?? "";

          companyUserName.text = item.customerName ?? "";
          companyUserPhoneNumber.text = item.customerPhone ?? "";
          companyUserNameSetup.text = item.customerName ?? "";
          companyUserPhoneNumberSetup.text = item.customerPhone ?? "";
        }
      }
    });

    _showAlertSubscription = ref
        .read(createProductOrderProvider.notifier)
        .alertController
        .stream
        .listen((data) {
      validateAddress(
        phoneNumber: userPhoneNumber.text,
        address: data[0],
        totalPrice: data[1],
      );
    });
    super.initState();
  }

  @override
  void dispose() {
    userPhoneNumber.dispose();
    userName.dispose();
    userPhoneNumberSetup.dispose();
    userNameSetup.dispose();
    taxCompany.dispose();
    companyName.dispose();
    companyPhoneNumber.dispose();
    companyUserName.dispose();
    companyUserPhoneNumber.dispose();

    companyUserNameSetup.dispose();
    companyUserPhoneNumberSetup.dispose();

    noteController.dispose();

    userPhoneFocusNode.dispose();
    taxFocusNode.dispose();
    phoneNumberCompanyFocusNode.dispose();

    _showAlertSubscription.cancel();
    super.dispose();
  }

  @override
  bool get wantKeepAlive => true;

  @override
  Widget build(BuildContext context) {
    super.build(context);
    var state = ref.watch(createProductOrderProvider);

    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      bottomNavigationBar: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: BaseButton(
            text: "Tiếp tục",
            onTap: onSaveInfo,
          ),
        ),
      ),
      body: Stack(
        children: [
          SingleChildScrollView(
            child: InkWell(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
              onTap: () {
                FocusScope.of(context).requestFocus(FocusNode());
              },
              child: Container(
                color: BaseColors.backgroundWhite,
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: <Widget>[
                    _buildCustomerInfo(),
                    DividerWidget(
                      height: 8,
                      color: BaseColors.backgroundGray,
                    ),
                    _buildSetupInfo(),
                  ],
                ),
              ),
            ),
          ),
          if (state.checkStaffStatus == LoadStatus.loading ||
              state.loadCustomerStatus == LoadStatus.loading) ...[
            const Center(
              child: LoadingIndicatorWidget(),
            )
          ],
        ],
      ),
    );
  }

  Widget _buildCustomerInfo() {
    var state = ref.watch(createProductOrderProvider);

    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 0, 16, 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            "Thông tin khách hàng",
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Loại khách hàng ',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
          ),
          const SizedBox(height: 12),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: Row(
              children: <Widget>[
                Expanded(
                  child: RadioWidget<UserType>(
                    value: UserType.personal,
                    groupValue: state.userType,
                    onChanged: (value) {
                      ref
                          .read(createProductOrderProvider.notifier)
                          .changeUserType(value);
                    },
                    displayWidget: (context, item) {
                      return _displayUserText(item);
                    },
                  ),
                ),
                Expanded(
                  child: RadioWidget<UserType>(
                    value: UserType.company,
                    groupValue: state.userType,
                    onChanged: (value) {
                      ref
                          .read(createProductOrderProvider.notifier)
                          .changeUserType(value);
                    },
                    displayWidget: (context, item) {
                      return _displayUserText(item);
                    },
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(height: 20),
          state.userType == UserType.personal
              ? _buildUserInfo(context)
              : _buildCompanyInfo(),
        ],
      ),
    );
  }

  Widget _buildSetupInfo() {
    var state = ref.watch(createProductOrderProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: <Widget>[
          Text(
            "Thông tin giao hàng",
            style: UITextStyle.body1SemiBold.copyWith(
              color: BaseColors.textTitle,
            ),
          ),
          state.userType == UserType.personal
              ? _buildUserInfoSetup()
              : _buildCompanyUserInfoSetup(),
        ],
      ),
    );
  }

  Widget _buildUserInfo(BuildContext context) {
    var state = ref.watch(createProductOrderProvider);

    return AbsorbPointer(
      absorbing: state.disableInputForm,
      child: Form(
        key: formUserKey,
        autovalidateMode: state.autoValidateUserForm,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            TextFieldWidget(
              controller: userPhoneNumber,
              focusNode: userPhoneFocusNode,
              isRequired: true,
              labelText: "Số điện thoại",
              maxLength: 11,
              keyboardType: TextInputType.phone,
              textInputAction: TextInputAction.done,
              maxSizeSuffix: true,
              onChanged: (value) {
                userPhoneNumberSetup.text = value;
              },
              suffix: !state.turnOnEditUserInfo
                  ? Cus360Widget(
                      customerId: state.customerInfo?.customerId,
                      customerPhone: userPhoneNumber.text,
                      customerType: UserType.personal.keyToServer,
                    )
                  : null,
              validator: (value) {
                return ValidateUtils.onValidatePhone(value);
              },
              onFocusChange: (isFocus) async {
                if (!isFocus) {
                  if (userPhoneNumber.text.validatePhone()) {
                    final result = await ref
                        .read(createProductOrderProvider.notifier)
                        .checkCommissionConfirm(
                          phoneNumber: userPhoneNumber.text,
                        );

                    if (result != null) {
                      if (result.isShowAlert ?? false) {
                        if (!context.mounted) return;
                        await AppDialog.showDialogConfirm(
                          context,
                          barrierDismissible: false,
                          title: result.title ?? '',
                          widgetContent: _buildContentCommissionConfirm(
                            message: result.message,
                            userRates: result.userRates,
                          ),
                          onConfirmAction: () {
                            checkStaff(
                              context,
                              phoneNumber: userPhoneNumber.text,
                            );
                          },
                          onCancelAction: () {
                            userPhoneNumber.text = '';
                            userPhoneNumberSetup.text = '';
                            ref
                                .read(createProductOrderProvider.notifier)
                                .reloadPage();
                          },
                        );
                      } else {
                        if (!context.mounted) return;
                        checkStaff(
                          context,
                          phoneNumber: userPhoneNumber.text,
                        );
                      }
                    }
                  } else {
                    ref
                        .read(createProductOrderProvider.notifier)
                        .enableEditUserForm();
                  }
                }
              },
            ),
            if (state.isInternalStaff ?? false) ...[
              const SizedBox(height: 4),
              const Text(
                "Nhân viên nội bộ VCC sẽ hưởng chính sách hoa hồng khác",
                style: BaseStyle.captionLarge,
              ),
            ],
            const SizedBox(height: 16),
            TextFieldWidget(
              controller: userName,
              isRequired: true,
              labelText: "Họ và tên",
              enabled: state.turnOnEditUserInfo,
              textInputAction: TextInputAction.done,
              textCapitalization: TextCapitalization.words,
              inputFormatters: [
                SpaceInputFormatter(),
                FilteringTextInputFormatter.allow(
                  RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
                ),
              ],
              onChanged: (value) {
                userNameSetup.text = value;
              },
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  userName.text = StringUtils.capitalizeEachWord(
                    userName.text,
                  );

                  userNameSetup.text = StringUtils.capitalizeEachWord(
                    userName.text,
                  );
                }
              },
              validator: (value) {
                return ValidateUtils.onValidateUserName(value);
              },
            ),
            const SizedBox(height: 16),
            DropdownWidget(
              labelText: "Địa chỉ",
              isRequired: true,
              content: state.customerAddress?.getFullAddress,
              suffix: MyAssets.icons.iconArrowRightS20.svg(),
              validator: (value) {
                return ValidateUtils.onValidateAddress(value);
              },
              onTap: () {
                onSelectAddress(
                  addressInfo: state.customerAddress,
                );
              },
            ),
            const SizedBox(height: 16),
            Text(
              'Giới tính',
              style: UITextStyle.body2Medium.copyWith(
                color: BaseColors.textLabel,
              ),
            ),
            const SizedBox(height: 12),
            Row(
              children: <Widget>[
                Expanded(
                  child: RadioWidget<GenderType?>(
                    value: GenderType.male,
                    groupValue: state.genderType,
                    onChanged: (value) {
                      ref
                          .read(createProductOrderProvider.notifier)
                          .changeGenderType(value!);
                    },
                    displayWidget: (context, item) {
                      return _displayGenderText(item!);
                    },
                  ),
                ),
                Expanded(
                  child: RadioWidget<GenderType?>(
                    value: GenderType.female,
                    groupValue: state.genderType,
                    onChanged: (value) {
                      ref
                          .read(createProductOrderProvider.notifier)
                          .changeGenderType(value!);
                    },
                    displayWidget: (context, item) {
                      return _displayGenderText(item!);
                    },
                  ),
                ),
                Expanded(
                  child: RadioWidget<GenderType?>(
                    value: GenderType.different,
                    groupValue: state.genderType,
                    onChanged: (value) {
                      ref
                          .read(createProductOrderProvider.notifier)
                          .changeGenderType(value!);
                    },
                    displayWidget: (context, item) {
                      return _displayGenderText(item!);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildCompanyInfo() {
    var state = ref.watch(createProductOrderProvider);
    const key = ValueKey("company");

    return Form(
      key: formCompanyKey,
      autovalidateMode: state.autoValidateCompanyForm,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: TextFieldWidget(
              controller: taxCompany,
              focusNode: taxFocusNode,
              isRequired: true,
              labelText: "Mã số thuế doanh nghiệp",
              maxLength: 14,
              textInputAction: TextInputAction.done,
              maxSizeSuffix: true,
              suffix: !state.turnOnEditCompanyInfo
                  ? Cus360Widget(
                      customerId: state.companyInfo?.customerId,
                      taxCode: taxCompany.text,
                      customerType: UserType.company.keyToServer,
                    )
                  : null,
              validator: (value) {
                return ValidateUtils.onValidateTax(value);
              },
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  getCompanyInfo360(
                    taxCode: taxCompany.text,
                  );
                }
              },
              keyboardType: const TextInputType.numberWithOptions(
                signed: true,
                decimal: false,
              ),
              inputFormatters: [
                TaxCodeInputFormatter(),
              ],
            ),
          ),
          const SizedBox(height: 16),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: TextFieldWidget(
              controller: companyName,
              labelText: "Tên doanh nghiệp",
              isRequired: true,
              enabled: state.turnOnEditCompanyInfo,
              textInputAction: TextInputAction.done,
              inputFormatters: [
                SpaceInputFormatter(),
              ],
              validator: (value) {
                return ValidateUtils.onValidateCompanyName(value);
              },
            ),
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyPhoneNumber,
            isRequired: true,
            maxLength: 12,
            labelText: "Số điện thoại doanh nghiệp",
            keyboardType: TextInputType.phone,
            // enabled: state.turnOnEditCompanyInfo,
            textInputAction: TextInputAction.done,
            validator: (value) {
              return ValidateUtils.onValidateCompanyPhone(value);
            },
          ),
          const SizedBox(height: 16),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: DropdownWidget(
              labelText: "Địa chỉ doanh nghiệp",
              isRequired: true,
              content: state.companyAddress?.getFullAddress,
              suffix: MyAssets.icons.iconArrowRightS20.svg(),
              validator: (value) {
                return ValidateUtils.onValidateAddress(value);
              },
              onTap: () {
                onSelectAddress(
                  addressInfo: state.companyAddress,
                );
              },
            ),
          ),
          const SizedBox(height: 16),
          AbsorbPointer(
            absorbing: state.disableInputForm,
            child: TextFieldWidget(
              controller: companyUserPhoneNumber,
              focusNode: phoneNumberCompanyFocusNode,
              isRequired: true,
              labelText: "Số điện thoại người liên hệ",
              textInputAction: TextInputAction.done,
              keyboardType: TextInputType.phone,
              onChanged: (value) {
                companyUserPhoneNumberSetup.text = value;
              },
              validator: (value) {
                return ValidateUtils.onValidatePhone(value);
              },
              onFocusChange: (isFocus) {
                if (!isFocus) {
                  if (companyUserPhoneNumber.text.validatePhone()) {
                    checkStaffContactCompany(
                      context: context,
                      phoneNumber: companyUserPhoneNumber.text,
                    );
                  }
                }
              },
            ),
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyUserName,
            isRequired: true,
            labelText: "Họ và tên người liên hệ",
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
              ),
            ],
            onFocusChange: (isFocus) {
              if (!isFocus) {
                companyUserName.text = StringUtils.capitalizeEachWord(
                  companyUserName.text,
                );
                companyUserNameSetup.text = StringUtils.capitalizeEachWord(
                  companyUserName.text,
                );
              }
            },
            onChanged: (value) {
              companyUserNameSetup.text = value;
            },
            validator: (value) {
              return ValidateUtils.onValidateUserName(value);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildUserInfoSetup() {
    var state = ref.watch(createProductOrderProvider);

    return Form(
      key: formUserSetupKey,
      autovalidateMode: state.autoValidateUserForm,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: userPhoneNumberSetup,
            isRequired: true,
            labelText: "Số điện thoại",
            keyboardType: TextInputType.phone,
            maxLength: 11,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            textInputAction: TextInputAction.done,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: userNameSetup,
            isRequired: true,
            labelText: "Họ và tên",
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
              ),
            ],
            onFocusChange: (isFocus) {
              if (!isFocus) {
                userNameSetup.text = StringUtils.capitalizeEachWord(
                  userNameSetup.text,
                );
              }
            },
            validator: (value) {
              return ValidateUtils.onValidateUserName(value);
            },
          ),
          const SizedBox(height: 16),
          DropdownWidget(
            labelText: "Địa chỉ",
            isRequired: true,
            content: state.customerSetupAddress?.getFullAddress,
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            validator: (value) {
              return ValidateUtils.onValidateAddress(value);
            },
            onTap: () {
              onSelectAddress(
                isSetup: true,
                addressInfo: state.customerSetupAddress,
              );
            },
          ),
          const SizedBox(height: 16),
          _buildTimeAndNoteSetup(),
        ],
      ),
    );
  }

  Widget _buildCompanyUserInfoSetup() {
    var state = ref.watch(createProductOrderProvider);
    const key = ValueKey("company_user_info_setup");

    return Form(
      key: formCompanySetupKey,
      autovalidateMode: state.autoValidateCompanyForm,
      child: Column(
        key: key,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: <Widget>[
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyUserPhoneNumberSetup,
            isRequired: true,
            labelText: "Số điện thoại",
            textInputAction: TextInputAction.done,
            keyboardType: TextInputType.phone,
            validator: (value) {
              return ValidateUtils.onValidatePhone(value);
            },
          ),
          const SizedBox(height: 16),
          TextFieldWidget(
            controller: companyUserNameSetup,
            isRequired: true,
            labelText: "Họ và tên",
            textInputAction: TextInputAction.done,
            textCapitalization: TextCapitalization.words,
            inputFormatters: [
              SpaceInputFormatter(),
              FilteringTextInputFormatter.allow(
                RegExp(r'[a-zA-Zà-ỹÀ-ỹ ]'),
              ),
            ],
            onFocusChange: (isFocus) {
              if (!isFocus) {
                companyUserNameSetup.text = StringUtils.capitalizeEachWord(
                  companyUserNameSetup.text,
                );
              }
            },
            validator: (value) {
              return ValidateUtils.onValidateUserName(value);
            },
          ),
          const SizedBox(height: 16),
          DropdownWidget(
            labelText: "Địa chỉ giao hàng",
            isRequired: true,
            content: state.companySetupAddress?.getFullAddress,
            suffix: MyAssets.icons.iconArrowRightS20.svg(),
            validator: (value) {
              return ValidateUtils.onValidateAddress(value);
            },
            onTap: () async {
              onSelectAddress(
                isSetup: true,
                addressInfo: state.companySetupAddress,
              );
            },
          ),
          const SizedBox(height: 16),
          _buildTimeAndNoteSetup(),
        ],
      ),
    );
  }

  Widget _displayGenderText(GenderType gender) {
    return Text(
      gender.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _displayUserText(UserType user) {
    return Text(
      user.display,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }

  Widget _buildTimeAndNoteSetup() {
    var state = ref.watch(createProductOrderProvider);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: <Widget>[
        RichText(
          text: TextSpan(
            text: 'Thời gian giao hàng',
            style: UITextStyle.body2Medium.copyWith(
              color: BaseColors.textLabel,
            ),
            children: <TextSpan>[
              TextSpan(
                text: ' *',
                style: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.primary,
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 8),
        DropdownWidget(
          labelText: "Chọn thời gian",
          content: state.userType == UserType.personal
              ? state.userTimeSetup?.getTime
              : state.companyTimeSetup?.getTime,
          suffix: MyAssets.icons.iconCalendarS24.svg(),
          validator: (value) {
            if ((value ?? "").isEmpty) {
              return "Vui lòng chọn Thời gian giao hàng";
            }
            return null;
          },
          onTap: () async {
            FocusScope.of(context).requestFocus(FocusNode());

            String? provinceCode = state.userType == UserType.personal
                ? state.customerAddress?.province?.code
                : state.companyAddress?.province?.code;
            provinceCode ??= GlobalData.instance.addressDefault?.province?.code;

            String? districtCode = state.userType == UserType.personal
                ? state.customerAddress?.district?.code
                : state.companyAddress?.district?.code;
            districtCode ??= GlobalData.instance.addressDefault?.district?.code;

            String? wardCode = state.userType == UserType.personal
                ? state.customerAddress?.ward?.code
                : state.companyAddress?.ward?.code;
            wardCode ??= GlobalData.instance.addressDefault?.ward?.code;

            AppBottomSheet.showNormalBottomSheet(
              context,
              title: "Thời gian giao hàng",
              isFlexible: true,
              child: SelectDateServiceView(
                content: "Vui lòng chọn khung giờ mong muốn",
                provinceCode: provinceCode,
                districtCode: districtCode,
                wardCode: wardCode,
                dateSelected: state.userType == UserType.personal
                    ? state.userTimeSetup
                    : state.companyTimeSetup,
                isCheckSchedule: false,
                onSelectDateCallback: (date) async {
                  if (kDebugMode) {
                    LogUtils.d('confirm $date');
                  }
                  ref.read(createProductOrderProvider.notifier).setUpTime(date);
                },
              ),
            );
          },
        ),
        const SizedBox(height: 20),
        TextFieldWidget.area(
          controller: noteController,
          maxLines: 4,
          maxLength: 2000,
          hintText: "Ghi chú",
          inputFormatters: [
            FilteringTextInputFormatter.allow(
              RegExp(
                Patterns.note,
                multiLine: true,
              ),
            ),
          ],
        ),
      ],
    );
  }

  void onSelectAddress({
    bool? isSetup,
    AddressInfo? addressInfo,
  }) async {
    AddressEntity? address;
    if (addressInfo != null) {
      address = AddressEntity(
        addressDetail: addressInfo.addressDetail,
        ward: addressInfo.ward,
        district: addressInfo.district,
        province: addressInfo.province,
        isGetFromLocal: addressInfo.isGetFromLocal,
      );
    }

    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        addressSelected: address,
        onSelectAddress: (address) {
          validateAddress(
            address: address.convertToAddressInfo,
            isSetup: isSetup,
          );
        },
      ),
    );
  }

  void checkStaff(
    BuildContext context, {
    String? phoneNumber,
  }) async {
    final isStaff =
        await ref.read(createProductOrderProvider.notifier).checkInternalStaff(
              phoneNumber: phoneNumber,
            );

    if (isStaff ?? false) {
      if (!context.mounted) return;
      AppDialog.showDialogConfirm(
        context,
        title: "Xác nhận",
        message:
            "Số điện thoại của nhân viên nội bộ, bạn có chắc chắn tạo đơn hàng không?",
        buttonNameConfirm: "Tạo đơn",
        onConfirmAction: () {
          getCustomerInfo360(
            phoneNumber: phoneNumber,
            isInternalStaff: true,
            onSuccess: (customerInfo) {
              userName.text = customerInfo.fullName ?? '';
              userNameSetup.text = customerInfo.fullName ?? '';
              userPhoneNumberSetup.text = customerInfo.phone ?? '';
            },
            onFailure: () {
              ref
                  .read(createProductOrderProvider.notifier)
                  .enableEditUserForm();
            },
          );
        },
        onCancelAction: () {
          userPhoneNumber.clear();
          userPhoneNumberSetup.clear();
        },
      );
    } else {
      getCustomerInfo360(
        phoneNumber: phoneNumber,
        onSuccess: (customerInfo) {
          userName.text = customerInfo.fullName ?? '';
          userNameSetup.text = customerInfo.fullName ?? '';
          userPhoneNumberSetup.text = customerInfo.phone ?? '';
        },
        onFailure: () {
          ref.read(createProductOrderProvider.notifier).enableEditUserForm();
        },
      );
    }
  }

  void checkStaffContactCompany({
    String? phoneNumber,
    required BuildContext context,
  }) async {
    final isStaff =
        await ref.read(createProductOrderProvider.notifier).checkInternalStaff(
              phoneNumber: phoneNumber,
            );

    if (isStaff ?? false) {
      if (!context.mounted) return;
      AppDialog.showDialogConfirm(
        context,
        title: "Xác nhận",
        message:
            "Số điện thoại của nhân viên nội bộ, bạn có chắc chắn tạo đơn hàng không?",
        buttonNameConfirm: "Tạo đơn",
        onConfirmAction: () {
          getCustomerInfo360(
            phoneNumber: phoneNumber,
            isInternalStaff: true,
            checkPrice: false,
            onSuccess: (customerInfo) {
              companyUserName.text = customerInfo.fullName ?? '';
              companyUserNameSetup.text = customerInfo.fullName ?? '';
            },
            onFailure: () {
              ref
                  .read(createProductOrderProvider.notifier)
                  .enableEditCompanyForm();
            },
          );
        },
        onCancelAction: () {
          companyUserPhoneNumber.clear();
          companyUserPhoneNumberSetup.clear();
        },
      );
    } else {
      getCustomerInfo360(
        phoneNumber: phoneNumber,
        checkPrice: false,
        onSuccess: (customerInfo) {
          companyUserName.text = customerInfo.fullName ?? '';
          companyUserNameSetup.text = customerInfo.fullName ?? '';
        },
        onFailure: () {
          ref.read(createProductOrderProvider.notifier).enableEditCompanyForm();
        },
      );
    }
  }

  Future<void> getCustomerInfo360({
    String? phoneNumber,
    bool? isInternalStaff,
    bool checkPrice = true,
    Function? onSuccess,
    Function? onFailure,
  }) async {
    final customerInfo =
        await ref.read(createProductOrderProvider.notifier).getCustomerInfo360(
              phoneNumber: phoneNumber,
              isInternalStaff: isInternalStaff ?? false,
              checkPrice: checkPrice,
            );

    if (customerInfo != null) {
      onSuccess?.call(customerInfo);
    }
  }

  void getCompanyInfo360({
    String? taxCode,
  }) async {
    if ((taxCode ?? '').validateTax()) {
      final companyInfo =
          await ref.read(createProductOrderProvider.notifier).getCompanyInfo360(
                taxCode: taxCode,
              );

      if (companyInfo != null) {
        companyName.text = companyInfo.fullName ?? '';
        companyPhoneNumber.text = companyInfo.phone ?? '';
      } else {
        companyName.text = "";
      }
    } else {
      ref.read(createProductOrderProvider.notifier).enableEditCompanyForm();
      companyName.text = "";
    }
  }

  void onSaveInfo() async {
    bool isPersonalType =
        ref.watch(createProductOrderProvider).userType == UserType.personal;

    if (isPersonalType) {
      final form = formUserKey.currentState;
      final formSetup = formUserSetupKey.currentState;

      if (form!.validate() && formSetup!.validate()) {
        form.save();
        onNextPage();
      } else {
        ref.read(createProductOrderProvider.notifier).onValidateUserForm();
      }
    } else {
      final form = formCompanyKey.currentState;
      final formSetup = formCompanySetupKey.currentState;

      if (form!.validate() && formSetup!.validate()) {
        form.save();
        onNextPage();
      } else {
        ref.read(createProductOrderProvider.notifier).onValidateCompanyForm();
      }
    }
  }

  void onNextPage() {
    final state = ref.watch(createProductOrderProvider);
    bool isPersonalType = state.userType == UserType.personal;
    final userInfo = isPersonalType
        ? CustomerInfoParam(
            customerType: UserType.personal.keyToServer,
            customerId: userPhoneNumber.text,
            phoneNumber: userPhoneNumber.text,
            fullName: userName.text,
            gender: state.genderType.keyToServer,
            address: state.customerAddress?.getFullAddress,
            wardCode: state.customerAddress?.ward?.code,
            districtCode: state.customerAddress?.district?.code,
            provinceCode: state.customerAddress?.province?.code,
            addressDetail: state.customerAddress?.addressDetail,
          )
        : CustomerInfoParam(
            customerType: state.userType.keyToServer,
            customerId: taxCompany.text,
            fullName: companyName.text,
            phoneNumber: companyPhoneNumber.text,
            contactName: companyUserName.text,
            contactNumber: companyUserPhoneNumber.text,
            address: state.companyAddress?.getFullAddress,
            wardCode: state.companyAddress?.ward?.code,
            districtCode: state.companyAddress?.district?.code,
            provinceCode: state.companyAddress?.province?.code,
            addressDetail: state.companyAddress?.addressDetail,
          );

    final shippingInfo = isPersonalType
        ? ShippingInfoParam(
            fullName: userNameSetup.text,
            phoneNumber: userPhoneNumberSetup.text,
            startTime: state.userTimeSetup?.getDateFrom.toIso8601String(),
            endTime: state.userTimeSetup?.getDateFrom.toIso8601String(),
            note: noteController.text,
            address: state.customerSetupAddress?.getFullAddress,
            wardCode: state.customerSetupAddress?.ward?.code,
            districtCode: state.customerSetupAddress?.district?.code,
            provinceCode: state.customerSetupAddress?.province?.code,
            addressDetail: state.customerSetupAddress?.addressDetail,
          )
        : ShippingInfoParam(
            fullName: companyUserNameSetup.text,
            phoneNumber: companyUserPhoneNumberSetup.text,
            startTime: state.companyTimeSetup?.getDateFrom.toIso8601String(),
            endTime: state.companyTimeSetup?.getDateFrom.toIso8601String(),
            note: noteController.text,
            address: state.companySetupAddress?.getFullAddress,
            wardCode: state.companySetupAddress?.ward?.code,
            districtCode: state.companySetupAddress?.district?.code,
            provinceCode: state.companySetupAddress?.province?.code,
            addressDetail: state.companySetupAddress?.addressDetail,
          );

    final productOrder = OrderParam(
      orderType: ProductType.product.keyToServer,
      userInfo: userInfo,
      shippingInfo: shippingInfo,
    );
    widget.onNextPageCallBack?.call(productOrder);
  }

  void initDataFromDraftOrder() {
    var state = ref.watch(createProductOrderProvider);
    ref.read(createProductOrderProvider.notifier).setDataFromDraftOrder();
    if (state.draftOrder?.userInfo?.customerType ==
        UserType.personal.keyToServer) {
      userPhoneNumber.text = state.draftOrder?.userInfo?.customerId ?? '';
      userName.text = state.draftOrder?.userInfo?.fullName ?? '';
      userPhoneNumberSetup.text =
          state.draftOrder?.shippingInfo?.phoneNumber ?? '';
      userNameSetup.text = state.draftOrder?.shippingInfo?.fullName ?? '';
      noteController.text = state.draftOrder?.shippingInfo?.note ?? '';
    } else {
      taxCompany.text = state.draftOrder?.userInfo?.customerId ?? '';
      companyName.text = state.draftOrder?.userInfo?.fullName ?? '';
      companyPhoneNumber.text = state.draftOrder?.userInfo?.phoneNumber ?? '';
      companyUserName.text = state.draftOrder?.userInfo?.fullName ?? '';
      companyUserPhoneNumber.text =
          state.draftOrder?.userInfo?.phoneNumber ?? '';

      companyUserNameSetup.text =
          state.draftOrder?.shippingInfo?.fullName ?? '';
      companyUserPhoneNumberSetup.text =
          state.draftOrder?.shippingInfo?.phoneNumber ?? '';
      noteController.text = state.draftOrder?.shippingInfo?.note ?? '';
    }
  }

  void validateAddress({
    required AddressInfo address,
    int? totalPrice,
    bool? isSetup,
    String? phoneNumber,
  }) async {
    var state = ref.watch(createProductOrderProvider);
    AddressInfo? addressSelected;

    if (state.userType == UserType.personal) {
      addressSelected = state.customerSetupAddress;
    } else {
      addressSelected = state.companySetupAddress;
    }

    if (addressSelected == null) {
      if (address.province?.code !=
              GlobalData.instance.addressDefault?.province?.code ||
          address.district?.code !=
              GlobalData.instance.addressDefault?.district?.code) {
        showAddressDifferentDefault(
          address: address,
          isSetup: isSetup ?? false,
        );
      } else {
        ref.read(createProductOrderProvider.notifier).selectAddress(
              address,
              isSetup: isSetup ?? false,
            );
      }
    } else {
      ///HiSmile bo sung logic check ma giam gia, check tai tho
      int? price;
      if (totalPrice != null) {
        price = totalPrice;
      } else {
        price =
            await ref.read(createProductOrderProvider.notifier).getPriceOrder(
                  phoneNumber: phoneNumber,
                  provinceCode: address.province?.code,
                  districtCode: address.district?.code,
                  wardCode: address.ward?.code,
                );
      }

      bool check = (price != state.totalAmount) ||
          (address.province?.code != addressSelected.province?.code ||
              address.district?.code != addressSelected.district?.code);

      if (check) {
        showDialogChangeAddress(
          address: address,
          price: price,
          isSetup: isSetup ?? false,
        );
      } else {
        ref.read(createProductOrderProvider.notifier).selectAddress(
              address,
              isSetup: isSetup ?? false,
            );
      }
    }
  }

  void showAddressDifferentDefault({
    AddressInfo? address,
    int? price,
    required bool isSetup,
  }) {
    AppDialog.showDialogConfirm(
      context,
      title: "Đồng ý",
      message:
          "Địa chỉ giao hàng khác với địa chỉ tham khảo. Giá, tồn kho và CTKM của sản phẩm có thể thay đổi.",
      onConfirmAction: () async {
        ref.read(createProductOrderProvider.notifier).selectAddress(
              address!,
              isSetup: isSetup,
            );
        ref
            .read(createProductOrderProvider.notifier)
            .updateListProduct(address: address);

        if (price != null) {
          ref
              .read(createProductOrderProvider.notifier)
              .changeTotalAmount(price);
        }
      },
    );
  }

  void showDialogChangeAddress({
    AddressInfo? address,
    int? price,
    required bool isSetup,
  }) {
    AppDialog.showDialogConfirm(
      context,
      title: "Xác nhận",
      message:
          "Thay đổi địa chỉ có thể thay đổi giá, tồn kho, CTKM của sản phẩm. Bạn xác nhận muốn thay đổi địa chỉ giao hàng?",
      onConfirmAction: () async {
        ref.read(createProductOrderProvider.notifier).selectAddress(
              address!,
              isSetup: isSetup,
            );
        ref
            .read(createProductOrderProvider.notifier)
            .updateListProduct(address: address);

        if (price != null) {
          ref
              .read(createProductOrderProvider.notifier)
              .changeTotalAmount(price);
        }
      },
    );
  }

  Widget _buildContentCommissionConfirm({
    String? message,
    List<UserRateEntity>? userRates,
  }) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: <Widget>[
        Text(
          message ?? "",
          style: UITextStyle.body2Regular.copyWith(
            color: BaseColors.textBody,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),
        if ((userRates ?? []).isNotEmpty) ...[
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: BaseColors.borderDefault,
              ),
            ),
            child: ListView.separated(
              itemCount: userRates?.length ?? 0,
              shrinkWrap: true,
              padding: EdgeInsets.zero,
              physics: const NeverScrollableScrollPhysics(),
              separatorBuilder: (_, __) {
                return const DividerWidget();
              },
              itemBuilder: (context, index) {
                final item = userRates?[index];
                return Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 10,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Text(
                          item?.title ?? '',
                          style: UITextStyle.body2Regular.copyWith(
                            color: BaseColors.textBody,
                          ),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Text(
                        "${item?.percent ?? 0}",
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ],
    );
  }
}
