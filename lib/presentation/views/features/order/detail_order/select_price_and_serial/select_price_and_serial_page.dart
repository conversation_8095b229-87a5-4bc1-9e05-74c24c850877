import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:pattern_formatter/pattern_formatter.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/entities/order/original_price_supply_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/stock_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/select_price_and_serial/select_price_and_serial_view_model.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/string_utils.dart';

class SelectPriceAndSerialArguments {
  final List<OriginalPriceSupplyEntity>? serialsSelected;
  final Function(List<OriginalPriceSupplyEntity> listData)? onSelected;
  final num? maxLength;
  final int? price;
  final bool? editPrice;
  final String supplyCode;
  final StockType? stockType;
  final OrderType? orderType;
  final String? orderCode;

  SelectPriceAndSerialArguments({
    this.serialsSelected,
    this.onSelected,
    this.maxLength,
    this.price,
    this.editPrice,
    required this.supplyCode,
    this.stockType,
    this.orderType,
    this.orderCode,
  });
}

class SelectPriceAndSerialPage extends StatefulHookConsumerWidget {
  final SelectPriceAndSerialArguments? arguments;

  const SelectPriceAndSerialPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<SelectPriceAndSerialPage> createState() =>
      _SelectPriceAndSerialState();
}

class _SelectPriceAndSerialState
    extends ConsumerState<SelectPriceAndSerialPage> {
  late TextEditingController priceController;
  late ScrollController controller;

  @override
  void initState() {
    priceController = TextEditingController(
      text: widget.arguments?.price != null
          ? StringUtils.displayMoney(widget.arguments!.price!)
          : '',
    );
    controller = ScrollController();
    controller.addListener(_scrollListener);

    Future(() {
      ref.read(selectPriceAndSerialProvider.notifier).initData(
            serials: widget.arguments?.serialsSelected ?? [],
            maxLength: widget.arguments?.maxLength,
            editPrice: widget.arguments?.editPrice,
            supplyCode: widget.arguments?.supplyCode ?? '',
            stockType: widget.arguments?.stockType,
            orderCode: widget.arguments?.orderCode,
          );
    });
    super.initState();
  }

  @override
  void dispose() {
    controller.dispose();
    priceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(selectPriceAndSerialProvider);

    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Chọn serial và giá vốn",
      ),
      bottomAction: (state.serialsSelected ?? []).isNotEmpty &&
              (state.serialsSelected?.length ?? 0) == state.maxLength
          ? Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 12,
              ),
              decoration: BoxDecoration(
                color: BaseColors.backgroundWhite,
                boxShadow: AppBoxShadows.shadowNormal,
              ),
              child: SafeArea(
                child: BaseButton(
                  text: "Xác nhận",
                  onTap: () {
                    widget.arguments?.onSelected?.call(
                      state.serialsSelected ?? [],
                    );
                    context.pop();
                  },
                ),
              ),
            )
          : const SizedBox(),
      body: Column(
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                TextFieldWidget(
                  controller: priceController,
                  labelText: 'Giá bán (VNĐ)',
                  hintText: 'VNĐ',
                  hintTextStyle: UITextStyle.body1Regular.copyWith(
                    color: BaseColors.textPlaceholder,
                  ),
                  enabled: state.editPrice ?? true,
                  textInputAction: TextInputAction.done,
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    ThousandsFormatter(
                      allowFraction: true,
                    ),
                  ],
                  onChanged: (value) {},
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    top: 4,
                    left: 16,
                  ),
                  child: Text(
                    "Giá bán MIN: ",
                    style: UITextStyle.body2Regular.copyWith(
                      color: BaseColors.textBody,
                    ),
                  ),
                ),
              ],
            ),
          ),
          DividerWidget(
            height: 4,
            color: BaseColors.backgroundGray,
          ),
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: <Widget>[
                RichText(
                  text: TextSpan(
                    text: "Serial và giá vốn đã chọn ",
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textSubtitle,
                    ),
                    children: <TextSpan>[
                      TextSpan(
                        text: ((state.maxLength ?? 0) > 0)
                            ? "${state.serialsSelected?.length ?? 0}/${state.maxLength ?? 0}"
                            : "",
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                    ],
                  ),
                ),
                AppTextButton(
                  title: "Thiết lập lại",
                  titleStyle: UITextStyle.caption1Medium.copyWith(
                    color: BaseColors.primary,
                  ),
                  onTap: () {
                    ref
                        .read(selectPriceAndSerialProvider.notifier)
                        .clearListSerial();
                  },
                ),
              ],
            ),
          ),
          Visibility(
            visible: (state.serialsSelected ?? []).isNotEmpty,
            child: SizedBox(
              width: double.infinity,
              child: Padding(
                padding: const EdgeInsets.only(
                  left: 16,
                  bottom: 16,
                ),
                child: Wrap(
                  direction: Axis.horizontal,
                  alignment: WrapAlignment.start,
                  crossAxisAlignment: WrapCrossAlignment.start,
                  spacing: 8,
                  runSpacing: 8,
                  children: (state.serialsSelected ?? []).map<Widget>(
                    (item) {
                      return Container(
                        padding: const EdgeInsets.fromLTRB(12, 4, 8, 4),
                        decoration: BoxDecoration(
                          color: BaseColors.backgroundGray,
                          borderRadius: BorderRadius.circular(4),
                        ),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              StringUtils.formatFlexibleDouble(item.price ?? 0),
                              overflow: TextOverflow.ellipsis,
                              style: UITextStyle.body2Regular,
                            ),
                            InkWellWidget(
                              onTap: () {
                                ref
                                    .read(selectPriceAndSerialProvider.notifier)
                                    .onSelectSerial(item);
                              },
                              child: Padding(
                                padding: const EdgeInsets.all(4),
                                child: MyAssets.icons.iconCloseBackS12.svg(),
                              ),
                            ),
                          ],
                        ),
                      );
                    },
                  ).toList(),
                ),
              ),
            ),
          ),
          DividerWidget(
            height: 4,
            color: BaseColors.backgroundGray,
          ),
          Expanded(
            child: _buildListSerial(),
          ),
        ],
      ),
    );
  }

  Future<void> refreshData() async {
    ref.read(selectPriceAndSerialProvider.notifier).getListSerial();
  }

  void _scrollListener() {
    final maxScroll = controller.position.maxScrollExtent;
    final currentScroll = controller.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(selectPriceAndSerialProvider.notifier).fetchNextData();
    }
  }

  Widget _buildListSerial() {
    final state = ref.watch(selectPriceAndSerialProvider);

    if (state.loadStatus == LoadStatus.loading) {
      return const LoadingIndicatorWidget();
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      if ((state.listSerial ?? []).isEmpty) {
        return EmptyListWidget(
          title: "Không có dữ liệu",
          onRefresh: refreshData,
        );
      } else {
        return RefreshIndicatorWidget(
          onRefresh: refreshData,
          child: ListView.builder(
            controller: controller,
            padding: EdgeInsets.zero,
            itemCount: state.listSerial?.length ?? 0,
            itemBuilder: (context, index) {
              final item = state.listSerial![index];
              bool isSelect = false;
              bool doneSelect = false;

              if (state.maxLength == null) {
                doneSelect = false;
              } else {
                if ((state.serialsSelected ?? []).length >=
                    (state.maxLength!)) {
                  doneSelect = true;
                } else {
                  doneSelect = false;
                }
              }

              if ((state.serialsSelected ?? []).isNotEmpty) {
                isSelect = state.serialsSelected!
                    .any((element) => element.serial == item.serial);
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  doneSelect && !isSelect
                      ? Stack(
                          children: [
                            Container(
                              padding: const EdgeInsets.all(16),
                              child: Row(
                                children: <Widget>[
                                  MyAssets.icons.iconCheckbox.svg(),
                                  const SizedBox(width: 16),
                                  Expanded(
                                    child: Text(
                                      item.serial ?? '',
                                      style: UITextStyle.body1Regular.copyWith(
                                        color: BaseColors.textBody,
                                      ),
                                    ),
                                  ),
                                  Text(
                                    StringUtils.formatFlexibleDouble(
                                        item.price ?? 0),
                                    style: UITextStyle.body1Regular.copyWith(
                                      color: BaseColors.textDisable,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Positioned.fill(
                              child: Container(
                                color:
                                    BaseColors.backgroundWhite.withOpacity(0.5),
                              ),
                            ),
                          ],
                        )
                      : InkWellWidget(
                          onTap: () {
                            ref
                                .read(selectPriceAndSerialProvider.notifier)
                                .onSelectSerial(item);
                          },
                          child: Padding(
                            padding: EdgeInsets.symmetric(
                              horizontal: 16,
                              vertical: isSelect ? 12 : 16,
                            ),
                            child: Row(
                              children: <Widget>[
                                isSelect
                                    ? MyAssets.icons.iconChecked.svg()
                                    : MyAssets.icons.iconCheckbox.svg(),
                                const SizedBox(width: 16),
                                Expanded(
                                  child: Text(
                                    item.serial ?? '',
                                    style: UITextStyle.body1Regular.copyWith(
                                      color: BaseColors.textBody,
                                    ),
                                  ),
                                ),
                                Text(
                                  StringUtils.formatFlexibleDouble(
                                      item.price ?? 0),
                                  style: UITextStyle.body1Regular.copyWith(
                                    color: BaseColors.textBody,
                                  ),
                                )
                              ],
                            ),
                          ),
                        ),
                  Padding(
                    padding: const EdgeInsets.only(
                      left: 16,
                    ),
                    child: DividerWidget(
                      height: 1,
                      color: BaseColors.backgroundGray,
                    ),
                  ),
                ],
              );
            },
          ),
        );
      }
    }
  }
}
