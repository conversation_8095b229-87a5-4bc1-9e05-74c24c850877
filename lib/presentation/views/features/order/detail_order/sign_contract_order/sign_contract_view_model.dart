import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/data/repositories/resource_repository.dart';
import 'package:vcc/data/repositories/user_profile_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/sent_otp_body.dart';
import 'package:vcc/domain/body/sign_item_warranty_body.dart';
import 'package:vcc/domain/entities/aio_contract/aio_invoice_response_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/params/aio_contract/aio_contract_digital_param.dart';

part 'sign_contract_state.dart';

final signContractProvider =
    StateNotifierProvider.autoDispose<SignContractViewModel, SignContractState>(
        (ref) => SignContractViewModel(ref: ref));

class SignContractViewModel extends StateNotifier<SignContractState> {
  final Ref ref;

  SignContractViewModel({required this.ref}) : super(const SignContractState());

  Future<void> getFileSign(String orderCode) async {
    state = state.copyWith(
      loadDataStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<OrderRepository>()
          .getFileContract(orderCode: orderCode);
      result?.when(
        success: (data) async {
          state = state.copyWith(
            base64: data.base64,
            loadDataStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadDataStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadDataStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getRequirementWarrantyFileSign(
    SignItemWarrantyBody signItemWarrantyBody,
  ) async {
    state = state.copyWith(
      loadDataStatus: LoadStatus.loading,
    );
    try {
      final result =
          await appLocator<OrderRepository>().getRequirementWarrantyFileSign(
        signItemWarrantyBody: signItemWarrantyBody,
      );
      result?.when(
        success: (data) async {
          state = state.copyWith(
            base64: data.base64,
            loadDataStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadDataStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadDataStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getDetailRequirementWarrantyFileSign(
    SignItemWarrantyBody signItemWarrantyBody,
  ) async {
    state = state.copyWith(
      loadDataStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<OrderRepository>()
          .getDetailRequirementWarrantyFileSign(
        signItemWarrantyBody: signItemWarrantyBody,
      );
      result?.when(
        success: (data) async {
          state = state.copyWith(
            base64: data.base64,
            loadDataStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadDataStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadDataStatus: LoadStatus.failure,
      );
    }
  }

  setLoadStatus(LoadStatus status) async {
    state = state.copyWith(
      loadDataStatus: status,
    );
  }

  Future<AioInvoiceResponseEntity?> findDigitalContractPath(
      AioContractDigitalParam? param) async {
    AioInvoiceResponseEntity? response;
    state = state.copyWith(
      loadDataStatus: LoadStatus.loading,
      message: null,
    );
    final result = await appLocator<AioOrderRepository>()
        .findDigitalContractPath(param: param);
    await result?.when(
      success: (data) async {
        response = data;
        state = state.copyWith(
          loadDataStatus: LoadStatus.success,
          digitalContractPath: response?.pathPdf ?? "",
        );
        if (data.message != null) {
          state = state.copyWith(
            loadDataStatus: LoadStatus.failure,
            digitalContractPath: "",
            message: data.message,
          );
        }
      },
      error: (err) async {
        state = state.copyWith(
          loadDataStatus: LoadStatus.failure,
          digitalContractPath: "",
          message: err.message,
        );
      },
    );
    return response;
  }

  setLoading(LoadStatus loading) {
    state = state.copyWith(
      loadDataStatus: loading,
    );
  }

  Future<void> sentOtp(String orderCode) async {
    state = state.copyWith(
      sentOtpStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<UserProfileRepository>()
          .sentOtp(orderCode: orderCode);
      result?.when(
        success: (data) async {
          state = state.copyWith(
            sentOtpStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            sentOtpStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        sentOtpStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> sentOtpRequirementWarranty(String orderCode) async {
    state = state.copyWith(
      sentOtpStatus: LoadStatus.loading,
    );
    try {
      final result =
          await appLocator<UserProfileRepository>().sentOtpRequirementWarranty(
        body: SentOtpBody(
          orderCode: orderCode,
        ),
      );
      result?.when(
        success: (data) async {
          state = state.copyWith(
            sentOtpStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            sentOtpStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        sentOtpStatus: LoadStatus.failure,
      );
    }
  }

  Future<List<String>> uploadImage(File file) async {
    state = state.copyWith(
      uploadImageStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<ResourceRepository>().uploadImage(
        file: file,
      );

      List<String> images = state.listImage ?? [];

      await result?.when(
        success: (data) async {
          images.addAll(data);

          state = state.copyWith(
            uploadImageStatus: LoadStatus.success,
            listImage: images,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            uploadImageStatus: LoadStatus.failure,
          );
        },
      );

      return images;
    } catch (error) {
      state = state.copyWith(
        uploadImageStatus: LoadStatus.failure,
      );
      return [];
    }
  }

  void deleteImage(String image) async {
    List<String> listImage = state.listImage ?? [];
    listImage.remove(image);

    state = state.copyWith(
      listImage: listImage,
    );
  }
}
