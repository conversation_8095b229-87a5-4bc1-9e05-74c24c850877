import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/domain/entities/order/detail_order_item_entity.dart';
import 'package:vcc/domain/enums/order_sub_status.dart';
import 'package:vcc/domain/enums/special_service_code.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/detail_order/checklist_service/checklist_service_page.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/plus_and_minus_widget.dart';
import 'package:vcc/utils/string_utils.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/app_configs/constants.dart';

class ServiceItemEdit extends StatelessWidget {
  final DetailOrderItemEntity service;
  final Function(num quantity) changeQuantity;
  final String? orderCode;
  final Function? onReloadData;
  final bool showCheckList;
  final OrderSubStatus? orderStatus;

  const ServiceItemEdit({
    required this.service,
    required this.changeQuantity,
    this.orderCode,
    this.onReloadData,
    this.showCheckList = false,
    this.orderStatus,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    bool disableChangeQuantity = [
      SpecialServiceCode.supportService.codeToServer,
      SpecialServiceCode.supportServiceUnlimited.codeToServer,
      SpecialServiceCode.hotService.codeToServer,
    ].contains(service.code);

    return Slidable(
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        extentRatio: 0.2,
        children: <Widget>[
          CustomSlidableAction(
            backgroundColor: BaseColors.primary,
            foregroundColor: Colors.yellow,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                MyAssets.icons.iconWhiteTrash.svg(),
                const SizedBox(height: 8),
                Text(
                  "Xoá",
                  style: UITextStyle.body2SemiBold.copyWith(
                    color: BaseColors.backgroundWhite,
                  ),
                ),
              ],
            ),
            onPressed: (context) {
              if (service.defaultService ?? false) {
                AppDialog.showDialogInfo(
                  context,
                  barrierDismissible: false,
                  title: "Thông báo",
                  message: "Dịch vụ mặc định không thể xóa",
                  buttonNameConfirm: "Đóng",
                  onConfirmAction: () {
                    changeQuantity(1);
                  },
                );
              } else {
                changeQuantity(0);
              }
            },
          )
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: <Widget>[
                Container(
                  height: 56,
                  width: 56,
                  decoration: BoxDecoration(
                    color: BaseColors.backgroundGray,
                  ),
                  child: ImageWidget(
                    service.thumbnailUrl ??
                        BaseConstant.imageDefaultServiceTest,
                    size: const Size(56, 56),
                  ),
                ),
                const SizedBox(width: 14),
                Expanded(
                  child: Column(
                    mainAxisSize: MainAxisSize.min,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: <Widget>[
                      Text(
                        service.name ?? '',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                      const SizedBox(height: 6),
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: <Widget>[
                          Expanded(
                            child: Padding(
                              padding: const EdgeInsets.symmetric(
                                vertical: 4,
                              ),
                              child: Column(
                                mainAxisSize: MainAxisSize.min,
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: <Widget>[
                                  Row(
                                    children: [
                                      Text(
                                        StringUtils.formatMoney(
                                          service.price ?? 0,
                                        ),
                                        style: UITextStyle.body2Medium.copyWith(
                                          color: BaseColors.primary,
                                        ),
                                      ),
                                      Text(
                                        "/${service.unit ?? ""}",
                                        style:
                                            UITextStyle.body2Regular.copyWith(
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                    ],
                                  ),
                                  if ((service.discount ?? 0) > 0) ...[
                                    const SizedBox(height: 2),
                                    Row(
                                      children: [
                                        Text(
                                          StringUtils.formatMoney(
                                            service.originalPrice ?? 0,
                                          ),
                                          style: UITextStyle.caption1Regular
                                              .copyWith(
                                            color: BaseColors.textSubtitle,
                                            decoration:
                                                TextDecoration.lineThrough,
                                          ),
                                        ),
                                        const SizedBox(width: 4),
                                        Container(
                                          height: 20,
                                          decoration: BoxDecoration(
                                            color: BaseColors.primarySurface,
                                            borderRadius:
                                                BorderRadius.circular(6),
                                          ),
                                          padding: const EdgeInsets.symmetric(
                                            vertical: 1,
                                            horizontal: 4,
                                          ),
                                          child: Center(
                                            child: Text(
                                              '-${service.discount?.toInt() ?? 0}%',
                                              style: UITextStyle.caption1Medium
                                                  .copyWith(
                                                color: BaseColors.primary,
                                              ),
                                            ),
                                          ),
                                        ),
                                      ],
                                    ),
                                  ],
                                ],
                              ),
                            ),
                          ),
                          PlusAndMinusWidget(
                            quantity: service.quantity ?? 1,
                            minQuantity: service.minItem,
                            maxQuantity: service.maxItem,
                            step: service.step,
                            dataType: service.valueType,
                            disableChangeQuantity: disableChangeQuantity,
                            onPlus: (value) {
                              changeQuantity(value);
                            },
                            onMinus: (value) {
                              if (value == 0) {
                                if (service.defaultService ?? false) {
                                  AppDialog.showDialogInfo(
                                    context,
                                    barrierDismissible: false,
                                    title: "Thông báo",
                                    message: "Dịch vụ mặc định không thể xóa",
                                    buttonNameConfirm: "Đóng",
                                    onConfirmAction: () {
                                      changeQuantity(1);
                                    },
                                  );
                                } else {
                                  showDialogRemoveService(
                                    context: context,
                                    serviceName: service.name ?? "",
                                  );
                                }
                              } else {
                                changeQuantity(value);
                              }
                            },
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          RichText(
                            text: TextSpan(
                              text: 'Thuế: ',
                              style: UITextStyle.caption1Medium.copyWith(
                                color: BaseColors.textBody,
                              ),
                              children: <TextSpan>[
                                TextSpan(
                                  text: "${service.vat ?? 0}%",
                                  style: UITextStyle.caption1SemiBold.copyWith(
                                    color: BaseColors.textBody,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          RichText(
                            text: TextSpan(
                              text: 'Thành tiền: ',
                              style: UITextStyle.caption1Medium.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                              children: <TextSpan>[
                                TextSpan(
                                  text: StringUtils.formatMoney(
                                    (service.quantity ?? 1) *
                                        (service.price ?? 0),
                                  ),
                                  style: UITextStyle.caption1SemiBold.copyWith(
                                    color: BaseColors.textLabel,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ],
            ),
            Visibility(
              visible: (service.isCheckList ?? false) && showCheckList,
              child: Padding(
                padding: const EdgeInsets.only(
                  top: 12,
                ),
                child: InkWellWidget(
                  onTap: () async {
                    await context.push(
                      RouterPaths.checklistService,
                      extra: ChecklistServiceArguments(
                        orderCode: orderCode ?? "",
                        service: service,
                        orderStatus: orderStatus,
                      ),
                    );
                    onReloadData?.call();
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 10,
                    ),
                    decoration: BoxDecoration(
                      color: BaseColors.backgroundGray,
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: BaseColors.borderDivider,
                      ),
                    ),
                    child: Row(
                      children: <Widget>[
                        (service.checkListInfo?.pass ?? 0) ==
                                (service.checkListInfo?.target ?? 0)
                            ? MyAssets.icons.iconCheckDoneEnable.svg()
                            : MyAssets.icons.iconCheckDoneDisable.svg(),
                        const SizedBox(width: 6),
                        Expanded(
                          child: Text(
                            "Checklist công việc",
                            style: UITextStyle.body2Regular.copyWith(
                              color: BaseColors.textLabel,
                            ),
                          ),
                        ),
                        Text(
                          "${service.checkListInfo?.pass ?? 0}/${service.checkListInfo?.target ?? 0}",
                          style: UITextStyle.body2Medium.copyWith(
                            color: BaseColors.textTitle,
                          ),
                        ),
                        MyAssets.icons.arrowRight.svg(),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void showDialogRemoveService({
    required BuildContext context,
    required String serviceName,
  }) {
    AppDialog.showDialogConfirm(
      context,
      title: "Xác nhận",
      message: 'Bạn có chắc chắn muốn xóa dịch vụ "$serviceName"?',
      buttonNameConfirm: "Xác nhận",
      onConfirmAction: () {
        changeQuantity(0);
      },
      onCancelAction: () {
        changeQuantity(1);
      },
    );
  }
}
