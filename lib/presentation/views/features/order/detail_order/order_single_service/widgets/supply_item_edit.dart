import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/domain/entities/order/detail_order_item_entity.dart';
import 'package:vcc/domain/entities/order/original_price_supply_entity.dart';
import 'package:vcc/domain/entities/order/serial_info_entity.dart';
import 'package:vcc/domain/enums/data_type.dart';
import 'package:vcc/domain/enums/tax_type.dart';
import 'package:vcc/domain/enums/warehouse_supply_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/bottom_sheet/input_original_price_order/input_original_price_order_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_supply_type/select_supply_type_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_tax_type/select_tax_view.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/detail_order/select_original_price/select_original_price_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/select_price_and_serial/select_price_and_serial_page.dart';
import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/plus_and_minus_widget.dart';
import 'package:vcc/utils/string_utils.dart';

class SupplyItemEdit extends StatelessWidget {
  final DetailOrderItemEntity supply;
  final bool isEditSupply;
  final Function(num quantity) changeQuantity;
  final Function(String originPrice, String price)? onChangeOriginalPrice;
  final Function(WarehouseSupplyType type)? onChangeSupplyType;
  final Function(TaxType type)? onChangeTaxType;
  final Function? reloadUI;
  final String? orderCode;

  final Function(
    List<OriginalPriceSupplyEntity> listData,
    num quantity,
  )? onSelectSerialAndOriginalPrice;

  final Function(
    List<OriginalPriceSupplyEntity> listData,
    num quantity,
  )? onSelectedOriginalPrice;

  const SupplyItemEdit({
    super.key,
    required this.supply,
    required this.changeQuantity,
    this.isEditSupply = false,
    this.onChangeOriginalPrice,
    this.onChangeSupplyType,
    this.onChangeTaxType,
    this.onSelectSerialAndOriginalPrice,
    this.onSelectedOriginalPrice,
    this.reloadUI,
    this.orderCode,
  });

  @override
  Widget build(BuildContext context) {
    bool showChooseSerialAndPrice =
        (supply.needChooseSerialAndPrice ?? false) && isEditSupply;
    bool showChooseOriginalPrice =
        (supply.needChooseOriginalPrice ?? false) && isEditSupply;
    bool canEditOriginalPrice =
        (supply.canChangeOriginPrice ?? false) && isEditSupply;
    bool canEditTax = (supply.canChangeVat ?? false) && isEditSupply;
    bool canEditSupplyType = (supply.canChangeWMSType ?? false) && isEditSupply;

    return Slidable(
      endActionPane: ActionPane(
        motion: const ScrollMotion(),
        extentRatio: 0.2,
        children: <Widget>[
          CustomSlidableAction(
            backgroundColor: BaseColors.primary,
            foregroundColor: Colors.yellow,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: <Widget>[
                MyAssets.icons.iconWhiteTrash.svg(),
                const SizedBox(height: 8),
                Text(
                  "Xoá",
                  style: UITextStyle.body2SemiBold.copyWith(
                    color: BaseColors.backgroundWhite,
                  ),
                ),
              ],
            ),
            onPressed: (context) {
              if (supply.defaultSupply ?? false) {
                AppDialog.showDialogInfo(
                  context,
                  barrierDismissible: false,
                  title: "Thông báo",
                  message: "Dịch vụ mặc định không thể xóa",
                  buttonNameConfirm: "Đóng",
                  onConfirmAction: () {
                    changeQuantity(1);
                  },
                );
              } else {
                changeQuantity(0);
              }
            },
          )
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            MyAssets.icons.iconSupply.svg(),
            const SizedBox(width: 8),
            Expanded(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Text(
                    supply.name ?? '',
                    style: UITextStyle.body2Medium.copyWith(
                      color: BaseColors.textLabel,
                    ),
                  ),
                  const SizedBox(height: 6),
                  Row(
                    children: <Widget>[
                      Expanded(
                        child: Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            RichText(
                              text: TextSpan(
                                text: StringUtils.formatMoney(
                                  supply.price ?? 0,
                                ),
                                style: UITextStyle.body2Medium.copyWith(
                                  color: BaseColors.primary,
                                ),
                                children: <TextSpan>[
                                  TextSpan(
                                    text: "/${supply.unit ?? ''}",
                                    style: UITextStyle.body2Regular.copyWith(
                                      color: BaseColors.textSubtitle,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                            Visibility(
                              visible: supply.originalPrice != null &&
                                  supply.getWmsName ==
                                      WarehouseSupplyType.notWms.display,
                              child: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Row(
                                  children: [
                                    RichText(
                                      text: TextSpan(
                                        style: UITextStyle.caption1Regular
                                            .copyWith(
                                          color: BaseColors.textBody,
                                        ),
                                        children: <TextSpan>[
                                          TextSpan(
                                            text: "Giá vốn:",
                                            style: UITextStyle.caption1Regular
                                                .copyWith(
                                              color: BaseColors.textSubtitle,
                                            ),
                                          ),
                                          TextSpan(
                                            text:
                                                ' ${StringUtils.formatFlexibleDouble((supply.serialInfo ?? []).isNotEmpty ? (supply.serialInfo![0].originalPrice ?? 0) : supply.rootPrice ?? 0)}',
                                          ),
                                        ],
                                      ),
                                    ),
                                    Visibility(
                                      visible: canEditOriginalPrice,
                                      child: InkWellWidget(
                                        onTap: () {
                                          onEditOriginalPrice(context);
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 4,
                                          ),
                                          child:
                                              MyAssets.icons.iconEditS12.svg(),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                            Visibility(
                              visible: supply.wmsType != null,
                              child: Padding(
                                padding: const EdgeInsets.only(top: 4),
                                child: Row(
                                  children: [
                                    Flexible(
                                      child: RichText(
                                        maxLines: 1,
                                        overflow: TextOverflow.ellipsis,
                                        text: TextSpan(
                                          style: UITextStyle.caption1Regular
                                              .copyWith(
                                            color: BaseColors.textBody,
                                          ),
                                          children: <TextSpan>[
                                            TextSpan(
                                              text: "Loại vật tư:",
                                              style: UITextStyle.caption1Regular
                                                  .copyWith(
                                                color: BaseColors.textSubtitle,
                                              ),
                                            ),
                                            TextSpan(
                                              text: ' ${supply.getWmsName}',
                                            ),
                                          ],
                                        ),
                                      ),
                                    ),
                                    Visibility(
                                      visible: canEditSupplyType,
                                      child: InkWellWidget(
                                        onTap: () {
                                          onEditSupplyType(context);
                                        },
                                        child: Padding(
                                          padding: const EdgeInsets.symmetric(
                                            horizontal: 4,
                                          ),
                                          child:
                                              MyAssets.icons.iconEditS12.svg(),
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                      const SizedBox(width: 8),
                      PlusAndMinusWidget(
                        quantity: supply.quantity ?? 1,
                        minQuantity: supply.minItem,
                        step: supply.step,
                        dataType: DataType.double,
                        onPlus: (value) async {
                          if ((supply.serialInfo ?? []).isNotEmpty) {
                            await onPlusOrMinusSerial(
                              context,
                              maxQuantity: value,
                              showChooseOriginalPrice: showChooseOriginalPrice,
                              showChooseSerialAndPrice:
                                  showChooseSerialAndPrice,
                            );
                            reloadUI?.call();
                          } else {
                            changeQuantity(value);
                          }
                        },
                        onMinus: (value) async {
                          if ((supply.serialInfo ?? []).isNotEmpty) {
                            await onPlusOrMinusSerial(
                              context,
                              maxQuantity: value,
                              showChooseOriginalPrice: showChooseOriginalPrice,
                              showChooseSerialAndPrice:
                                  showChooseSerialAndPrice,
                            );
                            reloadUI?.call();
                          } else {
                            if (value == 0) {
                              if (supply.defaultSupply ?? false) {
                                AppDialog.showDialogInfo(
                                  context,
                                  barrierDismissible: false,
                                  title: "Thông báo",
                                  message: "Vật tư mặc định không thể xóa",
                                  buttonNameConfirm: "Đóng",
                                  onConfirmAction: () {
                                    changeQuantity(1);
                                  },
                                );
                              } else {
                                AppDialog.showDialogConfirm(
                                  context,
                                  barrierDismissible: false,
                                  title: "Xác nhận",
                                  message:
                                      'Bạn có chắc chắn muốn xóa vật tư "${supply.name ?? ""}"?',
                                  buttonNameConfirm: "Xác nhận",
                                  onConfirmAction: () {
                                    changeQuantity(value);
                                  },
                                  onCancelAction: () {
                                    changeQuantity(1);
                                  },
                                );
                              }
                            } else {
                              changeQuantity(value);
                            }
                          }
                        },
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          RichText(
                            text: TextSpan(
                              text: 'Thuế: ',
                              style: UITextStyle.caption1Medium.copyWith(
                                color: BaseColors.textBody,
                              ),
                              children: <TextSpan>[
                                TextSpan(
                                  text: "${supply.vat ?? 0}%",
                                  style: UITextStyle.caption1SemiBold.copyWith(
                                    color: BaseColors.textBody,
                                  ),
                                ),
                              ],
                            ),
                          ),
                          Visibility(
                            visible: canEditTax,
                            child: InkWellWidget(
                              onTap: () {
                                onEditTaxType(context);
                              },
                              child: Padding(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 4,
                                ),
                                child: MyAssets.icons.iconEditS12.svg(),
                              ),
                            ),
                          ),
                        ],
                      ),
                      RichText(
                        text: TextSpan(
                          text: 'Thành tiền: ',
                          style: UITextStyle.caption1Medium.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                          children: <TextSpan>[
                            TextSpan(
                              text: StringUtils.formatMoney(
                                (supply.quantity ?? 1) * (supply.price ?? 0),
                              ),
                              style: UITextStyle.caption1SemiBold.copyWith(
                                color: BaseColors.textLabel,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  Visibility(
                    visible: showChooseSerialAndPrice,
                    child: _buildListData(
                      title: "Serial và giá vốn",
                      onSelect: () {
                        onSelectSerialAndPrice(
                          context,
                          maxQuantity: supply.quantity ?? 1,
                        );
                      },
                      listData: supply.serialInfo,
                    ),
                    /*(supply.serialInfo ?? []).isNotEmpty ?
                        : _buildCard(
                            title: "Serial và giá vốn",
                            onSelect: () {
                              onSelectSerialAndPrice(context);
                            },
                          ),*/
                  ),
                  Visibility(
                    visible: showChooseOriginalPrice,
                    child: _buildListData(
                      title: "Giá vốn",
                      onSelect: () {
                        onSelectOriginalPrice(
                          context,
                          maxQuantity: supply.quantity ?? 1,
                        );
                      },
                      listData: supply.serialInfo,
                    ),
                    /*(supply.serialInfo ?? []).isNotEmpty ?
                    : _buildCard(
                        title: "Giá vốn",
                        onSelect: () {
                          onSelectOriginalPrice(context);
                        },
                      ),*/
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  // Widget _buildCard({
  //   required String title,
  //   required Function onSelect,
  // }) {
  //   return Container(
  //     decoration: BoxDecoration(
  //       borderRadius: BorderRadius.circular(8),
  //       border: Border.all(
  //         color: BaseColors.borderDivider,
  //       ),
  //       color: BaseColors.backgroundGray1,
  //     ),
  //     margin: const EdgeInsets.only(top: 12),
  //     padding: const EdgeInsets.fromLTRB(16, 2, 2, 2),
  //     child: Row(
  //       children: <Widget>[
  //         Expanded(
  //           child: Text(
  //             title,
  //             style: UITextStyle.body2Regular.copyWith(
  //               color: BaseColors.textLabel,
  //             ),
  //           ),
  //         ),
  //         AppTextButton(
  //           title: "Chọn",
  //           titleStyle: UITextStyle.body2Regular.copyWith(
  //             color: BaseColors.textSubtitle,
  //           ),
  //           iconRight: MyAssets.icons.iconArrowRightS20.svg(),
  //           onTap: onSelect,
  //         ),
  //       ],
  //     ),
  //   );
  // }

  Widget _buildListData({
    required String title,
    required Function onSelect,
    List<SerialInfoEntity>? listData,
  }) {
    return Container(
      margin: const EdgeInsets.only(top: 12),
      decoration: BoxDecoration(
        border: Border.all(
          color: BaseColors.borderDivider,
        ),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          InkWellWidget(
            onTap: () {
              onSelect.call();
            },
            child: Container(
              decoration: BoxDecoration(
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
                border: Border.all(
                  color: BaseColors.borderDivider,
                ),
                color: BaseColors.backgroundGray1,
              ),
              padding: const EdgeInsets.fromLTRB(16, 2, 2, 2),
              child: Row(
                children: <Widget>[
                  Expanded(
                    child: Text(
                      title,
                      style: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ),
                  AbsorbPointer(
                    absorbing: true,
                    child: AppTextButton(
                      title: "Chọn",
                      titleStyle: UITextStyle.body2Regular.copyWith(
                        color: BaseColors.textSubtitle,
                      ),
                      iconRight: MyAssets.icons.iconArrowRightS20.svg(),
                      onTap: onSelect,
                    ),
                  ),
                ],
              ),
            ),
          ),
          ListView.builder(
            itemCount: listData?.length ?? 0,
            shrinkWrap: true,
            padding: EdgeInsets.zero,
            physics: const BouncingScrollPhysics(),
            itemBuilder: (context, index) {
              final item = listData![index];

              return Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: <Widget>[
                  Padding(
                    padding: const EdgeInsets.all(12),
                    child: Row(
                      children: <Widget>[
                        Expanded(
                          child: Text(
                            "${index + 1}. ${item.serial ?? ''}",
                            style: UITextStyle.body2Medium.copyWith(
                              color: BaseColors.textBody,
                            ),
                          ),
                        ),
                        Text(
                          " ${StringUtils.formatFlexibleDouble(item.originalPrice ?? 0)}",
                          style: UITextStyle.body2Regular.copyWith(
                            color: BaseColors.textLabel,
                          ),
                        ),
                      ],
                    ),
                  ),
                  const Padding(
                    padding: EdgeInsets.only(left: 16),
                    child: DividerWidget(),
                  ),
                ],
              );
            },
          )
        ],
      ),
    );
  }

  void onEditOriginalPrice(BuildContext context) {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Sửa giá vốn",
      isFlexible: true,
      child: InputOriginalPriceOrderView(
        price: supply.price ?? 0,
        enableInputPrice: true,
        onConfirm: (originPrice, price) {
          onChangeOriginalPrice?.call(originPrice, price);
        },
      ),
    );
  }

  void onEditSupplyType(BuildContext context) {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Chọn loại vật tư",
      isFlexible: true,
      child: SelectSupplyTypeView(
        onTapOption: (option) {
          onChangeSupplyType?.call(option);
        },
      ),
    );
  }

  void onEditTaxType(BuildContext context) {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Chọn loại thuế",
      isFlexible: true,
      child: SelectTaxView(
        onTapOption: (option) {
          onChangeTaxType?.call(option);
        },
      ),
    );
  }

  Future<void> onSelectSerialAndPrice(
    BuildContext context, {
    required num maxQuantity,
  }) async {
    await context.push(
      RouterPaths.selectPriceAndSerialPage,
      extra: SelectPriceAndSerialArguments(
        orderCode: orderCode,
        supplyCode: supply.code ?? '',
        maxLength: maxQuantity,
        editPrice: false,
        price: supply.price ?? 0,
        serialsSelected: supply.serialInfo?.map((e) {
          return OriginalPriceSupplyEntity(
            serial: e.serial,
            price: e.originalPrice,
          );
        }).toList(),
        onSelected: (data) {
          onSelectSerialAndOriginalPrice?.call(data, maxQuantity);
        },
      ),
    );
  }

  Future<void> onSelectOriginalPrice(
    BuildContext context, {
    required num maxQuantity,
  }) async {
    await context.push(
      RouterPaths.selectOriginalPricePage,
      extra: SelectOriginalPriceArguments(
        supplyCode: supply.code ?? '',
        maxLength: maxQuantity,
        editPrice: false,
        price: supply.price ?? 0,
        orderCode: orderCode,
        listPriceSelected: supply.serialInfo?.map((e) {
          return OriginalPriceSupplyEntity(
            serial: e.serial,
            price: e.originalPrice,
            quantity: e.quantity ?? 1,
          );
        }).toList(),
        onSelected: (data) {
          onSelectedOriginalPrice?.call(data, maxQuantity);
        },
      ),
    );
  }

  Future<void> onPlusOrMinusSerial(
    BuildContext context, {
    required num maxQuantity,
    bool? showChooseSerialAndPrice,
    bool? showChooseOriginalPrice,
  }) async {
    if (showChooseSerialAndPrice ?? false) {
      await onSelectSerialAndPrice(
        context,
        maxQuantity: maxQuantity,
      );
    } else if (showChooseOriginalPrice ?? false) {
      await onSelectOriginalPrice(
        context,
        maxQuantity: maxQuantity,
      );
    }
  }
}
