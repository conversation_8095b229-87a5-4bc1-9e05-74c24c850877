import 'dart:io';

import 'package:flutter/material.dart';
import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:go_router/go_router.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/order_sub_status.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/detail_order/widget/build_item_info.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/media_utils.dart';

class OrderInfoSignWidget extends StatelessWidget {
  final DetailOrderEntity order;
  final String orderCode;
  final Function(File file)? onAddImage;
  final Function(int index)? onDeleteImage;
  final List<String>? listImage;

  const OrderInfoSignWidget({
    required this.order,
    required this.orderCode,
    this.onAddImage,
    this.onDeleteImage,
    this.listImage,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    bool statusShowImage = order.status != OrderStatus.registered &&
        order.status != OrderStatus.receptionWaiting;
    bool canAddImage = false;

    if (order.canEditOrder) {
      if (order.orderStatus == OrderSubStatus.processWaiting ||
          order.orderStatus == OrderSubStatus.processing) {
        canAddImage = true;
      }
    }

    return Container(
      color: BaseColors.backgroundWhite,
      padding: const EdgeInsets.symmetric(
        horizontal: 16,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            height: 48,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Text(
                  orderCode,
                  style: UITextStyle.body2SemiBold.copyWith(
                    color: BaseColors.textTitle,
                  ),
                ),
                const SizedBox(width: 8),
                InkWellWidget(
                  onTap: () {
                    AppUtils.copyToClipboard(orderCode);
                  },
                  child: MyAssets.icons.copyClipBoard.svg(),
                )
              ],
            ),
          ),
          const DividerWidget(),
          const SizedBox(height: 4),
          BuildInfoRow(
            label: order.isOrderWarranty ? "Loại đơn đối tác: " : 'Loại đơn: ',
            value: order.isOrderWarranty
                ? "Bảo hành"
                : order.orderType?.label ?? "",
          ),
          BuildInfoRow(
            label: 'Nguồn đơn:',
            value: order.source?.display ?? "",
          ),
          BuildInfoRow(
            label: 'Mã YCTX:',
            value: order.contactRequestCode ?? "",
            visible: (order.contactRequestCode ?? "").isNotEmpty,
          ),
          if (order.isOrderPartner) ...[
            BuildInfoRow(
              label: 'Loại đơn bảo hành:',
              value: order.orderType?.label ?? "",
            ),
            BuildInfoRow(
              label: 'Mã ticket:',
              value: order.ticketCode ?? "",
            ),
            BuildInfoRow(
              label: 'Loại thu tiền:',
              value: order.paymentInfo?.billType?.display ?? "",
            ),
          ],
          if (statusShowImage) ...[
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 6,
              ),
              child: Text(
                "Ảnh:",
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
            ),
            SizedBox(
              height: 80,
              child: ListView.separated(
                itemCount: (listImage?.length ?? 0) + 1,
                scrollDirection: Axis.horizontal,
                padding: EdgeInsets.zero,
                separatorBuilder: (_, __) => const SizedBox(width: 8),
                itemBuilder: (context, index) {
                  if (index == (listImage?.length ?? 0)) {
                    if (canAddImage) {
                      return InkWellWidget(
                        onTap: () {
                          onTakePicture(context);
                        },
                        child: MyAssets.icons.iconUploadImageDashline2.svg(),
                      );
                    }
                    return const SizedBox();
                  }

                  return Stack(
                    children: [
                      Container(
                        height: 80,
                        width: 80,
                        padding: const EdgeInsets.only(
                          top: 4,
                          right: 4,
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: ImageWidget(
                            listImage![index],
                            enableShowPreview: true,
                            size: const Size(76, 76),
                          ),
                        ),
                      ),
                      Visibility(
                        visible: canAddImage,
                        child: Positioned(
                          top: 0,
                          right: 0,
                          child: InkWellWidget(
                            onTap: () {
                              onDeleteImage?.call(index);
                            },
                            child: MyAssets.icons.iconCloseRed.svg(),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
            const SizedBox(height: 12),
          ],
          if (order.certificates != null) ...[
            Padding(
              padding: const EdgeInsets.symmetric(
                vertical: 6,
              ),
              child: Text(
                "Biên bản nghiệm thu:",
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
            ),
            if (order.getListImage.isNotEmpty) ...[
              SizedBox(
                height: 80,
                child: ListView.separated(
                  itemCount: order.getListImage.length,
                  scrollDirection: Axis.horizontal,
                  padding: EdgeInsets.zero,
                  separatorBuilder: (_, __) => const SizedBox(width: 8),
                  physics: const ClampingScrollPhysics(),
                  itemBuilder: (context, index) {
                    return Container(
                      height: 80,
                      width: 80,
                      padding: const EdgeInsets.only(
                        top: 4,
                        right: 4,
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(8),
                        child: ImageWidget(
                          order.getListImage[index].url ?? "",
                          enableShowPreview: true,
                          size: const Size(76, 76),
                        ),
                      ),
                    );
                  },
                ),
              ),
            ],
            if (order.getListFile.isNotEmpty) ...[
              ListView.separated(
                itemCount: order.getListFile.length,
                padding: EdgeInsets.zero,
                separatorBuilder: (_, __) => const SizedBox(width: 8),
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemBuilder: (context, index) {
                  final item = order.getListFile[index];

                  return InkWellWidget(
                    onTap: () {
                      context.push(
                        RouterPaths.viewFileContract,
                        extra: item.url ?? "",
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 14,
                      ),
                      decoration: BoxDecoration(
                        color: BaseColors.backgroundGray1,
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        children: <Widget>[
                          MyAssets.icons.iconPdfGray.svg(),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              'Biên bản nghiệm thu.pdf',
                              style: UITextStyle.body2Regular.copyWith(
                                color: BaseColors.textLabel,
                              ),
                            ),
                          ),
                          const SizedBox(width: 8),
                          MyAssets.icons.iconEyeRedBorder.svg(),
                        ],
                      ),
                    ),
                  );
                },
              ),
            ],
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  void onTakePicture(BuildContext context) async {
    await MediaUtils.onTakeImage(
      context: context,
      isGetAddress: true,
      onSubmitImage: (file) async {
        onAddImage?.call(file);
      },
    );
  }
}
