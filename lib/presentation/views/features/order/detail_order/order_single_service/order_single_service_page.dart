import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/app_box_shadows.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/body/buy_supplies_for_customer_body.dart';
import 'package:vcc/domain/body/sign_acceptance_body.dart';
import 'package:vcc/domain/entities/order/detail_user_info_entity.dart';
import 'package:vcc/domain/enums/assign_type.dart';
import 'package:vcc/domain/enums/commission_confirm_status.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/impact_history_function.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/menu_type.dart';
import 'package:vcc/domain/enums/mobile_call_type.dart';
import 'package:vcc/domain/enums/order_generate_type_enum.dart';
import 'package:vcc/domain/enums/order_status.dart';
import 'package:vcc/domain/enums/order_sub_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/payment_method.dart';
import 'package:vcc/domain/enums/report_order_type.dart';
import 'package:vcc/domain/enums/verify_schedule_status.dart';
import 'package:vcc/domain/enums/worker_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/acceptance_contract_view/acceptance_contract_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/assign_order/assign_order_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_staff/choose_staff_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/extend_schedule/extend_schedule_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_order_report/upload_order_report_view.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/dialogs/warning_mobile_call.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/widgets/buy_supplies_for_customer_sheet.dart';
import 'package:vcc/presentation/views/features/order/detail_order/warning_order/warning_order_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/widgets/build_confirm_commission.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/widgets/members_order_widget.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/widgets/order_info_sign_widget.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/widgets/service_detail_widget.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/widgets/service_order_status_widget.dart';
import 'package:vcc/presentation/views/features/order/detail_order/order_single_service/widgets/time_delivery_widget.dart';
import 'package:vcc/presentation/views/features/order/detail_order/request_cancel/request_cancel_screen.dart';
import 'package:vcc/presentation/views/features/order/detail_order/sign_contract_order/sign_contract_order_page.dart';
import 'package:vcc/presentation/views/features/order/detail_order/widget/calculate_shipping_cost.dart';
import 'package:vcc/presentation/views/features/order/detail_order/widget/info_customer_widget.dart';
import 'package:vcc/presentation/views/features/order/detail_order/widget/voucher_widget.dart';
import 'package:vcc/presentation/views/features/work/debt_statistics/debt_statistics_page.dart';
import 'package:vcc/presentation/views/features/work/impact_history/impact_history_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/utils/app_utils.dart';
import 'package:vcc/utils/mobile_call_utils.dart';
import 'package:vcc/utils/show_loading_utils.dart';

import 'order_single_service_view_model.dart';
import 'widgets/payment_info_widget.dart';

class OrderSingleServiceArguments {
  final String orderCode;
  final bool isCommission;
  final int? sharePercent;
  final CommissionConfirmStatus? commissionStatus;

  OrderSingleServiceArguments({
    required this.orderCode,
    this.isCommission = false,
    this.sharePercent,
    this.commissionStatus,
  });
}

class OrderSingleServicePage extends StatefulHookConsumerWidget {
  const OrderSingleServicePage({
    super.key,
    required this.arguments,
  });

  final OrderSingleServiceArguments arguments;

  @override
  ConsumerState<OrderSingleServicePage> createState() =>
      _OrderSingleServicePageState();
}

class _OrderSingleServicePageState
    extends ConsumerState<OrderSingleServicePage> {
  @override
  void initState() {
    _init(context);
    super.initState();
  }

  Future<void> refreshData() async {
    ref.read(detailOrderSingleProvider.notifier).getDetailOrder(
          widget.arguments.orderCode,
          isFromCommission: widget.arguments.isCommission,
        );
  }

  void _init(BuildContext context) {
    Future(
      () async {
        await ref.read(detailOrderSingleProvider.notifier).getDetailOrder(
              widget.arguments.orderCode,
              isFromCommission: widget.arguments.isCommission,
            );

        WidgetsBinding.instance.addPostFrameCallback((_) async {
          var state = ref.watch(detailOrderSingleProvider);

          final isProcessWaiting =
              state.orderInfo?.status == OrderStatus.processWaiting;
          bool turnOnMobileCall = state.orderInfo?.isCalled ?? false;

          if (isProcessWaiting && !turnOnMobileCall) {
            final result = await ref
                .read(detailOrderSingleProvider.notifier)
                .checkWarningMobileCall(widget.arguments.orderCode);

            if (result != null) {
              if (result.isShowAlert ?? false) {
                if (!context.mounted) return;
                AppDialog.showDefaultDialog(
                  context: context,
                  child: WarningMobileCall(
                    content: result.message ?? "",
                    onConfirm: onCallCustomer,
                  ),
                );
              }
            }
          }
        });
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return LayoutPage(
      appbar: AppBarCustom(
        title: widget.arguments.orderCode,
        isMiddleEllipsisText: true,
        actionWidget: [
          InkWellWidget(
            onTap: () {
              context.push(
                RouterPaths.impactHistory,
                extra: ImpactHistoryArguments(
                  collectionCode: widget.arguments.orderCode,
                  impactHistoryFunction: ImpactHistoryFunction.serviceOrder,
                  orderCode: widget.arguments.orderCode,
                ),
              );
            },
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: MyAssets.icons.iconOclockWhite.svg(),
            ),
          ),
        ],
      ),
      backgroundColor: CoreColors.neutral00,
      bottomAction: _buildBottomFixedWidget(),
      body: _buildBodyWidget(context),
    );
  }

  Widget _buildBodyWidget(BuildContext context) {
    var orderState = ref.watch(detailOrderSingleProvider);

    if (orderState.loadDataStatus == LoadStatus.loading ||
        orderState.loadDataStatus == LoadStatus.initial) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (orderState.loadDataStatus == LoadStatus.failure) {
      return Expanded(
        child: Container(
          color: BaseColors.backgroundGray,
          child: EmptyListWidget(
            title: "Không có dữ liệu",
            onRefresh: refreshData,
          ),
        ),
      );
    } else {
      final order = orderState.orderInfo!;

      return RefreshIndicatorWidget(
        onRefresh: refreshData,
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ServiceOrderStatusWidget(
                order: order,
                sharePercent: widget.arguments.sharePercent,
              ),
              const SizedBox(height: 8),
              InfoCustomerWidget(
                order: order,
                orderCode: widget.arguments.orderCode,
                orderType: MenuType.service,
                showCallIcon: order.isShowCallOrder,
                onCallPhoneNumber: onCallCustomer,
              ),
              const SizedBox(height: 8),
              TimeDeliveryWidget(
                order: order,
                onCancelSchedule: () async {
                  await ref
                      .read(detailOrderSingleProvider.notifier)
                      .cancelSchedule(
                        orderCode: widget.arguments.orderCode,
                      );

                  if (ref.watch(detailOrderSingleProvider).updateStatus ==
                      LoadStatus.success) {
                    if (!context.mounted) return;
                    AppDialog.showDialogCenter(
                      context,
                      message: "Hủy lịch hẹn thành công",
                      status: DialogStatus.success,
                    );
                  }
                },
                onRequestScheduleSuccess: () {
                  refreshData();
                },
              ),
              const SizedBox(height: 8),
              if ((orderState.orderInfo!.status == OrderStatus.processing) &&
                  orderState.orderInfo!.isOrderPartner &&
                  orderState.orderInfo!.isWorker) ...[
                CalculateShippingCost(
                  order: orderState.orderInfo!,
                  callBack: (distance) async {
                    await ref
                        .read(detailOrderSingleProvider.notifier)
                        .calculateShippingCost(
                          distance: int.parse(distance),
                          orderCode: widget.arguments.orderCode,
                        );
                    if (ref.read(detailOrderSingleProvider).loadDataStatus ==
                        LoadStatus.success) {
                      Future(() {
                        AppDialog.showDialogCenter(
                          context,
                          message: "Áp dụng quãng đường vận chuyển thành công",
                          status: DialogStatus.success,
                        );
                        refreshData();
                      });
                    }
                  },
                ),
                const SizedBox(height: 8),
              ],
              const ServiceDetailWidget(),
              if (order.orderType == OrderType.service) ...[
                const SizedBox(height: 8),
                VoucherWidget(
                  order: order,
                  onCalculatePrice: (code) async {
                    return await ref
                        .read(detailOrderSingleProvider.notifier)
                        .getPromotionPrice(couponCode: code);
                  },
                  onSelectVoucher: (voucher) {
                    ref
                        .read(detailOrderSingleProvider.notifier)
                        .editOrder(voucherCode: voucher?.code);
                  },
                ),
              ],
              const SizedBox(height: 8),
              ServicePaymentBillWidget(
                order: order,
                showFeeWarranty: order.showMoneyDifferent,
                refreshOrder: () {
                  ref.read(detailOrderSingleProvider.notifier).refreshOrder();
                },
              ),
              const SizedBox(height: 8),
              MembersOrderWidget(
                order: order,
              ),
              const SizedBox(height: 8),
              OrderInfoSignWidget(
                order: order,
                orderCode: widget.arguments.orderCode,
                listImage: orderState.listImage ?? [],
                onAddImage: (file) async {
                  await ref
                      .read(detailOrderSingleProvider.notifier)
                      .addImageOrder(file);
                },
                onDeleteImage: (index) async {
                  await ref
                      .read(detailOrderSingleProvider.notifier)
                      .deleteImageOrder(index: index);
                },
              ),
              const SizedBox(height: 8),
              Padding(
                padding: const EdgeInsets.all(16),
                child: _buildActionWithOrder(),
              ),
            ],
          ),
        ),
      );
    }
  }

  Widget _buildActionWithOrder() {
    final state = ref.watch(detailOrderSingleProvider);

    if (state.orderInfo?.isFromCommission ?? false) {
      return const SizedBox();
    }

    switch (state.orderInfo?.status) {
      case OrderStatus.receptionWaiting:
      case OrderStatus.processing:
        if (state.orderInfo?.requestExtendKpiId != null) {
          return const SizedBox();
        } else {
          if (state.orderInfo?.status == OrderStatus.receptionWaiting) {
            if (((state.orderInfo?.isRegionalManager ?? false) ||
                (state.orderInfo?.isBoft3 ?? false) ||
                (state.orderInfo?.isFt3m ?? false) ||
                (state.orderInfo?.isFt3 ?? false))) {
              return _buildCancelOrderButton();
            } else {
              return const SizedBox();
            }
          } else if (state.orderInfo?.status == OrderStatus.processing) {
            if (state.orderInfo?.isWorker ?? false) {
              return _buildCancelOrderButton();
            } else {
              return const SizedBox();
            }
          }
          return const SizedBox();
        }
      case OrderStatus.processWaiting:
        if (state.orderInfo?.shippingInfo?.pendingSchedule ?? false) {
          return const SizedBox();
        } else {
          if (state.orderInfo?.isWorker ?? false) {
            return _buildProcessWaitingOrderButton(
              context: context,
            );
          } else {
            return const SizedBox();
          }
        }

      default:
        return const SizedBox();
    }
  }

  Widget _buildBottomFixedWidget() {
    final state = ref.watch(detailOrderSingleProvider);

    if (state.orderInfo?.isFromCommission ?? false) {
      if (state.orderInfo?.status == OrderStatus.registered) {
        return _buildConfirmCommission(context);
      }
      return const SizedBox();
    }

    switch (state.orderInfo?.status) {
      case OrderStatus.receptionWaiting:
        if ((state.orderInfo?.isRegionalManager ?? false) ||
            (state.orderInfo?.isBoft3 ?? false) ||
            (state.orderInfo?.isFt3m ?? false) ||
            (state.orderInfo?.isFt3 ?? false)) {
          return _buildWaitingAssignAction();
        }
        return const SizedBox();
      case OrderStatus.processWaiting:
        if (state.orderInfo?.shippingInfo?.pendingSchedule ?? false) {
          return const SizedBox();
        } else {
          if (state.orderInfo?.isWorker ?? false) {
            return _buildProcessWaitingAction();
          } else {
            return const SizedBox();
          }
        }
      case OrderStatus.processing:
        if (state.orderInfo?.requestExtendKpiId != null) {
          //hieptv11 ve sau check role nao an huy thi moi hien thi ra o cung role do
          return _buildCancelExtendSchedule();
        } else {
          if (state.orderInfo?.isWorker ?? false) {
            return _buildProcessingAction();
          } else {
            return const SizedBox();
          }
        }
      case OrderStatus.processed:
        if (state.orderInfo?.isWorker ?? false) {
          return _buildPaymentAction();
        } else {
          return const SizedBox();
        }
      case OrderStatus.cancel:
        if (state.orderInfo?.orderStatus == OrderSubStatus.cancelRequest) {
          return _buildReOrder();
        } else {
          return const SizedBox();
        }
      default:
        return const SizedBox();
    }
  }

  //widget of action with order
  Widget _buildCancelOrderButton() {
    return BaseButton(
      text: "Đề xuất hủy",
      borderColor: CoreColors.neutral04,
      backgroundColor: Colors.transparent,
      textColor: BaseColors.textLabel,
      onTap: () async {
        await context.push(
          RouterPaths.requestCancelPage,
          extra: RequestCancelArguments(
            order: ref.watch(detailOrderSingleProvider).orderInfo!,
          ),
        );

        ref.read(detailOrderSingleProvider.notifier).refreshOrder();
      },
    );
  }

  Widget _buildProcessWaitingOrderButton({
    required BuildContext context,
  }) {
    final state = ref.watch(detailOrderSingleProvider);
    return Column(
      children: [
        BaseButton(
          text: "Chuyển việc OFT3-GK",
          borderColor: BaseColors.primary,
          backgroundColor: BaseColors.backgroundGray,
          textColor: BaseColors.primary,
          onTap: () {
            String? schedule = state.orderInfo?.shippingInfo?.scheduleTime ??
                state.orderInfo?.shippingInfo?.startTime;
            AppBottomSheet.showNormalBottomSheet(
              context,
              title: "Thợ được chuyển",
              height: MediaQuery.of(context).size.height * 0.95,
              child: ChooseStaffPage(
                needVerify: true,
                workerType: WorkerType.oft3,
                onSelectStaff: (staff) async {
                  await ref
                      .read(detailOrderSingleProvider.notifier)
                      .assignWorker(
                        orderCode: widget.arguments.orderCode,
                        userCode: staff.username ?? '',
                        type: AssignType.transfer.keyToServer,
                      );

                  if (ref.watch(detailOrderSingleProvider).updateStatus ==
                      LoadStatus.success) {
                    if (!context.mounted) return;
                    AppDialog.showDialogCenter(
                      context,
                      message: "Chuyển việc cho OFT3-GK thành công",
                      status: DialogStatus.success,
                    );
                  }
                },
                scheduleTime: schedule != null
                    ? DateTime.parse(schedule)
                    : DateTime.now(),
              ),
            );
          },
        ),
        const SizedBox(height: 12),
        BaseButton(
          text: "Trả đơn hàng",
          borderColor: CoreColors.neutral04,
          backgroundColor: Colors.transparent,
          textColor: BaseColors.textLabel,
          onTap: () {
            onReturnOrder(context);
          },
        ),
        const SizedBox(height: 12),
        BaseButton(
          text: "Đề xuất hủy",
          borderColor: CoreColors.neutral04,
          backgroundColor: Colors.transparent,
          textColor: BaseColors.textLabel,
          onTap: () async {
            await context.push(
              RouterPaths.requestCancelPage,
              extra: RequestCancelArguments(
                order: ref.watch(detailOrderSingleProvider).orderInfo!,
              ),
            );

            ref.read(detailOrderSingleProvider.notifier).refreshOrder();
          },
        ),
        if (ref.watch(detailOrderSingleProvider).orderInfo?.generateType ==
            OrderGenerateTypeEnum.auto) ...[
          const SizedBox(height: 12),
          BaseButton(
            text: "Đóng đơn hàng",
            borderColor: CoreColors.neutral04,
            backgroundColor: Colors.transparent,
            textColor: BaseColors.textLabel,
            onTap: () {
              AppDialog.showDialogConfirm(
                context,
                message: "Bạn chắc chắn muốn đóng đơn hàng?",
                onConfirmAction: () async {
                  ref.read(detailOrderSingleProvider.notifier).completeOrder();
                  if (ref.watch(detailOrderSingleProvider).updateStatus ==
                      LoadStatus.success) {
                    AppDialog.showDialogCenter(
                      context,
                      message: "Đóng đơn hàng thành công",
                      status: DialogStatus.success,
                    );
                  }
                },
              );
            },
          ),
        ]
      ],
    );
  }

  //build bottom bar
  //hieptv11 confirm với lauld sau
  //happy case: ft3M, boft3, ft3 hiển thị nút giao chuyển việc, tiếp nhận đơn
  //miss case trao đổi với Lâu sau: TTQH ko có FT3 nào thì nút tiếp nhận hiển thị với tất cả các isFT hoặc OFT của trung tâm (các user ko thuộc role trong đơn)
  Widget _buildWaitingAssignAction() {
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundWhite,
        boxShadow: AppBoxShadows.shadowNormal,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              BaseButton(
                text: "Tiếp nhận",
                backgroundColor: CoreColors.white,
                textStyle: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.primary,
                ),
                borderColor: CoreColors.primary,
                onTap: () {
                  onReceiveOrder(context);
                },
              ),
              const SizedBox(height: 8),
              BaseButton(
                text: "Giao/Chuyển việc",
                textStyle: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.backgroundWhite,
                ),
                onTap: () {
                  onAssignWork(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProcessWaitingAction() {
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundWhite,
        boxShadow: AppBoxShadows.shadowNormal,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: BaseButton(
            text: "Đến nơi làm việc",
            textStyle: UITextStyle.body2Medium.copyWith(
              color: BaseColors.backgroundWhite,
            ),
            onTap: () {
              onGoToWork(context);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildProcessingAction() {
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundWhite,
        boxShadow: AppBoxShadows.shadowNormal,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              BaseButton(
                text: "Hoàn thành",
                textStyle: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.backgroundWhite,
                ),
                onTap: () {
                  onDoneOrder(context);
                },
              ),
              const SizedBox(height: 12),
              BaseButton(
                text: "Đề xuất gia hạn thời gian hoàn thành",
                backgroundColor: CoreColors.white,
                textStyle: UITextStyle.body2Medium.copyWith(
                  color: BaseColors.primary,
                ),
                borderColor: CoreColors.primary,
                onTap: () {
                  onExtendSchedule(context);
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCancelExtendSchedule() {
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundWhite,
        boxShadow: AppBoxShadows.shadowNormal,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: BaseButton(
            text: "Hủy đề xuất gia hạn",
            textStyle: UITextStyle.body2Medium.copyWith(
              color: BaseColors.backgroundWhite,
            ),
            onTap: () {
              onCancelExtendSchedule(context);
            },
          ),
        ),
      ),
    );
  }

  Widget _buildReOrder() {
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundWhite,
        boxShadow: AppBoxShadows.shadowNormal,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: BaseButton(
            text: "Hủy đề xuất huỷ",
            textStyle: UITextStyle.body2Medium.copyWith(
              color: BaseColors.backgroundWhite,
            ),
            onTap: () {
              ref.read(detailOrderSingleProvider.notifier).ignoreRequestCancel(
                    requestCancelOrderId: ref
                            .watch(detailOrderSingleProvider)
                            .orderInfo
                            ?.requestCancelOrderId ??
                        "",
                  );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildPaymentAction() {
    return Container(
      decoration: BoxDecoration(
        color: BaseColors.backgroundWhite,
        boxShadow: AppBoxShadows.shadowNormal,
      ),
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: BaseButton(
            text: "Thanh toán",
            textStyle: UITextStyle.body2Medium.copyWith(
              color: BaseColors.backgroundWhite,
            ),
            onTap: () {
              var state = ref.watch(detailOrderSingleProvider);
              final paymentType = state.orderInfo?.paymentInfo?.paymentMethod;

              context.push(
                RouterPaths.debtStatistics,
                extra: DebtStatisticsArguments(
                  orderCode: widget.arguments.orderCode,
                  page: paymentType == PaymentMethod.companyAccount ? 1 : 0,
                ),
              );
            },
          ),
        ),
      ),
    );
  }

  Widget _buildConfirmCommission(BuildContext context) {
    var state = ref.watch(detailOrderSingleProvider);

    String currentUser = GlobalData.instance.userInfo?.username ?? '';
    bool isSubmitChange = false;
    String? createdOrder;
    List<DetailUserInfoEntity> togetherUser = [];

    for (DetailUserInfoEntity userInfo
        in (state.orderInfo?.processUserInfo?.togetherUserInfo ?? [])) {
      if (userInfo.userCode == currentUser &&
          (userInfo.confirmStatus == CommissionConfirmStatus.waitConfirm)) {
        togetherUser.add(userInfo);
      }
      if (userInfo.confirmStatus == CommissionConfirmStatus.rejected) {
        isSubmitChange = true;
      }
      if (userInfo.isCreatedOrder == true) {
        createdOrder = userInfo.userCode ?? '';
      }
    }

    if (isSubmitChange && createdOrder == currentUser) {
      return SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 8,
          ),
          child: BaseButton(
            text: "Cập nhật hoa hồng",
            onTap: () async {
              var state = ref.watch(detailOrderSingleProvider);

              int totalPercent = 0;
              for (int i = 0; i < state.listSellerAndCommission!.length; i++) {
                if ((state.listSellerAndCommission![i].roseRate ?? 0) == 0) {
                  AppDialog.showDialogCenter(
                    context,
                    message: "Vui lòng nhập % hoa hồng cho người bán",
                    status: DialogStatus.error,
                  );
                  return;
                }
                totalPercent = totalPercent +
                    (state.listSellerAndCommission![i].roseRate ?? 0);
              }

              if (totalPercent != 100) {
                AppDialog.showDialogCenter(
                  context,
                  message: "Tổng % hoa hồng của người bán phải bằng 100%",
                  status: DialogStatus.error,
                );
                return;
              }

              await ref
                  .read(detailOrderSingleProvider.notifier)
                  .configCommissionChange();

              if (state.commissionStatus == LoadStatus.success) {
                if (!context.mounted) return;
                AppDialog.showDialogCenter(
                  context,
                  message: "Cập nhật hoa hồng thành công",
                  status: DialogStatus.success,
                );
              }
            },
          ),
        ),
      );
    }

    if (togetherUser.isNotEmpty &&
        state.orderInfo?.status == OrderStatus.registered &&
        state.orderInfo?.orderStatus == OrderSubStatus.commission) {
      return BuildConfirmCommission(
        code: state.orderInfo?.orderCode ?? '',
        percent: togetherUser[0].roseRate ?? 0,
        onConfirm: (status) async {
          await ref
              .read(detailOrderSingleProvider.notifier)
              .changeCommission(status.keyToServer);

          var state = ref.watch(detailOrderSingleProvider);
          if (state.commissionStatus == LoadStatus.success) {
            if (!context.mounted) return;
            AppDialog.showDialogCenter(
              context,
              message: "${status.title} hoa hồng thành công",
              status: DialogStatus.success,
            );
          }
        },
      );
    }

    return const SizedBox();
  }

  void onAssignWork(BuildContext context) {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Giao/Chuyển việc",
      height: MediaQuery.of(context).size.height * 0.65,
      child: AssignOrderView(
        orderInfo: ref.watch(detailOrderSingleProvider).orderInfo!,
        onAssign: (staff, type) async {
          ShowLoadingUtils.instance.turnOn();
          await ref.read(detailOrderSingleProvider.notifier).assignWorker(
                orderCode: widget.arguments.orderCode,
                userCode: staff.username ?? '',
                type: type.keyToServer,
              );

          ShowLoadingUtils.instance.turnOff();
          if (ref.watch(detailOrderSingleProvider).updateStatus ==
              LoadStatus.success) {
            String content = type == AssignType.assign
                ? "Giao việc thành công"
                : "Chuyển việc thành công";
            if (!context.mounted) return;
            AppDialog.showDialogCenter(
              context,
              message: content,
              status: DialogStatus.success,
            );
          }
        },
      ),
    );
  }

  void onReceiveOrder(BuildContext context) async {
    ShowLoadingUtils.instance.turnOn();
    await ref.read(detailOrderSingleProvider.notifier).receiveOrder(
          orderCode: widget.arguments.orderCode,
        );

    ShowLoadingUtils.instance.turnOff();
    if (ref.watch(detailOrderSingleProvider).updateStatus ==
        LoadStatus.success) {
      if (!context.mounted) return;
      AppDialog.showDialogCenter(
        context,
        message: "Tiếp nhận đơn hàng thành công",
        status: DialogStatus.success,
      );
    }
  }

  void onGoToWork(BuildContext context) async {
    var state = ref.watch(detailOrderSingleProvider);

    if ((state.orderInfo?.shippingInfo?.scheduleTime ?? '').isEmpty) {
      AppDialog.showDialogCenter(
        context,
        message: "Vui lòng hẹn lịch khách hàng trước khi đến nơi làm việc",
        status: DialogStatus.error,
      );
      return;
    }

    if (state.orderInfo?.shippingInfo?.scheduleStatus ==
        VerifyScheduleStatus.wait) {
      AppDialog.showDialogCenter(
        context,
        message: "Vui lòng chờ duyệt xác nhận lịch hẹn",
        status: DialogStatus.error,
      );
      return;
    }

    ShowLoadingUtils.instance.turnOn();
    await ref.read(detailOrderSingleProvider.notifier).nextStatusOrder(
          orderCode: widget.arguments.orderCode,
        );

    ShowLoadingUtils.instance.turnOff();
    if (ref.watch(detailOrderSingleProvider).updateStatus ==
        LoadStatus.success) {
      if (!context.mounted) return;
      AppDialog.showDialogCenter(
        context,
        message: "Cập nhật trạng thái đơn hàng thành công",
        status: DialogStatus.success,
      );
    }
  }

  void onReturnOrder(BuildContext context) {
    AppDialog.showDialogConfirm(
      context,
      message: "Bạn chắc chắc muốn trả đơn hàng?",
      onConfirmAction: () async {
        ShowLoadingUtils.instance.turnOn();
        await ref.read(detailOrderSingleProvider.notifier).returnOrder(
              orderCode: widget.arguments.orderCode,
            );

        ShowLoadingUtils.instance.turnOff();
        if (ref.watch(detailOrderSingleProvider).updateStatus ==
            LoadStatus.success) {
          if (!context.mounted) return;
          context.pop(true);
          AppDialog.showDialogCenter(
            context,
            message: "Trả đơn hàng thành công",
            status: DialogStatus.success,
          );
        }
      },
    );
  }

  void onExtendSchedule(BuildContext context) {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Gia hạn KPI triển khai",
      isFlexible: true,
      child: ExtendScheduleView(
        orderCode: widget.arguments.orderCode,
        onSave: (data) async {
          await ref.read(detailOrderSingleProvider.notifier).extendKpiOrder(
                scheduleTime: data.dateSelected!.toIso8601String(),
                note: data.reason,
              );

          if (ref.watch(detailOrderSingleProvider).updateStatus ==
              LoadStatus.success) {
            if (!context.mounted) return;
            AppDialog.showDialogCenter(
              context,
              message: "Gia hạn KPI triển khai thành công",
              status: DialogStatus.success,
            );
          }
        },
      ),
    );
  }

  void onCancelExtendSchedule(BuildContext context) {
    AppDialog.showDialogConfirm(
      context,
      message: "Bạn chắc chắn muốn hủy đề xuất gia hạn?",
      onConfirmAction: () async {
        await ref
            .read(detailOrderSingleProvider.notifier)
            .cancelExtendKpiOrder();

        if (ref.watch(detailOrderSingleProvider).updateStatus ==
            LoadStatus.success) {
          if (!context.mounted) return;
          AppDialog.showDialogCenter(
            context,
            message: "Hủy đề xuất gia hạn thành công",
            status: DialogStatus.success,
          );
        }
      },
    );
  }

  Future<void> onDoneOrder(BuildContext context) async {
    final listImplementer = ref
            .watch(detailOrderSingleProvider)
            .orderInfo
            ?.processUserInfo
            ?.processUser ??
        [];

    final totalPercent = listImplementer.fold<int>(
      0,
      (previousValue, element) => previousValue + (element.roseRate ?? 0),
    );

    if ((totalPercent > 100) || (totalPercent < 100)) {
      AppDialog.showDialogCenter(
        context,
        message: "Tổng % công việc của người thực hiện phải bằng 100%",
        status: DialogStatus.error,
      );
      return;
    }

    await ref.read(detailOrderSingleProvider.notifier).validateOrder(
          orderCode: widget.arguments.orderCode,
        );

    var state = ref.watch(detailOrderSingleProvider);

    if (!context.mounted) return;

    if (!state.isValidOrder) {
      if (state.messageCode == "A03003B038" ||
          state.messageCode == "A03003B039") {
        AppDialog.showDialogConfirm(
          context,
          message: "${state.message}",
          buttonNameConfirm: "Thanh toán",
          onConfirmAction: () {
            context.push(
              RouterPaths.debtStatistics,
              extra: DebtStatisticsArguments(
                page: state.messageCode == "A03003B039" ? 1 : 0,
              ),
            );
          },
        );
      } else if (state.messageCode == "A03003B037" ||
          state.messageCode == "A03003B040") {
        AppDialog.showDialogConfirm(
          context,
          message: "${state.message}",
          buttonNameConfirm: "Thực hiện",
          onConfirmAction: () {
            context.push(
              RouterPaths.warningOrder,
              extra: WarningOrderArguments(
                pageIndex: state.messageCode == "A03003B037" ? 0 : 1,
              ),
            );
          },
        );
      } else {
        ErrorDialog.showErrorDialog(
          (state.messageCode ?? '').isNotEmpty
              ? "${state.messageCode} - ${state.message}"
              : "Đã có lỗi xảy ra!",
        );
      }
      return;
    }

    if (!context.mounted) return;
    var orderInfo = state.orderInfo;

    ///Nếu là đơn đối tác khác (loại liên thông aio-hs và đơn thương mại điện tử) thì chỉ cho chụp ảnh file ký, các đơn còn lại chỉ cho ký OTP
    final isHardSign = (orderInfo?.isOrderPartner ?? false) &&
        !(orderInfo?.isConnectAio ?? false) &&
        !(orderInfo?.isEcommerceAio ?? false);

    if (orderInfo?.isOrderPartner ?? false) {
      AppBottomSheet.showNormalBottomSheet(
        context,
        title: "Biên bản nghiệm thu",
        height: MediaQuery.of(context).size.height * 0.5,
        child: AcceptanceContractView(
          orderCode: widget.arguments.orderCode,
          onlyHard: isHardSign,
          onlySignOtp: !isHardSign,
          onConfirm: (images) async {
            if (!isHardSign) {
              onSignContract(context);
            } else {
              await ref.read(detailOrderSingleProvider.notifier).confirmSignV2(
                    orderCode: widget.arguments.orderCode,
                    images: images ?? [],
                  );

              if (!context.mounted) return;
              final isSignSuccess =
                  ref.watch(detailOrderSingleProvider).updateStatus ==
                      LoadStatus.success;

              if (isSignSuccess) {
                context.pop();
                AppDialog.showDialogCenter(
                  context,
                  message: "Hoàn thành đơn thành công!",
                  status: DialogStatus.success,
                );
              }
            }
          },
        ),
      );
    } else {
      if (state.orderInfo?.orderType == OrderType.combo) {
        if (state.orderInfo?.paymentInfo?.paymentMethod ==
            PaymentMethod.companyAccount) {
          AppBottomSheet.showNormalBottomSheet(
            context,
            title: "Biên bản nghiệm thu",
            height: MediaQuery.of(context).size.height * 0.6,
            child: UploadOrderReportView(
              onConfirm: (type, images) {
                if (type == ReportOrderType.file) {
                  ref
                      .read(detailOrderSingleProvider.notifier)
                      .uploadReportOrder(
                        images: images,
                      );
                } else {
                  onShowInfoBuySupplies(
                    context,
                  );
                }
              },
            ),
          );
        } else {
          onShowInfoBuySupplies(
            context,
          );
        }
      } else if (state.orderInfo?.showDialogBuySupplyForCustomer ?? false) {
        onShowInfoBuySupplies(
          context,
        );
      } else {
        onSignContract(context);
      }
    }
  }

  void onConfirmBuySuppliesForCustomer(
    BuildContext context, {
    required BuySuppliesForCustomerBody arguments,
  }) async {
    ShowLoadingUtils.instance.turnOn();
    await ref
        .read(detailOrderSingleProvider.notifier)
        .confirmBySuppliesForCustomer(
          orderCode: widget.arguments.orderCode,
          body: arguments,
        );
    ShowLoadingUtils.instance.turnOff();
    final isSignSuccess =
        ref.watch(detailOrderSingleProvider).updateStatus == LoadStatus.success;
    if (!context.mounted) return;
    if (isSignSuccess) {
      AppDialog.showDialogCenter(
        context,
        message: "Đã lưu lại thông tin",
        status: DialogStatus.success,
        onConfirm: () {
          onSignContract(
            context,
          );
        },
        barrierDismissible: true,
      );
    }
  }

  void onSignContract(
    BuildContext context,
  ) {
    context.push(
      RouterPaths.signContractOrder,
      extra: SignContractOrderArguments(
        orderCode: widget.arguments.orderCode,
        orderEntity: ref.watch(detailOrderSingleProvider).orderInfo!,
        onCompleted: (SignAcceptanceBody signData) async {
          ShowLoadingUtils.instance.turnOn();
          await ref.read(detailOrderSingleProvider.notifier).confirmSign(
                orderCode: widget.arguments.orderCode,
                otp: signData.otp,
                base64customer: signData.customerSignature,
                base64employee: signData.staffSignature,
              );

          ShowLoadingUtils.instance.turnOff();
          final isSignSuccess =
              ref.watch(detailOrderSingleProvider).updateStatus ==
                  LoadStatus.success;

          if (isSignSuccess) {
            if (!context.mounted) return;
            context.pop();
            AppDialog.showDialogCenter(
              context,
              message: "Ký nghiệm thu thành công!",
              status: DialogStatus.success,
            );
            AppDialog.showDialogFeedback(
              context,
              source: "Màn hình nghiệm thu đơn thành công",
            );
          }
        },
      ),
    );
  }

  void onCallCustomer() async {
    var state = ref.watch(detailOrderSingleProvider);

    if (GlobalData.instance.userInfo?.isMobileCall ?? false) {
      final result = await MobileCallUtils.startCall(
        callerPhoneNumber: GlobalData.instance.userInfo?.phoneNumber ?? "",
        callerName: GlobalData.instance.userInfo?.fullName ?? "",
        customerNumber: state.orderInfo?.shippingInfo?.receiverPhone ?? "",
        customerName: state.orderInfo?.shippingInfo?.receiverName ?? "",
        code: state.orderInfo?.orderCode ?? "",
        content: state.orderInfo?.shippingInfo?.receiverAddress ?? "",
        workName: state.orderInfo?.orderName ?? '',
      );

      if (result != null) {
        await ref.read(detailOrderSingleProvider.notifier).saveLogMobileCall(
              body: result.convertMobileCallBody(
                mobileCallType: MobileCallType.order,
                referenceId: state.orderInfo?.orderCode,
                customerNumber:
                    state.orderInfo?.shippingInfo?.receiverPhone ?? "",
              ),
            );
      }
    } else {
      await AppUtils.callPhoneNumber(
        phoneNumber: state.orderInfo?.shippingInfo?.receiverPhone ?? "",
      );
      ref.read(detailOrderSingleProvider.notifier).updateCall();
    }

    ref
        .read(detailOrderSingleProvider.notifier)
        .insertFirstCallTime(orderCode: state.orderInfo?.orderCode ?? "");
    refreshData();
  }

  void onShowInfoBuySupplies(BuildContext context) {
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Thông tin mua hộ",
      isFlexible: true,
      child: BuySuppliesForCustomerSheet(
        onConfirm: (arguments) {
          onConfirmBuySuppliesForCustomer(
            context,
            arguments: arguments,
          );
        },
      ),
    );
  }
}
