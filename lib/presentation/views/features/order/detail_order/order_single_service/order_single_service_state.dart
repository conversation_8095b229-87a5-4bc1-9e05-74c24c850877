part of 'order_single_service_view_model.dart';

class OrderSingleServiceState extends Equatable {
  final LoadStatus loadDataStatus;
  final LoadStatus updateStatus;
  final LoadStatus commissionStatus;
  final LoadStatus uploadImageStatus;
  final LoadStatus validateStatus;
  final DetailOrderEntity? orderInfo;
  final CustomerInformationEntity? customerInformation;
  final String? message;
  final String? messageCode;
  final List<DetailOrderItemEntity>? listService;
  final List<DetailOrderItemEntity>? listSupply;
  final List<String>? listImage;
  final bool isValidOrder;
  final WarrantyOrderTypeEnum warrantyOrderType;
  final bool? isFromCommission;
  final ServiceInfoEntity? serviceInfo;
  final List<DetailUserInfoEntity>? listSellerAndCommission;
  final List<String>? imageBuySupplies;

  const OrderSingleServiceState({
    this.loadDataStatus = LoadStatus.initial,
    this.updateStatus = LoadStatus.initial,
    this.commissionStatus = LoadStatus.initial,
    this.uploadImageStatus = LoadStatus.initial,
    this.validateStatus = LoadStatus.initial,
    this.orderInfo,
    this.message,
    this.messageCode,
    this.customerInformation,
    this.listService,
    this.listSupply,
    this.listImage,
    this.isValidOrder = false,
    this.warrantyOrderType = WarrantyOrderTypeEnum.inWarranty,
    this.isFromCommission,
    this.serviceInfo,
    this.listSellerAndCommission,
    this.imageBuySupplies,
  });

  @override
  List<Object?> get props => [
        loadDataStatus,
        updateStatus,
        commissionStatus,
        uploadImageStatus,
        validateStatus,
        orderInfo,
        message,
        messageCode,
        customerInformation,
        listService,
        listSupply,
        listImage,
        isValidOrder,
        warrantyOrderType,
        isFromCommission,
        serviceInfo,
        listSellerAndCommission,
    imageBuySupplies,
      ];

  OrderSingleServiceState copyWith({
    LoadStatus? loadDataStatus,
    LoadStatus? updateStatus,
    LoadStatus? commissionStatus,
    LoadStatus? uploadImageStatus,
    LoadStatus? validateStatus,
    DetailOrderEntity? orderInfo,
    String? message,
    String? messageCode,
    CustomerInformationEntity? customerInformation,
    List<DetailOrderItemEntity>? listService,
    List<DetailOrderItemEntity>? listSupply,
    List<String>? listImage,
    bool? isValidOrder,
    WarrantyOrderTypeEnum? warrantyOrderType,
    bool? isFromCommission,
    ServiceInfoEntity? serviceInfo,
    List<DetailUserInfoEntity>? listSellerAndCommission,
    List<String>? imageBuySupplies,

  }) {
    return OrderSingleServiceState(
      loadDataStatus: loadDataStatus ?? this.loadDataStatus,
      commissionStatus: commissionStatus ?? this.commissionStatus,
      uploadImageStatus: uploadImageStatus ?? this.uploadImageStatus,
      validateStatus: validateStatus ?? this.validateStatus,
      orderInfo: orderInfo ?? this.orderInfo,
      message: message ?? this.message,
      messageCode: messageCode ?? this.messageCode,
      customerInformation: customerInformation ?? this.customerInformation,
      updateStatus: updateStatus ?? this.updateStatus,
      listService: listService ?? this.listService,
      listSupply: listSupply ?? this.listSupply,
      listImage: listImage ?? this.listImage,
      isValidOrder: isValidOrder ?? this.isValidOrder,
      warrantyOrderType: warrantyOrderType ?? this.warrantyOrderType,
      isFromCommission: isFromCommission ?? this.isFromCommission,
      serviceInfo: serviceInfo ?? this.serviceInfo,
      listSellerAndCommission:
          listSellerAndCommission ?? this.listSellerAndCommission,
      imageBuySupplies: imageBuySupplies ?? this.imageBuySupplies,
    );
  }
}
