import 'dart:io';

import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/data/repositories/resource_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/body/assign_worker_body.dart';
import 'package:vcc/domain/body/buy_supplies_for_customer_body.dart';
import 'package:vcc/domain/body/cancel_extend_kpi_order_body.dart';
import 'package:vcc/domain/body/change_commission_body.dart';
import 'package:vcc/domain/body/commission_partner_body.dart';
import 'package:vcc/domain/body/extend_kpi_order_body.dart';
import 'package:vcc/domain/body/mobile_call_body.dart';
import 'package:vcc/domain/body/service_order_body.dart';
import 'package:vcc/domain/body/update_commission_body.dart';
import 'package:vcc/domain/body/update_order_body.dart';
import 'package:vcc/domain/body/upload_report_order_body.dart';
import 'package:vcc/domain/entities/order/customer_information_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_item_entity.dart';
import 'package:vcc/domain/entities/order/detail_user_info_entity.dart';
import 'package:vcc/domain/entities/order/original_price_supply_entity.dart';
import 'package:vcc/domain/entities/order/serial_info_entity.dart';
import 'package:vcc/domain/entities/order/supply_entity.dart';
import 'package:vcc/domain/entities/order/warning_alert_entity.dart';
import 'package:vcc/domain/entities/service/service_info_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/warehouse_supply_type.dart';
import 'package:vcc/domain/enums/warranty_order_type.dart';
import 'package:vcc/domain/params/price_order_param.dart';
import 'package:vcc/domain/params/product_order_param.dart';
import 'package:vcc/utils/log_utils.dart';

part 'order_single_service_state.dart';

final detailOrderSingleProvider = StateNotifierProvider.autoDispose<
    OrderSingleServiceViewModel,
    OrderSingleServiceState>((ref) => OrderSingleServiceViewModel(ref: ref));

class OrderSingleServiceViewModel
    extends StateNotifier<OrderSingleServiceState> {
  final Ref ref;

  OrderSingleServiceViewModel({
    required this.ref,
  }) : super(const OrderSingleServiceState());

  void reloadUI() {
    state = state.copyWith();
  }

  Future<void> getDetailOrder(
    String orderCode, {
    bool? isFromCommission,
  }) async {
    state = state.copyWith(
      loadDataStatus: LoadStatus.loading,
      isFromCommission: isFromCommission,
    );

    try {
      final result = await appLocator<OrderRepository>()
          .getDetailOrder(orderCode: orderCode);

      await result?.when(
        success: (data) async {
          if ((data.orderItems ?? []).isNotEmpty) {
            List<DetailOrderItemEntity> listService = [];
            List<DetailOrderItemEntity> listSupply = [];

            for (int i = 0; i < data.orderItems!.length; i++) {
              if (data.orderItems![i].type == OrderType.supply) {
                listSupply.add(data.orderItems![i]);
              } else {
                listService.add(data.orderItems![i]);
              }
            }

            for (var element in listSupply) {
              if (element.getWmsType == WarehouseSupplyType.both) {
                element.wmsTypeDisplay = (element.maxItem != null)
                    ? (element.quantity ?? 1) > (element.maxItem ?? 0)
                        ? WarehouseSupplyType.wms.keyToServer
                        : WarehouseSupplyType.notWms.keyToServer
                    : element.wmsTypeDisplay;
              }
            }

            state = state.copyWith(
              listService: listService,
              listSupply: listSupply,
            );
          }

          List<DetailUserInfoEntity>? togetherUserInfo;
          DetailUserInfoEntity? createdUser;

          if ((data.processUserInfo?.togetherUserInfo ?? []).isNotEmpty) {
            final listUser = data.processUserInfo!.togetherUserInfo!;

            for (int i = 0; i < listUser.length; i++) {
              if (listUser[i].isCreatedOrder == true) {
                createdUser = listUser[i];
              }
              if (listUser[i].roseRate == 0) {
                listUser.removeAt(i);
              }
            }
            togetherUserInfo = listUser;
          }

          data.processUserInfo?.togetherUserInfo = togetherUserInfo;
          data.processUserInfo?.createdUser = createdUser;

          if (isFromCommission != null) {
            data.isFromCommission = isFromCommission;
          }

          state = state.copyWith(
            orderInfo: data,
            listSellerAndCommission: data.processUserInfo?.togetherUserInfo,
            listImage: data.images,
            loadDataStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadDataStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadDataStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> refreshOrder() async {
    try {
      final result = await appLocator<OrderRepository>().getDetailOrder(
        orderCode: state.orderInfo?.orderCode ?? "",
      );

      await result?.when(
        success: (data) async {
          if ((data.orderItems ?? []).isNotEmpty) {
            List<DetailOrderItemEntity> listService = [];
            List<DetailOrderItemEntity> listSupply = [];

            for (int i = 0; i < data.orderItems!.length; i++) {
              if (data.orderItems![i].type == OrderType.supply) {
                listSupply.add(data.orderItems![i]);
              } else {
                listService.add(data.orderItems![i]);
              }
            }

            for (var element in listSupply) {
              if (element.getWmsType == WarehouseSupplyType.both) {
                element.wmsTypeDisplay = (element.maxItem != null)
                    ? (element.quantity ?? 1) > (element.maxItem ?? 0)
                        ? WarehouseSupplyType.wms.keyToServer
                        : WarehouseSupplyType.notWms.keyToServer
                    : element.wmsTypeDisplay;
              }
            }

            state = state.copyWith(
              listService: listService,
              listSupply: listSupply,
            );
          }

          List<DetailUserInfoEntity>? togetherUserInfo;
          DetailUserInfoEntity? createdUser;

          if ((data.processUserInfo?.togetherUserInfo ?? []).isNotEmpty) {
            final listUser = data.processUserInfo!.togetherUserInfo!;

            for (int i = 0; i < listUser.length; i++) {
              if (listUser[i].isCreatedOrder == true) {
                createdUser = listUser[i];
              }
              if (listUser[i].roseRate == 0) {
                listUser.removeAt(i);
              }
            }
            togetherUserInfo = listUser;
          }

          data.processUserInfo?.togetherUserInfo = togetherUserInfo;
          data.processUserInfo?.createdUser = createdUser;
          data.isFromCommission = state.isFromCommission;

          state = state.copyWith(
            listImage: data.images,
            orderInfo: data,
            listSellerAndCommission: data.processUserInfo?.togetherUserInfo,
          );
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
          );
        },
      );
    } catch (error) {
      LogUtils.e("Error: $error");
    }
  }

  void onChangeSellers(List<InternalStaffEntity> listUser) {
    state = state.copyWith(
      listSellerAndCommission: listUser.map((e) {
        return DetailUserInfoEntity(
          name: e.fullName,
          userCode: e.username,
          roseRate: e.percentBonus ?? 0,
          userPhoneNumber: e.phoneNumber,
          isCreatedOrder: e.isCreatedOrder,
        );
      }).toList(),
    );
  }

  void deleteSeller(int index) {
    var listUser = state.listSellerAndCommission!;

    listUser.removeAt(index);

    state = state.copyWith(
      listSellerAndCommission: listUser,
    );
  }

  Future<void> getCustomerInformation(String orderCode) async {
    state = state.copyWith(
      loadDataStatus: LoadStatus.loading,
    );
    final result = await appLocator<OrderRepository>()
        .getCustomerInformation(orderCode: orderCode);

    try {
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            customerInformation: data,
            loadDataStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            loadDataStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadDataStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> assignWorker({
    required String orderCode,
    required String userCode,
    String? type,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    final result = await appLocator<OrderRepository>().assignWorker(
      body: AssignWorkerBody(
        orderCode: orderCode,
        userCode: userCode,
        type: type,
      ),
    );

    try {
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );

          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  void changeQuantity({
    required int indexItem,
    required num quantity,
  }) async {
    final listService = state.listService!;
    final item = listService[indexItem];
    final oldQuantity = listService[indexItem].quantity;

    bool isRemoveItem = false;

    if (quantity == 0) {
      listService.removeAt(indexItem);
      isRemoveItem = true;
    } else {
      listService[indexItem].quantity = quantity;
    }

    await editOrder();
    if (state.updateStatus != LoadStatus.success) {
      if (isRemoveItem) {
        listService.insert(indexItem, item);
      } else {
        item.quantity = oldQuantity;
        listService[indexItem] = item;
      }
    }

    state = state.copyWith(
      listService: listService,
    );
  }

  void changeSupplyQuantity({
    required int indexItem,
    required num quantity,
  }) async {
    final listSupply = state.listSupply!;
    final item = listSupply[indexItem];
    final oldQuantity = item.quantity;

    bool isRemoveItem = false;

    if (quantity == 0 && (item.code ?? '').isNotEmpty) {
      listSupply.removeAt(indexItem);
      isRemoveItem = true;
    } else {
      item.quantity = quantity;

      if (item.getWmsType == WarehouseSupplyType.both) {
        item.wmsTypeDisplay = (item.maxItem != null)
            ? (item.quantity ?? 1) > (item.maxItem ?? 0)
                ? WarehouseSupplyType.wms.keyToServer
                : WarehouseSupplyType.notWms.keyToServer
            : item.wmsTypeDisplay;
      }

      listSupply[indexItem] = item;
    }

    await editOrder();
    if (state.updateStatus != LoadStatus.success) {
      if (isRemoveItem) {
        listSupply.insert(indexItem, item);
      } else {
        item.quantity = oldQuantity;
        listSupply[indexItem] = item;
      }
    }

    state = state.copyWith(
      listSupply: listSupply,
    );
  }

  void changeSupplyOriginalPrice({
    required int indexItem,
    required int price,
    required double originPrice,
  }) async {
    final listSupply = state.listSupply!;
    final item = listSupply[indexItem];
    final oldOriginPrice = item.originalPrice;
    final oldPrice = item.price;

    item.rootPrice = originPrice;
    item.price = price;
    listSupply[indexItem] = item;

    await editOrder();
    if (state.updateStatus != LoadStatus.success) {
      item.rootPrice = oldOriginPrice;
      item.price = oldPrice;
      listSupply[indexItem] = item;
    }

    state = state.copyWith(
      listSupply: listSupply,
    );
  }

  void changeSupplyType({
    required int indexItem,
    required String type,
  }) async {
    final listSupply = state.listSupply!;
    final item = listSupply[indexItem];
    final oldType = item.supplyType;

    item.supplyType = type;
    listSupply[indexItem] = item;

    await editOrder();
    if (state.updateStatus != LoadStatus.success) {
      item.supplyType = oldType;
      listSupply[indexItem] = item;
    }

    state = state.copyWith(
      listSupply: listSupply,
    );
  }

  void changeSupplyVat({
    required int indexItem,
    required int vat,
  }) async {
    final listSupply = state.listSupply!;
    final item = listSupply[indexItem];
    final oldVat = item.vat;

    item.vat = vat;
    listSupply[indexItem] = item;

    await editOrder();
    if (state.updateStatus != LoadStatus.success) {
      item.vat = oldVat;
      listSupply[indexItem] = item;
    }

    state = state.copyWith(
      listSupply: listSupply,
    );
  }

  void selectPriceOfSupply({
    required int indexItem,
    List<OriginalPriceSupplyEntity>? listData,
    num? quantity,
  }) async {
    final listSupply = state.listSupply!;
    final item = listSupply[indexItem];
    final oldSerials = item.serialInfo;

    item.serialInfo = listData?.map((e) {
      return SerialInfoEntity(
        serial: e.serial,
        originalPrice: e.price,
        quantity: e.quantity.toDouble(),
      );
    }).toList();

    if (quantity != null) {
      item.quantity = quantity;
    }

    listSupply[indexItem] = item;

    await editOrder();
    if (state.updateStatus != LoadStatus.success) {
      item.serialInfo = oldSerials;
      listSupply[indexItem] = item;
    }

    state = state.copyWith(
      listSupply: listSupply,
    );
  }

  Future<void> receiveOrder({
    required String orderCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().receiveOrder(
        orderCode: orderCode,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> nextStatusOrder({
    required String orderCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().updateOrder(
        body: UpdateOrderBody(
          orderCode: orderCode,
        ),
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> returnOrder({
    required String orderCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>()
          .cancelReceive(orderCode: orderCode);
      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> extendKpiOrder({
    required String scheduleTime,
    String? note,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().extendKpiOrder(
        body: ExtendKpiOrderBody(
          orderCode: state.orderInfo?.orderCode ?? "",
          note: note,
          scheduleTime: scheduleTime,
        ),
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );

          getDetailOrder(
            state.orderInfo?.orderCode ?? "",
          );
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> cancelExtendKpiOrder() async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().cancelExtendKpiOrder(
        body: CancelExtendKpiOrderBody(
          orderCode: state.orderInfo?.orderCode ?? "",
          requestId: state.orderInfo?.requestExtendKpiId ?? "",
        ),
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );

          getDetailOrder(
            state.orderInfo?.orderCode ?? "",
          );
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> addServiceOrder(
    List<ServiceInfoEntity> services,
  ) async {
    final listService = state.listService ?? [];
    final oldListService = List<DetailOrderItemEntity>.from(listService);

    for (var newService in services) {
      bool serviceExists = false;

      for (var existingService in listService) {
        if (existingService.code == newService.code) {
          existingService.quantity =
              (existingService.quantity ?? 0) + newService.quantity;
          serviceExists = true;
          break;
        }
      }

      if (!serviceExists) {
        listService.add(
          DetailOrderItemEntity(
            code: newService.code,
            name: newService.name,
            quantity: newService.quantity,
            price: newService.price,
            vat: newService.vat,
            originalPrice: (newService.originalPrice ?? 0).toDouble(),
            discount: newService.discount,
            defaultService: newService.isRequired,
            outsourcePrice: newService.outsourcePrice,
          ),
        );
      }
    }

    await editOrder();
    if (state.updateStatus != LoadStatus.success) {
      state = state.copyWith(
        listService: oldListService,
      );
    } else {
      state = state.copyWith(
        listService: listService,
      );
    }
  }

  Future<void> changeSingleServiceOrderType(
    List<ServiceInfoEntity> services,
  ) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().editServiceOrder(
        body: ServiceOrderBody(
          orderCode: state.orderInfo?.orderCode ?? "",
          orderType: OrderType.service.keyToServer,
          servicesInfo: services,
        ),
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> addSuppliesOrder(
    List<SupplyEntity> supplies,
  ) async {
    final listSupply = state.listSupply ?? [];
    final oldListService = List<DetailOrderItemEntity>.from(listSupply);

    for (var newSupply in supplies) {
      bool supplyExists = false;

      for (var existingSupply in listSupply) {
        if (existingSupply.code == newSupply.code) {
          existingSupply.quantity =
              (existingSupply.quantity ?? 0) + newSupply.quantity;
          supplyExists = true;
          break;
        }
      }

      if (!supplyExists) {
        listSupply.add(
          DetailOrderItemEntity(
            code: newSupply.code,
            name: newSupply.name,
            quantity: newSupply.quantity,
            price: newSupply.price,
            vat: newSupply.vat,
            originalPrice: newSupply.originalPrice,
            wmsType: newSupply.wmsType,
          ),
        );
      }
    }

    await editOrder();
    if (state.updateStatus != LoadStatus.success) {
      state = state.copyWith(
        listSupply: oldListService,
      );
    } else {
      state = state.copyWith(
        listSupply: listSupply,
      );
    }
  }

  Future<void> editOrder({
    String? voucherCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      String? voucherTemp =
          (state.orderInfo?.paymentInfo?.couponInfo ?? []).isNotEmpty
              ? state.orderInfo?.paymentInfo?.couponInfo![0].couponCode
              : null;

      var listSupply = state.listSupply?.map((e) {
        return SupplyEntity(
          code: e.code,
          quantity: e.quantity ?? 1,
          price: e.price,
          outsourcePrice: e.outsourcePrice,
          originalPrice: (e.rootPrice ?? 0) > 0 ? e.rootPrice : e.originalPrice,
          supplyType: e.supplyType,
          vat: e.vat,
          wmsType: e.supplyType ?? e.wmsType,
          serialInfo: e.serialInfo,
        );
      }).toList();

      final body = ServiceOrderBody(
        orderCode: state.orderInfo?.orderCode ?? "",
        packageCode: state.orderInfo?.packageCode,
        comboCode: state.orderInfo?.packageCode,
        orderType:
            (state.orderInfo?.orderType ?? OrderType.service).keyToServer,
        servicesInfo: state.listService?.map((e) {
          return ServiceInfoEntity(
            code: e.code,
            quantity: e.quantity ?? 1,
            price: e.price,
            outsourcePrice: e.outsourcePrice,
            name: e.name,
          );
        }).toList(),
        suppliesInfo: listSupply,
        couponCode: voucherCode ?? voucherTemp,
      );

      final result = await appLocator<OrderRepository>().editServiceOrder(
        body: body,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> updateImplementorOrder({
    List<InternalStaffEntity>? listData,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final body = ServiceOrderBody(
        orderCode: state.orderInfo?.orderCode ?? "",
        orderType: OrderType.service.keyToServer,
        processUserInfo: listData?.map((e) {
          return DetailUserInfoEntity(
            name: e.fullName,
            userCode: e.username,
            roseRate: e.percentBonus ?? 0,
            userPhoneNumber: e.phoneNumber,
          );
        }).toList(),
      );

      final result = await appLocator<OrderRepository>().editServiceOrder(
        body: body,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> deleteImplementorOrder(int index) async {
    try {
      final listData = state.orderInfo?.processUserInfo?.processUser ?? [];
      listData.removeAt(index);

      final result = await appLocator<OrderRepository>().editServiceOrder(
        body: ServiceOrderBody(
          orderCode: state.orderInfo?.orderCode ?? "",
          orderType: OrderType.service.keyToServer,
          processUserInfo: listData,
        ),
      );

      result?.when(
        success: (data) async {
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            message: err.message,
          );
          LogUtils.e("Error: $err");
        },
      );
    } catch (e) {
      LogUtils.e("Error: $e");
    }
  }

  Future<void> ignoreRequestCancel({
    required String requestCancelOrderId,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().ignoreRequestCancel(
        requestCancelOrderId: requestCancelOrderId,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );

          getDetailOrder(state.orderInfo?.orderCode ?? "");
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> cancelSchedule({
    required String orderCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().cancelSchedule(
        orderCode: orderCode,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          getDetailOrder(orderCode);
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> addImageOrder(
    File file,
  ) async {
    state = state.copyWith(
      uploadImageStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().uploadOrderImage(
        file: file,
        orderCode: state.orderInfo?.orderCode ?? "",
      );

      List<String> images = state.listImage ?? [];

      await result?.when(
        success: (data) async {
          images.addAll(data);

          state = state.copyWith(
            uploadImageStatus: LoadStatus.success,
            listImage: images,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            uploadImageStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        uploadImageStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> deleteImageOrder({
    required int index,
  }) async {
    state = state.copyWith(
      uploadImageStatus: LoadStatus.loading,
    );

    try {
      final image = state.listImage![index];

      final result = await appLocator<OrderRepository>().deleteOrderImage(
        imgUrl: image,
        orderCode: state.orderInfo?.orderCode ?? "",
      );

      await result?.when(
        success: (data) async {
          List<String> listImage = state.listImage ?? [];
          listImage.remove(image);

          state = state.copyWith(
            listImage: listImage,
            uploadImageStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            uploadImageStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        uploadImageStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> confirmSign({
    required String orderCode,
    String? base64employee,
    String? base64customer,
    String? otp,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().signAcceptance(
        orderCode: orderCode,
        staffSignature: base64employee,
        customerSignature: base64customer,
        otp: otp,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> confirmSignV2({
    required String orderCode,
    required List<String> images,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().signAcceptanceV2(
        orderCode: orderCode,
        images: images,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> calculateShippingCost({
    required int distance,
    required String orderCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().calculateShippingCost(
        distance: distance,
        orderCode: orderCode,
      );
      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> changeWarrantyOrderType(WarrantyOrderTypeEnum value) async {
    await updateWarrantyStatus(
      hasWarranty: value == WarrantyOrderTypeEnum.inWarranty,
      orderCode: state.orderInfo?.orderCode ?? "",
    );

    state = state.copyWith(
      warrantyOrderType: value,
    );
  }

  Future<void> updateWarrantyStatus({
    required bool hasWarranty,
    required String orderCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().calculateShippingCost(
        hasWarranty: hasWarranty,
        orderCode: orderCode,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          getDetailOrder(orderCode);
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future getPromotionPrice({
    String? couponCode,
  }) async {
    try {
      final servicesInfo = state.listService?.map((e) {
        return ProductsInfoParam(
          code: e.code,
          quantity: e.quantity,
        );
      }).toList();

      final suppliesInfo = state.listSupply?.map((e) {
        return SupplyEntity(
          code: e.code,
          quantity: e.quantity ?? 1,
        );
      }).toList();

      final userInfo = state.orderInfo?.shippingInfo;

      final result = await appLocator<OrderRepository>().getPriceOrder(
        param: PriceOrderParam(
          couponCode: couponCode,
          phoneNumber: userInfo?.customerPhone ?? "",
          startTime: userInfo?.startTime ?? "",
          districtCode: userInfo?.districtCode ?? "",
          provinceCode: userInfo?.provinceCode,
          servicesInfo: servicesInfo,
          suppliesInfo: suppliesInfo,
          packageCode: state.orderInfo?.packageCode,
        ),
      );

      int? price;
      await result?.when(
        success: (data) async {
          price = data.discountAmount ?? 0;
        },
      );

      return price;
    } catch (error) {
      return null;
    }
  }

  Future<void> getServiceInfo(String swapCode) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().getServiceInfo(
        code: swapCode,
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            serviceInfo: data,
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> getPackageConfig({
    required String packageCode,
  }) async {
    try {
      final config = await appLocator<OrderRepository>().getPackageConfig(
        code: packageCode,
        startTime: state.orderInfo?.shippingInfo?.startTime ?? "",
        provinceCode: state.orderInfo?.shippingInfo?.provinceCode ?? "",
        districtCode: state.orderInfo?.shippingInfo?.districtCode,
      );

      await config?.when(
        success: (data) async {
          final listService = data.services ?? [];
          final listSupply = data.supplies ?? [];

          for (var element in listService) {
            if (element.quantity < (element.minItem ?? 0)) {
              element.quantity = element.minItem!;
            }
          }

          if (data.supplies != null) {
            for (var element in listSupply) {
              if (element.quantity < (element.minItem ?? 0)) {
                element.quantity = element.minItem!;
              }
            }
          }

          changePackageOrder(
            services: listService,
            supplies: listSupply,
            packageCode: packageCode,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        message: 'Có lỗi xảy ra',
      );
    }
  }

  void getSuppliesComboInfo({required String code}) async {
    try {
      final result =
          await appLocator<OrderRepository>().getDetailSuppliesComboInfo(
        comboCode: code,
      );
      await result?.when(
        success: (data) async {
          changePackageOrder(
            services: data.services,
            supplies: data.supplies,
            packageCode: code,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
          );
        },
      );
    } catch (e) {
      state = state.copyWith(
        message: 'Có lỗi xảy ra',
      );
    }
  }

  void changePackageOrder({
    List<ServiceInfoEntity>? services,
    List<SupplyEntity>? supplies,
    String? packageCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().editServiceOrder(
        body: ServiceOrderBody(
          orderCode: state.orderInfo?.orderCode ?? "",
          orderType: OrderType.package.keyToServer,
          packageCode: packageCode,
          servicesInfo: services,
          suppliesInfo: supplies,
        ),
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> completeOrder() async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    final result = await appLocator<OrderRepository>().completeOrder(
      orderCode: state.orderInfo?.orderCode ?? "",
    );

    try {
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<bool> saveLogMobileCall({
    required MobileCallBody body,
  }) async {
    try {
      final result = await appLocator<ResourceRepository>().saveLogMobileCall(
        body: body,
      );

      bool checkResult = false;

      await result?.when(
        success: (data) async {
          checkResult = true;
        },
      );
      return checkResult;
    } catch (error) {
      return false;
    }
  }

  void insertFirstCallTime({
    required String orderCode,
  }) async {
    try {
      await appLocator<OrderRepository>().updateCall(
        orderCode: orderCode,
      );
    } catch (error) {}
  }

  void updateCall() async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().updateCall(
        orderCode: state.orderInfo?.orderCode ?? "",
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> validateOrder({
    required String orderCode,
  }) async {
    state = state.copyWith(
      validateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().validateOrder(
        orderCode: orderCode,
        isShowError: false,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            isValidOrder: true,
            validateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            isValidOrder: false,
            message: err.message,
            messageCode: err.code,
            validateStatus: LoadStatus.failure,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        isValidOrder: false,
        validateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> uploadReportOrder({
    List<String>? images,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().uploadReportOrder(
        body: UploadReportOrderBody(
            orderCode: state.orderInfo?.orderCode, images: images),
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> materialRequest({
    required String orderCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<OrderRepository>().materialRequest(
        orderCode: orderCode,
      );

      result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
          getDetailOrder(orderCode);
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }

  void initWarrantyOrderType() {
    if (state.orderInfo?.orderType == OrderType.partnerWarrantyService) {
      state = state.copyWith(
        warrantyOrderType: WarrantyOrderTypeEnum.inWarranty,
      );
    } else if (state.orderInfo?.orderType ==
        OrderType.partnerNotWarrantyService) {
      state = state.copyWith(
        warrantyOrderType: WarrantyOrderTypeEnum.outOfWarranty,
      );
    }
  }

  Future<WarningAlertEntity?> checkWarningMobileCall(
    String orderCode,
  ) async {
    try {
      final result = await appLocator<OrderRepository>().showWarningMobileCall(
        orderCode: orderCode,
      );

      WarningAlertEntity? warningInfo;

      await result?.when(
        success: (data) async {
          warningInfo = data;
        },
      );

      return warningInfo;
    } catch (error) {
      state = state.copyWith(
        isValidOrder: false,
      );
    }
    return null;
  }

  Future<void> changeCommission(
    String confirmStatus,
  ) async {
    state = state.copyWith(
      commissionStatus: LoadStatus.loading,
    );

    try {
      var result = await appLocator<OrderRepository>().changeConfirmCommission(
        body: ChangeCommissionBody(
          referenceCode: state.orderInfo?.orderCode ?? "",
          confirmStatus: confirmStatus,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            commissionStatus: LoadStatus.success,
          );

          refreshOrder();
        },
        error: (err) async {
          state = state.copyWith(
            commissionStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        commissionStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> configCommissionChange() async {
    state = state.copyWith(
      commissionStatus: LoadStatus.success,
    );

    try {
      List<CommissionPartnerBody> data = [];
      for (DetailUserInfoEntity userInfo
          in (state.listSellerAndCommission ?? [])) {
        data.add(
          CommissionPartnerBody(
            username: userInfo.userCode ?? '',
            percent: userInfo.roseRate,
            participantType: "SELLER",
          ),
        );
      }

      var result = await appLocator<OrderRepository>().updateCommission(
        body: UpdateCommissionBody(
          referenceCode: state.orderInfo?.orderCode ?? "",
          participantType: "SELLER",
          users: data,
        ),
      );

      await result?.when(
        success: (data) async {
          state = state.copyWith(
            commissionStatus: LoadStatus.success,
          );

          refreshOrder();
        },
        error: (err) {
          state = state.copyWith(
            commissionStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        commissionStatus: LoadStatus.failure,
      );
    }
  }

  Future<void> confirmBySuppliesForCustomer({
    required BuySuppliesForCustomerBody body,
    required String orderCode,
  }) async {
    state = state.copyWith(
      updateStatus: LoadStatus.loading,
    );
    try {
      final result = await appLocator<OrderRepository>().buySuppliesForCustomer(
        body: body,
        orderCode: orderCode,
      );
      await result?.when(
        success: (data) async {
          state = state.copyWith(
            updateStatus: LoadStatus.success,
          );
        },
        error: (err) {
          state = state.copyWith(
            updateStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        updateStatus: LoadStatus.failure,
      );
    }
  }


  Future<List<String>> addImageSupplies(File file) async {
    state = state.copyWith(
      uploadImageStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<ResourceRepository>().uploadImage(
        file: file,
      );

      List<String> images = state.imageBuySupplies ?? [];

      await result?.when(
        success: (data) async {
          images.addAll(data);

          state = state.copyWith(
            uploadImageStatus: LoadStatus.success,
            imageBuySupplies: images,
          );
        },
        error: (err) async {
          state = state.copyWith(
            message: err.message,
            uploadImageStatus: LoadStatus.failure,
          );
        },
      );

      return images;
    } catch (error) {
      state = state.copyWith(
        uploadImageStatus: LoadStatus.failure,
      );
      return [];
    }
  }

  void deleteImageSupplies (int index){
    final List<String> updateList = List<String>.from(state.imageBuySupplies ?? []);
    updateList.removeAt(index);
    state = state.copyWith(
      imageBuySupplies: updateList,
    );
  }

  void initInfoBuySuppliesForCustomer() {
    state = state.copyWith(
      imageBuySupplies: [],
    );
  }
}
