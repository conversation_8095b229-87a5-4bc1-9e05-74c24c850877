import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/body/timekeeping/create_or_update_request_timekeeping_body.dart';
import 'package:vcc/domain/entities/timekeeping/detail_request_timekeeping_entity.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/timekeeping/timekeeping_type_enum.dart';
import 'package:vcc/domain/params/timekeeping/timekeeping_file.dart';
import 'package:vcc/extensions/file_type.dart';
import 'package:vcc/extensions/pattern.dart';
import 'package:vcc/extensions/string_extension.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/custom_bottom_picker/custom_bottom_picker_checkin.dart';
import 'package:vcc/presentation/views/bottom_sheet/upload_image/upload_image.dart';
import 'package:vcc/presentation/views/features/timekeeping/create_or_update_request_timekeeping/create_or_update_request_timekeeping_view_model.dart';
import 'package:vcc/presentation/views/features/timekeeping/widget/document_file_timekeeping_widget.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/dotted_border/dotted_border.dart';
import 'package:vcc/presentation/views/widgets/drop_down_complain_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/image_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/required_label_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
import 'package:vcc/utils/validate_utils.dart';
import 'package:vcc/extensions/date_time_extensions.dart';

class CreateOrUpdateRequestTimeKeepingArguments {
  final String? id;

  CreateOrUpdateRequestTimeKeepingArguments({
    this.id,
  });
}

class CreateOrUpdateRequestTimekeepingPage extends StatefulHookConsumerWidget {
  const CreateOrUpdateRequestTimekeepingPage({
    super.key,
    this.arguments,
  });

  final CreateOrUpdateRequestTimeKeepingArguments? arguments;

  @override
  ConsumerState<CreateOrUpdateRequestTimekeepingPage> createState() =>
      _CreateOrUpdateRequestTimekeepingPageState();
}

class _CreateOrUpdateRequestTimekeepingPageState
    extends ConsumerState<CreateOrUpdateRequestTimekeepingPage> {
  final formCreateOrUpdateRequestTimekeeping = GlobalKey<FormState>();

  final TextEditingController projectNameController = TextEditingController();
  final TextEditingController noteController = TextEditingController();

  @override
  void initState() {
    getDetail();
    super.initState();
  }

  void getDetail() {
    Future(() async {
      if (widget.arguments?.id != null) {
        await ref
            .read(createOrUpdateRequestTimekeepingProvider.notifier)
            .getDetailRequestTimekeeping(
              id: widget.arguments?.id ?? "",
            );
        final state = ref.read(createOrUpdateRequestTimekeepingProvider);
        projectNameController.text =
            state.detailRequestTimekeeping?.projectName ?? "";
        noteController.text = state.detailRequestTimekeeping?.note ?? "";
      }
    });
  }

  @override
  void dispose() {
    projectNameController.dispose();
    noteController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(createOrUpdateRequestTimekeepingProvider);

    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: LayoutPage(
        appbar: AppBarCustom(
          title: widget.arguments?.id == null
              ? "Yêu cầu chấm công"
              : "Sửa yêu cầu chấm công",
        ),
        bottomAction: buildActionBottom(
          context,
        ),
        body: Stack(
          children: [
            body(context),
            if (state.uploadFileStatus == LoadStatus.loading ||
                state.createOrUpdateStatus == LoadStatus.loading) ...[
              Positioned.fill(
                child: Container(
                  color: BaseColors.textOnColor.withOpacity(0.35),
                  child: const Center(
                    child: LoadingIndicatorWidget(),
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget? buildActionBottom(BuildContext context) {
    var state = ref.watch(createOrUpdateRequestTimekeepingProvider);
    if (state.loadStatus == LoadStatus.failure) {
      return null;
    }
    return IgnorePointer(
      ignoring: state.loadStatus == LoadStatus.loading ||
          state.uploadFileStatus == LoadStatus.loading ||
          state.createOrUpdateStatus == LoadStatus.loading,
      child: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: BaseSpacing.spacing4,
          vertical: BaseSpacing.spacing2,
        ),
        child: BaseButton(
          text: widget.arguments?.id == null ? "Gửi yêu cầu" : "Lưu",
          onTap: () async {
            FocusScope.of(context).unfocus();

            if (formCreateOrUpdateRequestTimekeeping.currentState?.validate() ??
                false) {
              if ((state.totalTime ?? 0) == 0) {
                buildPopupError(
                  note: "Vui lòng chọn thời gian hợp lệ",
                );
                return;
              }
              List<String> files = [];

              for (TimekeepingFileEntity item in (state.listFileUpload ?? [])) {
                files.add(item.link ?? '');
              }
              if (state.timekeepingType == EnumTimekeepingType.mission &&
                  files.isEmpty) {
                buildPopupError(
                  note: "Vui lòng tải lên văn bản công tác",
                );
                return;
              }
              final formRequest = CreateOrUpdateRequestTimekeepingBody(
                timeKeepingType: state.timekeepingType?.keyToServer,
                projectName: projectNameController.text,
                startTime: state.startTime,
                endTime: state.endTime,
                totalTime: state.totalTime,
                urls: state.timekeepingType == EnumTimekeepingType.mission
                    ? files
                    : null,
                note: noteController.text,
              );
              if (widget.arguments?.id == null) {
                final bool result = await ref
                    .read(createOrUpdateRequestTimekeepingProvider.notifier)
                    .createRequestTimekeeping(
                      body: formRequest,
                    );
                if (!context.mounted) return;
                if (result) {
                  onShowPopup(
                    isUpdate: false,
                  );
                  context.pop(result);
                }
              } else {
                final bool result = await ref
                    .read(createOrUpdateRequestTimekeepingProvider.notifier)
                    .updateRequestTimekeeping(
                      body: formRequest,
                      id: widget.arguments?.id ?? "",
                    );
                if (!context.mounted) return;
                if (result) {
                  onShowPopup(
                    isUpdate: true,
                  );
                  context.pop(result);
                }
              }
            }
          },
        ),
      ),
    );
  }

  Widget body(BuildContext context) {
    var state = ref.watch(createOrUpdateRequestTimekeepingProvider);
    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: "Không có dữ liệu",
        onRefresh: () async {
          getDetail();
        },
      );
    }

    return SingleChildScrollView(
      child: Form(
        key: formCreateOrUpdateRequestTimekeeping,
        child: Padding(
          padding: const EdgeInsets.all(
            BaseSpacing.spacing4,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            mainAxisSize: MainAxisSize.min,
            children: [
              DropDownComplainWidget(
                data: listTimekeepingType,
                title: "Loại chấm công",
                isRequired: true,
                onChangeItem: (value) {
                  if (value != state.timekeepingType) {
                    ref
                        .read(createOrUpdateRequestTimekeepingProvider.notifier)
                        .selectTimekeepingType(
                          value,
                        );
                    projectNameController.clear();
                  }
                },
                validator: (value) {
                  return ValidateUtils.onValidateNotNull(
                    title: "Loại chấm công",
                    value: value,
                  );
                },
                selected: state.timekeepingType,
                displayValue: (item) => item.label,
              ),
              if (state.timekeepingType == EnumTimekeepingType.project)
                Padding(
                  padding: const EdgeInsets.only(
                    top: BaseSpacing.spacing4,
                  ),
                  child: TextFieldWidget(
                    controller: projectNameController,
                    isRequired: true,
                    labelText: "Tên dự án",
                    textInputAction: TextInputAction.done,
                    textCapitalization: TextCapitalization.words,
                    validator: (value) {
                      return ValidateUtils.onValidateNotNull(
                        title: 'Tên dự án',
                        value: value,
                      );
                    },
                    onFocusChange: (isFocus) {
                      if (!isFocus) {}
                    },
                  ),
                ),
              Padding(
                padding: const EdgeInsets.only(
                  top: BaseSpacing.spacing4,
                ),
                child: DropdownWidget(
                  labelText: "Thời gian bắt đầu",
                  isRequired: true,
                  content: state.startTime?.displayView(
                    format: DateTimeFormater.eventFormat,
                  ),
                  suffix: MyAssets.icons.iconCalendarS24.svg(),
                  onTap: () async {
                    openDatetimePicker(
                      date: state.startTime,
                      listTime: listTimeStart,
                      onSubmit: (date) {
                        if (state.endTime != null) {
                          if (!date.isBefore(state.endTime!)) {
                            buildPopupError(
                              note:
                                  "Thời gian bắt đầu phải nhỏ hơn thời gian kết thúc",
                            );
                            return;
                          }
                        }
                        ref
                            .read(createOrUpdateRequestTimekeepingProvider
                                .notifier)
                            .setStartTime(date);
                        context.pop();
                      },
                    );
                  },
                  validator: (value) {
                    return ValidateUtils.onValidateNotNull(
                      title: "Thời gian bắt đầu",
                      value: value,
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: BaseSpacing.spacing4,
                ),
                child: DropdownWidget(
                  labelText: "Thời gian kết thúc",
                  isRequired: true,
                  content: state.endTime?.displayView(
                    format: DateTimeFormater.eventFormat,
                  ),
                  suffix: MyAssets.icons.iconCalendarS24.svg(),
                  onTap: () async {
                    openDatetimePicker(
                      date: state.endTime,
                      listTime: listTimeEnd,
                      onSubmit: (date) {
                        if (state.startTime != null) {
                          if (!date.isAfter(state.startTime!)) {
                            buildPopupError(
                              note:
                                  "Thời gian kết thúc phải lớn hơn thời gian bắt đầu",
                            );
                            return;
                          }
                        }
                        ref
                            .read(createOrUpdateRequestTimekeepingProvider
                                .notifier)
                            .setEndTime(date);
                        context.pop();
                      },
                    );
                  },
                  validator: (value) {
                    return ValidateUtils.onValidateNotNull(
                      title: "Thời gian kết thúc",
                      value: value,
                    );
                  },
                ),
              ),
              Padding(
                padding: const EdgeInsets.only(
                  top: BaseSpacing.spacing4,
                ),
                child: DropdownWidget(
                  labelText: "Số ngày yêu cầu",
                  isRequired: true,
                  content: '${(state.totalTime ?? 0).toString().formatNumber} ngày',
                  enabled: false,
                  suffix: MyAssets.icons.iconCalendarS24.svg(),
                ),
              ),
              uploadDocumentMissionView(),
              Padding(
                padding: const EdgeInsets.only(
                  top: BaseSpacing.spacing4,
                ),
                child: TextFieldWidget.area(
                  controller: noteController,
                  labelText: "Ghi chú",
                  hintText: "Ghi chú",
                  hintTextStyle: TextStyle(
                    color: BaseColors.textSubtitle,
                  ),
                  textInputAction: TextInputAction.done,
                  maxLines: 3,
                  height: 110,
                  alignment: Alignment.topLeft,
                  onChanged: (value) {},
                  inputFormatters: [
                    FilteringTextInputFormatter.allow(
                      RegExp(
                        Patterns.note,
                        multiLine: true,
                      ),
                    ),
                  ],
                  onFocusChange: (isFocus) {
                    if (!isFocus) {}
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget uploadDocumentMissionView() {
    var state = ref.watch(createOrUpdateRequestTimekeepingProvider);
    return state.timekeepingType == EnumTimekeepingType.mission
        ? Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Padding(
                padding: EdgeInsets.only(
                  top: BaseSpacing.spacing4,
                ),
                child: RequiredLabelWidget(
                  labelText: 'Văn bản công tác:',
                  required: true,
                ),
              ),
              buildFileView(
                files: state.listFileUpload ?? [],
              ),
            ],
          )
        : const SizedBox();
  }

  Widget buildFileView({
    required List<TimekeepingFileEntity> files,
  }) {
    final provider =
        ref.read(createOrUpdateRequestTimekeepingProvider.notifier);

    List<TimekeepingFileEntity> images = [];
    List<TimekeepingFileEntity> pdf = [];
    for (var element in files) {
      bool isPdf = FileTypeOption.isPdfLink(
        filePath: element.link ?? '',
      );
      if (isPdf) {
        pdf.add(element);
      } else {
        images.add(element);
      }
    }

    return Padding(
      padding: const EdgeInsets.only(
        top: BaseSpacing.spacing2,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (files.isEmpty) ...[
            _buildOnTakeFileVIew(
              onTap: () {
                onTakeFile();
              },
            ),
          ],
          // list image
          if (images.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.symmetric(
                vertical: BaseSpacing.spacing2,
              ),
              child: SizedBox(
                height: 80,
                child: ListView.separated(
                  shrinkWrap: true,
                  itemCount: images.length,
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.only(
                    right: BaseSpacing.spacing4,
                  ),
                  separatorBuilder: (_, __) => const SizedBox(
                    width: BaseSpacing.spacing2,
                  ),
                  itemBuilder: (context, index) {
                    return Stack(
                      children: [
                        Container(
                          height: 80,
                          width: 80,
                          padding: const EdgeInsets.only(
                            top: BaseSpacing.spacing1,
                            right: BaseSpacing.spacing1,
                          ),
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: ImageWidget(
                              enableShowPreview: true,
                              images[index].link ?? '',
                            ),
                          ),
                        ),
                        Positioned(
                          top: 0,
                          right: 0,
                          child: Visibility(
                            visible: true,
                            child: InkWellWidget(
                              onTap: () {
                                provider.deleteFile(
                                  images[index],
                                );
                              },
                              child: MyAssets.icons.iconCloseRed.svg(),
                            ),
                          ),
                        ),
                      ],
                    );
                  },
                ),
              ),
            ),
          ],
          //list pdf
          if (pdf.isNotEmpty) ...[
            SizedBox(
              child: ListView.separated(
                shrinkWrap: true,
                itemCount: pdf.length,
                scrollDirection: Axis.vertical,
                separatorBuilder: (_, __) => const SizedBox(
                  width: BaseSpacing.spacing2,
                ),
                physics: const NeverScrollableScrollPhysics(),
                reverse: true,
                itemBuilder: (context, index) {
                  return Stack(
                    children: [
                      Container(
                        height: 85,
                        width: double.infinity,
                        padding: const EdgeInsets.only(
                          top: BaseSpacing.spacing1,
                          right: BaseSpacing.spacing1,
                        ),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: ClipRRect(
                            borderRadius: BorderRadius.circular(8),
                            child: DocumentFileTimekeepingWidget(
                              label: "File đính kèm",
                              file: ListFileTimekeeping(
                                fileName:
                                    pdf[index].link?.split('/').last ?? '',
                                link: pdf[index].link ?? '',
                              ),
                            ),
                          ),
                        ),
                      ),
                      Positioned(
                        top: 0,
                        right: 0,
                        child: Visibility(
                          visible: true,
                          child: InkWellWidget(
                            onTap: () {
                              provider.deleteFile(
                                pdf[index],
                              );
                            },
                            child: MyAssets.icons.iconCloseRed.svg(),
                          ),
                        ),
                      ),
                    ],
                  );
                },
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildOnTakeFileVIew({
    void Function()? onTap,
  }) {
    final state = ref.watch(createOrUpdateRequestTimekeepingProvider);

    return Stack(
      children: [
        InkWell(
          onTap: onTap,
          child: DottedBorder(
            color: BaseColors.borderDefault,
            radius: const Radius.circular(
              BaseSpacing.spacing2,
            ),
            borderType: BorderType.rRect,
            padding: const EdgeInsets.all(
              BaseSpacing.spacing2,
            ),
            dashPattern: const [6, 8],
            child: Container(
              padding: const EdgeInsets.all(
                BaseSpacing.spacing2,
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.only(
                      right: BaseSpacing.spacing2,
                    ),
                    child: MyAssets.icons.iconDocUpload.svg(),
                  ),
                  Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Tải lên tập tin',
                        style: UITextStyle.body1Regular.copyWith(
                          color: BaseColors.textBody,
                        ),
                      ),
                      Text(
                        'Định dạng PDF, JPG, PNG',
                        style: UITextStyle.body1Regular.copyWith(
                          color: BaseColors.textSubtitle,
                        ),
                      )
                    ],
                  ),
                ],
              ),
            ),
          ),
        ),
        if (state.loadStatus == LoadStatus.loading)
          const Positioned.fill(
            child: Center(
              child: LoadingIndicatorWidget(),
            ),
          ),
      ],
    );
  }

  void onTakeFile() async {
    final provider =
        ref.read(createOrUpdateRequestTimekeepingProvider.notifier);
    AppBottomSheet.showNormalBottomSheet(
      context,
      title: "Tải lên tập tin",
      isFlexible: true,
      child: UploadImageBottomSheet(
        extensions: const [
          'png',
          'jpg',
          'pdf',
          'PNG',
          'JPG',
          'PDF',
        ],
        onPickFile: (file) async {
          final listFile = await provider.uploadFileRequestTimeKeeping(
            file: file,
          );
          provider.addFile(
            files: listFile,
          );
        },
      ),
    );
  }

  void openDatetimePicker({
    DateTime? date,
    required List<TimeOfDay> listTime,
    required Function(DateTime date) onSubmit,
  }) {
    DateTime now = DateTime.now();
    DateTime dateInit = DateTime(
      now.year,
      now.month,
      now.day,
      listTime[0].hour,
      listTime[0].minute,
    );
    DateTime minDateSet = DateTime(
      now.year,
      now.month,
      now.day,
      0,
      0,
      0,
    );

    if (date != null) {
      dateInit = date;
    }

    CustomBottomPickerCheckIn(
      height: MediaQuery.of(context).size.height * 0.40,
      title: "Chọn thời gian",
      buttonText: "Xác nhận",
      listTime: listTime,
      minDateTime: minDateSet,
      initialDateTime: dateInit,
      titleStyle: UITextStyle.body1SemiBold,
      pickerTextStyle: TextStyle(
        fontSize: 18,
        color: BaseColors.backgroundBlack,
      ),
      onSubmit: (date) => onSubmit(date),
    ).show(context);
  }

  void onShowPopup({
    required bool isUpdate,
  }) {
    AppDialog.showDialogCenter(
      context,
      message: isUpdate
          ? "Cập nhật yêu cầu chấm công thành công"
          : "Tạo mới yêu cầu chấm công thành công",
      status: DialogStatus.success,
    );
  }

  void buildPopupError({
    required String note,
  }) {
    AppDialog.showDialogCenter(
      context,
      message: note,
      status: DialogStatus.error,
    );
  }
}
