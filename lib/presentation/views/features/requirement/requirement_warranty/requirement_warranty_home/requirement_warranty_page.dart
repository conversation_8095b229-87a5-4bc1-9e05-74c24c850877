import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_warranty_view/filter_requirement_warranty_view.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/requirement_warranty_home/manage_warranty/manage_requirement_warranty.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/requirement_warranty_home/perform_warranty/perform_warranty_page.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/tab_bar_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/deBouncer.dart';
import 'requirement_warranty_view_model.dart';

class RequirementWarrantyPage extends StatefulHookConsumerWidget {
  const RequirementWarrantyPage({
    super.key,
  });

  @override
  ConsumerState<RequirementWarrantyPage> createState() => _WarrantyPageState();
}

class _WarrantyPageState extends ConsumerState<RequirementWarrantyPage>
    with TickerProviderStateMixin {
  List<String> listPage = [
    "Thực hiện",
    "Quản lý",
  ];

  late TabController tabController;
  late TextEditingController searchController;
  late Debounce<String> deBouncer;

  @override
  void initState() {
    searchController = TextEditingController();
    tabController = TabController(
      length: listPage.length,
      initialIndex: 0,
      vsync: this,
    );
    deBouncer = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref
            .read(requirementWarrantyProvider.notifier)
            .changeKeySearch(value ?? '');
      },
    );
    Future(
      () {
        ref.read(requirementWarrantyProvider.notifier).getAllWarranty();
      },
    );
    super.initState();
  }

  @override
  void dispose() {
    tabController.dispose();
    searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(requirementWarrantyProvider);

    return GestureDetector(
      onTap: () => FocusManager.instance.primaryFocus?.unfocus(),
      child: LayoutPage(
        backgroundColor: BaseColors.backgroundWhite,
        appbar: const AppBarCustom(
          title: "Yêu cầu bảo hành",
        ),
        body: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: <Widget>[
            TabBarWidget(
              tabItems: listPage,
              tabController: tabController,
              onTap: (index) {
                if (state.tabIndex != index) {
                  ref
                      .read(requirementWarrantyProvider.notifier)
                      .changeTabIndex(index);
                }
              },
            ),
            Expanded(
              child: Column(
                children: <Widget>[
                  _buildSearchZone(),
                  state.tabIndex == 0
                      ? const PerformWarrantyPage()
                      : const ManageWarrantyPage(),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSearchZone() {
    var state = ref.watch(requirementWarrantyProvider);
    bool activeFilter = (state.complainType ?? []).isNotEmpty ||
        (state.startDate?.toString() ?? '').isNotEmpty ||
        (state.endDate?.toString() ?? '').isNotEmpty;
    return Padding(
      padding: const EdgeInsets.symmetric(
        vertical: 8,
      ),
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: BaseSpacing.spacing4,
            ),
            child: Row(
              children: [
                Expanded(
                  child: SearchTextFieldWidget(
                    controller: searchController,
                    hintText: "Tìm kiếm mã, tên YCBH",
                    onChanged: (value) {
                      deBouncer.value = value;
                    },
                  ),
                ),
                const SizedBox(width: 16),
                InkWellWidget(
                  onTap: () {
                    FocusScope.of(context).unfocus();
                    var complainState = ref.watch(requirementWarrantyProvider);
                    AppBottomSheet.showNormalBottomSheet(
                      context,
                      title: "Bộ lọc",
                      height: MediaQuery.of(context).size.height * 0.8,
                      child: FilterWarrantyViewSheet(
                        startDateStr: complainState.startTimeStr,
                        endDateStr: complainState.endTimeStr,
                        startDate: complainState.startDate,
                        endDate: complainState.endDate,
                        complainType: complainState.complainType,
                        onApply: (FilterWarrantyArgumentCallBack value) {
                          ref
                              .read(requirementWarrantyProvider.notifier)
                              .changeDate(
                                value.startTime,
                                value.endTime,
                              );

                          ref
                              .read(requirementWarrantyProvider.notifier)
                              .getAllWarranty(
                                status: (value.requirementWarrantyStatus ?? [])
                                        .isNotEmpty
                                    ? value.requirementWarrantyStatus!
                                        .map(
                                          (e) => e.keyToServer,
                                        )
                                        .toList()
                                    : [],
                                startDate: value.startTime,
                                endDate: value.endTime,
                              );
                        },
                      ),
                    );
                  },
                  child: Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8),
                    child: Row(
                      children: [
                        activeFilter
                            ? MyAssets.icons.iconFilterActiveS24.svg()
                            : MyAssets.icons.filter.svg(),
                        Text(
                          'Lọc',
                          style: UITextStyle.caption1Medium.copyWith(
                            color: BaseColors.textSubtitle,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(
            height: BaseSpacing.spacing2,
          ),
          const DividerWidget(
            height: 8,
          ),
        ],
      ),
    );
  }
}
