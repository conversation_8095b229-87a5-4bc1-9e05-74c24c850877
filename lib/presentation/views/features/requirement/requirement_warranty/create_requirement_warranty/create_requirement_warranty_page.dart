import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/body/create_requirement_warranty_body.dart';
import 'package:vcc/domain/body/sign_acceptance_body.dart';
import 'package:vcc/domain/body/sign_create_warranty_body.dart';
import 'package:vcc/domain/body/sign_item_warranty_body.dart';
import 'package:vcc/domain/entities/complain/detail_complain_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/order/detail_order_shipping_entity.dart';
import 'package:vcc/domain/entities/requirement_warranty/order_item_warranty_entity.dart';
import 'package:vcc/domain/entities/search_warranty_entity.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/requirement_warranty/requirement_warranty_type_enum.dart';
import 'package:vcc/domain/enums/send_otp_type.dart';
import 'package:vcc/domain/enums/user_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/dialogs/error_dialog.dart';
import 'package:vcc/presentation/views/features/order/detail_order/sign_contract_order/sign_contract_order_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/add_product/add_product_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/build_warranty_info/build_warranty_info_item.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/history_requirement_warranty/history_requirement_warranty_page.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/requirement_warranty_home/widget/requirement_warranty_order_info.dart';
import 'package:vcc/presentation/views/features/requirement/requirement_warranty/user_contact_info/contact_user_info_page.dart';
import 'package:vcc/presentation/views/features/work/complain/detail_complain/widget/build_row_detail_complain_view.dart';
import 'package:vcc/presentation/views/features/work/warranty/detail_warranty/detail_warranty_page.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';
import 'package:vcc/utils/show_loading_utils.dart';
import 'create_requirement_warranty_info_view_model.dart';

class CreateRequirementWarrantyArguments {
  final DetailComplainEntity detailComplainInfo;

  CreateRequirementWarrantyArguments({
    required this.detailComplainInfo,
  });
}

class CreateComplainWarrantyPage extends StatefulHookConsumerWidget {
  final CreateRequirementWarrantyArguments? arguments;

  const CreateComplainWarrantyPage({
    super.key,
    this.arguments,
  });

  @override
  ConsumerState<CreateComplainWarrantyPage> createState() =>
      _CreateWarrantyPageState();
}

class _CreateWarrantyPageState
    extends ConsumerState<CreateComplainWarrantyPage> {
  final formCreateRequirementWarranty = GlobalKey<FormState>();
  List<RequirementWarrantyTypeEnum> listRequirementWarrantyAddress = [];
  bool? isHasWarrantyPolicy = false;

  @override
  void initState() {
    listRequirementWarrantyAddress = [
      RequirementWarrantyTypeEnum.serviceCenter,
      RequirementWarrantyTypeEnum.onSite,
    ];
    getDetailComplain();
    super.initState();
  }

  Future<void> onRefresh() async {
    final state = ref.watch(createRequirementWarrantyInfoProvider);
    Future(
      () {
        ref.read(createRequirementWarrantyInfoProvider.notifier).getOrderItems(
              state.detailComplainInfo?.packageName ?? '',
            );
      },
    );
  }

  Future<void> getDetailComplain() async {
    Future(
      () {
        ref
            .read(createRequirementWarrantyInfoProvider.notifier)
            .setDetailComplain(
              widget.arguments?.detailComplainInfo ?? DetailComplainEntity(),
            );
        onRefresh();
      },
    );
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(createRequirementWarrantyInfoProvider);
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).requestFocus(FocusNode());
      },
      child: LayoutPage(
        appbar: AppBarCustom(
          title: "Tạo yêu cầu bảo hành",
          actionWidget: [
            InkWellWidget(
              onTap: () {
                List<OrderItemWarrantyEntity> products = state.products ?? [];
                if (products.isEmpty) {
                  ErrorDialog.showErrorDialog("Bắt buộc phải thêm sản phẩm");
                  return;
                }
                context.push(
                  RouterPaths.historyRequirementWarranty,
                  extra: HistoryRequirementWarrantyArguments(
                    isFormCreate: true,
                    id: state.detailComplainInfo?.packageName,
                    codes: products
                        .map((e) => e.goodsCode)
                        .whereType<String>()
                        .toList(),
                  ),
                );
              },
              child: Padding(
                padding: const EdgeInsets.all(
                  BaseSpacing.spacing4,
                ),
                child: MyAssets.icons.iconOclockWhite.svg(),
              ),
            ),
          ],
        ),
        bottomAction: IgnorePointer(
          ignoring: state.loadStatus == LoadStatus.loading,
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: BaseSpacing.spacing4,
              vertical: BaseSpacing.spacing2,
            ),
            child: BaseButton(
              text: "Tạo",
              onTap: () async {
                onCreateWarranty(context);
              },
            ),
          ),
        ),
        body: state.loadStatus == LoadStatus.loading
            ? const Center(
                child: LoadingIndicatorWidget(),
              )
            : SingleChildScrollView(
                child: Form(
                  key: formCreateRequirementWarranty,
                  child: Padding(
                    padding: const EdgeInsets.all(
                      BaseSpacing.spacing4,
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        if (state.orderItemWarranty?.contractId != null) ...[
                          UserContactUserInfoPage(
                            contactUserInfo: DetailComplainEntity(
                              customerTypeStr:
                                  state.orderItemWarranty?.customerType,
                              taxCode: state.orderItemWarranty?.customerId,
                              contactAddress:
                                  state.orderItemWarranty?.customerAddress,
                              customerPhone:
                                  state.orderItemWarranty?.customerPhone,
                              customerName:
                                  state.orderItemWarranty?.customerName,
                              contactName: state.orderItemWarranty?.contactName,
                              contactPhoneNumber:
                                  state.orderItemWarranty?.contactNumber,
                            ),
                          ),
                        ],
                        const Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: BaseSpacing.spacing3,
                          ),
                          child: DividerWidget(
                            height: BaseSpacing.spacing15s,
                          ),
                        ),
                        RequirementWarrantyOrderInfoWidget(
                          orderCode:
                              state.detailComplainInfo?.packageName ?? '',
                          completedDate:
                              state.orderItemWarranty?.completeOrderTime,
                          complainCode: state.detailComplainInfo?.code ?? '',
                        ),
                        const Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: BaseSpacing.spacing3,
                          ),
                          child: DividerWidget(
                            height: BaseSpacing.spacing15s,
                          ),
                        ),
                        _buildWarrantyInfo(),
                        const Padding(
                          padding: EdgeInsets.symmetric(
                            vertical: BaseSpacing.spacing3,
                          ),
                          child: DividerWidget(
                            height: BaseSpacing.spacing15s,
                          ),
                        ),
                        _buildChangeWarrantyType(),
                      ],
                    ),
                  ),
                ),
              ),
      ),
    );
  }

  Widget _buildWarrantyInfo() {
    final state = ref.watch(createRequirementWarrantyInfoProvider);
    final rf = ref.read(createRequirementWarrantyInfoProvider.notifier);

    if ((state.products ?? []).isNotEmpty) {
      final isRepair = state.products?.firstWhere(
        (element) =>
            element.warrantyPolicy ==
            RequirementWarrantyTypeEnum.repair.keyToServer,
        orElse: () => OrderItemWarrantyEntity(),
      );
      if (isRepair?.goodsName != null) {
        isHasWarrantyPolicy = true;
      } else {
        isHasWarrantyPolicy = false;
      }
    }
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Thông tin bảo hành',
              style: UITextStyle.body1SemiBold.copyWith(
                color: BaseColors.textTitle,
              ),
            ),
            InkWellWidget(
              onTap: () async {
                context.push(
                  RouterPaths.addProductRequirementWarranty,
                  extra: AddProductOrderContractArguments(
                    orderContractCode:
                        state.detailComplainInfo?.packageName ?? '',
                    productsSelected: (state.products ?? []).toList(),
                    onConfirm: (mergedItems) {
                      rf.onUpdateProduct(
                        argProducts: mergedItems,
                        orderCode: state.detailComplainInfo?.packageName ?? '',
                      );
                    },
                  ),
                );
              },
              child: Row(
                children: [
                  MyAssets.icons.iconPlusS16.svg(
                    colorFilter: ColorFilter.mode(
                      BaseColors.primary,
                      BlendMode.srcIn,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                      left: BaseSpacing.spacing05,
                    ),
                    child: Text(
                      "Thêm sản phẩm",
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.primary,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        if ((state.products ?? []).isNotEmpty) ...[
          const Padding(
            padding: EdgeInsets.only(
              top: BaseSpacing.spacing2,
            ),
            child: DividerWidget(
              height: BaseSpacing.spacingPx,
            ),
          ),
        ],
        state.updateProductStatus == LoadStatus.loading
            ? const Center(
                child: LoadingIndicatorWidget(),
              )
            : ListView.separated(
                itemCount: state.products?.length ?? 0,
                physics: const NeverScrollableScrollPhysics(),
                shrinkWrap: true,
                separatorBuilder: (_, __) => const DividerWidget(),
                itemBuilder: (context, index) {
                  final item = state.products![index];
                  return Slidable(
                    endActionPane: ActionPane(
                      motion: const ScrollMotion(),
                      extentRatio: 0.3,
                      children: <Widget>[
                        CustomSlidableAction(
                          backgroundColor: BaseColors.primary,
                          foregroundColor: Colors.yellow,
                          child: Column(
                            mainAxisSize: MainAxisSize.min,
                            children: <Widget>[
                              MyAssets.icons.iconWhiteTrash.svg(),
                              Padding(
                                padding: const EdgeInsets.only(
                                  top: BaseSpacing.spacing2,
                                ),
                                child: Text(
                                  "Xoá",
                                  style: UITextStyle.body2SemiBold.copyWith(
                                    color: BaseColors.backgroundWhite,
                                  ),
                                ),
                              ),
                            ],
                          ),
                          onPressed: (context) {
                            showDialogConfirmRemoveProduct(
                              index: index,
                            );
                          },
                        )
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.only(
                        top: BaseSpacing.spacing2,
                      ),
                      child: BuildWarrantyInfoItem(
                        buildWarrantyInfoArguments: BuildWarrantyInfoArguments(
                          index: index,
                          orderItemEntity: item,
                          isShowProductCondition: true,
                          initProductConditionValue: item.itemStatus,
                          maxImage: item.numberOfImages ?? 0,
                          buildWarrantyPolicy: _buildPolicy(item),
                          listFile: item.images,
                          isChangeQuantity: (item.listSerials ?? []).isNotEmpty
                              ? false
                              : true,

                          /// có tháo lắp thay thế
                          onChangeStatusSetting: ({
                            value,
                          }) {
                            rf.onChangeStatusSetting(
                              index: index,
                              value: value,
                            );
                          },
                          statusSetting: item.isSetup,
                          // hiện trạng sản phẩm
                          onChangeProductConditionValue: ({
                            productConditionValue,
                          }) {
                            rf.onUpdateProductConditionValue(
                              itemStatus: productConditionValue ?? '',
                              index: index,
                            );
                          },
                          // hỉnh ảnh
                          onChangeImage: ({
                            files,
                          }) {
                            rf.onUpdateImage(
                              images: files ?? [],
                              index: index,
                            );
                          },
                          onRemoveImage: ({
                            image,
                          }) {
                            rf.onRemoveImage(
                              image: image,
                              index: index,
                            );
                          },
                          quantityCurrentValue: (value) {
                            if (value < 1) {
                              rf.removeProduct(index: index);
                              return;
                            }
                            rf.changeQuantity(
                              indexItem: index,
                              quantity: value,
                            );
                          },

                          onChangeSerials: ({
                            serials,
                          }) {
                            if ((serials ?? []).isEmpty) {
                              rf.removeProduct(
                                index: index,
                              );
                            } else {
                              rf.onUpdateSerial(
                                serials: serials ?? [],
                                index: index,
                              );
                            }
                          },
                        ),
                      ),
                    ),
                  );
                },
              ),
      ],
    );
  }

  List<InlineSpan> _buildPolicy(OrderItemWarrantyEntity product) {
    final state = ref.watch(createRequirementWarrantyInfoProvider);
    if (product.noPolicy) {
      return [
        TextSpan(
          text: 'Thiếu chính sách',
          style: UITextStyle.body2Regular,
        ),
      ];
    }
    if (product.onrForOne) {
      return [
        TextSpan(
          text: '1 đổi 1 ',
          style: UITextStyle.body2Regular,
        ),
        TextSpan(
          text: "Xem chi tiết",
          style: UITextStyle.caption1Medium.copyWith(
            color: BaseColors.info,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              context.push(
                RouterPaths.detailWarranty,
                extra: DetailWarrantyArguments(
                  body: SearchWarrantyEntity(
                    contractId: state.orderItemWarranty?.contractId,
                    type: state.orderItemWarranty?.type,
                    merEntityId: product.merEntityId,
                  ),
                ),
              );
            },
        ),
      ];
    }
    if (product.isRepair) {
      return [
        TextSpan(
          text: 'Sửa chữa ',
          style: UITextStyle.body2Regular,
        ),
        TextSpan(
          text: "Xem chi tiết",
          style: UITextStyle.caption1Medium.copyWith(
            color: BaseColors.info,
          ),
          recognizer: TapGestureRecognizer()
            ..onTap = () {
              context.push(
                RouterPaths.detailWarranty,
                extra: DetailWarrantyArguments(
                  body: SearchWarrantyEntity(
                    contractId: state.orderItemWarranty?.contractId,
                    type: state.orderItemWarranty?.type,
                    merEntityId: product.merEntityId,
                  ),
                ),
              );
            },
        ),
      ];
    }
    if (product.expired) {
      return [
        TextSpan(
          text: 'Đã hết hạn',
          style: UITextStyle.body2Regular,
        ),
      ];
    }
    return [];
  }

  void showDialogConfirmRemoveProduct({
    required int index,
  }) {
    AppDialog.showDialogConfirm(
      context,
      barrierDismissible: false,
      title: "Xác nhận",
      message: 'Bạn có chắc chắn muốn xoá sản phẩm này?',
      buttonNameConfirm: "Xoá",
      onConfirmAction: () async {
        ref.read(createRequirementWarrantyInfoProvider.notifier).removeProduct(
              index: index,
            );
      },
      buttonNameCancel: "Hủy bỏ",
    );
  }

  void onCreateWarranty(BuildContext context) async {
    final rf = ref.read(createRequirementWarrantyInfoProvider.notifier);
    final state = ref.watch(createRequirementWarrantyInfoProvider);

    FocusScope.of(context).unfocus();
    if (formCreateRequirementWarranty.currentState != null &&
        formCreateRequirementWarranty.currentState!.validate()) {
      List<OrderItemWarrantyEntity> productCurrent = state.products ?? [];
      if (productCurrent.isEmpty) {
        ErrorDialog.showErrorDialog("Vui lòng thêm sản phẩm");
        return;
      }
      OrderItemWarrantyEntity? invalidItem = productCurrent.firstWhere(
        (item) => (item.images?.length ?? 0) < (item.numberOfImages ?? 0),
        orElse: () => OrderItemWarrantyEntity(),
      );

      if (invalidItem.goodsName != null) {
        return ErrorDialog.showErrorDialog(
          "Sản phẩm ${invalidItem.goodsName} bắt buộc phải chụp ${invalidItem.numberOfImages} ảnh!",
        );
      }

      OrderItemWarrantyEntity? validErrorPolicy = productCurrent.firstWhere(
        (item) => (item.warrantyPolicy ?? '').isEmpty,
        orElse: () => OrderItemWarrantyEntity(),
      );
      if (validErrorPolicy.goodsName != null) {
        return ErrorDialog.showErrorDialog(
          "Có lỗi xảy ra, không lấy được thông tinh chính sách bảo hành.",
        );
      }

      OrderItemWarrantyEntity? validExpired = productCurrent.firstWhere(
        (item) =>
            (item.warrantyPolicy) ==
            RequirementWarrantyTypeEnum.expired.keyToServer,
        orElse: () => OrderItemWarrantyEntity(),
      );
      if (validExpired.goodsName != null) {
        return ErrorDialog.showErrorDialog(
          "Sản phẩm ${validExpired.goodsName} đã hết hạn bảo hành",
        );
      }
      List<ItemInfo> listItemInfo = [];

      for (OrderItemWarrantyEntity element in productCurrent) {
        ItemInfo itemInfo = ItemInfo(
          serials: element.listSerials,
          code: element.goodsCode,
          images: element.images ?? [],
          itemStatus: element.itemStatus ?? '',
          quantity: element.currentQuantity ?? 0,
          warrantyPolicy: element.warrantyPolicy ?? '',
          isSetUp: element.isSetup ?? false,
        );
        listItemInfo.add(itemInfo);
      }

      // trung tam bap hanh thi phai ky
      RequirementWarrantyTypeEnum warrantyType =
          RequirementWarrantyTypeEnum.oneForOne;
      if (isHasWarrantyPolicy == true &&
          state.detailComplainInfo?.isSolution == true) {
        warrantyType = state.warrantyPlaceSelected!;
        if (warrantyType == RequirementWarrantyTypeEnum.serviceCenter) {
          List<SignItemInfo> listFileSign = [];

          for (OrderItemWarrantyEntity element in productCurrent) {
            if ((element.listSerials ?? []).isNotEmpty) {
              // Nếu có serial, mỗi serial là một object riêng
              for (String serial in element.listSerials!) {
                listFileSign.add(SignItemInfo(
                  serial: serial,
                  itemCode: element.goodsCode,
                  itemStatus: element.itemStatus ?? '',
                  quantity: 1,
                ));
              }
            } else {
              // Nếu không có serial, add bình thường
              listFileSign.add(SignItemInfo(
                serial: "", // Không có serial thì để rỗng
                itemCode: element.goodsCode,
                itemStatus: element.itemStatus ?? '',
                quantity: element.currentQuantity ?? 0,
              ));
            }
          }
          context.push(
            RouterPaths.signContractOrder,
            extra: SignContractOrderArguments(
              textButton: "Ký biên bản",
              title: "Biên bản thu hồi",
              sendOtpType: SendOtpType.warranty,
              isFromRequirementWarranty: true,
              signItemWarrantyBody: SignItemWarrantyBody(
                orderCode: state.detailComplainInfo?.packageName ?? '',
                ticketCode: state.detailComplainInfo?.code ?? '',
                listItemInfo: listFileSign,
              ),
              orderEntity: DetailOrderEntity(
                shippingInfo: DetailOrderShippingEntity(
                  receiverPhone: state.orderItemWarranty?.customerType ==
                          UserType.personal.keyToServer
                      ? state.orderItemWarranty?.customerPhone ?? ''
                      : (state.orderItemWarranty?.contactNumber ?? '')
                              .isNotEmpty
                          ? state.orderItemWarranty!.contactNumber
                          : state.orderItemWarranty!.customerPhone,
                ),
              ),
              onCompleted: (SignAcceptanceBody signData) async {
                ShowLoadingUtils.instance.turnOn();
                final confirmSignEntity = await rf.confirmSign(
                  body: SignCreateWarrantyBody(
                    listItemInfo: listFileSign,
                    orderCode: state.detailComplainInfo?.packageName ?? '',
                    otp: signData.otp,
                    staffSignature: signData.staffSignature,
                    customerSignature: signData.customerSignature,
                  ),
                );
                if (!context.mounted) return;
                if (confirmSignEntity != null) {
                  await onSendRequirementWarranty(
                    warrantyType: warrantyType,
                    context: context,
                    listItemInfo: listItemInfo,
                    certificateUrl: confirmSignEntity.certificateUrl,
                  );
                }
                ShowLoadingUtils.instance.turnOff();
              },
              orderCode: state.detailComplainInfo?.packageName ?? '',
            ),
          );
          return;
        }
      }
      onSendRequirementWarranty(
        warrantyType: warrantyType,
        context: context,
        listItemInfo: listItemInfo,
      );
    }
  }

  Future<int?> onSendRequirementWarranty({
    required RequirementWarrantyTypeEnum warrantyType,
    required BuildContext context,
    required List<ItemInfo> listItemInfo,
    String? certificateUrl,
  }) async {
    final rf = ref.read(createRequirementWarrantyInfoProvider.notifier);
    final state = ref.watch(createRequirementWarrantyInfoProvider);

    final totalProductQuantity = await rf.onCreateWarranty(
      body: CreateRequirementWarrantyBody(
        warrantyType: warrantyType.keyToServer,
        orderCode: state.detailComplainInfo?.packageName ?? '',
        ticketCode: state.detailComplainInfo?.code ?? '',
        itemsInfo: listItemInfo,
        refundCertificate: certificateUrl,
      ),
    );
    if (totalProductQuantity != null) {
      if (!context.mounted) return 0;
      AppDialog.showDialogCenter(
        context,
        message: "Bạn đã tạo thành công $totalProductQuantity yêu cầu bảo hành",
        status: DialogStatus.success,
        onConfirm: () {
          context.go(
            RouterPaths.root,
          );
          WidgetsBinding.instance.addPostFrameCallback((_) {
            context.push(RouterPaths.requirementWarranty);
          });
        },
      );
    }
    return totalProductQuantity;
  }

  Widget _buildChangeWarrantyType() {
    final rf = ref.read(createRequirementWarrantyInfoProvider.notifier);
    final state = ref.watch(createRequirementWarrantyInfoProvider);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      children: [
        Text(
          "Thông tin thực hiện",
          style: UITextStyle.body1SemiBold,
        ),
        Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing3,
          ),
          child: DividerWidget(
            height: BaseSpacing.spacingPx,
            color: BaseColors.secondaryBackground,
          ),
        ),
        if (state.detailComplainInfo?.isSolution == true &&
            isHasWarrantyPolicy!) ...[
          Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: BaseSpacing.spacing2,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildRequiredTitle("Nơi bảo hành"),
                    Text(
                      "Áp dụng cho sản phẩm bảo hành sửa chữa",
                      style: UITextStyle.caption1Regular,
                    ),
                  ],
                ),
              ),
              ListView.builder(
                shrinkWrap: true,
                itemCount: listRequirementWarrantyAddress.length,
                itemBuilder: (context, index) {
                  final item = listRequirementWarrantyAddress[index];
                  return Padding(
                    padding: const EdgeInsets.symmetric(
                      vertical: BaseSpacing.spacing1,
                    ),
                    child: RadioWidget(
                      value: item,
                      groupValue: state.warrantyPlaceSelected,
                      onChanged: (value) {
                        rf.changeWarrantyPlace(value!);
                      },
                      displayWidget: (context, item) => Text(
                        item?.label ?? '',
                      ),
                    ),
                  );
                },
              ),
            ],
          ),
        ],
        _buildOrderInfoItem(
          title: "Người thực hiện",
          value: state.detailComplainInfo?.performerName ?? '',
        ),
      ],
    );
  }

  Widget _buildOrderInfoItem({
    required String title,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(
        bottom: BaseSpacing.spacing2,
      ),
      child: BuildRowDetailComplainView(
        title: title,
        value: value,
      ),
    );
  }

  Widget _buildRequiredTitle(String title) {
    return Row(
      children: [
        Text(
          title,
          style: UITextStyle.body2Medium,
        ),
        Padding(
          padding: const EdgeInsets.only(
            left: BaseSpacing.spacing1,
          ),
          child: Text(
            "*",
            style: UITextStyle.caption1Regular.copyWith(
              color: BaseColors.primary,
            ),
          ),
        )
      ],
    );
  }
}
