import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';

class CardCustomWidget extends StatelessWidget {
  final Widget? prefix;
  final Widget? suffix;
  final Widget? titleWidget;
  final String? title;
  final TextStyle? titleStyle;
  final Function? onPress;
  final EdgeInsetsGeometry? padding;
  final Color? borderColor;
  final Color? backgroundColor;

  const CardCustomWidget({
    super.key,
    this.prefix,
    this.suffix,
    this.titleWidget,
    this.title,
    this.titleStyle,
    this.onPress,
    this.padding,
    this.borderColor,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    return InkWellWidget(
      onTap: () {
        onPress?.call();
      },
      child: Container(
        padding: padding ??
            const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 12,
            ),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: borderColor ?? BaseColors.borderDefault,
            width: 1,
          ),
          color: backgroundColor ?? BaseColors.backgroundWhite,
        ),
        child: Row(
          children: <Widget>[
            if (prefix != null) ...[
              prefix!,
              const SizedBox(width: 8),
            ],
            Expanded(
              child: (titleWidget != null)
                  ? titleWidget!
                  : Text(
                      title ?? '',
                      style: titleStyle ??
                          UITextStyle.body1Regular.copyWith(
                            color: BaseColors.textBody,
                          ),
                    ),
            ),
            if (suffix != null) ...[
              const SizedBox(width: 8),
              suffix!,
            ],
          ],
        ),
      ),
    );
  }
}
