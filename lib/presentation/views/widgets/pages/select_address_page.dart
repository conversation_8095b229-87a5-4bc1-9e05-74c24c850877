import 'package:flutter/material.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/enums/address_type.dart';
import 'package:vcc/presentation/base/layout_page.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/select_address_view.dart';
import 'package:vcc/presentation/views/widgets/app_bar/app_bar_custom.dart';

class SelectAddressArguments {
  final AddressEntity? addressSelected;
  final Function(AddressEntity address)? onSelectAddress;
  final List<CodeEntity>? districtsSelected;
  final Function(List<CodeEntity> districts)? onSelectDistricts;
  final bool isSelectOnlyProvince;
  final bool isSelectOnlyDistrict;

  SelectAddressArguments({
    this.onSelectAddress,
    this.onSelectDistricts,
    this.addressSelected,
    this.isSelectOnlyProvince = false,
    this.isSelectOnlyDistrict = false,
    this.districtsSelected = const <CodeEntity>[],
  });
}

class SelectAddressPage extends StatefulWidget {
  final SelectAddressArguments? arguments;

  const SelectAddressPage({
    super.key,
    this.arguments,
  });

  @override
  State<SelectAddressPage> createState() => _SelectAddressPageState();
}

class _SelectAddressPageState extends State<SelectAddressPage> {
  @override
  Widget build(BuildContext context) {
    return LayoutPage(
      appbar: const AppBarCustom(
        title: "Chọn địa chỉ",
      ),
      body: SelectAddressView(
        addressSelected: widget.arguments?.addressSelected,
        maxSelect: AddressType.fullAddress,
        onSelectAddress: widget.arguments?.onSelectAddress,
        onSelectDistricts: widget.arguments?.onSelectDistricts,
        isSelectOnlyProvince: widget.arguments?.isSelectOnlyProvince ?? false,
        isSelectOnlyDistrict: widget.arguments?.isSelectOnlyDistrict ?? false,
        districtsSelected: widget.arguments?.districtsSelected,
      ),
    );
  }
}
