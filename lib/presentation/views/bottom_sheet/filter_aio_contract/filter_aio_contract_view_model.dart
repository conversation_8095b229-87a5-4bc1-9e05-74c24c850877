import 'package:vcc/utils/string_utils.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_order_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_status_type.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_aio_contract/filter_aio_contract_state.dart';

final filterAioContractProvider = StateNotifierProvider.autoDispose<
    FilterAioContractViewModel,
    FilterAioContractState>((ref) => FilterAioContractViewModel(ref: ref));

List<AioContractStatusType> listStatus = [
  AioContractStatusType.notDone,
  AioContractStatusType.doing,
  AioContractStatusType.suggestSuspension,
  AioContractStatusType.suggestCanceled,
  AioContractStatusType.waitingDistrict,
  AioContractStatusType.delivered,
];
List<AioContractOrderType> listOrderType = [
  AioContractOrderType.deploy,
  AioContractOrderType.manage,
];
List<AioContractStatusType> listStatusContractSale = [
  AioContractStatusType.notPerformStaff,
  AioContractStatusType.notDone,
  AioContractStatusType.doing,
  AioContractStatusType.completed,
  AioContractStatusType.suggestCanceled,
  AioContractStatusType.delivered,
  AioContractStatusType.waitingGoodsBranch,
  AioContractStatusType.waitingDistrict,
  AioContractStatusType.preparingGoods,
  AioContractStatusType.waitingCreateOrder,
];
List<AioContractOrderType> listOrderTypeContractSale = [
  AioContractOrderType.create,
];

class FilterAioContractViewModel extends StateNotifier<FilterAioContractState> {
  final Ref ref;

  FilterAioContractViewModel({
    required this.ref,
  }) : super(const FilterAioContractState());

  void initData({
    bool isContractSale = false,
    DateTime? startDate,
    DateTime? endDate,
    List<AioContractStatusType>? statusSelected,
    List<AioContractOrderType>? orderTypeSelected,
  }) {
    state = state.copyWith(
      statusSelected: statusSelected,
      orderTypeSelected: orderTypeSelected,
      listStatus: isContractSale ? listStatusContractSale : listStatus,
      listOrderType: isContractSale ? listOrderTypeContractSale : listOrderType,
    );

    setStartDate(startDate);
    setEndDate(endDate);
  }

  void setStartDate(DateTime? startDate) {
    state = state.copyWith(
      startDateStr: startDate != null
          ? StringUtils.dateToStringFormat(
              startDate,
              DateTimeFormater.dateFormatVi,
            )
          : null,
      startDate: startDate,
    );
  }

  void setEndDate(DateTime? endDate) {
    state = state.copyWith(
      endDateStr: endDate != null
          ? StringUtils.dateToStringFormat(
              endDate,
              DateTimeFormater.dateFormatVi,
            )
          : null,
      endDate: endDate,
    );
  }

  void selectStatus(AioContractStatusType value) {
    final listStatusSelected = state.statusSelected ?? [];
    final isSelected = listStatusSelected.any(
      (element) => element.keyToServer == value.keyToServer,
    );
    if (isSelected) {
      listStatusSelected.removeWhere(
        (element) => element.keyToServer == value.keyToServer,
      );
    } else {
      listStatusSelected.add(value);
    }
    state = state.copyWith(
      statusSelected: listStatusSelected,
    );
  }

  void selectOrderType(AioContractOrderType value) {
    final listOrderTypeSelected = state.orderTypeSelected ?? [];
    final isSelected = listOrderTypeSelected.any(
      (element) => element.keyToServer == value.keyToServer,
    );
    if (isSelected) {
      listOrderTypeSelected.removeWhere(
        (element) => element.keyToServer == value.keyToServer,
      );
    } else {
      listOrderTypeSelected.add(value);
    }
    state = state.copyWith(
      orderTypeSelected: listOrderTypeSelected,
    );
  }

  void resetData() {
    state = state.copyWith(
      startDateStr: null,
      endDateStr: null,
      startDate: null,
      endDate: null,
      listStatus: null,
      statusSelected: null,
      orderTypeSelected: null,
    );
  }
}
