import 'package:base_ui/base_ui.dart';
import 'package:flutter/material.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:vcc/presentation/views/dialogs/date_range.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_order_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_status_type.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_aio_contract/filter_aio_contract_view_model.dart';

class FilterArgumentCallback {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<AioContractStatusType>? statusSelected;
  final List<AioContractOrderType>? orderTypeSelected;

  FilterArgumentCallback({
    this.startDate,
    this.endDate,
    this.statusSelected,
    this.orderTypeSelected,
  });
}

class FilterAioContractViewSheet extends StatefulHookConsumerWidget {
  final bool isContractSale;
  final DateTime? endDate;
  final DateTime? startDate;
  final List<AioContractStatusType>? statusSelected;
  final List<AioContractOrderType>? orderTypeSelected;
  final Function(FilterArgumentCallback value) onApply;

  const FilterAioContractViewSheet({
    super.key,
    this.isContractSale = false,
    this.startDate,
    this.endDate,
    this.statusSelected,
    this.orderTypeSelected,
    required this.onApply,
  });

  @override
  ConsumerState<FilterAioContractViewSheet> createState() =>
      _FilterTicketViewSheetState();
}

class _FilterTicketViewSheetState
    extends ConsumerState<FilterAioContractViewSheet> {
  @override
  void initState() {
    Future(() {
      ref.read(filterAioContractProvider.notifier).initData(
            startDate: widget.startDate,
            endDate: widget.endDate,
            statusSelected: widget.statusSelected,
            orderTypeSelected: widget.orderTypeSelected,
            isContractSale: widget.isContractSale,
          );
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(filterAioContractProvider);

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: BaseColors.backgroundWhite,
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: BaseSpacing.spacing2,
          horizontal: BaseSpacing.spacing4,
        ),
        child: SafeArea(
          child: Row(
            children: <Widget>[
              Expanded(
                child: BaseButton(
                  text: "Đặt lại",
                  backgroundColor: BaseColors.backgroundWhite,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: BaseColors.primary,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  textColor: BaseColors.primary,
                  onTap: () {
                    ref.read(filterAioContractProvider.notifier).resetData();
                    Navigator.pop(context);
                    widget.onApply(
                      FilterArgumentCallback(
                        statusSelected: state.listStatus,
                        orderTypeSelected: state.listOrderType,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(
                width: BaseSpacing.spacing3,
              ),
              Expanded(
                child: BaseButton(
                  text: "Áp dụng",
                  onTap: () {
                    Navigator.pop(context);
                    widget.onApply(
                      FilterArgumentCallback(
                        startDate: state.startDate,
                        endDate: state.endDate,
                        statusSelected:
                            state.statusSelected ?? state.listStatus,
                        orderTypeSelected:
                            state.orderTypeSelected ?? state.listOrderType,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(
          BaseSpacing.spacing4,
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing3,
                ),
                child: Text(
                  'Thời gian',
                  textAlign: TextAlign.start,
                  style: UITextStyle.body1Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              DropdownWidget(
                labelText: "Từ ngày - Đến ngày",
                enabled: true,
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                content: state.startDateStr != null
                    ? "${state.startDateStr} đến ${state.endDateStr}"
                    : null,
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      child: DateRangePicker(
                        startDate: state.startDate ??
                            DateTime.now().subtract(
                              const Duration(days: 1),
                            ),
                        endDate: state.endDate ?? DateTime.now(),
                        showButton: true,
                        lastDate: DateTime.now(),
                        onChange: (startDate, endDate) {
                          ref
                              .read(filterAioContractProvider.notifier)
                              .setStartDate(startDate);
                          ref
                              .read(filterAioContractProvider.notifier)
                              .setEndDate(endDate);
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing3,
                ),
                child: Text(
                  'Trạng thái',
                  textAlign: TextAlign.start,
                  style: UITextStyle.body1Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              SizedBox(
                width: double.infinity,
                child: Wrap(
                  spacing: BaseSpacing.spacing2,
                  runSpacing: BaseSpacing.spacing2,
                  children: (state.listStatus ?? []).map((item) {
                    return buildSingleCard(
                      content: item.display,
                      isSelected: state.statusSelected?.any(
                            (itemSelected) =>
                                itemSelected.keyToServer == item.keyToServer,
                          ) ??
                          false,
                      onTap: () {
                        ref
                            .read(filterAioContractProvider.notifier)
                            .selectStatus(item);
                      },
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing3,
                ),
                child: Text(
                  'Loại đơn',
                  textAlign: TextAlign.start,
                  style: UITextStyle.body1Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              SizedBox(
                width: double.infinity,
                child: Wrap(
                  spacing: BaseSpacing.spacing2,
                  runSpacing: BaseSpacing.spacing2,
                  children: (state.listOrderType ?? []).map((item) {
                    return buildSingleCard(
                      content: item.display,
                      isSelected: state.orderTypeSelected?.any(
                            (itemSelected) =>
                                itemSelected.keyToServer == item.keyToServer,
                          ) ??
                          false,
                      onTap: () {
                        ref
                            .read(filterAioContractProvider.notifier)
                            .selectOrderType(item);
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildSingleCard({
    String? content,
    bool isSelected = false,
    required Function() onTap,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? CoreColors.primary01 : BaseColors.borderDisable,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
            horizontal: BaseSpacing.spacing4,
          ),
          child: Text(
            content ?? '',
            style: UITextStyle.body2Regular.copyWith(
              color: isSelected ? BaseColors.primary : BaseColors.textBody,
            ),
          ),
        ),
      ),
    );
  }
}
