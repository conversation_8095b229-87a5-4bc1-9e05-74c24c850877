import 'package:equatable/equatable.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_order_type.dart';
import 'package:vcc/domain/enums/aio_contract/aio_contract_status_type.dart';

class FilterAioContractState extends Equatable {
  final LoadStatus loadStatus;
  final String? startDateStr;
  final String? endDateStr;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<AioContractStatusType>? listStatus;
  final List<AioContractStatusType>? statusSelected;
  final List<AioContractOrderType>? listOrderType;
  final List<AioContractOrderType>? orderTypeSelected;

  const FilterAioContractState({
    this.loadStatus = LoadStatus.initial,
    this.startDateStr,
    this.endDateStr,
    this.startDate,
    this.endDate,
    this.listStatus,
    this.statusSelected,
    this.listOrderType,
    this.orderTypeSelected,
  });

  FilterAioContractState copyWith({
    LoadStatus? loadStatus,
    CodeEntity? province,
    List<CodeEntity>? districts,
    String? startDateStr,
    String? endDateStr,
    DateTime? startDate,
    DateTime? endDate,
    List<AioContractStatusType>? listStatus,
    List<AioContractStatusType>? statusSelected,
    List<AioContractOrderType>? listOrderType,
    List<AioContractOrderType>? orderTypeSelected,
  }) {
    return FilterAioContractState(
      loadStatus: loadStatus ?? this.loadStatus,
      startDateStr: startDateStr ?? this.startDateStr,
      endDateStr: endDateStr ?? this.endDateStr,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      listStatus: listStatus ?? this.listStatus,
      statusSelected: statusSelected ?? this.statusSelected,
      listOrderType: listOrderType ?? this.listOrderType,
      orderTypeSelected: orderTypeSelected ?? this.orderTypeSelected,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        startDateStr,
        endDateStr,
        startDate,
        endDate,
        listStatus,
        statusSelected,
        listOrderType,
        orderTypeSelected,
      ];
}
