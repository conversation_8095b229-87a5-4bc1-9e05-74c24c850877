import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/enums/mission/mission_kpi.dart';
import 'package:vcc/domain/enums/mission/mission_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/dialogs/date_range.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'filter_mission_view_model.dart';

class FilterArgumentCallback {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<MissionTypeEnum>? missionTypeSelected;
  final List<MissionKpiEnum>? missionKpiSelected;

  FilterArgumentCallback({
    this.startDate,
    this.endDate,
    this.missionTypeSelected,
    this.missionKpiSelected,
  });
}

class FilterMissionViewSheet extends StatefulHookConsumerWidget {
  final DateTime? startDate;
  final DateTime? endDate;
  final List<MissionTypeEnum>? missionTypeSelected;
  final List<MissionKpiEnum>? missionKpiSelected;
  final Function(FilterArgumentCallback value) onApply;

  const FilterMissionViewSheet({
    super.key,
    this.startDate,
    this.endDate,
    this.missionTypeSelected,
    this.missionKpiSelected,
    required this.onApply,
  });

  @override
  ConsumerState<FilterMissionViewSheet> createState() =>
      _FilterTicketViewSheetState();
}

class _FilterTicketViewSheetState
    extends ConsumerState<FilterMissionViewSheet> {
  @override
  void initState() {
    Future(() {
      ref.read(filterMissionProvider.notifier).initData(
            startDate: widget.startDate,
            endDate: widget.endDate,
            missionTypeSelected: widget.missionTypeSelected,
            missionKpiSelected: widget.missionKpiSelected,
          );
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(filterMissionProvider);

    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: BaseColors.backgroundWhite,
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(
          vertical: BaseSpacing.spacing2,
          horizontal: BaseSpacing.spacing4,
        ),
        child: SafeArea(
          child: Row(
            children: <Widget>[
              Expanded(
                child: BaseButton(
                  text: "Đặt lại",
                  backgroundColor: BaseColors.backgroundWhite,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: BaseColors.primary,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  textColor: BaseColors.primary,
                  onTap: () {
                    ref.read(filterMissionProvider.notifier).resetData();
                    Navigator.pop(context);
                    widget.onApply(
                      FilterArgumentCallback(),
                    );
                  },
                ),
              ),
              const SizedBox(
                width: BaseSpacing.spacing3,
              ),
              Expanded(
                child: BaseButton(
                  text: "Áp dụng",
                  onTap: () {
                    Navigator.pop(context);
                    widget.onApply(
                      FilterArgumentCallback(
                        startDate: state.startDate,
                        endDate: state.endDate,
                        missionTypeSelected: state.missionTypeSelected,
                        missionKpiSelected: state.missionKpiSelected,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(
          BaseSpacing.spacing4,
        ),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing3,
                ),
                child: Text(
                  'Ngày tạo',
                  textAlign: TextAlign.start,
                  style: UITextStyle.body1Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              DropdownWidget(
                labelText: "Từ ngày - Đến ngày",
                enabled: true,
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                content: state.startDateStr != null
                    ? "${state.startDateStr} đến ${state.endDateStr}"
                    : null,
                onTap: () {
                  showDialog(
                    context: context,
                    builder: (context) => Dialog(
                      child: DateRangePicker(
                        startDate: state.startDate ??
                            DateTime.now().subtract(
                              const Duration(days: 1),
                            ),
                        endDate: state.endDate ?? DateTime.now(),
                        showButton: true,
                        lastDate: DateTime.now(),
                        onChange: (startDate, endDate) {
                          ref
                              .read(filterMissionProvider.notifier)
                              .setStartDate(startDate);
                          ref
                              .read(filterMissionProvider.notifier)
                              .setEndDate(endDate);
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                  );
                },
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing3,
                ),
                child: Text(
                  'Loại nhiệm vụ',
                  textAlign: TextAlign.start,
                  style: UITextStyle.body1Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              SizedBox(
                width: double.infinity,
                child: Wrap(
                  spacing: BaseSpacing.spacing2,
                  runSpacing: BaseSpacing.spacing2,
                  children: listMissionType.map((item) {
                    return buildSingleCard(
                      content: item.label,
                      isSelected: state.missionTypeSelected?.any(
                            (itemSelected) =>
                                itemSelected.keyToServer == item.keyToServer,
                          ) ??
                          false,
                      onTap: () {
                        ref
                            .read(filterMissionProvider.notifier)
                            .selectMissionType(item);
                      },
                    );
                  }).toList(),
                ),
              ),
              const SizedBox(height: 20),
              Padding(
                padding: const EdgeInsets.only(
                  bottom: BaseSpacing.spacing3,
                ),
                child: Text(
                  'KPI',
                  textAlign: TextAlign.start,
                  style: UITextStyle.body1Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
              ),
              SizedBox(
                width: double.infinity,
                child: Wrap(
                  spacing: BaseSpacing.spacing2,
                  runSpacing: BaseSpacing.spacing2,
                  children: listMissionExpired.map((item) {
                    return buildSingleCard(
                      content: item.label,
                      isSelected: state.missionKpiSelected?.any(
                            (itemSelected) =>
                                itemSelected.keyToServer == item.keyToServer,
                          ) ??
                          false,
                      onTap: () {
                        ref
                            .read(filterMissionProvider.notifier)
                            .selectMissionExpired(item);
                      },
                    );
                  }).toList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildSingleCard({
    String? content,
    bool isSelected = false,
    required Function() onTap,
  }) {
    return InkWellWidget(
      onTap: onTap,
      child: Container(
        decoration: BoxDecoration(
          color: isSelected ? CoreColors.primary01 : BaseColors.borderDisable,
          borderRadius: BorderRadius.circular(8),
        ),
        child: Padding(
          padding: const EdgeInsets.symmetric(
            vertical: BaseSpacing.spacing2,
            horizontal: BaseSpacing.spacing4,
          ),
          child: Text(
            content ?? '',
            style: UITextStyle.body2Regular.copyWith(
              color: isSelected ? BaseColors.primary : BaseColors.textBody,
            ),
          ),
        ),
      ),
    );
  }
}
