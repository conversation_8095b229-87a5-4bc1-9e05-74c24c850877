import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/domain/enums/mission/mission_kpi.dart';
import 'package:vcc/domain/enums/mission/mission_type.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_mission/filter_mission_state.dart';
import 'package:vcc/utils/string_utils.dart';

final filterMissionProvider = StateNotifierProvider.autoDispose<
    FilterMissionViewModel,
    FilterMissionState>((ref) => FilterMissionViewModel(ref: ref));

List<MissionTypeEnum> listMissionType = [
  MissionTypeEnum.plan,
  MissionTypeEnum.incident,
];

List<MissionKpiEnum> listMissionExpired = [
  MissionKpiEnum.onTime,
  MissionKpiEnum.overdue,
];

class FilterMissionViewModel extends StateNotifier<FilterMissionState> {
  final Ref ref;

  FilterMissionViewModel({
    required this.ref,
  }) : super(const FilterMissionState());

  void initData({
    DateTime? startDate,
    DateTime? endDate,
    List<MissionTypeEnum>? missionTypeSelected,
    List<MissionKpiEnum>? missionKpiSelected,
  }) {
    state = state.copyWith(
      missionTypeSelected: missionTypeSelected,
      missionKpiSelected: missionKpiSelected,
    );

    setStartDate(startDate);
    setEndDate(endDate);
  }

  void setStartDate(DateTime? startDate) {
    state = state.copyWith(
      startDateStr: startDate != null
          ? StringUtils.dateToStringFormat(
              startDate,
              DateTimeFormater.dateFormatVi,
            )
          : null,
      startDate: startDate,
    );
  }

  void setEndDate(DateTime? endDate) {
    state = state.copyWith(
      endDateStr: endDate != null
          ? StringUtils.dateToStringFormat(
              endDate,
              DateTimeFormater.dateFormatVi,
            )
          : null,
      endDate: endDate,
    );
  }

  void selectMissionType(MissionTypeEnum value) {
    final listMissionTypeSelected = state.missionTypeSelected ?? [];
    final isSelected = listMissionTypeSelected.any(
      (element) => element.keyToServer == value.keyToServer,
    );
    if (isSelected) {
      listMissionTypeSelected.removeWhere(
        (element) => element.keyToServer == value.keyToServer,
      );
    } else {
      listMissionTypeSelected.add(value);
    }
    state = state.copyWith(
      missionTypeSelected: listMissionTypeSelected,
    );
  }

  void selectMissionExpired(MissionKpiEnum value) {
    final listMissionExpiredSelected = state.missionKpiSelected ?? [];
    final isSelected = listMissionExpiredSelected.any(
      (element) => element.keyToServer == value.keyToServer,
    );
    if (isSelected) {
      listMissionExpiredSelected.removeWhere(
        (element) => element.keyToServer == value.keyToServer,
      );
    } else {
      listMissionExpiredSelected.add(value);
    }
    state = state.copyWith(
      missionKpiSelected: listMissionExpiredSelected,
    );
  }

  void resetData() {
    state = state.copyWith(
      startDateStr: null,
      endDateStr: null,
      startDate: null,
      endDate: null,
      missionTypeSelected: null,
      missionKpiSelected: null,
    );
  }
}
