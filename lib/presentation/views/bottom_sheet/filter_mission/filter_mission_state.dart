import 'package:equatable/equatable.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/mission/mission_kpi.dart';
import 'package:vcc/domain/enums/mission/mission_type.dart';

class FilterMissionState extends Equatable {
  final LoadStatus loadStatus;
  final String? startDateStr;
  final String? endDateStr;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<MissionTypeEnum>? missionTypeSelected;
  final List<MissionKpiEnum>? missionKpiSelected;

  const FilterMissionState({
    this.loadStatus = LoadStatus.initial,
    this.startDateStr,
    this.endDateStr,
    this.startDate,
    this.endDate,
    this.missionTypeSelected,
    this.missionKpiSelected,
  });

  FilterMissionState copyWith({
    LoadStatus? loadStatus,
    CodeEntity? province,
    List<CodeEntity>? districts,
    String? startDateStr,
    String? endDateStr,
    DateTime? startDate,
    DateTime? endDate,
    List<MissionTypeEnum>? missionTypeSelected,
    List<MissionKpiEnum>? missionKpiSelected,
  }) {
    return FilterMissionState(
      loadStatus: loadStatus ?? this.loadStatus,
      startDateStr: startDateStr ?? this.startDateStr,
      endDateStr: endDateStr ?? this.endDateStr,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      missionTypeSelected: missionTypeSelected ?? this.missionTypeSelected,
      missionKpiSelected: missionKpiSelected ?? this.missionKpiSelected,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        startDateStr,
        endDateStr,
        startDate,
        endDate,
        missionTypeSelected,
        missionKpiSelected,
      ];
}
