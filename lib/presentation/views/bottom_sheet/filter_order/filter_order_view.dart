import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/enums/order_type_filter.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_order/widgets/chip_entity.dart';
import 'package:vcc/presentation/views/dialogs/date_range.dart';
import 'package:vcc/presentation/views/routers/paths.dart';
import 'package:vcc/presentation/views/widgets/pages/select_address_page.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';

import 'filter_order_view_model.dart';

class FilterArgumentCallback {
  final List<String>? participantTypes;
  final CodeEntity? province;
  final List<CodeEntity>? districts;
  final DateTime? startTime;
  final DateTime? endTime;
  final List<OrderTypeFilter>? ordersType;

  FilterArgumentCallback({
    this.districts,
    this.participantTypes,
    this.province,
    this.startTime,
    this.endTime,
    this.ordersType,
  });
}

class FilterOrderViewSheet extends StatefulHookConsumerWidget {
  final Function(FilterArgumentCallback value) onApply;
  final String? startDateStr;
  final String? endDateStr;
  final List<String>? orderUserType;
  final List<String>? ordersType;
  final CodeEntity? province;
  final List<CodeEntity>? districts;
  final DateTime? startDate;
  final DateTime? endDate;
  final bool? showOrderType;

  const FilterOrderViewSheet({
    super.key,
    required this.onApply,
    this.startDateStr,
    this.endDateStr,
    this.orderUserType,
    this.province,
    this.districts,
    this.endDate,
    this.startDate,
    this.ordersType,
    this.showOrderType,
  });

  @override
  ConsumerState<FilterOrderViewSheet> createState() => _FilterOrderViewState();
}

class _FilterOrderViewState extends ConsumerState<FilterOrderViewSheet> {
  void onSelectAddress({
    bool? isSelectOnlyProvince,
  }) async {
    var filterState = ref.watch(filterOrderProvider);
    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        onSelectAddress: (address) {
          ref.read(filterOrderProvider.notifier).selectAddress(address);
        },
        addressSelected: AddressEntity(province: filterState.province),
        isSelectOnlyProvince: isSelectOnlyProvince ?? false,
      ),
    );
  }

  void onSelectDistricts() async {
    var filterState = ref.watch(filterOrderProvider);
    await context.push(
      RouterPaths.selectAddress,
      extra: SelectAddressArguments(
        onSelectDistricts: (districts) {
          ref.read(filterOrderProvider.notifier).selectDistricts(districts);
        },
        districtsSelected: filterState.districts,
        addressSelected:
            AddressEntity(province: ref.watch(filterOrderProvider).province),
        isSelectOnlyDistrict: true,
      ),
    );
  }

  @override
  void initState() {
    Future(() {
      ref.read(filterOrderProvider.notifier).initData(
            startDate: widget.startDate,
            endDate: widget.endDate,
            districts: widget.districts,
            address: widget.province != null
                ? AddressEntity(
                    province: widget.province,
                  )
                : null,
            orderUserTypeSelected: widget.orderUserType,
            ordersTypeSelect: widget.ordersType,
          );
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var filterState = ref.watch(filterOrderProvider);

    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      resizeToAvoidBottomInset: false,
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: Row(
            children: <Widget>[
              Expanded(
                child: BaseButton(
                  text: "Đặt lại",
                  backgroundColor: BaseColors.backgroundWhite,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: BaseColors.primary,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  textColor: BaseColors.primary,
                  onTap: () {
                    ref.read(filterOrderProvider.notifier).clearDataFilter();
                    Navigator.pop(context);
                    widget.onApply(
                      FilterArgumentCallback(),
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: BaseButton(
                  text: "Áp dụng",
                  onTap: () {
                    Navigator.pop(context);
                    var state = ref.watch(filterOrderProvider);

                    List<String> participantTypes = [];
                    for (var element in (state.orderUserType ?? [])) {
                      if (element.isSelected) {
                        participantTypes.add(element.code);
                      }
                    }

                    widget.onApply(
                      FilterArgumentCallback(
                        startTime: filterState.startDate,
                        endTime: filterState.endDate,
                        participantTypes: participantTypes,
                        province: filterState.province,
                        districts: filterState.districts,
                        ordersType: filterState.ordersTypeSelected,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Thời gian hẹn lịch',
                textAlign: TextAlign.start,
                style: UITextStyle.body1Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              const SizedBox(height: 12),
              DropdownWidget(
                labelText: "Từ ngày - Đến ngày",
                enabled: true,
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                content: filterState.startDateStr != null
                    ? "${filterState.startDateStr} đến ${filterState.endDateStr}"
                    : null,
                onTap: () {
                  showDialog(
                    builder: (context) => Dialog(
                      child: DateRangePicker(
                        startDate: filterState.startDate ??
                            DateTime.now().subtract(const Duration(days: 1)),
                        endDate: filterState.endDate ?? DateTime.now(),
                        showButton: true,
                        lastDate: DateTime.now(),
                        onChange: (startDate, endDate) {
                          ref.read(filterOrderProvider.notifier).setStartDate(
                                startDate,
                              );

                          ref.read(filterOrderProvider.notifier).setEndDate(
                                endDate,
                              );

                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    context: context,
                  );
                },
              ),
              const SizedBox(height: 20),
              Text(
                'Loại đơn',
                textAlign: TextAlign.start,
                style: UITextStyle.body1Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              const SizedBox(height: 12),
              SizedBox(
                width: double.infinity,
                child: Wrap(
                  children: List.generate(
                    filterState.orderUserType?.length ?? 0,
                    (index) {
                      return buildSingleCard(
                        ChipEntity(
                          content: filterState.orderUserType?[index].content,
                          code: filterState.orderUserType?[index].code,
                          isSelected:
                              filterState.orderUserType?[index].isSelected ??
                                  false,
                        ),
                        onTap: () {
                          ref
                              .read(filterOrderProvider.notifier)
                              .selectOrderUserType(index);
                        },
                      );
                    },
                  ),
                ),
              ),
              const SizedBox(height: 20),
              if (widget.showOrderType ?? false) ...[
                Text(
                  'Loại đơn hàng',
                  textAlign: TextAlign.start,
                  style: UITextStyle.body1Medium.copyWith(
                    color: BaseColors.textLabel,
                  ),
                ),
                const SizedBox(height: 12),
                SizedBox(
                  width: double.infinity,
                  child: Wrap(
                    children: List.generate(
                      filterState.ordersType?.length ?? 0,
                      (index) {
                        return buildSingleCard(
                          ChipEntity(
                            content: filterState.ordersType?[index].label,
                            code: filterState.ordersType?[index].keyToServer,
                            isSelected: filterState.ordersTypeSelected
                                    ?.contains(
                                        filterState.ordersType?[index]) ??
                                false,
                          ),
                          onTap: () {
                            ref
                                .read(filterOrderProvider.notifier)
                                .selectOrderType(index);
                          },
                        );
                      },
                    ),
                  ),
                ),
                const SizedBox(height: 20),
              ],
              DropdownWidget(
                labelText: "Tỉnh/Thành phố",
                enabled: true,
                content: filterState.province?.name ?? "",
                suffix: MyAssets.icons.iconArrowRightS20.svg(),
                onTap: () {
                  onSelectAddress(isSelectOnlyProvince: true);
                },
              ),
              const SizedBox(height: 20),
              DropdownWidget(
                labelText: "Quận/Huyện",
                enabled: filterState.province?.code != null,
                content: "",
                contentWidget: (filterState.districts ?? []).isEmpty
                    ? null
                    : Wrap(
                        children: List.generate(
                          (filterState.districts ?? []).length,
                          (index) {
                            return Padding(
                              padding: const EdgeInsets.only(
                                right: 8.0,
                                bottom: 8,
                              ),
                              child: Container(
                                decoration: BoxDecoration(
                                  color: BaseColors.borderDisable,
                                  borderRadius: BorderRadius.circular(8),
                                ),
                                child: Padding(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 7,
                                  ),
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      Text(
                                        filterState.districts![index].name ??
                                            '',
                                        style:
                                            UITextStyle.caption1Medium.copyWith(
                                          color: BaseColors.textBody,
                                        ),
                                      ),
                                      const SizedBox(
                                        width: 4,
                                      ),
                                      InkWellWidget(
                                        onTap: () {
                                          ref
                                              .read(
                                                  filterOrderProvider.notifier)
                                              .removeDistrict(
                                                index,
                                              );
                                        },
                                        child: Icon(
                                          Icons.close,
                                          size: 16,
                                          color: BaseColors.textSubtitle,
                                        ),
                                      ),
                                    ],
                                  ),
                                ),
                              ),
                            );
                          },
                        ),
                      ),
                suffix: MyAssets.icons.iconArrowRightS20.svg(),
                onTap: () {
                  onSelectDistricts();
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildSingleCard(
    ChipEntity item, {
    required Function() onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(
        right: 8,
        bottom: 8,
      ),
      child: InkWellWidget(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: item.isSelected
                ? CoreColors.primary01
                : BaseColors.borderDisable,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 7,
            ),
            child: Text(
              item.content ?? "",
              style: UITextStyle.body2Regular.copyWith(
                color:
                    item.isSelected ? BaseColors.primary : BaseColors.textBody,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
