import 'package:equatable/equatable.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/data/repositories/user_profile_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_user_select_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/worker_type.dart';

part 'choose_staff_state.dart';

final chooseStaffProvider =
    StateNotifierProvider.autoDispose<ChooseStaffViewModel, ChooseStaffState>(
  (ref) => ChooseStaffViewModel(ref: ref),
);

class ChooseStaffViewModel extends StateNotifier<ChooseStaffState> {
  final Ref ref;

  ChooseStaffViewModel({
    required this.ref,
  }) : super(const ChooseStaffState());

  void initData({
    OrderType? orderType,
    WorkerType? workerType,
    String? provinceCode,
    String? wardCode,
    String? keyword,
    bool? needVerify,
    AioUserSelectType? aioUserSelectType,
    DateTime? schedule,
  }) {
    state = state.copyWith(
      orderType: orderType,
      workerType: workerType,
      keyword: keyword,
      needVerify: needVerify,
      schedule: schedule ?? DateTime.now(),
    );

    if (workerType == WorkerType.delivery ||
        workerType == WorkerType.assign ||
        workerType == WorkerType.distributionCreate) {
      getListWorker(
        keyword: keyword,
        provinceCode: provinceCode,
        wardCode: wardCode,
        orderType: orderType,
      );
    } else if (workerType == WorkerType.transfer) {
      getListTransferWorker(
        keyword: keyword,
        provinceCode: provinceCode,
        wardCode: wardCode,
        orderType: orderType,
      );
    } else if (workerType == WorkerType.oft3) {
      getListOFT3(
        keyword: keyword,
      );
    } else {
      if (aioUserSelectType == AioUserSelectType.internalStaff) {
        getStaffByEmployeeCode(
          keyword: keyword,
        );
      } else if (aioUserSelectType == AioUserSelectType.referUser) {
        getForAutoCompleteSysUser(
          keyword: keyword,
        );
      } else if (aioUserSelectType == AioUserSelectType.performUser) {
        getListUserStock(
          keyword: keyword,
          areaCode: provinceCode,
        );
      } else {
        getListStaff(
          keyword: keyword,
        );
      }
    }
  }

  void fetchNextData({
    OrderType? orderType,
    String? provinceCode,
    String? wardCode,
    AioUserSelectType? aioUserSelectType,
  }) {
    if (state.workerType == WorkerType.delivery) {
      fetchNextWorker(
        provinceCode: provinceCode,
        wardCode: wardCode,
        orderType: state.orderType,
      );
    } else if (state.workerType == WorkerType.assign) {
      fetchNextTransferWorker(
        provinceCode: provinceCode,
        wardCode: wardCode,
      );
    } else if (state.workerType == WorkerType.transfer) {
      fetchNextTransferWorker(
        provinceCode: provinceCode,
        wardCode: wardCode,
      );
    } else if (state.workerType == WorkerType.oft3) {
      fetchNextOFT3();
    } else {
      if (aioUserSelectType != null) {
      } else {
        fetchNextStaff();
      }
    }
  }

  void selectStaff(InternalStaffEntity staff) {
    state = state.copyWith(
      staffSelected: staff,
    );
  }

  void getListStaff({
    String? keyword,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListStaff(
        keyword: keyword,
        page: 0,
        pageSize: BaseConstant.defaultLimitSize,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            totalResult: data.total ?? 0,
            listStaff: data.data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void getStaffByEmployeeCode({
    String? keyword,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result =
          await appLocator<AioOrderRepository>().getStaffByEmployeeCode(
        keySearch: keyword,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            totalResult: data.total ?? 0,
            listStaff: data.data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void getForAutoCompleteSysUser({
    String? keyword,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result =
          await appLocator<AioOrderRepository>().getForAutoCompleteSysUser(
        keySearch: keyword,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            totalResult: data.total ?? 0,
            listStaff: data.data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void getListUserStock({
    String? keyword,
    String? areaCode,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<AioOrderRepository>().getListUserStock(
        keySearch: keyword,
        areaCode: areaCode,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            totalResult: data.total ?? 0,
            listStaff: data.data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void fetchNextStaff() async {
    if (state.listStaff!.length >= state.totalResult) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListStaff(
        keyword: state.keyword,
        page: state.currentPage + 1,
        pageSize: BaseConstant.defaultLimitSize,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            currentPage: state.currentPage + 1,
            listStaff: state.listStaff! + (data.data ?? []),
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void getListWorker({
    String? keyword,
    String? provinceCode,
    String? wardCode,
    OrderType? orderType,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListWorker(
        provinceCode: provinceCode,
        wardCode: wardCode,
        schedule: state.schedule?.toIso8601String(),
        orderType: orderType?.keyToServer,
        keyword: keyword,
        page: 0,
        pageSize: BaseConstant.defaultLimitSize,
        workerType: state.workerType?.keyToServer,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            totalResult: data.total ?? 0,
            listStaff: data.data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void fetchNextWorker({
    String? provinceCode,
    String? wardCode,
    OrderType? orderType,
  }) async {
    if (state.listStaff!.length >= state.totalResult) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListWorker(
        provinceCode: provinceCode,
        wardCode: wardCode,
        orderType: orderType?.keyToServer,
        keyword: state.keyword,
        page: state.currentPage + 1,
        pageSize: BaseConstant.defaultLimitSize,
        schedule: state.schedule?.toIso8601String(),
        workerType: state.workerType?.keyToServer,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            currentPage: state.currentPage + 1,
            listStaff: state.listStaff! + (data.data ?? []),
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void getListTransferWorker({
    String? keyword,
    String? provinceCode,
    String? wardCode,
    OrderType? orderType,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListWorker(
        keyword: keyword,
        page: 0,
        pageSize: BaseConstant.defaultLimitSize,
        provinceCode: provinceCode,
        wardCode: wardCode,
        orderType: orderType?.keyToServer,
        workerType: WorkerType.transfer.keyToServer,
        schedule: state.schedule?.toIso8601String(),
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            totalResult: data.total ?? 0,
            listStaff: data.data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void fetchNextTransferWorker({
    String? provinceCode,
    String? wardCode,
  }) async {
    if (state.listStaff!.length >= state.totalResult) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListWorker(
        keyword: state.keyword,
        page: state.currentPage + 1,
        pageSize: BaseConstant.defaultLimitSize,
        provinceCode: provinceCode,
        wardCode: wardCode,
        schedule: state.schedule?.toIso8601String(),
        workerType: WorkerType.transfer.keyToServer,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            currentPage: state.currentPage + 1,
            listStaff: state.listStaff! + (data.data ?? []),
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void getListOFT3({
    String? keyword,
  }) async {
    state = state.copyWith(
      loadStatus: LoadStatus.loading,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListOFT3(
        keyword: keyword,
        schedule: state.schedule?.toIso8601String(),
        page: 0,
        pageSize: BaseConstant.defaultLimitSize,
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            totalResult: data.total ?? 0,
            listStaff: data.data,
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }

  void fetchNextOFT3() async {
    if (state.listStaff!.length >= state.totalResult) {
      return;
    }

    if (state.loadStatus != LoadStatus.success) {
      return;
    }

    state = state.copyWith(
      loadStatus: LoadStatus.loadMore,
    );

    try {
      final result = await appLocator<UserProfileRepository>().getListOFT3(
        keyword: state.keyword,
        page: state.currentPage + 1,
        pageSize: BaseConstant.defaultLimitSize,
        schedule: state.schedule?.toIso8601String(),
      );

      await result.when(
        success: (data) async {
          state = state.copyWith(
            currentPage: state.currentPage + 1,
            listStaff: state.listStaff! + (data.data ?? []),
            loadStatus: LoadStatus.success,
          );
        },
        error: (err) async {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
            message: err.message,
          );
        },
      );
    } catch (error) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
    }
  }
}
