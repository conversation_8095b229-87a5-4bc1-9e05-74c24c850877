import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/constants.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/aio_contract/aio_user_select_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/order_type.dart';
import 'package:vcc/domain/enums/worker_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_staff/choose_staff_view_model.dart';
import 'package:vcc/presentation/views/widgets/divider_widget.dart';
import 'package:vcc/presentation/views/widgets/empty_list_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/refresh_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/text_field/search_text_field_widget.dart';
import 'package:vcc/utils/debouncer.dart';

class ChooseStaffPage extends StatefulHookConsumerWidget {
  final OrderType? orderType;
  final WorkerType? workerType;
  final String? provinceCode;
  final String? wardCode;
  final bool needVerify;
  final Function(InternalStaffEntity staff) onSelectStaff;
  final AioUserSelectType? aioUserSelectType;
  final DateTime? scheduleTime;

  const ChooseStaffPage({
    super.key,
    this.orderType,
    this.workerType,
    this.provinceCode,
    this.wardCode,
    this.needVerify = false,
    required this.onSelectStaff,
    this.aioUserSelectType,
    this.scheduleTime,
  });

  @override
  ConsumerState<ChooseStaffPage> createState() => _ChooseStaffPageState();
}

class _ChooseStaffPageState extends ConsumerState<ChooseStaffPage> {
  late TextEditingController searchController;
  late ScrollController controller;
  late Debounce<String> deBouncer;

  @override
  void initState() {
    searchController = TextEditingController();
    controller = ScrollController();
    controller.addListener(_scrollListener);

    deBouncer = Debounce<String>(
      const Duration(milliseconds: 500),
      (value) {
        ref.read(chooseStaffProvider.notifier).initData(
              orderType: widget.orderType,
              workerType: widget.workerType,
              provinceCode: widget.provinceCode,
              wardCode: widget.wardCode,
              aioUserSelectType: widget.aioUserSelectType,
              keyword: value,
            );
      },
    );

    Future(() {
      ref.read(chooseStaffProvider.notifier).initData(
            orderType: widget.orderType,
            workerType: widget.workerType,
            provinceCode: widget.provinceCode,
            wardCode: widget.wardCode,
            needVerify: widget.needVerify,
            aioUserSelectType: widget.aioUserSelectType,
          );
    });
    super.initState();
  }

  @override
  void dispose() {
    searchController.dispose();
    controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: () {
        FocusScope.of(context).unfocus();
      },
      child: Column(
        children: <Widget>[
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 8,
            ),
            child: SearchTextFieldWidget(
              controller: searchController,
              hintText: "Họ tên, mã nhân viên",
              onChanged: (value) {
                deBouncer.value = value;
              },
            ),
          ),
          Expanded(
            child: _buildListStaff(),
          ),
        ],
      ),
    );
  }

  Future<void> refreshData() async {
    ref.read(chooseStaffProvider.notifier).initData(
          orderType: widget.orderType,
          workerType: widget.workerType,
          provinceCode: widget.provinceCode,
          wardCode: widget.wardCode,
          keyword: searchController.text,
          needVerify: widget.needVerify,
          aioUserSelectType: widget.aioUserSelectType,
        );
  }

  void _scrollListener() {
    final maxScroll = controller.position.maxScrollExtent;
    final currentScroll = controller.position.pixels;
    if (maxScroll - currentScroll <= BaseConstant.scrollThreshold) {
      ref.read(chooseStaffProvider.notifier).fetchNextData(
            orderType: widget.orderType,
            provinceCode: widget.provinceCode,
            wardCode: widget.wardCode,
            aioUserSelectType: widget.aioUserSelectType,
          );
    }
  }

  Widget _buildListStaff() {
    final state = ref.watch(chooseStaffProvider);

    if (state.loadStatus == LoadStatus.loading) {
      return const LoadingIndicatorWidget();
    } else if (state.loadStatus == LoadStatus.failure) {
      return EmptyListWidget(
        title: state.message ?? 'Đã có lỗi xảy ra!',
        onRefresh: refreshData,
      );
    } else {
      if ((state.listStaff ?? []).isEmpty) {
        return EmptyListWidget(
          title: "Không có dữ liệu",
          onRefresh: refreshData,
        );
      } else {
        return RefreshIndicatorWidget(
          onRefresh: refreshData,
          child: ListView.separated(
            controller: controller,
            padding: EdgeInsets.zero,
            itemCount: state.listStaff?.length ?? 0,
            separatorBuilder: (_, __) {
              return const Padding(
                padding: EdgeInsets.only(left: 16),
                child: DividerWidget(),
              );
            },
            itemBuilder: (context, index) {
              final item = state.listStaff![index];

              return InkWellWidget(
                onTap: () {
                  if (state.needVerify) {
                    ref.read(chooseStaffProvider.notifier).selectStaff(
                          item,
                        );
                  } else {
                    context.pop();
                    widget.onSelectStaff.call(item);
                  }
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  child: Row(
                    children: [
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: <Widget>[
                            Text(
                              item.fullName ?? '',
                              style: UITextStyle.body2Medium.copyWith(
                                color: BaseColors.textTitle,
                              ),
                            ),
                            Text(
                              item.username ??
                                  item.employeeCode ??
                                  item.loginName ??
                                  '',
                              style: UITextStyle.caption1Regular.copyWith(
                                color: BaseColors.textSubtitle,
                              ),
                            ),
                          ],
                        ),
                      ),
                      Visibility(
                        visible: (state.staffSelected?.username ??
                                state.staffSelected?.employeeCode ??
                                state.staffSelected?.loginName) ==
                            (item.username ??
                                item.employeeCode ??
                                item.loginName ??
                                ''),
                        child: MyAssets.icons.iconCheckedS24.svg(),
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      }
    }
  }
}
