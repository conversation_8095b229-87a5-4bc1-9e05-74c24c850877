import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/domain/entities/order/detail_order_entity.dart';
import 'package:vcc/domain/entities/user/internal_staff_entity.dart';
import 'package:vcc/domain/enums/assign_type.dart';
import 'package:vcc/domain/enums/dialog_status.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/worker_type.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/presentation/base/app_bottom_sheet.dart';
import 'package:vcc/presentation/base/app_dialog.dart';
import 'package:vcc/presentation/views/bottom_sheet/assign_order/assign_order_view_model.dart';
import 'package:vcc/presentation/views/bottom_sheet/choose_staff/choose_staff_view.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/card_custom_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'package:vcc/presentation/views/widgets/loading_indicator_widget.dart';
import 'package:vcc/presentation/views/widgets/radio_widget.dart';

class AssignOrderView extends StatefulHookConsumerWidget {
  final DetailOrderEntity orderInfo;
  final Function(InternalStaffEntity staff, AssignType type)? onAssign;

  const AssignOrderView({
    super.key,
    required this.orderInfo,
    this.onAssign,
  });

  @override
  ConsumerState<ConsumerStatefulWidget> createState() => _AssignOrderView();
}

class _AssignOrderView extends ConsumerState<AssignOrderView> {
  @override
  void initState() {
    getData();
    super.initState();
  }

  Future<void> getData() async {
    Future(() {
      ref.read(assignOrderProvider.notifier).initData(
            widget.orderInfo.orderCode ?? '',
          );
    });
  }

  @override
  Widget build(BuildContext context) {
    var state = ref.watch(assignOrderProvider);

    if (state.loadStatus == LoadStatus.loading) {
      return const Center(
        child: LoadingIndicatorWidget(),
      );
    } else {
      return Stack(
        children: [
          Container(
            color: Colors.white,
            child: ListView(
              shrinkWrap: true,
              physics: const BouncingScrollPhysics(),
              children: <Widget>[
                Visibility(
                  visible: (state.countAssign ?? 0) > 0,
                  child: Container(
                    width: double.infinity,
                    color: BaseColors.backgroundGray,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    child: Text(
                      "Đã Giao/Chuyển việc: ${state.countAssign ?? 0} lần",
                      style: UITextStyle.body2Medium.copyWith(
                        color: BaseColors.textLabel,
                      ),
                    ),
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: <Widget>[
                      if (state.assignType == AssignType.assign) ...[
                        Container(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8),
                            border: Border.all(
                              color: BaseColors.infoBorder,
                            ),
                            color: BaseColors.infoSurface,
                          ),
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Padding(
                                padding: const EdgeInsets.only(top: 2),
                                child: MyAssets.icons.iconInfoBlue.svg(),
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  "Khi bạn giao việc sẽ đồng thời thông báo đến thợ được giao và khách hàng",
                                  style: UITextStyle.body2Regular.copyWith(
                                    color: BaseColors.textTitle,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 24),
                      ],
                      Text(
                        'Lựa chọn',
                        style: UITextStyle.body2Medium.copyWith(
                          color: BaseColors.textLabel,
                        ),
                      ),
                      const SizedBox(height: 12),
                      Row(
                        children: <Widget>[
                          Expanded(
                            child: RadioWidget<AssignType?>(
                              value: AssignType.assign,
                              groupValue: state.assignType,
                              onChanged: (value) {
                                ref
                                    .read(assignOrderProvider.notifier)
                                    .changeAssignType(value!);
                              },
                              displayWidget: (context, item) {
                                return _displayAssignText(item!);
                              },
                            ),
                          ),
                          Expanded(
                            child: RadioWidget<AssignType?>(
                              value: AssignType.transfer,
                              groupValue: state.assignType,
                              onChanged: (value) {
                                ref
                                    .read(assignOrderProvider.notifier)
                                    .changeAssignType(value!);
                              },
                              displayWidget: (context, item) {
                                return _displayAssignText(item!);
                              },
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 24),
                      CardCustomWidget(
                        titleWidget: state.staff != null
                            ? Row(
                                children: [
                                  Expanded(
                                    child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      mainAxisSize: MainAxisSize.min,
                                      children: <Widget>[
                                        Text(
                                          state.assignType.getHint,
                                          style: UITextStyle.caption1Regular
                                              .copyWith(
                                            color: BaseColors.textSubtitle,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                        Text(
                                          state.staff?.fullName ?? '',
                                          style: UITextStyle.body1Regular,
                                        ),
                                        const SizedBox(height: 2),
                                        Text(
                                          state.staff?.username ?? '',
                                          style: UITextStyle.caption1Regular
                                              .copyWith(
                                            color: BaseColors.textSubtitle,
                                          ),
                                        ),
                                        const SizedBox(height: 4),
                                      ],
                                    ),
                                  ),
                                  Visibility(
                                    visible: state.staff != null,
                                    child: InkWellWidget(
                                      onTap: () {
                                        ref
                                            .read(assignOrderProvider.notifier)
                                            .deleteStaff();
                                      },
                                      child:
                                          MyAssets.icons.iconCloseCircle.svg(),
                                    ),
                                  ),
                                ],
                              )
                            : Row(
                                children: <Widget>[
                                  Expanded(
                                    child: Text(
                                      state.assignType.getHint,
                                      style: UITextStyle.body1Regular,
                                    ),
                                  ),
                                  MyAssets.icons.iconSearchS20.svg(),
                                ],
                              ),
                        titleStyle: UITextStyle.body1Regular.copyWith(
                          color: BaseColors.textPlaceholder,
                        ),
                        padding: state.staff != null
                            ? const EdgeInsets.symmetric(
                                horizontal: 16,
                                vertical: 8,
                              )
                            : const EdgeInsets.all(16),
                        onPress: () {
                          String? schedule =
                              widget.orderInfo.shippingInfo?.scheduleTime ??
                                  widget.orderInfo.shippingInfo?.startTime;
                          AppBottomSheet.showNormalBottomSheet(
                            context,
                            title: state.assignType.getHint,
                            height: MediaQuery.of(context).size.height * 0.95,
                            child: ChooseStaffPage(
                              orderType: widget.orderInfo.orderType,
                              workerType: state.assignType == AssignType.assign
                                  ? WorkerType.assign
                                  : WorkerType.transfer,
                              provinceCode:
                                  widget.orderInfo.shippingInfo?.provinceCode ??
                                      '',
                              wardCode:
                                  widget.orderInfo.shippingInfo?.wardCode ??
                                      '',
                              onSelectStaff: (staff) {
                                ref
                                    .read(assignOrderProvider.notifier)
                                    .selectStaff(
                                      staff: staff,
                                    );
                              },
                              scheduleTime: schedule != null
                                  ? DateTime.parse(schedule)
                                  : DateTime.now(),
                            ),
                          );
                        },
                      ),
                      const SizedBox(height: 24),
                    ],
                  ),
                ),
              ],
            ),
          ),
          Positioned(
            left: 0,
            right: 0,
            bottom: 0,
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 8,
              ),
              child: SafeArea(
                child: Row(
                  children: <Widget>[
                    Expanded(
                      child: BaseButton(
                        text: "Huỷ bỏ",
                        backgroundColor: BaseColors.backgroundGray,
                        textColor: BaseColors.textLabel,
                        onTap: () {
                          context.pop();
                        },
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: BaseButton(
                        text: "Xác nhận",
                        onTap: () {
                          if (state.staff == null) {
                            AppDialog.showDialogCenter(
                              context,
                              message:
                                  "Bạn chưa chọn thợ được giao/chuyển việc",
                              status: DialogStatus.error,
                            );
                            return;
                          } else {
                            context.pop();
                            widget.onAssign
                                ?.call(state.staff!, state.assignType);
                          }
                        },
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      );
    }
  }

  Widget _displayAssignText(AssignType type) {
    return Text(
      type.getTitle,
      style: UITextStyle.body1Regular.copyWith(
        color: BaseColors.textBody,
      ),
    );
  }
}
