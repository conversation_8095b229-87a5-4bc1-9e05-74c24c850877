import 'package:equatable/equatable.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/domain/enums/requirement_warranty/requirement_warranty_status_enum.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_order/widgets/chip_entity.dart';

class FilterWorkScheduleState extends Equatable {
  final LoadStatus loadStatus;
  final String? startDateStr;
  final String? endDateStr;
  final DateTime? startDate;
  final DateTime? endDate;
  final List<EnumRequirementWarrantyStatus>? warrantyStatus;
  final List<EnumRequirementWarrantyStatus>? warrantyStatusSelected;
  final List<ChipEntity>? complainUserType;

  const FilterWorkScheduleState({
    this.loadStatus = LoadStatus.initial,
    this.startDateStr,
    this.endDateStr,
    this.startDate,
    this.endDate,
    this.warrantyStatus,
    this.warrantyStatusSelected,
    this.complainUserType,
  });

  FilterWorkScheduleState copyWith({
    LoadStatus? loadStatus,
    CodeEntity? province,
    List<CodeEntity>? districts,
    String? startDateStr,
    String? endDateStr,
    DateTime? startDate,
    DateTime? endDate,
    List<EnumRequirementWarrantyStatus>? warrantyStatus,
    List<EnumRequirementWarrantyStatus>? warrantyStatusSelected,
    List<ChipEntity>? complainUserType,
  }) {
    return FilterWorkScheduleState(
      loadStatus: loadStatus ?? this.loadStatus,
      startDateStr: startDateStr ?? this.startDateStr,
      endDateStr: endDateStr ?? this.endDateStr,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      warrantyStatus: warrantyStatus ?? this.warrantyStatus,
      warrantyStatusSelected:
          warrantyStatusSelected ?? this.warrantyStatusSelected,
      complainUserType: complainUserType ?? this.complainUserType,
    );
  }

  @override
  List<Object?> get props => [
        loadStatus,
        startDateStr,
        endDateStr,
        startDate,
        endDate,
        warrantyStatus,
        warrantyStatusSelected,
        complainUserType,
      ];
}
