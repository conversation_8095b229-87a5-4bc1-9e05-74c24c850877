import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:base_ui/typography/typography.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/generated/assets.gen.dart';
import 'package:vcc/domain/entities/complain/chip_complain_entity.dart';
import 'package:vcc/presentation/views/dialogs/date_range.dart';
import 'package:vcc/presentation/views/widgets/button/base_button.dart';
import 'package:vcc/presentation/views/widgets/drop_down_widget.dart';
import 'package:vcc/presentation/views/widgets/inkwell_widget.dart';
import 'filter_work_schedule_view_model.dart';

class FilterWorkScheduleArgumentCallBack {
  final DateTime? startTime;
  final DateTime? endTime;

  FilterWorkScheduleArgumentCallBack({
    this.startTime,
    this.endTime,
  });
}

class FilterWorkScheduleViewSheet extends StatefulHookConsumerWidget {
  final Function(FilterWorkScheduleArgumentCallBack value) onApply;
  final String? startDateStr;
  final String? endDateStr;
  final DateTime? startDate;
  final DateTime? endDate;

  const FilterWorkScheduleViewSheet({
    super.key,
    required this.onApply,
    this.startDateStr,
    this.endDateStr,
    this.endDate,
    this.startDate,
  });

  @override
  ConsumerState<FilterWorkScheduleViewSheet> createState() =>
      _FilterTicketViewSheetState();
}

class _FilterTicketViewSheetState
    extends ConsumerState<FilterWorkScheduleViewSheet> {
  @override
  void initState() {
    Future(
      () {
        ref.read(filterWarrantyProvider.notifier).initData(
              startDate: widget.startDate,
              endDate: widget.endDate,
            );
      },
    );
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    var filterState = ref.watch(filterWarrantyProvider);

    return Scaffold(
      backgroundColor: BaseColors.backgroundWhite,
      resizeToAvoidBottomInset: false,
      bottomNavigationBar: Padding(
        padding: const EdgeInsets.symmetric(
          horizontal: 16,
          vertical: 8,
        ),
        child: SafeArea(
          child: Row(
            children: <Widget>[
              Expanded(
                child: BaseButton(
                  text: "Đặt lại",
                  backgroundColor: BaseColors.backgroundWhite,
                  decoration: BoxDecoration(
                    border: Border.all(
                      color: BaseColors.primary,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  textColor: BaseColors.primary,
                  onTap: () {
                    ref.read(filterWarrantyProvider.notifier).clearDataFilter();
                    Navigator.pop(context);
                    widget.onApply(
                      FilterWorkScheduleArgumentCallBack(
                        startTime: null,
                        endTime: null,
                      ),
                    );
                  },
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: BaseButton(
                  text: "Áp dụng",
                  onTap: () {
                    Navigator.pop(context);
                    widget.onApply(
                      FilterWorkScheduleArgumentCallBack(
                        startTime: filterState.startDate,
                        endTime: filterState.endDate,
                      ),
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Thời gian bắt đầu - kết thúc',
                textAlign: TextAlign.start,
                style: UITextStyle.body1Medium.copyWith(
                  color: BaseColors.textLabel,
                ),
              ),
              const SizedBox(height: 12),
              DropdownWidget(
                labelText: "Từ ngày - Đến ngày",
                enabled: true,
                suffix: MyAssets.icons.iconCalendarS24.svg(),
                content: filterState.startDateStr != null
                    ? "${filterState.startDateStr} đến ${filterState.endDateStr}"
                    : null,
                onTap: () {
                  showDialog(
                    builder: (context) => Dialog(
                      child: DateRangePicker(
                        startDate: filterState.startDate ??
                            DateTime.now().subtract(const Duration(days: 1)),
                        endDate: filterState.endDate ?? DateTime.now(),
                        showButton: true,
                        lastDate: DateTime.now(),
                        onChange: (startDate, endDate) {
                          ref
                              .read(filterWarrantyProvider.notifier)
                              .setStartDate(
                                startDate,
                              );
                          ref.read(filterWarrantyProvider.notifier).setEndDate(
                                endDate,
                              );
                          Navigator.of(context).pop();
                        },
                      ),
                    ),
                    context: context,
                  );
                },
              ),
              const SizedBox(height: 20),
            ],
          ),
        ),
      ),
    );
  }

  Widget buildSingleCard({
    required ChipComplainEntity item,
    required Function() onTap,
  }) {
    return Padding(
      padding: const EdgeInsets.only(
        right: 8,
        bottom: 8,
      ),
      child: InkWellWidget(
        onTap: onTap,
        child: Container(
          decoration: BoxDecoration(
            color: item.isSelected
                ? CoreColors.primary01
                : BaseColors.borderDisable,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16,
              vertical: 7,
            ),
            child: Text(
              item.content ?? "",
              style: UITextStyle.body2Regular.copyWith(
                color:
                    item.isSelected ? BaseColors.primary : BaseColors.textBody,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
