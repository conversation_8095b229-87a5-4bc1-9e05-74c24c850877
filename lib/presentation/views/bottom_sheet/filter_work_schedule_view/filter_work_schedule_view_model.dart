import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/app_configs/date_time_formater.dart';
import 'package:vcc/presentation/views/bottom_sheet/filter_work_schedule_view/filter_work_schedule_state.dart';
import 'package:vcc/utils/string_utils.dart';

final filterWarrantyProvider = StateNotifierProvider.autoDispose<
    FilterWorkScheduleViewModel,
    FilterWorkScheduleState>((ref) => FilterWorkScheduleViewModel(ref: ref));

class FilterWorkScheduleViewModel
    extends StateNotifier<FilterWorkScheduleState> {
  final Ref ref;

  FilterWorkScheduleViewModel({
    required this.ref,
  }) : super(const FilterWorkScheduleState());

  void initData({
    DateTime? startDate,
    DateTime? endDate,
  }) {
    setStartDate(startDate);
    setEndDate(endDate);
  }

  void setStartDate(DateTime? startDate) {
    state = state.copyWith(
      startDateStr: startDate != null
          ? StringUtils.dateToStringFormat(
              startDate,
              DateTimeFormater.dateFormatVi,
            )
          : null,
      startDate: startDate,
    );
  }

  void setEndDate(DateTime? endDate) {
    state = state.copyWith(
      endDateStr: endDate != null
          ? StringUtils.dateToStringFormat(
              endDate,
              DateTimeFormater.dateFormatVi,
            )
          : null,
      endDate: endDate,
    );
  }

  void clearDataFilter() {
    state = state.copyWith(
      startDate: null,
      endDate: null,
    );
  }
}
