import 'package:base_ui/base_ui.dart';
import 'package:base_ui/typography/app_text_styles.dart';
import 'package:flutter/material.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';

class LocationSelectedWidget extends StatelessWidget {
  final AddressEntity? address;
  final Function(int index)? onTap;

  const LocationSelectedWidget({
    super.key,
    this.address,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    List<CodeEntity> locations = [];

    if (address?.ward != null) {
      locations = [
        address!.province!,
        address!.district!,
        address!.ward!,
      ];
    } else if (address?.district != null) {
      locations = [
        address!.province!,
        address!.district!,
      ];
    } else if (address?.province != null) {
      locations = [
        address!.province!,
      ];
    }

    if (locations.isEmpty) {
      return const SizedBox();
    }

    return SizedBox(
      height: 32,
      child: ListView.separated(
        scrollDirection: Axis.horizontal,
        shrinkWrap: true,
        itemCount: locations.length,
        separatorBuilder: (_, __) => const SizedBox(width: 8),
        itemBuilder: (context, index) {
          return InkWell(
            highlightColor: Colors.transparent,
            splashColor: Colors.transparent,
            onTap: () {
              onTap?.call(index);
            },
            child: Container(
              padding: const EdgeInsets.symmetric(
                horizontal: 12,
                vertical: 6,
              ),
              decoration: BoxDecoration(
                color: BaseColors.backgroundGray,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                locations[index].name ?? '',
                style: UITextStyle.body2Regular.copyWith(
                  color: BaseColors.textBody,
                ),
              ),
            ),
          );
        },
      ),
    );
  }
}
