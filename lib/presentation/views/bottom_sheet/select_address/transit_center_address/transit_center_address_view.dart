import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/domain/entities/code_entity.dart';
import 'package:vcc/domain/entities/isar/address_entity.dart';
import 'package:vcc/domain/enums/address_type.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/select_address_reduce_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/select_address_view.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/transit_center_address/transit%20center_address_view_model.dart';

/// TransitCenterAddressView dùng để hiển thị thông tin địa chỉ trung tâm trung chuyển giữa địa bàn 2 cấp và 3 cấp
class TransitCenterAddressView extends StatefulHookConsumerWidget {
  final AddressEntity? addressSelected;
  final List<CodeEntity>? districtsSelected;
  final Function(AddressEntity address)? onSelectAddress;
  final Function(List<CodeEntity> districts)? onSelectDistricts;
  final AddressType? maxSelect;
  final bool isSelectOnlyProvince;
  final bool isSelectOnlyDistrict;

  const TransitCenterAddressView({
    super.key,
    this.addressSelected,
    this.onSelectAddress,
    this.onSelectDistricts,
    this.maxSelect,
    this.isSelectOnlyProvince = false,
    this.isSelectOnlyDistrict = false,
    this.districtsSelected = const <CodeEntity>[],
  });

  @override
  ConsumerState<TransitCenterAddressView> createState() =>
      _SelectAddressReduceViewState();
}

class _SelectAddressReduceViewState
    extends ConsumerState<TransitCenterAddressView> {
  @override
  void initState() {
    super.initState();
    _initializeAddressData();
  }

  Future<void> _initializeAddressData() async {
    Future(() async {
      await ref.read(transitCenterAddressProvider.notifier).getConfigArea();
    });
  }

  @override
  void dispose() {
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(transitCenterAddressProvider);

    return state.loadStatus == LoadStatus.success &&
            GlobalData.instance.isActiveArea
        ? SelectAddressReduceView(
            addressSelected: widget.addressSelected,
            onSelectAddress: widget.onSelectAddress,
            maxSelect: widget.maxSelect ?? AddressType.province,
            isSelectOnlyProvince: widget.isSelectOnlyProvince,
          )
        : SelectAddressView(
            addressSelected: widget.addressSelected,
            onSelectAddress: widget.onSelectAddress,
            onSelectDistricts: widget.onSelectDistricts,
            maxSelect: widget.maxSelect ?? AddressType.district,
            isSelectOnlyProvince: widget.isSelectOnlyProvince,
            isSelectOnlyDistrict: widget.isSelectOnlyDistrict,
            districtsSelected: widget.districtsSelected,
          );
  }
}
