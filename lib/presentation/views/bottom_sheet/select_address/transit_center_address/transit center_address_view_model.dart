import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:vcc/data/global/global_data.dart';
import 'package:vcc/data/repositories/resource_repository.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/domain/enums/load_status.dart';
import 'package:vcc/presentation/views/bottom_sheet/select_address/transit_center_address/transit_center_address_state.dart';


final transitCenterAddressProvider = StateNotifierProvider.autoDispose<
    TransitCenterAddressViewModel, TransitCenterAddressState>(
  (ref) => TransitCenterAddressViewModel(ref: ref),
);

class TransitCenterAddressViewModel extends StateNotifier<TransitCenterAddressState> {
  final Ref ref;

  TransitCenterAddressViewModel({
    required this.ref,
  }) : super(const TransitCenterAddressState());

  Future<void> getConfigArea() async {
    try {
      state = state.copyWith(
        loadStatus: LoadStatus.loading,
      );
      final result = await appLocator<ResourceRepository>().getConfigArea();

      result?.when(
        success: (data) async {
          state = state.copyWith(
            loadStatus: LoadStatus.success,
          );
          GlobalData.instance.isActiveArea = data.isActive ?? false;
        },
        error: (err) {
          state = state.copyWith(
            loadStatus: LoadStatus.failure,
          );
          GlobalData.instance.isActiveArea = false;
        },
      );
    } catch (e) {
      state = state.copyWith(
        loadStatus: LoadStatus.failure,
      );
      GlobalData.instance.isActiveArea = false;
    }
  }
}
