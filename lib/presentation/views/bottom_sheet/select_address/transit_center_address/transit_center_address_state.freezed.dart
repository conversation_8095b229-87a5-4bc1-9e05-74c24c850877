// coverage:ignore-file
// GENERATED CODE - DO NOT MODIFY BY HAND
// ignore_for_file: type=lint
// ignore_for_file: unused_element, deprecated_member_use, deprecated_member_use_from_same_package, use_function_type_syntax_for_parameters, unnecessary_const, avoid_init_to_null, invalid_override_different_default_values_named, prefer_expression_function_bodies, annotate_overrides, invalid_annotation_target, unnecessary_question_mark

part of 'transit_center_address_state.dart';

// **************************************************************************
// FreezedGenerator
// **************************************************************************

T _$identity<T>(T value) => value;

final _privateConstructorUsedError = UnsupportedError(
    'It seems like you constructed your class using `MyClass._()`. This constructor is only meant to be used by freezed and you are not supposed to need it nor use it.\nPlease check the documentation here for more information: https://github.com/rrousselGit/freezed#adding-getters-and-methods-to-our-models');

/// @nodoc
mixin _$TransitCenterAddressState {
  LoadStatus get loadStatus => throw _privateConstructorUsedError;

  @JsonKey(ignore: true)
  $TransitCenterAddressStateCopyWith<TransitCenterAddressState> get copyWith =>
      throw _privateConstructorUsedError;
}

/// @nodoc
abstract class $TransitCenterAddressStateCopyWith<$Res> {
  factory $TransitCenterAddressStateCopyWith(TransitCenterAddressState value,
          $Res Function(TransitCenterAddressState) then) =
      _$TransitCenterAddressStateCopyWithImpl<$Res, TransitCenterAddressState>;
  @useResult
  $Res call({LoadStatus loadStatus});
}

/// @nodoc
class _$TransitCenterAddressStateCopyWithImpl<$Res,
        $Val extends TransitCenterAddressState>
    implements $TransitCenterAddressStateCopyWith<$Res> {
  _$TransitCenterAddressStateCopyWithImpl(this._value, this._then);

  // ignore: unused_field
  final $Val _value;
  // ignore: unused_field
  final $Res Function($Val) _then;

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadStatus = null,
  }) {
    return _then(_value.copyWith(
      loadStatus: null == loadStatus
          ? _value.loadStatus
          : loadStatus // ignore: cast_nullable_to_non_nullable
              as LoadStatus,
    ) as $Val);
  }
}

/// @nodoc
abstract class _$$TransitCenterAddressStateImplCopyWith<$Res>
    implements $TransitCenterAddressStateCopyWith<$Res> {
  factory _$$TransitCenterAddressStateImplCopyWith(
          _$TransitCenterAddressStateImpl value,
          $Res Function(_$TransitCenterAddressStateImpl) then) =
      __$$TransitCenterAddressStateImplCopyWithImpl<$Res>;
  @override
  @useResult
  $Res call({LoadStatus loadStatus});
}

/// @nodoc
class __$$TransitCenterAddressStateImplCopyWithImpl<$Res>
    extends _$TransitCenterAddressStateCopyWithImpl<$Res,
        _$TransitCenterAddressStateImpl>
    implements _$$TransitCenterAddressStateImplCopyWith<$Res> {
  __$$TransitCenterAddressStateImplCopyWithImpl(
      _$TransitCenterAddressStateImpl _value,
      $Res Function(_$TransitCenterAddressStateImpl) _then)
      : super(_value, _then);

  @pragma('vm:prefer-inline')
  @override
  $Res call({
    Object? loadStatus = null,
  }) {
    return _then(_$TransitCenterAddressStateImpl(
      loadStatus: null == loadStatus
          ? _value.loadStatus
          : loadStatus // ignore: cast_nullable_to_non_nullable
              as LoadStatus,
    ));
  }
}

/// @nodoc

class _$TransitCenterAddressStateImpl implements _TransitCenterAddressState {
  const _$TransitCenterAddressStateImpl({this.loadStatus = LoadStatus.initial});

  @override
  @JsonKey()
  final LoadStatus loadStatus;

  @override
  String toString() {
    return 'TransitCenterAddressState(loadStatus: $loadStatus)';
  }

  @override
  bool operator ==(Object other) {
    return identical(this, other) ||
        (other.runtimeType == runtimeType &&
            other is _$TransitCenterAddressStateImpl &&
            (identical(other.loadStatus, loadStatus) ||
                other.loadStatus == loadStatus));
  }

  @override
  int get hashCode => Object.hash(runtimeType, loadStatus);

  @JsonKey(ignore: true)
  @override
  @pragma('vm:prefer-inline')
  _$$TransitCenterAddressStateImplCopyWith<_$TransitCenterAddressStateImpl>
      get copyWith => __$$TransitCenterAddressStateImplCopyWithImpl<
          _$TransitCenterAddressStateImpl>(this, _$identity);
}

abstract class _TransitCenterAddressState implements TransitCenterAddressState {
  const factory _TransitCenterAddressState({final LoadStatus loadStatus}) =
      _$TransitCenterAddressStateImpl;

  @override
  LoadStatus get loadStatus;
  @override
  @JsonKey(ignore: true)
  _$$TransitCenterAddressStateImplCopyWith<_$TransitCenterAddressStateImpl>
      get copyWith => throw _privateConstructorUsedError;
}
