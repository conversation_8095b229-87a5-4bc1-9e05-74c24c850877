// import 'package:base_ui/base_ui.dart';
// import 'package:base_ui/typography/app_text_styles.dart';
// import 'package:flutter/material.dart';
// import 'package:go_router/go_router.dart';
// import 'package:hooks_riverpod/hooks_riverpod.dart';
// import 'package:vcc/data/global/global_data.dart';
// import 'package:vcc/domain/entities/code_entity.dart';
// import 'package:vcc/domain/entities/isar/address_entity.dart';
// import 'package:vcc/domain/enums/address_type.dart';
// import 'package:vcc/presentation/views/bottom_sheet/select_address/select_address_view_model.dart';
// import 'package:vcc/presentation/views/bottom_sheet/select_address/widgets/choose_address_widget.dart';
// import 'package:vcc/presentation/views/bottom_sheet/select_address/widgets/location_selected_widget.dart';
// import 'package:vcc/presentation/views/widgets/button/app_text_button.dart';
// import 'package:vcc/presentation/views/widgets/button/base_button.dart';
// import 'package:vcc/presentation/views/widgets/text_field/text_field_widget.dart';
//
//
// class SelectAddressReduceView extends StatefulHookConsumerWidget {
//   final AddressEntity? addressSelected;
//   final List<CodeEntity>? districtsSelected;
//   final Function(AddressEntity address)? onSelectAddress;
//   final Function(List<CodeEntity> districts)? onSelectDistricts;
//   final AddressType maxSelect;
//   final bool isSelectOnlyProvince;
//   final bool isSelectOnlyDistrict;
//
//   const SelectAddressReduceView({
//     super.key,
//     this.addressSelected,
//     this.onSelectAddress,
//     this.onSelectDistricts,
//     this.maxSelect = AddressType.district,
//     this.isSelectOnlyProvince = false,
//     this.isSelectOnlyDistrict = false,
//     this.districtsSelected = const <CodeEntity>[],
//   });
//
//   @override
//   ConsumerState<SelectAddressReduceView> createState() => _SelectAddressReduceViewState();
// }
//
// class _SelectAddressReduceViewState extends ConsumerState<SelectAddressReduceView>
//     with TickerProviderStateMixin {
//   late final PageController _pageController = PageController();
//   late final TextEditingController _detailController;
//   final _formKey = GlobalKey<FormState>();
//
//   @override
//   void initState() {
//     super.initState();
//
//     _detailController = TextEditingController(
//       text: widget.addressSelected?.addressDetail,
//     );
//
//     _initializeAddressData();
//   }
//
//   Future<void> _initializeAddressData() async {
//     Future(() async {
//       await ref.read(selectAddressProvider.notifier).getConfigArea();
//       ref.read(selectAddressProvider.notifier).getProvinces();
//
//       if (widget.addressSelected != null) {
//         ref
//             .read(selectAddressProvider.notifier)
//             .setAddressDefault(widget.addressSelected!);
//         if (!widget.isSelectOnlyProvince) {
//           navigateToPage(3);
//         }
//       }
//
//       if (widget.isSelectOnlyDistrict) {
//         await ref.read(selectAddressProvider.notifier).getDistricts(
//           provinceCode: widget.addressSelected?.province?.code ?? '',
//           districts: widget.districtsSelected,
//         );
//         ref
//             .read(selectAddressProvider.notifier)
//             .setDistrictsSelected(widget.districtsSelected ?? []);
//         navigateToPage(1);
//       }
//     });
//   }
//
//   @override
//   void dispose() {
//     _pageController.dispose();
//     _detailController.dispose();
//     super.dispose();
//   }
//
//   void navigateToPage(int index) {
//     _pageController.animateToPage(
//       index,
//       duration: const Duration(milliseconds: 200),
//       curve: Curves.ease,
//     );
//   }
//
//   void _onConfirm(SelectAddressState state) {
//     if (_formKey.currentState?.validate() ?? false) {
//       final address = state.addressSelected!
//         ..addressDetail = _detailController.text;
//       context.pop();
//       widget.onSelectAddress?.call(address);
//     }
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     final state = ref.watch(selectAddressProvider);
//     final notifier = ref.read(selectAddressProvider.notifier);
//
//     return PopScope(
//       canPop: false,
//       child: Scaffold(
//         backgroundColor: Colors.white,
//         resizeToAvoidBottomInset: false,
//         bottomNavigationBar: _buildBottomButton(state),
//         body: GestureDetector(
//           onTap: () => FocusScope.of(context).unfocus(),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               if (state.addressSelected?.province != null)
//                 _buildSelectedArea(state, notifier),
//               Expanded(child: _buildPageView(state, notifier)),
//               if (widget.isSelectOnlyDistrict) ...[
//                 _buildConfirmDistrictButton(state),
//               ]
//             ],
//           ),
//         ),
//       ),
//     );
//   }
//
//   Widget _buildBottomButton(SelectAddressState state) {
//     final address = state.addressSelected;
//     final canConfirm = GlobalData.instance.isActiveArea
//         ? address?.province != null
//         : address?.province != null && address?.district != null;
//
//     if (widget.maxSelect ==
//         (GlobalData.instance.isActiveArea
//             ? AddressType.province
//             : AddressType.district)) {
//       return SafeArea(
//         child: Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
//           child: BaseButton(
//             text: "Xác nhận",
//             isEnable: canConfirm,
//             onTap: () {
//               context.pop();
//               widget.onSelectAddress?.call(address!);
//             },
//           ),
//         ),
//       );
//     }
//     return const SizedBox();
//   }
//
//   Widget _buildSelectedArea(
//       SelectAddressState state, SelectAddressViewModel notifier) {
//     return Container(
//       padding: const EdgeInsets.fromLTRB(16, 8, 8, 16),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           Row(
//             children: [
//               Expanded(
//                 child: Text(
//                   "Khu vực được chọn",
//                   style: UITextStyle.body2Medium
//                       .copyWith(color: BaseColors.textSubtitle),
//                 ),
//               ),
//               AppTextButton(
//                 title: "Thiết lập lại",
//                 titleStyle:
//                 UITextStyle.body2Medium.copyWith(color: BaseColors.primary),
//                 onTap: () {
//                   if (!widget.isSelectOnlyDistrict) {
//                     notifier.resetDataSelected();
//                     _detailController.clear();
//                     FocusScope.of(context).unfocus();
//                     navigateToPage(0);
//                   }
//                 },
//               ),
//             ],
//           ),
//           const SizedBox(height: 8),
//           widget.isSelectOnlyDistrict
//               ? DistrictsSelectedWidget(
//             districts: state.districtsSelected ?? [],
//             onClose: (data) {
//               notifier.selectDistricts(data);
//               FocusScope.of(context).unfocus();
//             },
//           )
//               : LocationSelectedWidget(
//             address: state.addressSelected,
//             onTap: (index) {
//               navigateToPage(index);
//               FocusScope.of(context).unfocus();
//             },
//           ),
//         ],
//       ),
//     );
//   }
//
//   Widget _buildPageView(
//       SelectAddressState state, SelectAddressViewModel notifier) {
//     return PageView(
//       controller: _pageController,
//       physics: const NeverScrollableScrollPhysics(),
//       children: [
//         ChooseAddressWidget(
//           title: "Chọn Tỉnh/Thành phố",
//           hintSearch: "Tìm Tỉnh/Thành phố",
//           data: state.provinces ?? [],
//           locationSelected: state.addressSelected?.province,
//           onSearch: (value) => notifier.getProvinces(keySearch: value),
//           onSelect: (province) async {
//             notifier.selectAddress(province: province);
//
//             if (widget.isSelectOnlyProvince) {
//               context.pop();
//               widget.onSelectAddress
//                   ?.call(ref.read(selectAddressProvider).addressSelected!);
//             } else if (widget.maxSelect.stepNumber >= 2) {
//               if (GlobalData.instance.isActiveArea) {
//                 await notifier.getWards(districtCode: province.code ?? '');
//               } else {
//                 await notifier.getDistricts(
//                   provinceCode: province.code ?? '',
//                 );
//               }
//               navigateToPage(1);
//             }
//           },
//         ),
//         if (!GlobalData.instance.isActiveArea)
//           _buildDistrictSelection(state, notifier),
//         ChooseAddressWidget(
//           title: "Chọn Phường/Xã",
//           hintSearch: "Tìm Phường/Xã",
//           data: state.wards ?? [],
//           locationSelected: state.addressSelected?.ward,
//           onSearch: (value) => notifier.getWards(
//             districtCode: state.addressSelected?.district?.code ?? '',
//             keySearch: value,
//           ),
//           onSelect: (ward) {
//             notifier.selectAddress(ward: ward);
//             if (widget.maxSelect.stepNumber >=
//                 (GlobalData.instance.isActiveArea ? 3 : 4)) {
//               navigateToPage(GlobalData.instance.isActiveArea ? 2 : 3);
//             }
//           },
//         ),
//         Padding(
//           padding: const EdgeInsets.all(16),
//           child: Column(
//             children: [
//               Form(
//                 key: _formKey,
//                 child: TextFieldWidget(
//                   controller: _detailController,
//                   labelText: "Địa chỉ chi tiết",
//                   autofocus: true,
//                   isRequired: true,
//                   validator: (value) =>
//                   value.isEmpty ? "Vui lòng nhập Địa chỉ chi tiết" : null,
//                   textInputAction: TextInputAction.done,
//                 ),
//               ),
//               const SizedBox(height: 40),
//               BaseButton(text: "Xác nhận", onTap: () => _onConfirm(state)),
//             ],
//           ),
//         ),
//       ],
//     );
//   }
//
//   Widget _buildDistrictSelection(
//       SelectAddressState state, SelectAddressViewModel notifier) {
//     final provinceCode = state.addressSelected?.province?.code ?? '';
//     if (widget.isSelectOnlyDistrict) {
//       return ChooseDistrictsWidget(
//         title: "Chọn Quận/Huyện",
//         hintSearch: "Tìm Quận/Huyện",
//         data: state.districts ?? [],
//         locationSelected: state.addressSelected?.district,
//         onSearch: (value) =>
//             notifier.getDistricts(provinceCode: provinceCode, keySearch: value),
//         onSelect: (data, _) => notifier.selectDistricts(data),
//       );
//     }
//
//     return ChooseAddressWidget(
//       title: "Chọn Quận/Huyện",
//       hintSearch: "Tìm Quận/Huyện",
//       data: state.districts ?? [],
//       locationSelected: state.addressSelected?.district,
//       onSearch: (value) =>
//           notifier.getDistricts(provinceCode: provinceCode, keySearch: value),
//       onSelect: (district) async {
//         notifier.selectAddress(district: district);
//         if (widget.maxSelect.stepNumber >= 3) {
//           await notifier.getWards(districtCode: district.code ?? '');
//           navigateToPage(2);
//         }
//       },
//     );
//   }
//
//   Widget _buildConfirmDistrictButton(SelectAddressState state) {
//     return Column(
//       children: [
//         const SizedBox(height: 8),
//         Padding(
//           padding: const EdgeInsets.symmetric(horizontal: 16),
//           child: BaseButton(
//             text: "Xác nhận",
//             onTap: () {
//               context.pop();
//               widget.onSelectDistricts?.call(state.districtsSelected ?? []);
//             },
//           ),
//         ),
//         const SizedBox(height: 12),
//       ],
//     );
//   }
// }
