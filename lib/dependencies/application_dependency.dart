import 'package:vcc/data/remote/api_client.dart';
import 'package:vcc/data/remote/base/api_setup.dart';
import 'package:vcc/data/remote/api_vcc_public.dart';
import 'package:vcc/data/repositories/aio_order_repository.dart';
import 'package:vcc/data/repositories/contract_draft_repository.dart';
import 'package:vcc/data/repositories/customer_quote_repository.dart';
import 'package:vcc/data/repositories/explanation_repository.dart';
import 'package:vcc/data/repositories/complain_repository.dart';
import 'package:vcc/data/repositories/order_agency_repository.dart';
import 'package:vcc/data/repositories/register_repository.dart';
import 'package:vcc/data/repositories/requirement_repository.dart';
import 'package:vcc/data/repositories/authentication_repository.dart';
import 'package:vcc/data/repositories/llm_repository.dart';
import 'package:vcc/data/repositories/notification_repository.dart';
import 'package:vcc/data/repositories/order_repository.dart';
import 'package:vcc/data/repositories/payment_repository.dart';
import 'package:vcc/data/repositories/requirement_warranty_repository.dart';
import 'package:vcc/data/repositories/support_repository.dart';
import 'package:vcc/data/repositories/ticket_repository.dart';
import 'package:vcc/data/repositories/resource_repository.dart';
import 'package:vcc/data/repositories/store_repository.dart';
import 'package:vcc/data/repositories/timekeeping_repository.dart';
import 'package:vcc/data/repositories/user_profile_repository.dart';
import 'package:vcc/dependencies/base_dependencies.dart';
import 'package:vcc/di/locator.dart';
import 'package:vcc/env/env_config.dart';

class ApplicationDependency implements BaseDependencies {
  @override
  apiDependency() {
    appLocator.registerFactory(
      () => ApiClient(
        ApiSetup.getDio(),
        baseUrl: EnvConfig.baseUrl,
      ),
    );

    appLocator.registerFactory(
      () => ApiVccPublic(
        ApiSetup.getDioVccPublic(),
        baseUrl: EnvConfig.baseUrlChatBot,
      ),
    );
  }

  @override
  repositoryDependency() {
    appLocator.registerLazySingleton<LlmRepository>(
      () => LlmRepositoryImpl(
        apiVccPublic: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<AuthRepository>(
      () => AuthRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<UserProfileRepository>(
      () => UserProfileRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<NotificationRepository>(
      () => NotificationRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<OrderRepository>(
      () => OrderRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<AioOrderRepository>(
      () => AioOrderRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<RequirementRepository>(
      () => RequirementRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<StoreRepository>(
      () => StoreRepositoryImpl(),
    );
    appLocator.registerLazySingleton<SupportRepository>(
      () => SupportRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<PaymentRepository>(
      () => PaymentRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<ResourceRepository>(
      () => ResourceRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<TicketRepository>(
      () => TicketRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<ExplanationRepository>(
      () => ExplanationRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<ComplainRepository>(
      () => ComplainRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<RequirementWarrantyRepository>(
      () => RequirementWarrantyRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<RegisterRepository>(
      () => RegisterRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<OrderAgencyRepository>(
      () => OrderAgencyRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<CustomerQuoteRepository>(
      () => CustomerQuoteRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<ContractDraftRepository>(
      () => ContractDraftRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
    appLocator.registerLazySingleton<TimekeepingRepository>(
      () => TimekeepingRepositoryImpl(
        apiClient: appLocator(),
      ),
    );
  }

  @override
  init() {
    apiDependency();
    repositoryDependency();
  }
}
