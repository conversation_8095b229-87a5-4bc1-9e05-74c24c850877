name: vcc
description: "A new Flutter project."
publish_to: 'none' # Remove this line if you wish to publish to pub.dev
version: 1.1.8+47
#version: 1.1.8+1

environment:
  sdk: '>=3.3.0 <4.0.0'
dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  base_ui:
    #    git: "http://**************/mobile/base_ui.git"
    path: ../base_ui
  persistent_storage:
    path: packages/storage/persistent_storage
  cupertino_icons: ^1.0.6
  hooks_riverpod: ^2.5.1
  riverpod_annotation: ^2.3.5
  flutter_hooks: ^0.20.5
  logging: ^1.1.1
  json_annotation: ^4.8.1
  connectivity_plus: ^6.0.3
  gap: ^3.0.0
  go_router: ^14.0.2
  intl: ^0.19.0
  get_it: ^7.6.0
  dio: ^5.2.1+1
  extended_image: ^8.1.0
  uuid: ^4.4.0
  flutter_svg: ^2.0.10+1
  crypto: ^3.0.1
  encrypt: ^5.0.3
  pointycastle: ^3.9.1
  path_provider: ^2.0.11
  asn1lib: ^1.0.0
  equatable: ^2.0.5
  flutter_secure_storage: ^9.2.2
  convex_bottom_bar: ^3.2.0
  flutter_udid: ^3.0.0
  device_info_plus: ^10.1.0
  shared_preferences: ^2.0.17
  cached_network_image: ^3.3.1
  pattern_formatter: ^4.0.0
  flutter_widget_from_html: ^0.15.1
  flutter_widget_from_html_core: ^0.15.1
  url_launcher: ^6.3.1
  url_launcher_ios: ^6.3.2
  share_plus: ^9.0.0
  permission_handler: ^11.3.1
  readmore: ^3.0.0
  fluro: ^2.0.5
  image_picker: ^1.1.2
  file_picker: ^8.0.6
  syncfusion_flutter_pdfviewer: ^26.1.42
  photo_view: ^0.15.0
  flutter_inappwebview: ^6.0.0
  firebase_core: ^2.32.0
  firebase_messaging: ^14.9.4
  pinput: ^5.0.0
  another_flushbar: ^1.12.30
  webview_flutter: ^4.8.0
  retrofit: ^4.1.0
  auto_size_text: ^3.0.0
  saver_gallery: ^3.0.6
  geocoding: ^2.2.0
  location: ^5.0.0
  flutter_image_compress: ^2.0.4
  localstore: ^1.4.0
  flutter_slidable: ^3.1.1
  rxdart: ^0.27.4
  http: ^1.2.1
  open_file: ^3.3.2
  flutter_pdfview: ^1.2.3
  percent_indicator: ^4.2.4
  fl_chart: ^0.69.0
  video_player: ^2.9.2
  chunked_uploader: ^1.1.0
  video_thumbnail: ^0.5.3
  mime: ^1.0.4
  flutter_date_pickers: ^0.4.3
  lottie: ^3.1.3
  syncfusion_flutter_signaturepad: 26.1.42
  speech_to_text: ^6.6.0
  typewritertext: ^3.0.7
  just_audio: ^0.9.26
  audio_video_progress_bar: ^2.0.3
  path: ^1.9.0
  package_info_plus: ^8.0.0
  store_redirect: ^2.0.4
  qr_code_scanner: ^1.0.1
  flutter_markdown: ^0.7.6+2
  any_link_preview:
  timeline_tile: ^2.0.0
  diacritic: ^0.1.6
  camera: ^0.10.6
  camera_android: ^0.10.9+11
  screenshot:
  freezed_annotation: ^2.4.1
  timeago: ^3.7.1
  number_to_vietnamese_words: ^1.0.1+1
  table_calendar: ^3.1.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  build_runner: ^2.4.11
  retrofit_generator: ^8.1.2
  json_serializable: ^6.7.0
  flutter_lints: ^3.0.0
  flutter_gen_runner: ^5.3.1
  riverpod_generator: ^2.2.1
  custom_lint: ^0.6.4
  change_app_package_name: ^1.1.0
  flutter_launcher_icons: ^0.13.1
  test:
  freezed: ^2.4.5

flutter:
  uses-material-design: true
  generate: true
  assets:
    - assets/icons/
    - assets/images/
    - assets/public_key.pem

flutter_gen:
  assets:
    enabled: true
    outputs:
      class_name: MyAssets
  #      package_parameter_enabled: true
  fonts:
    enabled: true
    outputs:
      class_name: MyFontFamily
  output: lib/generated/
  line_length: 80

  integrations:
    flutter_svg: true

flutter_intl:
  enabled: true
  class_name: AppStrings
